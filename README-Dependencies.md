# WMP项目依赖提取和Maven仓库安装指南

## 概述

本指南帮助您从Spring Boot Fat JAR中提取所有依赖并安装到本地Maven仓库中，使您可以通过Maven仓库的方式获取这些依赖。

## 已完成的工作

### 1. 依赖提取
- ✅ 从 `E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\jres-adapter\wmpweb\wmp\wmp-service-zip\target\wmp-service.zip` 解压
- ✅ 提取了Spring Boot Fat JAR中的所有依赖（200个jar文件）
- ✅ 分析了每个jar文件的Maven坐标信息

### 2. 生成的文件
- `lib-extracted/` - 包含所有提取的jar文件（200个）
- `install-to-maven.bat` - Maven安装脚本
- `dependencies-list.txt` - 完整的依赖清单
- `create-maven-install.ps1` - 依赖分析脚本

## 安装步骤

### 第一步：安装依赖到本地Maven仓库

运行以下命令将所有200个依赖安装到您的本地Maven仓库：

```bash
.\install-to-maven.bat
```

这个脚本将：
- 逐个安装200个jar文件到本地Maven仓库
- 显示安装进度和结果
- 统计成功和失败的安装数量
- 自动生成POM文件

### 第二步：验证安装

安装完成后，您可以在以下位置找到依赖：
- Windows: `%USERPROFILE%\.m2\repository\`
- Linux/Mac: `~/.m2/repository/`

### 第三步：在项目中使用依赖

查看 `dependencies-list.txt` 文件，找到您需要的依赖，然后添加到您的 `pom.xml` 中。

例如：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <version>2.2.2.RELEASE</version>
</dependency>
```

## 主要依赖类别

### Spring生态系统
- Spring Boot 2.2.2.RELEASE
- Spring Framework 5.2.9.RELEASE
- Spring Data Redis 2.2.3.RELEASE

### 数据库和缓存
- MySQL Connector 8.0.25
- Redis Jedis 3.1.0
- Apache Commons DBCP2 2.7.0

### JSON处理
- Jackson 2.10.x系列
- Fastjson 1.2.68
- Gson 2.8.6

### 网络通信
- Netty 4.1.x系列
- Apache HttpClient 4.5.10
- Nacos Client 1.1.4

### 工具库
- Apache Commons系列
- Apache POI 3.17
- Guava 25.0-jre

### 自定义模块
- K-Cloud框架模块
- JresCloud 3.0.8.4系列
- WMP业务模块（com.kayakwise.wmp）

## 故障排除

### 如果某些依赖安装失败
1. 检查Maven是否正确安装并配置
2. 确保有足够的磁盘空间
3. 检查网络连接（某些依赖可能需要下载额外资源）

### 如果项目编译时找不到依赖
1. 确认依赖已成功安装到本地仓库
2. 检查pom.xml中的groupId、artifactId、version是否正确
3. 运行 `mvn clean compile` 重新编译

### 版本冲突问题
如果遇到版本冲突，可以：
1. 使用Maven的依赖管理功能
2. 排除冲突的传递依赖
3. 统一版本号

## 文件说明

- `lib-extracted/` - 提取的200个jar文件
- `install-to-maven.bat` - 自动安装脚本（2017行）
- `dependencies-list.txt` - 完整依赖清单，包含Maven坐标
- `create-maven-install.ps1` - PowerShell脚本，用于分析jar文件并生成安装脚本
- `generate-dependency-list.ps1` - 生成依赖清单的脚本

## 注意事项

1. **备份重要数据**：在运行安装脚本前，建议备份现有的Maven本地仓库
2. **磁盘空间**：200个依赖大约需要几百MB的磁盘空间
3. **网络环境**：某些依赖可能需要访问外部资源，确保网络连接正常
4. **Maven版本**：建议使用Maven 3.6+版本

## 成功标志

安装成功后，您应该能够：
1. 在本地Maven仓库中找到所有依赖
2. 在新的Maven项目中引用这些依赖
3. 成功编译包含这些依赖的项目

---

**提示**：如果您需要在其他机器上使用这些依赖，可以将整个 `~/.m2/repository` 目录复制到目标机器，或者重新运行 `install-to-maven.bat` 脚本。
