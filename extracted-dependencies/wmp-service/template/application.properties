##################################
#   JRES
##################################
# \u670D\u52A1\u522B\u79F0
app.alias=$!{appType}
#app.alias=WmpWebService
# \u670D\u52A1\u540D\u79F0
app.name=$!{appType}
#app.name=WmpWebService
# \u670D\u52A1\u5206\u7EC4
app.group=$!{group}
# \u7248\u672C
app.version=2.0
# \u90E8\u7F72\u5305\u7248\u672C
app.deploy.version=$!{version}
# \u6CE8\u518C\u4E2D\u5FC3\u5730\u5740
app.registry.address=$!{svc_zookeeper_connect_string}
# \u670D\u52A1WEB\u7AEF\u53E3
app.server.port=$!{app_server_port}
# \u5168\u5C40\u53C2\u6570\u6821\u9A8C
rpc.validation.enable=true

app.host=$!{app_host}
app.owner=$!{environment}

config.location=config/middleware.properties
logging.config=config/log4j2.xml

EAI_HOME=${workspace}/${app_name}/config/FDS/

##################################
#   \u914D\u7F6E\u4E2D\u5FC3
##################################
# \u914D\u7F6E\u4E2D\u5FC3\u5730\u5740
configCenterUrls=$!{config_center_urls}
# \u5206\u652F
configBranchName=$!{config_branch_name}
# \u662F\u5426\u542F\u7528\u81EA\u52A8\u66F4\u65B0
configcenter.spring.properties.auto-update-enabled=true