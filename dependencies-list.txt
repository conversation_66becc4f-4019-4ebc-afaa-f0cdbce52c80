# WMP Project Dependencies List
# Generated from extracted jar files
# Total: 200 dependencies

## Maven Dependencies (for pom.xml)

<dependency>
    <groupId>unknown</groupId>
    <artifactId>animal-sniffer-annotations</artifactId>
    <version>1.14</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>antisamy</artifactId>
    <version>1.5.3</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>apm-toolkit-opentracing</artifactId>
    <version>8.1.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>apm-toolkit-trace</artifactId>
    <version>8.1.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>asm</artifactId>
    <version>4.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>aspectjweaver</artifactId>
    <version>1.9.5</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>audience-annotations</artifactId>
    <version>0.5.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>batik-css</artifactId>
    <version>1.8</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>batik-ext</artifactId>
    <version>1.8</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>batik-util</artifactId>
    <version>1.8</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>bcprov-jdk15on</artifactId>
    <version>1.60</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>bsh-core-2.0b4</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>cglib</artifactId>
    <version>3.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>checker-compat-qual</artifactId>
    <version>2.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>classmate</artifactId>
    <version>1.5.1</version>
</dependency>

<dependency>
    <groupId>commons-beanutils</groupId>
    <artifactId>commons-beanutils</artifactId>
    <version>1.9.4</version>
</dependency>

<dependency>
    <groupId>commons-beanutils-core</groupId>
    <artifactId>commons-beanutils-core</artifactId>
    <version>1.8.3</version>
</dependency>

<dependency>
    <groupId>commons-codec</groupId>
    <artifactId>commons-codec</artifactId>
    <version>1.10</version>
</dependency>

<dependency>
    <groupId>commons-collections</groupId>
    <artifactId>commons-collections</artifactId>
    <version>3.2.2</version>
</dependency>

<dependency>
    <groupId>commons-collections4</groupId>
    <artifactId>commons-collections4</artifactId>
    <version>4.4</version>
</dependency>

<dependency>
    <groupId>commons-configuration</groupId>
    <artifactId>commons-configuration</artifactId>
    <version>1.10</version>
</dependency>

<dependency>
    <groupId>commons-dbcp2</groupId>
    <artifactId>commons-dbcp2</artifactId>
    <version>2.7.0</version>
</dependency>

<dependency>
    <groupId>commons-fileupload</groupId>
    <artifactId>commons-fileupload</artifactId>
    <version>1.3.3</version>
</dependency>

<dependency>
    <groupId>commons-httpclient</groupId>
    <artifactId>commons-httpclient</artifactId>
    <version>3.1</version>
</dependency>

<dependency>
    <groupId>commons-io</groupId>
    <artifactId>commons-io</artifactId>
    <version>2.5</version>
</dependency>

<dependency>
    <groupId>commons-lang</groupId>
    <artifactId>commons-lang</artifactId>
    <version>2.6</version>
</dependency>

<dependency>
    <groupId>commons-lang3</groupId>
    <artifactId>commons-lang3</artifactId>
    <version>3.12.0</version>
</dependency>

<dependency>
    <groupId>commons-logging</groupId>
    <artifactId>commons-logging</artifactId>
    <version>1.2</version>
</dependency>

<dependency>
    <groupId>commons-pool2</groupId>
    <artifactId>commons-pool2</artifactId>
    <version>2.7.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>curator-client</artifactId>
    <version>4.2.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>curator-framework</artifactId>
    <version>4.2.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>curator-recipes</artifactId>
    <version>4.2.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>curvesapi</artifactId>
    <version>1.04</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>cust-center-api</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>cust-center-springcloud-dubbo</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>disruptor</artifactId>
    <version>3.4.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>dom4j</artifactId>
    <version>2.1.3</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>easyexcel</artifactId>
    <version>2.0.5</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>ehcache</artifactId>
    <version>********</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>ehcache</artifactId>
    <version>3.8.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>error_prone_annotations</artifactId>
    <version>2.1.3</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>esapi</artifactId>
    <version>2.1.0.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>FastInfoset</artifactId>
    <version>1.2.16</version>
</dependency>

<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>1.2.68</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>fina-center-api</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>fina-center-springcloud-dubbo</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>ftsp-sdk1.8</artifactId>
    <version>2.2</version>
</dependency>

<dependency>
    <groupId>com.google.code.gson</groupId>
    <artifactId>gson</artifactId>
    <version>2.8.6</version>
</dependency>

<dependency>
    <groupId>com.google.guava</groupId>
    <artifactId>guava</artifactId>
    <version>25.0-jre</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>HdrHistogram</artifactId>
    <version>2.1.11</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>hibernate-validator-6.0.18.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>hibernate-validator-cdi-6.1.0.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
    <version>4.5.10</version>
</dependency>

<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpcore</artifactId>
    <version>4.4.12</version>
</dependency>

<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpmime</artifactId>
    <version>4.5.3</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>istack-commons-runtime</artifactId>
    <version>3.0.8</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>j2objc-annotations</artifactId>
    <version>1.1</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-annotations</artifactId>
    <version>2.10.1</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-core</artifactId>
    <version>2.10.1</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.10.3</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-dataformat-xml</artifactId>
    <version>2.10.1</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-datatype-jdk8</artifactId>
    <version>2.10.1</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-datatype-jsr310</artifactId>
    <version>2.10.1</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-module-jaxb-annotations</artifactId>
    <version>2.10.1</version>
</dependency>

<dependency>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-module-parameter-names</artifactId>
    <version>2.10.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jakarta.activation-api</artifactId>
    <version>1.2.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jakarta.annotation-api</artifactId>
    <version>1.3.5</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jakarta.validation-api</artifactId>
    <version>2.0.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jakarta.xml.bind-api</artifactId>
    <version>2.3.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>javassist</artifactId>
    <version>3.20.0-GA</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>javax.servlet-api</artifactId>
    <version>4.0.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jaxb-runtime</artifactId>
    <version>2.3.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jboss-logging-3.4.1.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jcl-over-slf4j</artifactId>
    <version>1.7.29</version>
</dependency>

<dependency>
    <groupId>redis.clients</groupId>
    <artifactId>jedis</artifactId>
    <version>3.1.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jline</artifactId>
    <version>0.9.94</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>joda-time</artifactId>
    <version>2.10.10</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-common</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-common-base</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-common-gm</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-common-partition</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-common-t2</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-monitor-api</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-monitor-common</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-rpc-api</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-rpc-def</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-rpc-def-log4j2</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-rpc-def-monitor</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-rpc-t2</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-starter</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-starter-rpc-def</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-trace-api</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud</groupId>
    <artifactId>jrescloud-trace-common</artifactId>
    <version>3.0.8.4</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>json</artifactId>
    <version>20171018</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jsr305</artifactId>
    <version>3.0.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>jul-to-slf4j</artifactId>
    <version>1.7.29</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-action</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-action-dubbo</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-cache</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-config</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-core</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-dao</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-service-starter-micro-server</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>k-cloud</groupId>
    <artifactId>k-cloud-xsql</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>kayakwise-transaction-api-QZ</artifactId>
    <version>0.0.4</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>LatencyUtils</artifactId>
    <version>2.0.3</version>
</dependency>

<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j</artifactId>
    <version>1.2.17</version>
</dependency>

<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-api</artifactId>
    <version>2.13.3</version>
</dependency>

<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-core</artifactId>
    <version>2.13.3</version>
</dependency>

<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-jul</artifactId>
    <version>2.13.3</version>
</dependency>

<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-slf4j-impl</artifactId>
    <version>2.13.3</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>lombok</artifactId>
    <version>1.18.10</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>micrometer-core</artifactId>
    <version>1.3.1</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud.middleware</groupId>
    <artifactId>middleware-base-common</artifactId>
    <version>1.0.7</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud.middleware</groupId>
    <artifactId>middleware-base-exception</artifactId>
    <version>1.0.7</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud.middleware</groupId>
    <artifactId>middleware-configcenter-client</artifactId>
    <version>1.2.14</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud.middleware</groupId>
    <artifactId>middleware-configcenter-common</artifactId>
    <version>1.2.14</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud.middleware</groupId>
    <artifactId>middleware-tenant-api</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>com.hundsun.jrescloud.middleware</groupId>
    <artifactId>middleware-tenant-sdk</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <version>8.0.25</version>
</dependency>

<dependency>
    <groupId>com.alibaba.nacos</groupId>
    <artifactId>nacos-api</artifactId>
    <version>1.1.4</version>
</dependency>

<dependency>
    <groupId>com.alibaba.nacos</groupId>
    <artifactId>nacos-client</artifactId>
    <version>1.1.4</version>
</dependency>

<dependency>
    <groupId>com.alibaba.nacos</groupId>
    <artifactId>nacos-common</artifactId>
    <version>1.1.4</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>nekohtml</artifactId>
    <version>1.9.22</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-3.10.6.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-buffer-4.1.48.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-codec-4.1.48.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-codec-http-4.1.43.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-common-4.1.48.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-handler-4.1.48.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-resolver-4.1.48.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-transport-4.1.48.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-transport-native-epoll-4.1.43.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>netty-transport-native-unix-common-4.1.43.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>opentracing-api</artifactId>
    <version>0.31.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>opentracing-noop</artifactId>
    <version>0.31.0</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>paym-api</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>plan-center-api</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>plan-center-springcloud-dubbo</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi</artifactId>
    <version>3.17</version>
</dependency>

<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>3.17</version>
</dependency>

<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml-schemas</artifactId>
    <version>3.17</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>prod-center-api</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>prod-center-springcloud-dubbo</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>rxjava</artifactId>
    <version>1.3.8</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>rxnetty</artifactId>
    <version>0.4.20</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>simpleclient</artifactId>
    <version>0.5.0</version>
</dependency>

<dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-api</artifactId>
    <version>1.7.29</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>snakeyaml</artifactId>
    <version>1.26</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spotbugs-annotations</artifactId>
    <version>3.1.9</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-aop-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-beans-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-actuator-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-actuator-autoconfigure-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-autoconfigure-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-legacy-2.1.0.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-actuator-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-aop-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-json-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-log4j2-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-redis-1.4.1.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-tomcat-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-validation-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-boot-starter-web-2.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-context-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-context-support-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-core-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-data-commons-2.2.3.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-data-keyvalue-2.2.3.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-data-redis-2.2.3.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-expression-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-jcl-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-oxm-5.2.2.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-tx-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-web-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>spring-webmvc-5.2.9.RELEASE</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>stax-api</artifactId>
    <version>1.0.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>stax-ex</artifactId>
    <version>1.8.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>stax2-api</artifactId>
    <version>4.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>sun-client</artifactId>
    <version>1.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>sun-ws-all</artifactId>
    <version>3.2.0</version>
</dependency>

<dependency>
    <groupId>org.apache.tomcat.embed</groupId>
    <artifactId>tomcat-embed-core</artifactId>
    <version>9.0.37</version>
</dependency>

<dependency>
    <groupId>org.apache.tomcat.embed</groupId>
    <artifactId>tomcat-embed-el</artifactId>
    <version>9.0.37</version>
</dependency>

<dependency>
    <groupId>org.apache.tomcat.embed</groupId>
    <artifactId>tomcat-embed-websocket</artifactId>
    <version>9.0.37</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>txw2</artifactId>
    <version>2.3.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>validation-api-2.0.1.Final</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>com.kayakwise.wmp</groupId>
    <artifactId>wmp-base-api</artifactId>
    <version>0.0.1-RELEASE</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>woodstox-core</artifactId>
    <version>6.0.2</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xalan</artifactId>
    <version>2.7.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xercesImpl</artifactId>
    <version>2.8.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xml-apis</artifactId>
    <version>1.3.03</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xml-apis-ext</artifactId>
    <version>1.3.04</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xmlbeans</artifactId>
    <version>2.6.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xmlpull</artifactId>
    <version>1.1.3.1</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xom</artifactId>
    <version>1.2.5</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xpp3_min-1.1.4c</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>xstream</artifactId>
    <version>1.4.10</version>
</dependency>

<dependency>
    <groupId>unknown</groupId>
    <artifactId>zookeeper</artifactId>
    <version>3.4.14</version>
</dependency>

## Summary
Total dependencies extracted: 200

## Key Dependencies Include:
- Spring Boot 2.2.2.RELEASE
- Spring Framework 5.2.9.RELEASE
- Jackson 2.10.x
- Apache Commons libraries
- Netty 4.1.x
- JresCloud 3.0.8.4
- Nacos 1.1.4
- MySQL Connector 8.0.25
- Redis Jedis 3.1.0
- Apache POI 3.17
- Log4j2 2.13.3
- Custom WMP modules (com.kayakwise.wmp)
- K-Cloud framework modules

## Installation Instructions:
1. Run install-to-maven.bat to install all dependencies to local Maven repository
2. Add required dependencies to your pom.xml from the list above
3. Run 'mvn clean compile' to verify dependencies are resolved
