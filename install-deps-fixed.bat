@echo off
echo Starting Maven installation of dependencies...
echo.

REM Install k-cloud dependencies first
echo Installing k-cloud dependencies...
mvn install:install-file -Dfile="lib-extracted\k-cloud-action-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-action -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\k-cloud-action-dubbo-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-action-dubbo -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\k-cloud-cache-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-cache -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\k-cloud-config-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-config -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\k-cloud-dao-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-dao -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\k-cloud-service-starter-micro-server-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-service-starter-micro-server -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\k-cloud-xsql-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-xsql -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true

REM Install parent POM
echo Installing k-cloud parent POM...
mvn install:install-file -Dfile="k-cloud-service-parent.pom" -DgroupId=k-cloud -DartifactId=k-cloud-service -Dversion=1.0.0 -Dpackaging=pom

REM Install WMP dependencies
echo Installing WMP dependencies...
mvn install:install-file -Dfile="lib-extracted\wmp-base-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=wmp-base-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\fina-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=fina-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\fina-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=fina-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\prod-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=prod-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\prod-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=prod-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\plan-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=plan-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\plan-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=plan-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\cust-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=cust-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\cust-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=cust-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\paym-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=paym-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\bala-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=bala-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true

REM Install other critical dependencies
echo Installing other critical dependencies...
mvn install:install-file -Dfile="lib-extracted\ftsp-sdk1.8-2.2.jar" -DgroupId=com.dcfs.fts -DartifactId=ftsp-sdk1.8 -Dversion=2.2 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\sun-client-1.0.jar" -DgroupId=sun-client -DartifactId=sun-client -Dversion=1.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\sun-ws-all-3.2.0.jar" -DgroupId=com.sunyard.ws -DartifactId=sun-ws-all -Dversion=3.2.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="lib-extracted\middleware-configcenter-client-1.2.14.jar" -DgroupId=com.hundsun.jrescloud.middleware -DartifactId=middleware-configcenter-client -Dversion=1.2.14 -Dpackaging=jar -DgeneratePom=true

echo.
echo Critical dependencies installed successfully!
echo Now you can try to compile the project.
pause
