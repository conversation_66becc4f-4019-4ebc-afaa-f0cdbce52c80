mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-action-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-action -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-dao-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-dao -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-action-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-action -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-cache-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-cache -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\fastjson-1.2.68.jar" -DgroupId=com.alibaba -DartifactId=fastjson -Dversion=1.2.68 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-rpc-api-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-rpc-api -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-lang-2.6.jar" -DgroupId=unknown -DartifactId=commons-lang -Dversion=2.6 -Dpackaging=jar -DgeneratePom=true
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-lang3-3.12.0.jar" -DgroupId=org.apache.commons -DartifactId=commons-lang3 -Dversion=3.12.0 -Dpackaging=jar -DgeneratePom=true
