# Comment out bala-related imports in Java files
Write-Host "Commenting out bala-related imports..." -ForegroundColor Green

$balaServiceFiles = Get-ChildItem "wmp-service\src\main\java\com\kayak\bala\service" -Filter "*.java"
$processedCount = 0

foreach ($file in $balaServiceFiles) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Encoding UTF8
    $modified = $false
    
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # Comment out bala-related imports
        if ($line -match "^import com\.kayakwise\.bala\.") {
            $content[$i] = "// $line"
            $modified = $true
        }
        
        # Comment out bala-related class declarations and usages
        if ($line -match "T91[5-9]DubboDecorator|T92[0-6]DubboDecorator") {
            $content[$i] = "// $line"
            $modified = $true
        }
    }
    
    if ($modified) {
        $content | Out-File $file.FullName -Encoding UTF8
        Write-Host "  Modified: $($file.Name)" -ForegroundColor Green
        $processedCount++
    } else {
        Write-Host "  No changes: $($file.Name)" -ForegroundColor Yellow
    }
}

Write-Host "`nProcessed $processedCount files" -ForegroundColor Green

# Also check other files that might import bala classes
Write-Host "`nChecking other files for bala imports..." -ForegroundColor Green

$otherFiles = Get-ChildItem "wmp-service\src\main\java" -Filter "*.java" -Recurse | Where-Object {
    $_.FullName -notlike "*\bala\*" -and 
    (Get-Content $_.FullName -ErrorAction SilentlyContinue | Select-String "kayakwise\.bala")
}

foreach ($file in $otherFiles) {
    Write-Host "Found bala import in: $($file.FullName)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Encoding UTF8
    $modified = $false
    
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        if ($line -match "import com\.kayakwise\.bala\.") {
            $content[$i] = "// $line"
            $modified = $true
        }
    }
    
    if ($modified) {
        $content | Out-File $file.FullName -Encoding UTF8
        Write-Host "  Modified: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Bala imports commenting completed!" -ForegroundColor Green
