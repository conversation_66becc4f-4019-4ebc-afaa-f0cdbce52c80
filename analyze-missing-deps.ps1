# Analyze missing dependencies from compilation errors
Write-Host "Analyzing missing dependencies from jar files..." -ForegroundColor Green

# Get all jar files from lib-extracted
$jarFiles = Get-ChildItem "lib-extracted" -Filter "*.jar"
Write-Host "Found $($jarFiles.Count) jar files to analyze" -ForegroundColor Yellow

# Create a comprehensive mapping of missing packages to jar files
$packageMappings = @{}

foreach ($jar in $jarFiles) {
    $jarName = $jar.BaseName
    Write-Host "Analyzing: $jarName" -ForegroundColor Cyan
    
    try {
        # Extract package information from jar
        $packages = & jar -tf $jar.FullName | Where-Object { $_ -match "\.class$" } | ForEach-Object {
            $classPath = $_ -replace "\.class$", ""
            $packagePath = ($classPath -split "/")[0..($classPath.Split("/").Length - 2)] -join "."
            $packagePath
        } | Sort-Object -Unique
        
        foreach ($package in $packages) {
            if ($package -and $package.Length -gt 0) {
                if (-not $packageMappings.ContainsKey($package)) {
                    $packageMappings[$package] = @()
                }
                $packageMappings[$package] += $jarName
            }
        }
    }
    catch {
        Write-Host "Error analyzing $jarName`: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Define the missing packages from compilation errors
$missingPackages = @(
    "com.kayak.aspect.annotations",
    "com.kayak.core.sql",
    "com.kayak.core.util", 
    "com.kayak.core.dao",
    "com.kayak.core.exception",
    "com.kayak.core.system",
    "com.kayak.base.dao",
    "com.kayak.graphql.annotation",
    "com.kayak.graphql.model",
    "com.kayak.cache.util",
    "com.alibaba.fastjson",
    "com.hundsun.jrescloud.rpc.annotation",
    "org.springframework.beans.factory.annotation",
    "org.springframework.stereotype",
    "org.springframework.util",
    "org.apache.commons.lang3"
)

Write-Host "`nAnalyzing missing packages..." -ForegroundColor Green
$foundMappings = @()

foreach ($missingPkg in $missingPackages) {
    Write-Host "`nLooking for package: $missingPkg" -ForegroundColor Yellow
    
    $foundJars = @()
    foreach ($pkg in $packageMappings.Keys) {
        if ($pkg -like "*$missingPkg*" -or $missingPkg -like "*$pkg*") {
            $foundJars += $packageMappings[$pkg]
        }
    }
    
    if ($foundJars.Count -gt 0) {
        $uniqueJars = $foundJars | Sort-Object -Unique
        Write-Host "  Found in jars: $($uniqueJars -join ', ')" -ForegroundColor Green
        $foundMappings += @{Package = $missingPkg; Jars = $uniqueJars}
    } else {
        Write-Host "  NOT FOUND in any jar" -ForegroundColor Red
    }
}

# Generate installation commands for found dependencies
Write-Host "`nGenerating installation commands..." -ForegroundColor Green
$installCommands = @()

foreach ($mapping in $foundMappings) {
    foreach ($jarName in $mapping.Jars) {
        $jarFile = Get-ChildItem "lib-extracted" -Filter "$jarName.jar" -ErrorAction SilentlyContinue
        if ($jarFile) {
            # Parse Maven coordinates from jar name
            if ($jarName -match "^(.+?)-(\d+(?:\.\d+)*(?:-[A-Za-z0-9\.]+)?)$") {
                $artifactId = $matches[1]
                $version = $matches[2]
                
                # Determine groupId
                $groupId = switch -Regex ($jarName) {
                    "^spring-" { "org.springframework" }
                    "^fastjson" { "com.alibaba" }
                    "^commons-lang3" { "org.apache.commons" }
                    "^jrescloud-" { "com.hundsun.jrescloud" }
                    "^k-cloud-" { "k-cloud" }
                    default { "unknown" }
                }
                
                $installCmd = "mvn install:install-file -Dfile=`"$($jarFile.FullName)`" -DgroupId=$groupId -DartifactId=$artifactId -Dversion=$version -Dpackaging=jar -DgeneratePom=true"
                $installCommands += $installCmd
            }
        }
    }
}

# Save installation script
$installScript = "install-missing-deps.bat"
$installCommands | Out-File -FilePath $installScript -Encoding UTF8

Write-Host "`nAnalysis complete!" -ForegroundColor Green
Write-Host "Found $($foundMappings.Count) package mappings" -ForegroundColor Yellow
Write-Host "Generated $($installCommands.Count) installation commands" -ForegroundColor Yellow
Write-Host "Installation script saved to: $installScript" -ForegroundColor Yellow
