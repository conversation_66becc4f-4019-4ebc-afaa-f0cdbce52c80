@echo off
echo Starting Maven installation of 200 dependencies...
echo.
set SUCCESS_COUNT=0
set FAIL_COUNT=0

echo [1/200] Installing: unknown:animal-sniffer-annotations:1.14
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\animal-sniffer-annotations-1.14.jar" -DgroupId=unknown -DartifactId=animal-sniffer-annotations -Dversion=1.14 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: animal-sniffer-annotations-1.14
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: animal-sniffer-annotations-1.14
    set /a SUCCESS_COUNT+=1
)
echo.
echo [2/200] Installing: unknown:antisamy:1.5.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\antisamy-1.5.3.jar" -DgroupId=unknown -DartifactId=antisamy -Dversion=1.5.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: antisamy-1.5.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: antisamy-1.5.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [3/200] Installing: unknown:apm-toolkit-opentracing:8.1.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\apm-toolkit-opentracing-8.1.0.jar" -DgroupId=unknown -DartifactId=apm-toolkit-opentracing -Dversion=8.1.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: apm-toolkit-opentracing-8.1.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: apm-toolkit-opentracing-8.1.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [4/200] Installing: unknown:apm-toolkit-trace:8.1.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\apm-toolkit-trace-8.1.0.jar" -DgroupId=unknown -DartifactId=apm-toolkit-trace -Dversion=8.1.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: apm-toolkit-trace-8.1.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: apm-toolkit-trace-8.1.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [5/200] Installing: unknown:asm:4.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\asm-4.2.jar" -DgroupId=unknown -DartifactId=asm -Dversion=4.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: asm-4.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: asm-4.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [6/200] Installing: unknown:aspectjweaver:1.9.5
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\aspectjweaver-1.9.5.jar" -DgroupId=unknown -DartifactId=aspectjweaver -Dversion=1.9.5 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: aspectjweaver-1.9.5
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: aspectjweaver-1.9.5
    set /a SUCCESS_COUNT+=1
)
echo.
echo [7/200] Installing: unknown:audience-annotations:0.5.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\audience-annotations-0.5.0.jar" -DgroupId=unknown -DartifactId=audience-annotations -Dversion=0.5.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: audience-annotations-0.5.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: audience-annotations-0.5.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [8/200] Installing: unknown:batik-css:1.8
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\batik-css-1.8.jar" -DgroupId=unknown -DartifactId=batik-css -Dversion=1.8 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: batik-css-1.8
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: batik-css-1.8
    set /a SUCCESS_COUNT+=1
)
echo.
echo [9/200] Installing: unknown:batik-ext:1.8
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\batik-ext-1.8.jar" -DgroupId=unknown -DartifactId=batik-ext -Dversion=1.8 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: batik-ext-1.8
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: batik-ext-1.8
    set /a SUCCESS_COUNT+=1
)
echo.
echo [10/200] Installing: unknown:batik-util:1.8
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\batik-util-1.8.jar" -DgroupId=unknown -DartifactId=batik-util -Dversion=1.8 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: batik-util-1.8
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: batik-util-1.8
    set /a SUCCESS_COUNT+=1
)
echo.
echo [11/200] Installing: unknown:bcprov-jdk15on:1.60
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\bcprov-jdk15on-1.60.jar" -DgroupId=unknown -DartifactId=bcprov-jdk15on -Dversion=1.60 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: bcprov-jdk15on-1.60
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: bcprov-jdk15on-1.60
    set /a SUCCESS_COUNT+=1
)
echo.
echo [12/200] Installing: unknown:bsh-core-2.0b4:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\bsh-core-2.0b4.jar" -DgroupId=unknown -DartifactId=bsh-core-2.0b4 -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: bsh-core-2.0b4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: bsh-core-2.0b4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [13/200] Installing: unknown:cglib:3.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\cglib-3.1.jar" -DgroupId=unknown -DartifactId=cglib -Dversion=3.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: cglib-3.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: cglib-3.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [14/200] Installing: unknown:checker-compat-qual:2.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\checker-compat-qual-2.0.0.jar" -DgroupId=unknown -DartifactId=checker-compat-qual -Dversion=2.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: checker-compat-qual-2.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: checker-compat-qual-2.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [15/200] Installing: unknown:classmate:1.5.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\classmate-1.5.1.jar" -DgroupId=unknown -DartifactId=classmate -Dversion=1.5.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: classmate-1.5.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: classmate-1.5.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [16/200] Installing: commons-beanutils:commons-beanutils:1.9.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-beanutils-1.9.4.jar" -DgroupId=commons-beanutils -DartifactId=commons-beanutils -Dversion=1.9.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-beanutils-1.9.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-beanutils-1.9.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [17/200] Installing: commons-beanutils-core:commons-beanutils-core:1.8.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-beanutils-core-1.8.3.jar" -DgroupId=commons-beanutils-core -DartifactId=commons-beanutils-core -Dversion=1.8.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-beanutils-core-1.8.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-beanutils-core-1.8.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [18/200] Installing: commons-codec:commons-codec:1.10
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-codec-1.10.jar" -DgroupId=commons-codec -DartifactId=commons-codec -Dversion=1.10 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-codec-1.10
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-codec-1.10
    set /a SUCCESS_COUNT+=1
)
echo.
echo [19/200] Installing: commons-collections:commons-collections:3.2.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-collections-3.2.2.jar" -DgroupId=commons-collections -DartifactId=commons-collections -Dversion=3.2.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-collections-3.2.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-collections-3.2.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [20/200] Installing: commons-collections4:commons-collections4:4.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-collections4-4.4.jar" -DgroupId=commons-collections4 -DartifactId=commons-collections4 -Dversion=4.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-collections4-4.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-collections4-4.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [21/200] Installing: commons-configuration:commons-configuration:1.10
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-configuration-1.10.jar" -DgroupId=commons-configuration -DartifactId=commons-configuration -Dversion=1.10 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-configuration-1.10
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-configuration-1.10
    set /a SUCCESS_COUNT+=1
)
echo.
echo [22/200] Installing: commons-dbcp2:commons-dbcp2:2.7.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-dbcp2-2.7.0.jar" -DgroupId=commons-dbcp2 -DartifactId=commons-dbcp2 -Dversion=2.7.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-dbcp2-2.7.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-dbcp2-2.7.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [23/200] Installing: commons-fileupload:commons-fileupload:1.3.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-fileupload-1.3.3.jar" -DgroupId=commons-fileupload -DartifactId=commons-fileupload -Dversion=1.3.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-fileupload-1.3.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-fileupload-1.3.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [24/200] Installing: commons-httpclient:commons-httpclient:3.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-httpclient-3.1.jar" -DgroupId=commons-httpclient -DartifactId=commons-httpclient -Dversion=3.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-httpclient-3.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-httpclient-3.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [25/200] Installing: commons-io:commons-io:2.5
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-io-2.5.jar" -DgroupId=commons-io -DartifactId=commons-io -Dversion=2.5 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-io-2.5
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-io-2.5
    set /a SUCCESS_COUNT+=1
)
echo.
echo [26/200] Installing: commons-lang:commons-lang:2.6
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-lang-2.6.jar" -DgroupId=commons-lang -DartifactId=commons-lang -Dversion=2.6 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-lang-2.6
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-lang-2.6
    set /a SUCCESS_COUNT+=1
)
echo.
echo [27/200] Installing: commons-lang3:commons-lang3:3.12.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-lang3-3.12.0.jar" -DgroupId=commons-lang3 -DartifactId=commons-lang3 -Dversion=3.12.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-lang3-3.12.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-lang3-3.12.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [28/200] Installing: commons-logging:commons-logging:1.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-logging-1.2.jar" -DgroupId=commons-logging -DartifactId=commons-logging -Dversion=1.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-logging-1.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-logging-1.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [29/200] Installing: commons-pool2:commons-pool2:2.7.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\commons-pool2-2.7.0.jar" -DgroupId=commons-pool2 -DartifactId=commons-pool2 -Dversion=2.7.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: commons-pool2-2.7.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: commons-pool2-2.7.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [30/200] Installing: unknown:curator-client:4.2.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\curator-client-4.2.0.jar" -DgroupId=unknown -DartifactId=curator-client -Dversion=4.2.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: curator-client-4.2.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: curator-client-4.2.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [31/200] Installing: unknown:curator-framework:4.2.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\curator-framework-4.2.0.jar" -DgroupId=unknown -DartifactId=curator-framework -Dversion=4.2.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: curator-framework-4.2.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: curator-framework-4.2.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [32/200] Installing: unknown:curator-recipes:4.2.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\curator-recipes-4.2.0.jar" -DgroupId=unknown -DartifactId=curator-recipes -Dversion=4.2.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: curator-recipes-4.2.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: curator-recipes-4.2.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [33/200] Installing: unknown:curvesapi:1.04
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\curvesapi-1.04.jar" -DgroupId=unknown -DartifactId=curvesapi -Dversion=1.04 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: curvesapi-1.04
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: curvesapi-1.04
    set /a SUCCESS_COUNT+=1
)
echo.
echo [34/200] Installing: com.kayakwise.wmp:cust-center-api:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\cust-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=cust-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: cust-center-api-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: cust-center-api-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [35/200] Installing: com.kayakwise.wmp:cust-center-springcloud-dubbo:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\cust-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=cust-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: cust-center-springcloud-dubbo-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: cust-center-springcloud-dubbo-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [36/200] Installing: unknown:disruptor:3.4.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\disruptor-3.4.2.jar" -DgroupId=unknown -DartifactId=disruptor -Dversion=3.4.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: disruptor-3.4.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: disruptor-3.4.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [37/200] Installing: unknown:dom4j:2.1.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\dom4j-2.1.3.jar" -DgroupId=unknown -DartifactId=dom4j -Dversion=2.1.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: dom4j-2.1.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: dom4j-2.1.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [38/200] Installing: unknown:easyexcel:2.0.5
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\easyexcel-2.0.5.jar" -DgroupId=unknown -DartifactId=easyexcel -Dversion=2.0.5 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: easyexcel-2.0.5
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: easyexcel-2.0.5
    set /a SUCCESS_COUNT+=1
)
echo.
echo [39/200] Installing: unknown:ehcache:2.10.9.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\ehcache-2.10.9.2.jar" -DgroupId=unknown -DartifactId=ehcache -Dversion=2.10.9.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: ehcache-2.10.9.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: ehcache-2.10.9.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [40/200] Installing: unknown:ehcache:3.8.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\ehcache-3.8.1.jar" -DgroupId=unknown -DartifactId=ehcache -Dversion=3.8.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: ehcache-3.8.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: ehcache-3.8.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [41/200] Installing: unknown:error_prone_annotations:2.1.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\error_prone_annotations-2.1.3.jar" -DgroupId=unknown -DartifactId=error_prone_annotations -Dversion=2.1.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: error_prone_annotations-2.1.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: error_prone_annotations-2.1.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [42/200] Installing: unknown:esapi:2.1.0.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\esapi-2.1.0.1.jar" -DgroupId=unknown -DartifactId=esapi -Dversion=2.1.0.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: esapi-2.1.0.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: esapi-2.1.0.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [43/200] Installing: unknown:FastInfoset:1.2.16
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\FastInfoset-1.2.16.jar" -DgroupId=unknown -DartifactId=FastInfoset -Dversion=1.2.16 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: FastInfoset-1.2.16
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: FastInfoset-1.2.16
    set /a SUCCESS_COUNT+=1
)
echo.
echo [44/200] Installing: com.alibaba:fastjson:1.2.68
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\fastjson-1.2.68.jar" -DgroupId=com.alibaba -DartifactId=fastjson -Dversion=1.2.68 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: fastjson-1.2.68
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: fastjson-1.2.68
    set /a SUCCESS_COUNT+=1
)
echo.
echo [45/200] Installing: com.kayakwise.wmp:fina-center-api:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\fina-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=fina-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: fina-center-api-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: fina-center-api-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [46/200] Installing: com.kayakwise.wmp:fina-center-springcloud-dubbo:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\fina-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=fina-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: fina-center-springcloud-dubbo-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: fina-center-springcloud-dubbo-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [47/200] Installing: unknown:ftsp-sdk1.8:2.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\ftsp-sdk1.8-2.2.jar" -DgroupId=unknown -DartifactId=ftsp-sdk1.8 -Dversion=2.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: ftsp-sdk1.8-2.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: ftsp-sdk1.8-2.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [48/200] Installing: com.google.code.gson:gson:2.8.6
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\gson-2.8.6.jar" -DgroupId=com.google.code.gson -DartifactId=gson -Dversion=2.8.6 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: gson-2.8.6
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: gson-2.8.6
    set /a SUCCESS_COUNT+=1
)
echo.
echo [49/200] Installing: com.google.guava:guava:25.0-jre
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\guava-25.0-jre.jar" -DgroupId=com.google.guava -DartifactId=guava -Dversion=25.0-jre -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: guava-25.0-jre
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: guava-25.0-jre
    set /a SUCCESS_COUNT+=1
)
echo.
echo [50/200] Installing: unknown:HdrHistogram:2.1.11
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\HdrHistogram-2.1.11.jar" -DgroupId=unknown -DartifactId=HdrHistogram -Dversion=2.1.11 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: HdrHistogram-2.1.11
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: HdrHistogram-2.1.11
    set /a SUCCESS_COUNT+=1
)
echo.
echo [51/200] Installing: unknown:hibernate-validator-6.0.18.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\hibernate-validator-6.0.18.Final.jar" -DgroupId=unknown -DartifactId=hibernate-validator-6.0.18.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: hibernate-validator-6.0.18.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: hibernate-validator-6.0.18.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [52/200] Installing: unknown:hibernate-validator-cdi-6.1.0.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\hibernate-validator-cdi-6.1.0.Final.jar" -DgroupId=unknown -DartifactId=hibernate-validator-cdi-6.1.0.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: hibernate-validator-cdi-6.1.0.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: hibernate-validator-cdi-6.1.0.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [53/200] Installing: org.apache.httpcomponents:httpclient:4.5.10
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\httpclient-4.5.10.jar" -DgroupId=org.apache.httpcomponents -DartifactId=httpclient -Dversion=4.5.10 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: httpclient-4.5.10
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: httpclient-4.5.10
    set /a SUCCESS_COUNT+=1
)
echo.
echo [54/200] Installing: org.apache.httpcomponents:httpcore:4.4.12
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\httpcore-4.4.12.jar" -DgroupId=org.apache.httpcomponents -DartifactId=httpcore -Dversion=4.4.12 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: httpcore-4.4.12
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: httpcore-4.4.12
    set /a SUCCESS_COUNT+=1
)
echo.
echo [55/200] Installing: org.apache.httpcomponents:httpmime:4.5.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\httpmime-4.5.3.jar" -DgroupId=org.apache.httpcomponents -DartifactId=httpmime -Dversion=4.5.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: httpmime-4.5.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: httpmime-4.5.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [56/200] Installing: unknown:istack-commons-runtime:3.0.8
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\istack-commons-runtime-3.0.8.jar" -DgroupId=unknown -DartifactId=istack-commons-runtime -Dversion=3.0.8 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: istack-commons-runtime-3.0.8
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: istack-commons-runtime-3.0.8
    set /a SUCCESS_COUNT+=1
)
echo.
echo [57/200] Installing: unknown:j2objc-annotations:1.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\j2objc-annotations-1.1.jar" -DgroupId=unknown -DartifactId=j2objc-annotations -Dversion=1.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: j2objc-annotations-1.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: j2objc-annotations-1.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [58/200] Installing: com.fasterxml.jackson.core:jackson-annotations:2.10.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-annotations-2.10.1.jar" -DgroupId=com.fasterxml.jackson.core -DartifactId=jackson-annotations -Dversion=2.10.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-annotations-2.10.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-annotations-2.10.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [59/200] Installing: com.fasterxml.jackson.core:jackson-core:2.10.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-core-2.10.1.jar" -DgroupId=com.fasterxml.jackson.core -DartifactId=jackson-core -Dversion=2.10.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-core-2.10.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-core-2.10.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [60/200] Installing: com.fasterxml.jackson.core:jackson-databind:2.10.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-databind-2.10.3.jar" -DgroupId=com.fasterxml.jackson.core -DartifactId=jackson-databind -Dversion=2.10.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-databind-2.10.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-databind-2.10.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [61/200] Installing: com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.10.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-dataformat-xml-2.10.1.jar" -DgroupId=com.fasterxml.jackson.dataformat -DartifactId=jackson-dataformat-xml -Dversion=2.10.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-dataformat-xml-2.10.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-dataformat-xml-2.10.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [62/200] Installing: com.fasterxml.jackson.dataformat:jackson-datatype-jdk8:2.10.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-datatype-jdk8-2.10.1.jar" -DgroupId=com.fasterxml.jackson.dataformat -DartifactId=jackson-datatype-jdk8 -Dversion=2.10.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-datatype-jdk8-2.10.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-datatype-jdk8-2.10.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [63/200] Installing: com.fasterxml.jackson.dataformat:jackson-datatype-jsr310:2.10.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-datatype-jsr310-2.10.1.jar" -DgroupId=com.fasterxml.jackson.dataformat -DartifactId=jackson-datatype-jsr310 -Dversion=2.10.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-datatype-jsr310-2.10.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-datatype-jsr310-2.10.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [64/200] Installing: com.fasterxml.jackson.dataformat:jackson-module-jaxb-annotations:2.10.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-module-jaxb-annotations-2.10.1.jar" -DgroupId=com.fasterxml.jackson.dataformat -DartifactId=jackson-module-jaxb-annotations -Dversion=2.10.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-module-jaxb-annotations-2.10.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-module-jaxb-annotations-2.10.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [65/200] Installing: com.fasterxml.jackson.dataformat:jackson-module-parameter-names:2.10.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jackson-module-parameter-names-2.10.1.jar" -DgroupId=com.fasterxml.jackson.dataformat -DartifactId=jackson-module-parameter-names -Dversion=2.10.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jackson-module-parameter-names-2.10.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jackson-module-parameter-names-2.10.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [66/200] Installing: unknown:jakarta.activation-api:1.2.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jakarta.activation-api-1.2.1.jar" -DgroupId=unknown -DartifactId=jakarta.activation-api -Dversion=1.2.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jakarta.activation-api-1.2.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jakarta.activation-api-1.2.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [67/200] Installing: unknown:jakarta.annotation-api:1.3.5
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jakarta.annotation-api-1.3.5.jar" -DgroupId=unknown -DartifactId=jakarta.annotation-api -Dversion=1.3.5 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jakarta.annotation-api-1.3.5
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jakarta.annotation-api-1.3.5
    set /a SUCCESS_COUNT+=1
)
echo.
echo [68/200] Installing: unknown:jakarta.validation-api:2.0.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jakarta.validation-api-2.0.1.jar" -DgroupId=unknown -DartifactId=jakarta.validation-api -Dversion=2.0.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jakarta.validation-api-2.0.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jakarta.validation-api-2.0.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [69/200] Installing: unknown:jakarta.xml.bind-api:2.3.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jakarta.xml.bind-api-2.3.2.jar" -DgroupId=unknown -DartifactId=jakarta.xml.bind-api -Dversion=2.3.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jakarta.xml.bind-api-2.3.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jakarta.xml.bind-api-2.3.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [70/200] Installing: unknown:javassist:3.20.0-GA
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\javassist-3.20.0-GA.jar" -DgroupId=unknown -DartifactId=javassist -Dversion=3.20.0-GA -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: javassist-3.20.0-GA
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: javassist-3.20.0-GA
    set /a SUCCESS_COUNT+=1
)
echo.
echo [71/200] Installing: unknown:javax.servlet-api:4.0.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\javax.servlet-api-4.0.1.jar" -DgroupId=unknown -DartifactId=javax.servlet-api -Dversion=4.0.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: javax.servlet-api-4.0.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: javax.servlet-api-4.0.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [72/200] Installing: unknown:jaxb-runtime:2.3.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jaxb-runtime-2.3.2.jar" -DgroupId=unknown -DartifactId=jaxb-runtime -Dversion=2.3.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jaxb-runtime-2.3.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jaxb-runtime-2.3.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [73/200] Installing: unknown:jboss-logging-3.4.1.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jboss-logging-3.4.1.Final.jar" -DgroupId=unknown -DartifactId=jboss-logging-3.4.1.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jboss-logging-3.4.1.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jboss-logging-3.4.1.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [74/200] Installing: unknown:jcl-over-slf4j:1.7.29
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jcl-over-slf4j-1.7.29.jar" -DgroupId=unknown -DartifactId=jcl-over-slf4j -Dversion=1.7.29 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jcl-over-slf4j-1.7.29
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jcl-over-slf4j-1.7.29
    set /a SUCCESS_COUNT+=1
)
echo.
echo [75/200] Installing: redis.clients:jedis:3.1.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jedis-3.1.0.jar" -DgroupId=redis.clients -DartifactId=jedis -Dversion=3.1.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jedis-3.1.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jedis-3.1.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [76/200] Installing: unknown:jline:0.9.94
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jline-0.9.94.jar" -DgroupId=unknown -DartifactId=jline -Dversion=0.9.94 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jline-0.9.94
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jline-0.9.94
    set /a SUCCESS_COUNT+=1
)
echo.
echo [77/200] Installing: unknown:joda-time:2.10.10
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\joda-time-2.10.10.jar" -DgroupId=unknown -DartifactId=joda-time -Dversion=2.10.10 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: joda-time-2.10.10
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: joda-time-2.10.10
    set /a SUCCESS_COUNT+=1
)
echo.
echo [78/200] Installing: com.hundsun.jrescloud:jrescloud-common:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-common-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-common -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-common-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-common-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [79/200] Installing: com.hundsun.jrescloud:jrescloud-common-base:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-common-base-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-common-base -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-common-base-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-common-base-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [80/200] Installing: com.hundsun.jrescloud:jrescloud-common-gm:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-common-gm-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-common-gm -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-common-gm-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-common-gm-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [81/200] Installing: com.hundsun.jrescloud:jrescloud-common-partition:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-common-partition-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-common-partition -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-common-partition-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-common-partition-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [82/200] Installing: com.hundsun.jrescloud:jrescloud-common-t2:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-common-t2-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-common-t2 -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-common-t2-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-common-t2-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [83/200] Installing: com.hundsun.jrescloud:jrescloud-monitor-api:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-monitor-api-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-monitor-api -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-monitor-api-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-monitor-api-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [84/200] Installing: com.hundsun.jrescloud:jrescloud-monitor-common:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-monitor-common-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-monitor-common -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-monitor-common-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-monitor-common-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [85/200] Installing: com.hundsun.jrescloud:jrescloud-rpc-api:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-rpc-api-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-rpc-api -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-rpc-api-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-rpc-api-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [86/200] Installing: com.hundsun.jrescloud:jrescloud-rpc-def:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-rpc-def-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-rpc-def -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-rpc-def-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-rpc-def-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [87/200] Installing: com.hundsun.jrescloud:jrescloud-rpc-def-log4j2:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-rpc-def-log4j2-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-rpc-def-log4j2 -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-rpc-def-log4j2-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-rpc-def-log4j2-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [88/200] Installing: com.hundsun.jrescloud:jrescloud-rpc-def-monitor:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-rpc-def-monitor-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-rpc-def-monitor -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-rpc-def-monitor-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-rpc-def-monitor-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [89/200] Installing: com.hundsun.jrescloud:jrescloud-rpc-t2:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-rpc-t2-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-rpc-t2 -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-rpc-t2-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-rpc-t2-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [90/200] Installing: com.hundsun.jrescloud:jrescloud-starter:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-starter-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-starter -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-starter-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-starter-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [91/200] Installing: com.hundsun.jrescloud:jrescloud-starter-rpc-def:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-starter-rpc-def-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-starter-rpc-def -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-starter-rpc-def-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-starter-rpc-def-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [92/200] Installing: com.hundsun.jrescloud:jrescloud-trace-api:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-trace-api-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-trace-api -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-trace-api-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-trace-api-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [93/200] Installing: com.hundsun.jrescloud:jrescloud-trace-common:3.0.8.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jrescloud-trace-common-3.0.8.4.jar" -DgroupId=com.hundsun.jrescloud -DartifactId=jrescloud-trace-common -Dversion=3.0.8.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jrescloud-trace-common-3.0.8.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jrescloud-trace-common-3.0.8.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [94/200] Installing: unknown:json:20171018
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\json-20171018.jar" -DgroupId=unknown -DartifactId=json -Dversion=20171018 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: json-20171018
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: json-20171018
    set /a SUCCESS_COUNT+=1
)
echo.
echo [95/200] Installing: unknown:jsr305:3.0.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jsr305-3.0.2.jar" -DgroupId=unknown -DartifactId=jsr305 -Dversion=3.0.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jsr305-3.0.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jsr305-3.0.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [96/200] Installing: unknown:jul-to-slf4j:1.7.29
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\jul-to-slf4j-1.7.29.jar" -DgroupId=unknown -DartifactId=jul-to-slf4j -Dversion=1.7.29 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: jul-to-slf4j-1.7.29
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: jul-to-slf4j-1.7.29
    set /a SUCCESS_COUNT+=1
)
echo.
echo [97/200] Installing: k-cloud:k-cloud-action:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-action-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-action -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-action-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-action-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [98/200] Installing: k-cloud:k-cloud-action-dubbo:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-action-dubbo-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-action-dubbo -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-action-dubbo-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-action-dubbo-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [99/200] Installing: k-cloud:k-cloud-cache:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-cache-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-cache -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-cache-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-cache-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [100/200] Installing: k-cloud:k-cloud-config:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-config-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-config -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-config-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-config-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [101/200] Installing: k-cloud:k-cloud-core:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-core-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-core -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-core-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-core-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [102/200] Installing: k-cloud:k-cloud-dao:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-dao-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-dao -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-dao-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-dao-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [103/200] Installing: k-cloud:k-cloud-service-starter-micro-server:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-service-starter-micro-server-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-service-starter-micro-server -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-service-starter-micro-server-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-service-starter-micro-server-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [104/200] Installing: k-cloud:k-cloud-xsql:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\k-cloud-xsql-1.0.0.jar" -DgroupId=k-cloud -DartifactId=k-cloud-xsql -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: k-cloud-xsql-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: k-cloud-xsql-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [105/200] Installing: unknown:kayakwise-transaction-api-QZ:0.0.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\kayakwise-transaction-api-QZ-0.0.4.jar" -DgroupId=unknown -DartifactId=kayakwise-transaction-api-QZ -Dversion=0.0.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: kayakwise-transaction-api-QZ-0.0.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: kayakwise-transaction-api-QZ-0.0.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [106/200] Installing: unknown:LatencyUtils:2.0.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\LatencyUtils-2.0.3.jar" -DgroupId=unknown -DartifactId=LatencyUtils -Dversion=2.0.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: LatencyUtils-2.0.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: LatencyUtils-2.0.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [107/200] Installing: org.apache.logging.log4j:log4j:1.2.17
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\log4j-1.2.17.jar" -DgroupId=org.apache.logging.log4j -DartifactId=log4j -Dversion=1.2.17 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: log4j-1.2.17
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: log4j-1.2.17
    set /a SUCCESS_COUNT+=1
)
echo.
echo [108/200] Installing: org.apache.logging.log4j:log4j-api:2.13.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\log4j-api-2.13.3.jar" -DgroupId=org.apache.logging.log4j -DartifactId=log4j-api -Dversion=2.13.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: log4j-api-2.13.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: log4j-api-2.13.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [109/200] Installing: org.apache.logging.log4j:log4j-core:2.13.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\log4j-core-2.13.3.jar" -DgroupId=org.apache.logging.log4j -DartifactId=log4j-core -Dversion=2.13.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: log4j-core-2.13.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: log4j-core-2.13.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [110/200] Installing: org.apache.logging.log4j:log4j-jul:2.13.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\log4j-jul-2.13.3.jar" -DgroupId=org.apache.logging.log4j -DartifactId=log4j-jul -Dversion=2.13.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: log4j-jul-2.13.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: log4j-jul-2.13.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [111/200] Installing: org.apache.logging.log4j:log4j-slf4j-impl:2.13.3
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\log4j-slf4j-impl-2.13.3.jar" -DgroupId=org.apache.logging.log4j -DartifactId=log4j-slf4j-impl -Dversion=2.13.3 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: log4j-slf4j-impl-2.13.3
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: log4j-slf4j-impl-2.13.3
    set /a SUCCESS_COUNT+=1
)
echo.
echo [112/200] Installing: unknown:lombok:1.18.10
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\lombok-1.18.10.jar" -DgroupId=unknown -DartifactId=lombok -Dversion=1.18.10 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: lombok-1.18.10
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: lombok-1.18.10
    set /a SUCCESS_COUNT+=1
)
echo.
echo [113/200] Installing: unknown:micrometer-core:1.3.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\micrometer-core-1.3.1.jar" -DgroupId=unknown -DartifactId=micrometer-core -Dversion=1.3.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: micrometer-core-1.3.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: micrometer-core-1.3.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [114/200] Installing: com.hundsun.jrescloud.middleware:middleware-base-common:1.0.7
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\middleware-base-common-1.0.7.jar" -DgroupId=com.hundsun.jrescloud.middleware -DartifactId=middleware-base-common -Dversion=1.0.7 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: middleware-base-common-1.0.7
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: middleware-base-common-1.0.7
    set /a SUCCESS_COUNT+=1
)
echo.
echo [115/200] Installing: com.hundsun.jrescloud.middleware:middleware-base-exception:1.0.7
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\middleware-base-exception-1.0.7.jar" -DgroupId=com.hundsun.jrescloud.middleware -DartifactId=middleware-base-exception -Dversion=1.0.7 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: middleware-base-exception-1.0.7
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: middleware-base-exception-1.0.7
    set /a SUCCESS_COUNT+=1
)
echo.
echo [116/200] Installing: com.hundsun.jrescloud.middleware:middleware-configcenter-client:1.2.14
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\middleware-configcenter-client-1.2.14.jar" -DgroupId=com.hundsun.jrescloud.middleware -DartifactId=middleware-configcenter-client -Dversion=1.2.14 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: middleware-configcenter-client-1.2.14
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: middleware-configcenter-client-1.2.14
    set /a SUCCESS_COUNT+=1
)
echo.
echo [117/200] Installing: com.hundsun.jrescloud.middleware:middleware-configcenter-common:1.2.14
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\middleware-configcenter-common-1.2.14.jar" -DgroupId=com.hundsun.jrescloud.middleware -DartifactId=middleware-configcenter-common -Dversion=1.2.14 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: middleware-configcenter-common-1.2.14
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: middleware-configcenter-common-1.2.14
    set /a SUCCESS_COUNT+=1
)
echo.
echo [118/200] Installing: com.hundsun.jrescloud.middleware:middleware-tenant-api:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\middleware-tenant-api-1.0.0.jar" -DgroupId=com.hundsun.jrescloud.middleware -DartifactId=middleware-tenant-api -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: middleware-tenant-api-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: middleware-tenant-api-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [119/200] Installing: com.hundsun.jrescloud.middleware:middleware-tenant-sdk:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\middleware-tenant-sdk-1.0.0.jar" -DgroupId=com.hundsun.jrescloud.middleware -DartifactId=middleware-tenant-sdk -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: middleware-tenant-sdk-1.0.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: middleware-tenant-sdk-1.0.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [120/200] Installing: mysql:mysql-connector-java:8.0.25
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\mysql-connector-java-8.0.25.jar" -DgroupId=mysql -DartifactId=mysql-connector-java -Dversion=8.0.25 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: mysql-connector-java-8.0.25
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: mysql-connector-java-8.0.25
    set /a SUCCESS_COUNT+=1
)
echo.
echo [121/200] Installing: com.alibaba.nacos:nacos-api:1.1.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\nacos-api-1.1.4.jar" -DgroupId=com.alibaba.nacos -DartifactId=nacos-api -Dversion=1.1.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: nacos-api-1.1.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: nacos-api-1.1.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [122/200] Installing: com.alibaba.nacos:nacos-client:1.1.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\nacos-client-1.1.4.jar" -DgroupId=com.alibaba.nacos -DartifactId=nacos-client -Dversion=1.1.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: nacos-client-1.1.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: nacos-client-1.1.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [123/200] Installing: com.alibaba.nacos:nacos-common:1.1.4
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\nacos-common-1.1.4.jar" -DgroupId=com.alibaba.nacos -DartifactId=nacos-common -Dversion=1.1.4 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: nacos-common-1.1.4
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: nacos-common-1.1.4
    set /a SUCCESS_COUNT+=1
)
echo.
echo [124/200] Installing: unknown:nekohtml:1.9.22
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\nekohtml-1.9.22.jar" -DgroupId=unknown -DartifactId=nekohtml -Dversion=1.9.22 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: nekohtml-1.9.22
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: nekohtml-1.9.22
    set /a SUCCESS_COUNT+=1
)
echo.
echo [125/200] Installing: unknown:netty-3.10.6.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-3.10.6.Final.jar" -DgroupId=unknown -DartifactId=netty-3.10.6.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-3.10.6.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-3.10.6.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [126/200] Installing: unknown:netty-buffer-4.1.48.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-buffer-4.1.48.Final.jar" -DgroupId=unknown -DartifactId=netty-buffer-4.1.48.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-buffer-4.1.48.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-buffer-4.1.48.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [127/200] Installing: unknown:netty-codec-4.1.48.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-codec-4.1.48.Final.jar" -DgroupId=unknown -DartifactId=netty-codec-4.1.48.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-codec-4.1.48.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-codec-4.1.48.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [128/200] Installing: unknown:netty-codec-http-4.1.43.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-codec-http-4.1.43.Final.jar" -DgroupId=unknown -DartifactId=netty-codec-http-4.1.43.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-codec-http-4.1.43.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-codec-http-4.1.43.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [129/200] Installing: unknown:netty-common-4.1.48.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-common-4.1.48.Final.jar" -DgroupId=unknown -DartifactId=netty-common-4.1.48.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-common-4.1.48.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-common-4.1.48.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [130/200] Installing: unknown:netty-handler-4.1.48.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-handler-4.1.48.Final.jar" -DgroupId=unknown -DartifactId=netty-handler-4.1.48.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-handler-4.1.48.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-handler-4.1.48.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [131/200] Installing: unknown:netty-resolver-4.1.48.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-resolver-4.1.48.Final.jar" -DgroupId=unknown -DartifactId=netty-resolver-4.1.48.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-resolver-4.1.48.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-resolver-4.1.48.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [132/200] Installing: unknown:netty-transport-4.1.48.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-transport-4.1.48.Final.jar" -DgroupId=unknown -DartifactId=netty-transport-4.1.48.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-transport-4.1.48.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-transport-4.1.48.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [133/200] Installing: unknown:netty-transport-native-epoll-4.1.43.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-transport-native-epoll-4.1.43.Final.jar" -DgroupId=unknown -DartifactId=netty-transport-native-epoll-4.1.43.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-transport-native-epoll-4.1.43.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-transport-native-epoll-4.1.43.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [134/200] Installing: unknown:netty-transport-native-unix-common-4.1.43.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\netty-transport-native-unix-common-4.1.43.Final.jar" -DgroupId=unknown -DartifactId=netty-transport-native-unix-common-4.1.43.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: netty-transport-native-unix-common-4.1.43.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: netty-transport-native-unix-common-4.1.43.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [135/200] Installing: unknown:opentracing-api:0.31.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\opentracing-api-0.31.0.jar" -DgroupId=unknown -DartifactId=opentracing-api -Dversion=0.31.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: opentracing-api-0.31.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: opentracing-api-0.31.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [136/200] Installing: unknown:opentracing-noop:0.31.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\opentracing-noop-0.31.0.jar" -DgroupId=unknown -DartifactId=opentracing-noop -Dversion=0.31.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: opentracing-noop-0.31.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: opentracing-noop-0.31.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [137/200] Installing: com.kayakwise.wmp:paym-api:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\paym-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=paym-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: paym-api-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: paym-api-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [138/200] Installing: com.kayakwise.wmp:plan-center-api:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\plan-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=plan-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: plan-center-api-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: plan-center-api-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [139/200] Installing: com.kayakwise.wmp:plan-center-springcloud-dubbo:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\plan-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=plan-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: plan-center-springcloud-dubbo-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: plan-center-springcloud-dubbo-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [140/200] Installing: org.apache.poi:poi:3.17
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\poi-3.17.jar" -DgroupId=org.apache.poi -DartifactId=poi -Dversion=3.17 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: poi-3.17
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: poi-3.17
    set /a SUCCESS_COUNT+=1
)
echo.
echo [141/200] Installing: org.apache.poi:poi-ooxml:3.17
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\poi-ooxml-3.17.jar" -DgroupId=org.apache.poi -DartifactId=poi-ooxml -Dversion=3.17 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: poi-ooxml-3.17
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: poi-ooxml-3.17
    set /a SUCCESS_COUNT+=1
)
echo.
echo [142/200] Installing: org.apache.poi:poi-ooxml-schemas:3.17
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\poi-ooxml-schemas-3.17.jar" -DgroupId=org.apache.poi -DartifactId=poi-ooxml-schemas -Dversion=3.17 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: poi-ooxml-schemas-3.17
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: poi-ooxml-schemas-3.17
    set /a SUCCESS_COUNT+=1
)
echo.
echo [143/200] Installing: com.kayakwise.wmp:prod-center-api:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\prod-center-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=prod-center-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: prod-center-api-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: prod-center-api-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [144/200] Installing: com.kayakwise.wmp:prod-center-springcloud-dubbo:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\prod-center-springcloud-dubbo-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=prod-center-springcloud-dubbo -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: prod-center-springcloud-dubbo-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: prod-center-springcloud-dubbo-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [145/200] Installing: unknown:rxjava:1.3.8
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\rxjava-1.3.8.jar" -DgroupId=unknown -DartifactId=rxjava -Dversion=1.3.8 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: rxjava-1.3.8
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: rxjava-1.3.8
    set /a SUCCESS_COUNT+=1
)
echo.
echo [146/200] Installing: unknown:rxnetty:0.4.20
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\rxnetty-0.4.20.jar" -DgroupId=unknown -DartifactId=rxnetty -Dversion=0.4.20 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: rxnetty-0.4.20
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: rxnetty-0.4.20
    set /a SUCCESS_COUNT+=1
)
echo.
echo [147/200] Installing: unknown:simpleclient:0.5.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\simpleclient-0.5.0.jar" -DgroupId=unknown -DartifactId=simpleclient -Dversion=0.5.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: simpleclient-0.5.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: simpleclient-0.5.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [148/200] Installing: org.slf4j:slf4j-api:1.7.29
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\slf4j-api-1.7.29.jar" -DgroupId=org.slf4j -DartifactId=slf4j-api -Dversion=1.7.29 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: slf4j-api-1.7.29
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: slf4j-api-1.7.29
    set /a SUCCESS_COUNT+=1
)
echo.
echo [149/200] Installing: unknown:snakeyaml:1.26
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\snakeyaml-1.26.jar" -DgroupId=unknown -DartifactId=snakeyaml -Dversion=1.26 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: snakeyaml-1.26
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: snakeyaml-1.26
    set /a SUCCESS_COUNT+=1
)
echo.
echo [150/200] Installing: unknown:spotbugs-annotations:3.1.9
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spotbugs-annotations-3.1.9.jar" -DgroupId=unknown -DartifactId=spotbugs-annotations -Dversion=3.1.9 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spotbugs-annotations-3.1.9
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spotbugs-annotations-3.1.9
    set /a SUCCESS_COUNT+=1
)
echo.
echo [151/200] Installing: unknown:spring-aop-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-aop-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-aop-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-aop-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-aop-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [152/200] Installing: unknown:spring-beans-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-beans-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-beans-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-beans-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-beans-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [153/200] Installing: unknown:spring-boot-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [154/200] Installing: unknown:spring-boot-actuator-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-actuator-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-actuator-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-actuator-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-actuator-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [155/200] Installing: unknown:spring-boot-actuator-autoconfigure-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-actuator-autoconfigure-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-actuator-autoconfigure-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-actuator-autoconfigure-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-actuator-autoconfigure-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [156/200] Installing: unknown:spring-boot-autoconfigure-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-autoconfigure-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-autoconfigure-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-autoconfigure-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-autoconfigure-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [157/200] Installing: unknown:spring-boot-legacy-2.1.0.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-legacy-2.1.0.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-legacy-2.1.0.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-legacy-2.1.0.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-legacy-2.1.0.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [158/200] Installing: unknown:spring-boot-starter-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [159/200] Installing: unknown:spring-boot-starter-actuator-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-actuator-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-actuator-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-actuator-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-actuator-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [160/200] Installing: unknown:spring-boot-starter-aop-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-aop-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-aop-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-aop-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-aop-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [161/200] Installing: unknown:spring-boot-starter-json-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-json-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-json-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-json-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-json-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [162/200] Installing: unknown:spring-boot-starter-log4j2-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-log4j2-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-log4j2-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-log4j2-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-log4j2-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [163/200] Installing: unknown:spring-boot-starter-redis-1.4.1.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-redis-1.4.1.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-redis-1.4.1.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-redis-1.4.1.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-redis-1.4.1.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [164/200] Installing: unknown:spring-boot-starter-tomcat-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-tomcat-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-tomcat-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-tomcat-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-tomcat-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [165/200] Installing: unknown:spring-boot-starter-validation-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-validation-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-validation-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-validation-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-validation-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [166/200] Installing: unknown:spring-boot-starter-web-2.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-boot-starter-web-2.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-boot-starter-web-2.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-boot-starter-web-2.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-boot-starter-web-2.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [167/200] Installing: unknown:spring-context-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-context-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-context-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-context-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-context-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [168/200] Installing: unknown:spring-context-support-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-context-support-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-context-support-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-context-support-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-context-support-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [169/200] Installing: unknown:spring-core-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-core-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-core-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-core-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-core-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [170/200] Installing: unknown:spring-data-commons-2.2.3.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-data-commons-2.2.3.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-data-commons-2.2.3.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-data-commons-2.2.3.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-data-commons-2.2.3.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [171/200] Installing: unknown:spring-data-keyvalue-2.2.3.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-data-keyvalue-2.2.3.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-data-keyvalue-2.2.3.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-data-keyvalue-2.2.3.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-data-keyvalue-2.2.3.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [172/200] Installing: unknown:spring-data-redis-2.2.3.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-data-redis-2.2.3.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-data-redis-2.2.3.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-data-redis-2.2.3.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-data-redis-2.2.3.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [173/200] Installing: unknown:spring-expression-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-expression-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-expression-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-expression-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-expression-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [174/200] Installing: unknown:spring-jcl-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-jcl-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-jcl-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-jcl-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-jcl-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [175/200] Installing: unknown:spring-oxm-5.2.2.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-oxm-5.2.2.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-oxm-5.2.2.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-oxm-5.2.2.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-oxm-5.2.2.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [176/200] Installing: unknown:spring-tx-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-tx-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-tx-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-tx-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-tx-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [177/200] Installing: unknown:spring-web-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-web-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-web-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-web-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-web-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [178/200] Installing: unknown:spring-webmvc-5.2.9.RELEASE:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\spring-webmvc-5.2.9.RELEASE.jar" -DgroupId=unknown -DartifactId=spring-webmvc-5.2.9.RELEASE -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: spring-webmvc-5.2.9.RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: spring-webmvc-5.2.9.RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [179/200] Installing: unknown:stax-api:1.0.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\stax-api-1.0.1.jar" -DgroupId=unknown -DartifactId=stax-api -Dversion=1.0.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: stax-api-1.0.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: stax-api-1.0.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [180/200] Installing: unknown:stax-ex:1.8.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\stax-ex-1.8.1.jar" -DgroupId=unknown -DartifactId=stax-ex -Dversion=1.8.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: stax-ex-1.8.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: stax-ex-1.8.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [181/200] Installing: unknown:stax2-api:4.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\stax2-api-4.2.jar" -DgroupId=unknown -DartifactId=stax2-api -Dversion=4.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: stax2-api-4.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: stax2-api-4.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [182/200] Installing: unknown:sun-client:1.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\sun-client-1.0.jar" -DgroupId=unknown -DartifactId=sun-client -Dversion=1.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: sun-client-1.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: sun-client-1.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [183/200] Installing: unknown:sun-ws-all:3.2.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\sun-ws-all-3.2.0.jar" -DgroupId=unknown -DartifactId=sun-ws-all -Dversion=3.2.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: sun-ws-all-3.2.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: sun-ws-all-3.2.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [184/200] Installing: org.apache.tomcat.embed:tomcat-embed-core:9.0.37
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\tomcat-embed-core-9.0.37.jar" -DgroupId=org.apache.tomcat.embed -DartifactId=tomcat-embed-core -Dversion=9.0.37 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: tomcat-embed-core-9.0.37
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: tomcat-embed-core-9.0.37
    set /a SUCCESS_COUNT+=1
)
echo.
echo [185/200] Installing: org.apache.tomcat.embed:tomcat-embed-el:9.0.37
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\tomcat-embed-el-9.0.37.jar" -DgroupId=org.apache.tomcat.embed -DartifactId=tomcat-embed-el -Dversion=9.0.37 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: tomcat-embed-el-9.0.37
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: tomcat-embed-el-9.0.37
    set /a SUCCESS_COUNT+=1
)
echo.
echo [186/200] Installing: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.37
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\tomcat-embed-websocket-9.0.37.jar" -DgroupId=org.apache.tomcat.embed -DartifactId=tomcat-embed-websocket -Dversion=9.0.37 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: tomcat-embed-websocket-9.0.37
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: tomcat-embed-websocket-9.0.37
    set /a SUCCESS_COUNT+=1
)
echo.
echo [187/200] Installing: unknown:txw2:2.3.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\txw2-2.3.2.jar" -DgroupId=unknown -DartifactId=txw2 -Dversion=2.3.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: txw2-2.3.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: txw2-2.3.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [188/200] Installing: unknown:validation-api-2.0.1.Final:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\validation-api-2.0.1.Final.jar" -DgroupId=unknown -DartifactId=validation-api-2.0.1.Final -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: validation-api-2.0.1.Final
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: validation-api-2.0.1.Final
    set /a SUCCESS_COUNT+=1
)
echo.
echo [189/200] Installing: com.kayakwise.wmp:wmp-base-api:0.0.1-RELEASE
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\wmp-base-api-0.0.1-RELEASE.jar" -DgroupId=com.kayakwise.wmp -DartifactId=wmp-base-api -Dversion=0.0.1-RELEASE -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: wmp-base-api-0.0.1-RELEASE
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: wmp-base-api-0.0.1-RELEASE
    set /a SUCCESS_COUNT+=1
)
echo.
echo [190/200] Installing: unknown:woodstox-core:6.0.2
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\woodstox-core-6.0.2.jar" -DgroupId=unknown -DartifactId=woodstox-core -Dversion=6.0.2 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: woodstox-core-6.0.2
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: woodstox-core-6.0.2
    set /a SUCCESS_COUNT+=1
)
echo.
echo [191/200] Installing: unknown:xalan:2.7.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xalan-2.7.0.jar" -DgroupId=unknown -DartifactId=xalan -Dversion=2.7.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xalan-2.7.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xalan-2.7.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [192/200] Installing: unknown:xercesImpl:2.8.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xercesImpl-2.8.0.jar" -DgroupId=unknown -DartifactId=xercesImpl -Dversion=2.8.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xercesImpl-2.8.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xercesImpl-2.8.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [193/200] Installing: unknown:xml-apis:1.3.03
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xml-apis-1.3.03.jar" -DgroupId=unknown -DartifactId=xml-apis -Dversion=1.3.03 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xml-apis-1.3.03
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xml-apis-1.3.03
    set /a SUCCESS_COUNT+=1
)
echo.
echo [194/200] Installing: unknown:xml-apis-ext:1.3.04
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xml-apis-ext-1.3.04.jar" -DgroupId=unknown -DartifactId=xml-apis-ext -Dversion=1.3.04 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xml-apis-ext-1.3.04
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xml-apis-ext-1.3.04
    set /a SUCCESS_COUNT+=1
)
echo.
echo [195/200] Installing: unknown:xmlbeans:2.6.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xmlbeans-2.6.0.jar" -DgroupId=unknown -DartifactId=xmlbeans -Dversion=2.6.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xmlbeans-2.6.0
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xmlbeans-2.6.0
    set /a SUCCESS_COUNT+=1
)
echo.
echo [196/200] Installing: unknown:xmlpull:1.1.3.1
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xmlpull-1.1.3.1.jar" -DgroupId=unknown -DartifactId=xmlpull -Dversion=1.1.3.1 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xmlpull-1.1.3.1
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xmlpull-1.1.3.1
    set /a SUCCESS_COUNT+=1
)
echo.
echo [197/200] Installing: unknown:xom:1.2.5
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xom-1.2.5.jar" -DgroupId=unknown -DartifactId=xom -Dversion=1.2.5 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xom-1.2.5
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xom-1.2.5
    set /a SUCCESS_COUNT+=1
)
echo.
echo [198/200] Installing: unknown:xpp3_min-1.1.4c:1.0.0
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xpp3_min-1.1.4c.jar" -DgroupId=unknown -DartifactId=xpp3_min-1.1.4c -Dversion=1.0.0 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xpp3_min-1.1.4c
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xpp3_min-1.1.4c
    set /a SUCCESS_COUNT+=1
)
echo.
echo [199/200] Installing: unknown:xstream:1.4.10
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\xstream-1.4.10.jar" -DgroupId=unknown -DartifactId=xstream -Dversion=1.4.10 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: xstream-1.4.10
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: xstream-1.4.10
    set /a SUCCESS_COUNT+=1
)
echo.
echo [200/200] Installing: unknown:zookeeper:3.4.14
mvn install:install-file -Dfile="E:\java_proj\svn\svn\Sources\Branch_DEV\wmp\wmpweb\wmp\lib-extracted\zookeeper-3.4.14.jar" -DgroupId=unknown -DartifactId=zookeeper -Dversion=3.4.14 -Dpackaging=jar -DgeneratePom=true
if errorlevel 1 (
    echo [ERROR] Installation failed: zookeeper-3.4.14
    set /a FAIL_COUNT+=1
) else (
    echo [SUCCESS] Installation successful: zookeeper-3.4.14
    set /a SUCCESS_COUNT+=1
)
echo.
echo ==================================
echo Installation Summary:
echo Total dependencies: 200
echo Successful installations: %SUCCESS_COUNT%
echo Failed installations: %FAIL_COUNT%
echo ==================================
echo.
echo All dependencies processing completed!
echo You can now use these dependencies in your Maven projects.
pause
