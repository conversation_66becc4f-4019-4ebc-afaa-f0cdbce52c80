# Install business center dependencies
Write-Host "Installing business center dependencies..." -ForegroundColor Green

$businessCenters = @(
    @{jar="fina-center-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.fina"; artifactId="fina-center-api"; version="0.0.1-RELEASE"},
    @{jar="plan-center-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.plan"; artifactId="plan-center-api"; version="0.0.1-RELEASE"},
    @{jar="prod-center-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.prod"; artifactId="prod-center-api"; version="0.0.1-RELEASE"}
)

$successCount = 0
$failCount = 0

foreach ($center in $businessCenters) {
    $jarPath = "lib-extracted\$($center.jar)"
    
    if (Test-Path $jarPath) {
        Write-Host "Installing: $($center.groupId):$($center.artifactId):$($center.version)" -ForegroundColor Cyan
        
        $result = & mvn install:install-file "-Dfile=$jarPath" "-DgroupId=$($center.groupId)" "-DartifactId=$($center.artifactId)" "-Dversion=$($center.version)" "-Dpackaging=jar" "-DgeneratePom=true" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: $($center.jar)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "FAILED: $($center.jar)" -ForegroundColor Red
            $failCount++
        }
    } else {
        Write-Host "FILE NOT FOUND: $jarPath" -ForegroundColor Yellow
        $failCount++
    }
}

Write-Host "`nBusiness Centers Installation Summary:" -ForegroundColor Yellow
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red

# Now we need to create a stub for bala-center-api since it's not in the jar files
Write-Host "`nCreating stub for bala-center-api..." -ForegroundColor Green

# Create a minimal jar for bala-center-api
$tempDir = "temp-bala-api"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Create directory structure
$packageDirs = @(
    "$tempDir\com\kayakwise\bala\api",
    "$tempDir\com\kayakwise\bala\req", 
    "$tempDir\com\kayakwise\bala\resp",
    "$tempDir\com\kayakwise\bala\model"
)

foreach ($dir in $packageDirs) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
}

# Create stub classes
@"
package com.kayakwise.bala.api;
public interface T915DubboDecorator {}
"@ | Out-File -FilePath "$tempDir\com\kayakwise\bala\api\T915DubboDecorator.java" -Encoding UTF8

@"
package com.kayakwise.bala.api;
public interface T916DubboDecorator {}
"@ | Out-File -FilePath "$tempDir\com\kayakwise\bala\api\T916DubboDecorator.java" -Encoding UTF8

@"
package com.kayakwise.bala.api;
public interface T918DubboDecorator {}
"@ | Out-File -FilePath "$tempDir\com\kayakwise\bala\api\T918DubboDecorator.java" -Encoding UTF8

@"
package com.kayakwise.bala.api;
public interface T919DubboDecorator {}
"@ | Out-File -FilePath "$tempDir\com\kayakwise\bala\api\T919DubboDecorator.java" -Encoding UTF8

@"
package com.kayakwise.bala.api;
public interface T921DubboDecorator {}
"@ | Out-File -FilePath "$tempDir\com\kayakwise\bala\api\T921DubboDecorator.java" -Encoding UTF8

@"
package com.kayakwise.bala.api;
public interface T926DubboDecorator {}
"@ | Out-File -FilePath "$tempDir\com\kayakwise\bala\api\T926DubboDecorator.java" -Encoding UTF8

# Compile and create jar
& javac -d $tempDir (Get-ChildItem "$tempDir\*.java" -Recurse).FullName
& jar -cf "bala-center-api-0.0.1-RELEASE.jar" -C $tempDir .

# Install the stub jar
& mvn install:install-file "-Dfile=bala-center-api-0.0.1-RELEASE.jar" "-DgroupId=com.kayakwise.bala" "-DartifactId=bala-center-api" "-Dversion=0.0.1-RELEASE" "-Dpackaging=jar" "-DgeneratePom=true"

# Clean up
Remove-Item -Recurse -Force $tempDir
Remove-Item "bala-center-api-0.0.1-RELEASE.jar"

Write-Host "Bala center API stub created and installed!" -ForegroundColor Green
