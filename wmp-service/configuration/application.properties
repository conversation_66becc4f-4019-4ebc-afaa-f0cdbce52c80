server.tomcat.maxHttpHeaderSize=102400
spring.main.allow-bean-definition-overriding=true
spring.redis.host=************
spring.redis.port=6379
spring.redis.timeout=30000
spring.redis.password=000000
spring.redis.pool.max-active=8
spring.redis.pool.max-wait=-1
spring.redis.pool.max-idle=100
spring.redis.pool.min-idle=20
graphql.package=com.kayak
upload.path=/home/<USER>/upload/wmp-service
workflow.scheduled.cron=0 0/59 *  * * ?
workflow.scheduled.threadPool.minCount=3
workflow.scheduled.threadPool.maxCount=5
workflow.scheduled.threadPool.blockingQueue.count=5
excel.tem.path=/home/<USER>/upload/wmp-service/excel/tem
excel.conf.path=/home/<USER>/upload/wmp-service/excel/conf
json.conf.path=/home/<USER>/upload/wmp-service/conf
mysql.pwd=MIma1102
mysql.username=fdsusr

jdbc.master=0
jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.nodes=0,1,2,3,4,5

jdbc.0.name=0
jdbc.0.url=****************************************************************************************************************************************************************************
jdbc.0.user=${mysql.username}
jdbc.0.password=${mysql.pwd}
jdbc.0.max=10

jdbc.1.name=1
jdbc.1.url=****************************************************************************************************************************************************************************
jdbc.1.user=${mysql.username}
jdbc.1.password=${mysql.pwd}
jdbc.1.max=10

jdbc.2.name=2
jdbc.2.url=****************************************************************************************************************************************************************************
jdbc.2.user=${mysql.username}
jdbc.2.password=${mysql.pwd}
jdbc.2.max=10

#jdbc.3.name=3
#jdbc.3.url=****************************************************************************************************************************************************************************
#jdbc.3.user=${mysql.username}
#jdbc.3.password=${mysql.pwd}
#jdbc.3.max=10
#
#jdbc.4.name=4
#jdbc.4.url=***********************************************************************************************************************************************
#jdbc.4.user=${mysql.username}
#jdbc.4.password=${mysql.pwd}
#jdbc.4.max=10

jdbc.5.name=5
jdbc.5.url=****************************************************************************************************************************************************************************
jdbc.5.user=${mysql.username}
jdbc.5.password=${mysql.pwd}
jdbc.5.max=10

bank.bank_org_no=1000
bank.bank_sale_org_no=1001

wmp.trans.database.list=mysql

app.server.port=38089
#\u4EA7\u54C1\u6587\u6863\u534F\u8BAE\u4E0A\u4F20\u8DEF\u5F84
file.tem.path: share/share1/file

