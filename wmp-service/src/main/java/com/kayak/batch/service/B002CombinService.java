package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.batch.dao.B002Dao;
import com.kayak.batch.model.B002;
import com.kayak.batch.model.B002ClearGroupMember;
import com.kayak.batch.model.B002ClearTaskSet;
import com.kayak.batch.model.B002Combin;
import com.kayak.common.util.DbOfModuleidUtils;
import com.kayak.core.dao.DaoService;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lfzh
 * @date: 2021-01-26 19:03
 */
@Service
@APIDefine(desc = "清算提交", model = B002.class)
public class B002CombinService {

    @Autowired
    private DaoService daoService;
    @Autowired
    private B002Dao b002Dao;

    @API(desc = "修改清算组、成员及任务配置", auth = APIAuth.YES)
    public String submit(SqlParam<B002Combin> param) throws Exception {
        try{
            String moduleid = param.getModel().getModuleid();
            assert StringUtils.isNotBlank(moduleid) : "模块ID不能为空";
            DaoUtil.doTrans(() -> {
                //删除所有ta_clear_group_info中数据
                b002Dao.DeleteAllData(moduleid);
                //先删除所有的成员
                b002Dao.deleteAllTaClearGroupMember(moduleid);
                //清空所有的任务
                b002Dao.deleteAllTaClearTaskSet(param.getModel().getModuleid());
                for (B002 res : param.getModel().getClearGroupListStr()) {
                    //添加数据到ta_clear_group_info中doTrans(()
                    b002Dao.insertTaClearGroupInfo(res);
                    //处理成员
                    Map<String, Object> members = new HashMap<>();
                    List<B002ClearGroupMember> memberList = new ArrayList<>();
                    members.put("taskGroup", res.getTaskGroup());
                    if (res.getMember() != null) {
                        for (B002ClearGroupMember member : res.getMember())
                            if ("1".equals(member.getIsGroupMember())) {
                                member.setTaskGroup(res.getTaskGroup());
                                if(Tools.isBlank(member.getExecTaskType())){
                                    member.setExecTaskType(res.getExecTaskType());
                                }
                                b002Dao.insertTaClearGroupMember(member);
                            }
                    }
                    //处理任务设置
                    List<B002ClearTaskSet> taskList = res.getExistTaskInfos();
                    if(taskList != null)
                        setClearTask(taskList,res.getTaskGroup());
                }},DbOfModuleidUtils.selectDbByModuleid(param.getModel().getModuleid()));
        }catch (Exception ex){
            return RequestSupport.updateReturnJson(false, "修改失败:"+ex.getMessage(), null).toString();
        }
        return RequestSupport.updateReturnJson(true, "提交修改成功", null).toString();
    }

    private void setClearTask(List<B002ClearTaskSet> taskSetList, String taskGroup) throws Exception {
        String preTaskIdStr = "";
        for (int index = 0; index < taskSetList.size(); index++) {

            B002ClearTaskSet taskSet = taskSetList.get(index);
            taskSet.setTaskGroup(taskGroup);

            //展示顺序
            taskSet.setDisplayOrder(index+"");

            //前置任务
            taskSet.setPreTaskId(preTaskIdStr);

            //前置任务， #表示当前任务组，同一组内的任务依次依赖，不允许出现并行执行，后一个任务的前置任务包含排在它前面的所有任务id
            if(index==0){
                preTaskIdStr = preTaskIdStr + "#:" + taskSet.getTaskId();
            }else{
                preTaskIdStr = preTaskIdStr + "|#:" + taskSet.getTaskId();
            }
            b002Dao.addTaClearTaskSets(taskSet);
        }
    }
}
