package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.batch.dao.WmpBatchLogDao;
import com.kayak.batch.model.WmpBatchLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 文件名: Ta5004Service.java
 * 描述:  清算日志
 * 创建人: zengzt
 * 创建时间:2020年4月27日上午11:04:37
 */
@Service
@APIDefine(desc = "清算日志", model = WmpBatchLog.class)
public class WmpBatchLogService {

	@Autowired
	private WmpBatchLogDao tA5004Dao;

	@API(desc = "查询清算日志信息", auth = APIAuth.NO)
	public SqlResult<WmpBatchLog> findWmpBatchLogs(SqlParam<WmpBatchLog> params) throws Exception {
		
		//是否自动追加参数
		params.setMakeSql(true);
		return tA5004Dao.queryBatchLog(params);
		
	}
	
	
	
}
