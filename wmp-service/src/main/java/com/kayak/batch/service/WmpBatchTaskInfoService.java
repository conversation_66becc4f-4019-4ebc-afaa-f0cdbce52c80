package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.system.RequestSupport;
import com.kayak.batch.dao.WmpBatchTaskInfoDao;
import com.kayak.batch.model.WmpBatchTaskInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 清算任务信息操作服务
 */
@Service
@APIDefine(desc = "清算任务信息操作服务", model = WmpBatchTaskInfo.class)
public class WmpBatchTaskInfoService {

	@Autowired
	private WmpBatchTaskInfoDao wmpBatchTaskInfoDao;
	
	@API(desc = "查询清算任务信息", auth = APIAuth.NO)
	public SqlResult<WmpBatchTaskInfo> findWmpBatchTaskInfos(SqlParam<WmpBatchTaskInfo> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<WmpBatchTaskInfo> sqlResult =  wmpBatchTaskInfoDao.queryWmpBatchTaskInfos(params);
		return sqlResult;
	}

	@API(desc = "关联查询清算任务信息、清算任务配置", auth = APIAuth.NO)
	public SqlResult<WmpBatchTaskInfo> findWmpBatchTaskInfoWithFlag(SqlParam<WmpBatchTaskInfo> params) throws Exception {

		params.setMakeSql(false);
		return wmpBatchTaskInfoDao.queryWmpBatchTaskInfoWithFlag(params);
	}

	@API(desc = "新增清算任务", auth = APIAuth.NO)
	public String insertWmpBatchTaskInfo(SqlParam<WmpBatchTaskInfo> params) throws Exception {
		params.setMakeSql(false);
		//先校验组件ID是否已经存在
		System.out.println(params.getModel().getClass());
		List<SqlRow> queryResult = wmpBatchTaskInfoDao.queryWmpBatchTaskInfoById(params.getModel().getTaskId(),params.getModel().getModuleid());
		if(queryResult.size()>0){
			throw new PromptException("任务ID已经存在，新增清算组件失败");
		}
		wmpBatchTaskInfoDao.insertWmpBatchTaskInfo(params);
		return RequestSupport.updateReturnJson(true, "新增组件成功", null).toString();
	}

	@API(desc = "修改清算任务", auth = APIAuth.NO)
	public String updateWmpBatchTaskInfo(SqlParam<WmpBatchTaskInfo> params) throws Exception {

		params.setMakeSql(false);
		wmpBatchTaskInfoDao.updateWmpBatchTaskInfo(params);
		return RequestSupport.updateReturnJson(true, "修改组件成功", null).toString();
	}

	@API(desc = "删除清算任务",params = "taskId", auth = APIAuth.NO)
	public String deleteWmpBatchTaskInfo(SqlParam<WmpBatchTaskInfo> params) throws Exception {

		params.setMakeSql(false);
		//删除组件前，先判断是否有任务使用了该组件
		List<SqlRow>  result = wmpBatchTaskInfoDao.checkTaskIdIsUsed(params.getModel().getTaskId(),params.getModel().getModuleid());
		if(result.get(0).getInteger("setNum")>0){
			throw new PromptException("已有清算配置使用了该组件，不允许删除");
		}

		wmpBatchTaskInfoDao.deleteWmpBatchTaskInfo(params);
		return RequestSupport.updateReturnJson(true, "删除组件成功", null).toString();
	}

	@API(desc = "根据任务组信息查询", auth = APIAuth.NO)
	public SqlResult<WmpBatchTaskInfo> findWmpBatchTaskInfosByGroupInfo(SqlParam<WmpBatchTaskInfo> params) throws Exception {

		params.setMakeSql(false);
		return wmpBatchTaskInfoDao.findWmpBatchTaskInfosByGroupInfo(params);
	}
	
}
