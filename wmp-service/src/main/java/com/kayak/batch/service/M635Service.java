package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.dao.M635Dao;
import com.kayak.batch.model.M635;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.dao.M231Dao;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 基金划款报表
 * lsy
 * 20210728
 */
@Service
@APIDefine(desc = "基金划款报表", model = M635.class)
public class M635Service {

    @Autowired
    private M635Dao m635Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private M231Dao m231Dao;

    @Autowired
    private ReportformUtil reportformUtil;


    @API(desc = "查询清算日志信息", auth = APIAuth.YES)
    public SqlResult<M635> findWmpBatchLogs(SqlParam<M635> params) throws Exception {
        //是否自动追加参数
        params.setMakeSql(true);
        /**if(StringUtils.isNotBlank(params.getModel().getMaxTransDate()) && StringUtils.isNotBlank(params.getModel().getMinTransDate())){
         if(!SimpleDataUtil.compareDate(params.getModel().getMaxTransDate(),params.getModel().getMinTransDate())){
         throw new PromptException("申请开始日期必须大于申请结束日期");
         }
         }*/
        SqlResult<M635> sqlResult = m635Dao.queryBatchLog(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0) {
            for (M635 m635 : sqlResult.getRows()) {
                /**if(Tools.isNotBlank(m635.getTano())){
                 Map<String,Object> map = new HashMap<>();
                 map.put("tano",m635.getTano());
                 SqlParam<M231> fundParams = new FetcherData<>(map, M231.class);
                 SqlResult<M231> m231 = m231Dao.select(fundParams);
                 if(m231 != null && m231.getRows() != null && m231.getRows().size() > 0)
                 m635.setTaName(m231.getRows().get(0).getTaName());
                 }*/
                if (Tools.isNotBlank(m635.getProdCode())) {
                    Map<String, Object> m215Map = new HashMap<>();
                    m215Map.put("prodCode", m635.getProdCode());
                    SqlParam<M215> dateParams = new FetcherData<>(m215Map, M215.class);
                    dateParams.setMakeSql(true);
                    SqlResult<Map<String, String>> m215Info = m215Service.queryProdInfoList(dateParams);
                    if (m215Info.getRows() != null && m215Info.getRows().size() > 0) {
                        m635.setProdName(m215Info.getRows().get(0).get("prod_name"));
                    }
                }
            }
        }
        sqlResult.setDesensitized(false);
        return sqlResult;

    }

}
