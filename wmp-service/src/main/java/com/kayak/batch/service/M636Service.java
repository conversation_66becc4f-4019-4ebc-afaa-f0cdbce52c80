package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.dao.M636Dao;
import com.kayak.batch.model.M636;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基金返款报表
 * lsy
 * 20210728
 */
@Service
@APIDefine(desc = "基金返款报表", model = M636.class)
public class M636Service {

	@Autowired
	private M636Dao m636Dao;

	@Autowired
	private M101Dao m101Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private M215Service m215Service;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询清算日志信息", auth = APIAuth.YES)
	public SqlResult<M636> findWmpBatchLogs(SqlParam<M636> params) throws Exception {
		if(StringUtils.isBlank(params.getModel().getBusiDate())){
			throw new PromptException("请选择交易日期范围");
		}
		//是否自动追加参数
		//params.setMakeSql(true);
		params.getModel().setLegalCode(null);
		SqlResult<M636> sqlResult =  m636Dao.queryBatchLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				if(Tools.isNotBlank(item.getProdCode())){
					Map<String,Object> m215Map = new HashMap<>();
					m215Map.put("prodCode",item.getProdCode());
					SqlParam<M215> dateParams = new FetcherData<>(m215Map, M215.class);
					dateParams.setMakeSql(true);
					SqlResult<Map<String, String>> m215Info = m215Service.queryProdInfoList(dateParams);
					if(m215Info.getRows() != null && m215Info.getRows().size() > 0){
						item.setProdName(m215Info.getRows().get(0).get("prod_name"));
					}
				}

				if(Tools.isNotBlank(item.getBranchCode())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getBranchCode());
					SqlParam<M001> branchParams = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(branchParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setBranchName(m001Info.getRows().get(0).getOrgname());
					}
				}

				if(Tools.isNotBlank(item.getSubBranchCode())){
					Map<String,Object> map2 = new HashMap<>();
					map2.put("orgno",item.getSubBranchCode());
					SqlParam<M001> subBranchParam = new FetcherData<>(map2, M001.class);
					SqlResult<M001> m001Info1 = m001Dao.find(subBranchParam);
					if(m001Info1.getRows() != null && m001Info1.getRows().size() > 0){
						item.setSubBranchName(m001Info1.getRows().get(0).getOrgname());
					}
				}

			} catch (Exception e) {
				throw new RuntimeException("M933错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
