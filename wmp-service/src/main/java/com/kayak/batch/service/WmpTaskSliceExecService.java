package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.model.B001;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.batch.dao.WmpTaskSliceExecDao;

import com.kayak.batch.model.WmpTaskSliceExec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "分片任务信息查询", model = B001.class)
public class WmpTaskSliceExecService {
    @Autowired
    WmpTaskSliceExecDao wmpTaskSliceExecDao;

    @API(desc="清算任务管理分片任务")
    public SqlResult<WmpTaskSliceExec> query(SqlParam<WmpTaskSliceExec> param)throws  Exception{
        return wmpTaskSliceExecDao.queryByTargeCode(param);
    }

    public SqlResult<WmpTaskSliceExec> queryStepNo(SqlParam<WmpTaskSliceExec> param)throws  Exception{
        return wmpTaskSliceExecDao.queryStepNo(param);
    }

    public SqlResult<WmpTaskSliceExec> querySliceStatus(SqlParam<WmpTaskSliceExec> param)throws  Exception{
        return wmpTaskSliceExecDao.querySliceStatus(param);
    }

    public SqlResult<WmpTaskSliceExec> queryDatasource(SqlParam<WmpTaskSliceExec> param)throws  Exception{
        return wmpTaskSliceExecDao.queryDatasource(param);
    }

    public SqlResult<WmpTaskSliceExec> queryServerIp(SqlParam<WmpTaskSliceExec> param)throws  Exception{
        return wmpTaskSliceExecDao.queryServerIp(param);
    }

}
