package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.dao.M637Dao;
import com.kayak.batch.model.M637;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.dao.M231Dao;
import com.kayak.fund.model.M231;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售机构与基金公司对账报表
 * lsy
 * 20210728
 */
@Service
@APIDefine(desc = "销售机构与基金公司对账报表", model = M637.class)
public class M637Service {

	@Autowired
	private M637Dao m637Dao;

	@Autowired
	private M231Dao m231Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M215Service m215Service;

	@API(desc = "销售机构与基金公司对账报表", auth = APIAuth.YES)
	public SqlResult<M637> findWmpBatchLogs(SqlParam<M637> params) throws Exception {
		//1、参数包括查询时间、tano、prodCode，后两个非必传
		SqlResult<M637> prodResult = m637Dao.queryAllProd(params);
		reportformUtil.checkMaxExcel(prodResult.getRows().size());
		//获取产品名称、TA名称
		if(null!=prodResult&&prodResult.getRows().size()>0){
			List<M637> sqlList=prodResult.getRows();
			for(M637 m637:sqlList){
				Map<String, String> prodInfoMap = m215Service.getProdInfo("ROOT", SystemNo.FUND,m637.getTano(),m637.getProdCode());
				if(prodInfoMap != null && prodInfoMap.size() > 0){
					m637.setProdName(prodInfoMap.get("prod_name"));
				}
				/**if(null!=m637){
					m637.setTaName(reportformUtil.getFundTaName(m637.getTano()));
				}*/
			}
		}
		return prodResult;
		
	}

}
