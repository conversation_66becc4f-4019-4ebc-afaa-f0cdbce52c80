package com.kayak.batch.service;

import com.alibaba.fastjson.JSON;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.dao.B002Dao;
import com.kayak.batch.model.B002;
import com.kayak.batch.model.B002ClearGroupMember;
import com.kayak.batch.model.B002ClearTaskSet;
import com.kayak.batch.model.B003;
import com.kayak.common.util.SequenceUtils;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: lfzh
 * @date: 2020-12-24 15:37
 */
@Service
@APIDefine(desc = "清算编辑", model = B002.class)
public class B002Service {

    @Autowired
    private B002Dao b002Dao;

    @Autowired
    private SequenceUtils sequenceUtils;

    @API(desc = "查询清算组配置信息", auth = APIAuth.NO)
    public String queryB002(SqlParam<B002> param) throws Exception {
        param.setMakeSql(false);
        SqlResult<B002> data = b002Dao.queryClearGroups(param);
        List<B002> rows = data.getRows();
        rows.forEach(res -> {
            if (res.getPreTaskGroup() == null) {
                res.setLastTaskGroup("");
                res.setPreTaskGroup("");
            }
        });
        rows.sort(new B002());
        List<Map<String, Object>> firstDay = new ArrayList<>();
        List<Map<String, Object>> secondDay = new ArrayList<>();
        List<Map<String, Object>> thirdDay = new ArrayList<>();
        List<B002> noTimeList = new ArrayList<>();
        Map<String, List<B002>> temp = new HashMap<>();

        for (B002 row : rows) {

            if (row.getLastTaskGroup() == null) {
                row.setLastTaskGroup("");
            }
            if (row.getPreTaskGroup() == null) {
                row.setPreTaskGroup("");
            }
            if ("999999".equals(row.getShouldExecTime())) {
                noTimeList.add(row);
            } else {
                String shouldExecTime = row.getShouldExecTime().substring(0, 2) + ":" + row.getShouldExecTime().substring(2, 4);
                if ("1".equals(row.getRunningType())) {
                    List<B002> list = new ArrayList<>();
                    if (temp.containsKey(shouldExecTime + "1")) {
                        temp.get(shouldExecTime + "1").add(row);
                    } else {
                        list.add(row);
                        temp.put(shouldExecTime + "1", list);
                        Map<String, Object> map = new HashMap<>();
                        map.put("time", shouldExecTime);
                        map.put("id", UUID.randomUUID().toString());
                        map.put("rows", list);
                        firstDay.add(map);
                    }
                } else if ("0".equals(row.getRunningType())) {
                    List<B002> list = new ArrayList<>();
                    if (temp.containsKey(shouldExecTime + "0")) {
                        temp.get(shouldExecTime + "0").add(row);
                    } else {
                        list.add(row);
                        temp.put(shouldExecTime + "0", list);
                        Map<String, Object> map = new HashMap<>();
                        map.put("time", shouldExecTime);
                        map.put("id", UUID.randomUUID().toString());
                        map.put("rows", list);
                        secondDay.add(map);
                    }
                } else if ("2".equals(row.getRunningType())) {
                    List<B002> list = new ArrayList<>();
                    if (temp.containsKey(shouldExecTime + "2")) {
                        temp.get(shouldExecTime + "2").add(row);
                    } else {
                        list.add(row);
                        temp.put(shouldExecTime + "2", list);
                        Map<String, Object> map = new HashMap<>();
                        map.put("time", shouldExecTime);
                        map.put("id", UUID.randomUUID().toString());
                        map.put("rows", list);
                        thirdDay.add(map);
                    }
                }
            }
        }
        this.sortByTime(firstDay);
        this.sortByTime(secondDay);
        this.sortByTime(thirdDay);
        Map<String, Object> map = new HashMap();
        map.put("success", true);
        map.put("firstDay", firstDay);
        map.put("secondDay", secondDay);
        map.put("thirdDay", thirdDay);
        map.put("noTimeList", noTimeList);
        return JSON.toJSONString(map);
    }


    public String queryB003(SqlParam<B002> param) throws Exception {
        HashMap<String, Object> map = new HashMap<>();
        map.put("moduleid", param.getModel().getModuleid());
        //所有任务信息
        FetcherData<B003> b003Param = new FetcherData<>(map, B003.class);
        SqlResult<B003> taskInfos = b002Dao.getTaskInfos(b003Param);
        HashMap<String, List> b003HashMap = new HashMap<>();
        for (B003 row : taskInfos.getRows()) {
            if (StringUtils.isNotBlank(row.getTaskType())) {
                List list = b003HashMap.computeIfAbsent(row.getTaskType(), (f) -> {
                    List<B003> b003s = new ArrayList<>();
                    return b003s;
                });
                list.add(row);
            }
        }

        FetcherData<B002ClearTaskSet> taskSetParam = new FetcherData<>(map, B002ClearTaskSet.class);
        SqlResult<B002ClearTaskSet> existTaskSet = b002Dao.getExistTaskSet(taskSetParam);
        HashMap<String, List> existTaskSetMap = new HashMap<>();
        for (B002ClearTaskSet row : existTaskSet.getRows()) {
            if (StringUtils.isNotBlank(row.getTaskGroup())) {
                List list = existTaskSetMap.computeIfAbsent(row.getTaskGroup(), (f) -> {
                    List<B002ClearTaskSet> b002ClearTaskSets = new ArrayList<>();
                    return b002ClearTaskSets;
                });
                list.add(row);
            }
        }

        //成员查询
        FetcherData<B002ClearGroupMember> groupMemberParam = new FetcherData<>(map, B002ClearGroupMember.class);
        SqlResult<B002ClearGroupMember> existMemberInfo = b002Dao.findExistMemberInfo(groupMemberParam);
        SqlResult<B002ClearGroupMember> groupMemberSqlResult = b002Dao.findAllMemberInfo(groupMemberParam);
        Map<String, List<B002ClearGroupMember>> groupMemberMap = new HashMap<>();
        Map<String, String> existTaNoMap = new HashMap<>();
        for (B002ClearGroupMember row : existMemberInfo.getRows()) {
            if (StringUtils.isNotBlank(row.getTaskGroup())) {
                List<B002ClearGroupMember> list = groupMemberMap.computeIfAbsent(row.getTaskGroup(), (f) -> {
                    List<B002ClearGroupMember> b002ClearGroupMembers = new ArrayList<>();
                    return b002ClearGroupMembers;
                });
                list.add(row);
                if ("0".equals(row.getIsGroupMember())) {
                    existTaNoMap.put(row.getTaskGroup(), "");
                    continue;
                }
                existTaNoMap.computeIfAbsent(row.getTaskGroup(),(f) -> {
                   return "";
                });
                existTaNoMap.put(row.getTaskGroup(),existTaNoMap.get(row.getTaskGroup())+"|"+row.getGroupMember());
            }
        }
        for (String taskGroup : existTaNoMap.keySet()) {
            String existTaNoStr = existTaNoMap.get(taskGroup);
            if (existTaNoStr == null) continue;
            List<B002ClearGroupMember> b002ClearGroupMembers = groupMemberMap.get(taskGroup);
            String execTaskType = b002ClearGroupMembers.get(0).getExecTaskType();
            if ("".equals(existTaNoStr)) {
                List<B002ClearGroupMember> list = new ArrayList<B002ClearGroupMember>();
                if(groupMemberSqlResult != null){
                    groupMemberSqlResult.getRows().forEach(data -> {
                        B002ClearGroupMember b002ClearGroupMember = new B002ClearGroupMember();
                        b002ClearGroupMember.setGroupMember(data.getGroupMember());
                        b002ClearGroupMember.setGroupMemberName(data.getGroupMemberName());
                        b002ClearGroupMember.setExecTaskType(execTaskType);
                        b002ClearGroupMember.setTaskGroup(taskGroup);
                        b002ClearGroupMember.setIsGroupMember("0");
                        b002ClearGroupMember.setModuleid(data.getModuleid());
                        list.add(b002ClearGroupMember);
                    });
                }

                groupMemberMap.put(taskGroup,list);
                continue;
            }

            for (B002ClearGroupMember row : groupMemberSqlResult.getRows()) {
                if (existTaNoStr.indexOf(row.getGroupMember()) == -1) {
                    B002ClearGroupMember b002ClearGroupMember = new B002ClearGroupMember();
                    b002ClearGroupMember.setGroupMember(row.getGroupMember());
                    b002ClearGroupMember.setGroupMemberName(row.getGroupMemberName());
                    b002ClearGroupMember.setExecTaskType(execTaskType);
                    b002ClearGroupMember.setTaskGroup(taskGroup);
                    b002ClearGroupMember.setIsGroupMember("0");
                    b002ClearGroupMember.setModuleid(row.getModuleid());
                    b002ClearGroupMembers.add(b002ClearGroupMember);
                }
            }

        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("taskInfoMap", b003HashMap);
        result.put("existTaskInfoMap", existTaskSetMap);
        result.put("groupMemberMap", groupMemberMap);
        return JSON.toJSONString(result);
    }

    //添加清算组时需要获取清算组id
//    @API(desc = "添加清算组时需要获取清算组id", auth = APIAuth.NO)
    public String getSequence(SqlParam<B002> param) throws Exception {
        String sequence = sequenceUtils.getSequence("wmp_batch_gropu_info" , param.getModel().getModuleid(), 8);
        Map<String, Object> result = new HashMap<>();
        result.put("success", "true");
        result.put("sequence", sequence);
        return JSON.toJSONString(result);
    }


    public void sortByTime(List<Map<String, Object>> list) {
        list.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                int time1 = Integer.parseInt(((String) o1.get("time")).replace(":", ""));
                int time2 = Integer.parseInt(((String) o2.get("time")).replace(":", ""));
                return time1 - time2;
            }
        });
    }

    //检查清算组名是否重复
//    @API(desc = "检查清算组名是否重复", auth = APIAuth.NO)
    public String getAllTaskGloupName(SqlParam<B002> param) throws Exception {
        param.setMakeSql(false);
        List<SqlRow> rows = b002Dao.getAllTaskGloupName(param);
        Map<String, Object> result = new HashMap<>();
        List temp = rows.stream().map(res -> res.get("NAME")).collect(Collectors.toList());
        result.put("success", "true");
        result.put("rows", temp);
        return JSON.toJSONString(result);
    }

     public SqlResult<B002ClearGroupMember> findTaClearGroupMembers(SqlParam<B002> param) throws Exception {
         param.setMakeSql(false);
         Map<String, Object> map = new HashMap<>();
         map.put("taskGroup", param.getModel().getTaskGroup());
         map.put("execTaskGroup", param.getModel().getExecTaskType());
         map.put("moduleid", param.getModel().getModuleid());
         FetcherData<B002ClearGroupMember> memberParam = new FetcherData<B002ClearGroupMember>(map, B002ClearGroupMember.class);
         SqlResult<B002ClearGroupMember> sqlResult = b002Dao.getAllGroupMember(memberParam);
         return sqlResult;
     }
}
