package com.kayak.batch.service;

import com.alibaba.fastjson.JSON;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.dao.B001Dao;
import com.kayak.batch.model.B001;
import com.kayak.batch.model.B002GroupList;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "清算流程", model = B001.class)
public class B001Service {
    @Autowired
    private B001Dao b001Dao;

    @API(desc = "查询指定工作日已注册的所有清算组", auth = APIAuth.NO)
    public String queryClearGroup(SqlParam<B001> param) throws Exception {
        param.setMakeSql(false);
        Map<String, Object> dataMap = new HashMap();
        if(Tools.isNotBlank(param.getModel().getQueryTaskDate())){
            SqlResult<B001> data = b001Dao.queryClearGroups(param);
            List<B001>      rows = data.getRows();
            //将pre_task_group和last_task_group如果为null,则赋值为"";
            rows.forEach(res -> {
                if (res.getPreTaskGroup() == null) {
                    res.setLastTaskGroup("");
                    res.setPreTaskGroup("");
                }
            });
            rows.sort(new B001());
            List<String>              list1        = rows.stream().map(row -> row.getTaskGroup()).collect(Collectors.toList());
            List<Map<String, Object>> firstDay     = new ArrayList<>();
            List<Map<String, Object>> secondDay    = new ArrayList<>();
            List<Map<String, Object>> thirdDay    = new ArrayList<>();
            List<B001>              notTimeGroup = new ArrayList<>();
            Map<String, List<B001>> temp         = new HashMap<>();
            //获取所有的清算组合该清算组的上一个清算组
            SqlResult<B002GroupList> res = b001Dao.getAllGroup(param.getModel().getModuleid());
            if (res != null) {
                Map<String, String> groups = new HashMap();
                for (B002GroupList map : res.getRows()) {
                    groups.put(map.getTaskGroup(), map.getLastTaskGroup());
                }
                for (B001 row : rows) {
                    //校验清算组集合中的上一个清算组
                    if (row.getLastTaskGroup() != "") {
                        StringBuilder sb  = new StringBuilder();
                        if(row.getLastTaskGroup() != null){
                            String[]      arr = row.getLastTaskGroup().split(",");
                            checkLastGroup(list1, groups, row.getLastTaskGroup(), sb);
                            row.setLastTaskGroup(sb.toString());
                        }

                    }
                }
            }
            for (B001 row : rows) {
                String shouldExecTime = "";
                if ("999999".equals(row.getShouldExecTime())) {
                    notTimeGroup.add(row);
                } else {
                    if (row.getShouldExecTime() != null) {
                        shouldExecTime = row.getShouldExecTime().substring(0, 2) + ":" + row.getShouldExecTime().substring(2, 4);
                    }
                    if ("1".equals(row.getRunningType())) {
                        List<B001> list = new ArrayList<>();
                        if (temp.containsKey(shouldExecTime + "1")) {
                            temp.get(shouldExecTime + "1").add(row);
                        } else {
                            list.add(row);
                            temp.put(shouldExecTime + "1", list);
                            Map<String, Object> map = new HashMap<>();
                            map.put("time", shouldExecTime);
                            map.put("rows", list);
                            firstDay.add(map);
                        }
                    } else if ("0".equals(row.getRunningType())) {
                        List<B001> list = new ArrayList<>();
                        if (temp.containsKey(shouldExecTime + "0")) {
                            temp.get(shouldExecTime + "0").add(row);
                        } else {
                            list.add(row);
                            temp.put(shouldExecTime + "0", list);
                            Map<String, Object> map = new HashMap<>();
                            map.put("time", shouldExecTime);
                            map.put("rows", list);
                            secondDay.add(map);
                        }
                    }else if ("2".equals(row.getRunningType())) {
                        List<B001> list = new ArrayList<>();
                        if (temp.containsKey(shouldExecTime + "2")) {
                            temp.get(shouldExecTime + "2").add(row);
                        } else {
                            list.add(row);
                            temp.put(shouldExecTime + "2", list);
                            Map<String, Object> map = new HashMap<>();
                            map.put("time", shouldExecTime);
                            map.put("rows", list);
                            thirdDay.add(map);
                        }
                    }
                }
            }
            this.sortByTime(firstDay);
            this.sortByTime(secondDay);
            this.sortByTime(thirdDay);
            dataMap.put("success", true);
            dataMap.put("firstDay", firstDay);
            dataMap.put("secondDay", secondDay);
            dataMap.put("thirdDay", thirdDay);
            dataMap.put("notTimeGroup", notTimeGroup);
        }
        return JSON.toJSONString(dataMap);
    }


    private void checkLastGroup(List<String> rows, Map<String, String> groups, String lastGroup, StringBuilder sb) {
        if (lastGroup == null) {
            return;
        }
        String[] arr = lastGroup.split(",");
        for (String s : arr) {
            if (!rows.contains(s)) {
                this.checkLastGroup(rows, groups, groups.get(s), sb);
            } else {
                if (!"".equals(sb.toString())) {
                    sb.append(",");
                }
                sb.append(s);
            }
        }
    }

    public void sortByTime(List<Map<String, Object>> list) {
        list.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                int time1 = Integer.parseInt(((String) o1.get("time")).replace(":", ""));
                int time2 = Integer.parseInt(((String) o2.get("time")).replace(":", ""));
                return time1 - time2;
            }
        });
    }

    @API(desc = "根据清算组ID查询所有失败任务的Task_Id", auth = APIAuth.YES)
    public SqlResult<String> queryErrorTaskByTaskGroup(SqlParam<B001> param) throws Exception {
        param.setMakeSql(false);
        SqlResult<B001> result  = b001Dao.queryErrorTaskByTaskGroup(param);
        List<B001>      list    = result.getRows();
        List<String>      taskIds = list.stream().map(item -> item.getTaskId()).collect(Collectors.toList());
        SqlResult<String> build   = SqlResult.build(taskIds);
        return build;
    }

}
