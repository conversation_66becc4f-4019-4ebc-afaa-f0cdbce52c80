package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.batch.dao.WmpBatchTaskSetDao;
import com.kayak.batch.model.WmpBatchTaskSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc 清算任务配置操作服务
 */
@Service
@APIDefine(desc = "清算任务配置操作服务", model = WmpBatchTaskSet.class)
public class WmpBatchTaskSetService {

    @Autowired
    private WmpBatchTaskSetDao WmpBatchTaskSetDao;

    @API(desc = "查询清算任务配置", auth = APIAuth.NO)
    public SqlResult<WmpBatchTaskSet> findWmpBatchTaskSets(SqlParam<WmpBatchTaskSet> params) throws Exception {

        params.setMakeSql(true);

        return WmpBatchTaskSetDao.queryWmpBatchTaskSets(params);
    }

    @API(desc = "新增清算任务配置", auth = APIAuth.NO)
    public String insertWmpBatchTaskSet(SqlParam<WmpBatchTaskSet> params) throws Exception {

        params.setMakeSql(false);

        SqlResult<WmpBatchTaskSet> sqlResult = WmpBatchTaskSetDao.queryWmpBatchTaskSetByTaskGroup(params);
        if (sqlResult.getRows().size() > 0){
            return RequestSupport.updateReturnJson(false, "已存在任务配置", null).toString();
        }
        WmpBatchTaskSetDao.insertWmpBatchTaskSet(params);

        return RequestSupport.updateReturnJson(true, "新增任务配置成功", null).toString();
    }

    @API(desc = "修改清算任务配置", auth = APIAuth.NO)
    public String updateWmpBatchTaskSet(SqlParam<WmpBatchTaskSet> params) throws Exception {

        params.setMakeSql(false);

        WmpBatchTaskSetDao.updateWmpBatchTaskSet(params);

        return RequestSupport.updateReturnJson(true, "修改任务配置成功", null).toString();
    }

    @API(desc = "删除清算任务配置", auth = APIAuth.NO)
    public String deleteWmpBatchTaskSet(SqlParam<WmpBatchTaskSet> params) throws Exception {

        params.setMakeSql(false);

        WmpBatchTaskSetDao.deleteWmpBatchTaskSet(params);

        return RequestSupport.updateReturnJson(true, "删除任务配置成功", null).toString();
    }
}
