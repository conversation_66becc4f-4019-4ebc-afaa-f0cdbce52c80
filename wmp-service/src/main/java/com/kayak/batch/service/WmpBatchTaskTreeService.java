package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.batch.dao.WmpBatchTaskTreeDao;
import com.kayak.batch.model.WmpBatchTaskTree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc 清算任务管理操作服务
 */
@Service
@APIDefine(desc = "清算任务管理操作服务", model = WmpBatchTaskTree.class)
public class WmpBatchTaskTreeService {
    @Autowired
    private WmpBatchTaskTreeDao WmpBatchTaskTreeDao;

    @API(desc = "查询清算管理信息", auth = APIAuth.NO)
    public SqlResult<WmpBatchTaskTree> findWmpBatchTaskTrees(SqlParam<WmpBatchTaskTree> params) throws Exception {

        //是否自动追加参数,这里SQL比较复杂，自己拼接好SQL，不需要追加参数了
        params.setMakeSql(false);

        return WmpBatchTaskTreeDao.queryWmpBatchTaskTree(params);
    }
}
