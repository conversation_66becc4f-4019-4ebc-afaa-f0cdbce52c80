package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.dao.B001DetailDao;
import com.kayak.batch.model.B001;
import com.kayak.batch.model.B001Detail;
import com.kayak.common.constants.B001DetailRootStatus;
import com.kayak.common.constants.BatchTaskStatus;
import com.kayak.common.constants.BatchTaskType;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;

/**
 * 清算详情列表
 */
@Service
@APIDefine(desc = "清算详情列表", model = B001.class)
public class B001DetailService {
    @Autowired
    private B001DetailDao b001DetailDao;

    @API(desc = "查询清算任务信息", auth = APIAuth.YES)
    public SqlResult<B001Detail> findB001Detail(SqlParam<B001Detail> params) throws Exception {
        assert StringUtils.isNotBlank(params.getModel().getModuleid()) : "模块ID不能为空";
        //是否自动追加参数,这里SQL比较复杂，自己拼接好SQL，不需要追加参数了
        params.setMakeSql(false);
        //如果是清算组类型为产品或者销售商类,有叶子节点.先查第一层
        String                  execGridId = "(";
        B001Detail            model      = params.getModel();
        String                  type       = model.getExecTaskType();
        SqlResult<B001Detail> rootData   = null;
        if ( BatchTaskType.isTABatchGroup(type)) {
            Map<String, Object> map = new HashMap<>();
            rootData = b001DetailDao.queryRootData(params);
            map.put("queryTaskDate", model.getQueryTaskDate());
            map.put("taskGroup", model.getTaskGroup());
            map.put("moduleid", model.getModuleid());
            //存下页子所查到execGridId
            for (B001Detail item : rootData.getRows()) {
                if ("(".equals(execGridId)) {
                    execGridId = execGridId + "'" + item.getExecGridId() + "'";
                } else {
                    execGridId = execGridId + ",'" + item.getExecGridId() + "'";
                }
            }
            execGridId = execGridId + ")";
            FetcherData<B001DetailRootStatus> paramsTemp   = new FetcherData<B001DetailRootStatus>(map, B001DetailRootStatus.class);
            SqlResult<B001DetailRootStatus>   statusResult = b001DetailDao.queryRootStatus(paramsTemp);
            Map<String, B001DetailRootStatus> rootStatus   = new HashMap<>();
            statusResult.getRows().forEach(row -> {
                rootStatus.put(row.getMapKey(), row);
            });
            rootData.getRows().forEach(row -> {
                if (StringUtils.isBlank(row.getParentExecGridId())) {
                    B001DetailRootStatus item = rootStatus.get(row.getExecGridId());
                    if (item != null) {
                        String rtnDesc = "失败:" + item.getFailure() + ";成功:"
                                + item.getSuccess() + ";未执行:" + item.getNoExcute() + ";未注册:" + item.getNoRegistry();
                        row.setRtnDesc(rtnDesc);
                        if (Integer.parseInt(item.getFailure()) > 0) {
                            row.setExecStatus("6");
                        } else if (Integer.parseInt(item.getExcuting()) > 0) {
                            row.setExecStatus("2");
                        } else if (Integer.parseInt(item.getNoExcute()) > 0) {
                            row.setExecStatus("0");
                        } else if (Integer.parseInt(item.getSuccess()) > 0) {
                            row.setExecStatus("5");
                        }
                    }
                }
            });
        }
        SqlResult<B001Detail> result = b001DetailDao.queryClearTaskExecInfo(params, execGridId);
        //将任务pre_task为null的置为"",并排序
        result.getRows().forEach(res -> {
            if (res.getPreTaskId() == null){
                res.setPreTaskId("");
            }
        });
        result.getRows().sort(new Comparator<B001Detail>() {
            @Override
            public int compare(B001Detail o1, B001Detail o2) {
                return o1.getPreTaskId().split("|").length - o2.getPreTaskId().split("|").length;
            }
        });
        if (rootData != null &&  BatchTaskType.isTABatchGroup(type)) {
            result.getRows().addAll(rootData.getRows());
            result.setResults(rootData.getResults());
        }
        return result;
    }

    @API(desc = "更新清算执行状态为跳过", auth = APIAuth.YES)
    public String updateStatusSkip(SqlParam<B001Detail> params) throws Exception {

        params.setMakeSql(false);
        params.getModel().setExecStatus(BatchTaskStatus.SKIP);
        b001DetailDao.updateExecStatus(params.getModel(), null);

        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    @API(desc = "查询回滚任务", auth = APIAuth.YES)
    public SqlResult<B001Detail> queryRevocation(SqlParam<B001Detail> params) throws Exception {
        params.setMakeSql(false);
        return b001DetailDao.queryRevocation(params);

    }

}
