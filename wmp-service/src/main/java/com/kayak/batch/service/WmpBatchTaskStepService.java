package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.batch.dao.WmpBatchTaskStepDao;
import com.kayak.batch.model.WmpBatchTaskStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 文件名: WmpBatchTaskStepService.java
 * 描述: 清算子步骤表
 * 创建人: zengzt
 * 创建时间:2020年6月6日下午4:46:44
 */
@Service
@APIDefine(desc = "清算子步骤操作服务", model = WmpBatchTaskStep.class)
public class WmpBatchTaskStepService {

	@Autowired
	private WmpBatchTaskStepDao wmpBatchTaskStepDao;

	@API(desc = "查询清算子步骤信息", auth = APIAuth.YES)
	public SqlResult<WmpBatchTaskStep> findWmpBatchTaskSteps(SqlParam<WmpBatchTaskStep> params) throws Exception {
		params.setMakeSql(true);
		return wmpBatchTaskStepDao.findWmpBatchTaskSteps(params);
	}

	@API(desc = "新增清算子步骤信息", auth = APIAuth.NO)
	public String insertWmpBatchTaskStep(SqlParam<WmpBatchTaskStep> params) throws Exception {
		params.setMakeSql(false);
		wmpBatchTaskStepDao.insertWmpBatchTaskStep(params);
		return RequestSupport.updateReturnJson(true, "新增清算子步骤信息成功", null).toString();
	}

	@API(desc = "修改清算子步骤信息", auth = APIAuth.NO)
	public String updateWmpBatchTaskStep(SqlParam<WmpBatchTaskStep> params) throws Exception {
		params.setMakeSql(false);
		wmpBatchTaskStepDao.updateWmpBatchTaskStep(params);
		return RequestSupport.updateReturnJson(true, "修改清算子步骤信息成功", null).toString();
	}

	@API(desc = "删除清算子步骤信息", auth = APIAuth.NO)
	public String deleteWmpBatchTaskStep(SqlParam<WmpBatchTaskStep> params) throws Exception {
		params.setMakeSql(false);
		wmpBatchTaskStepDao.deleteWmpBatchTaskStep(params);
		return RequestSupport.updateReturnJson(true, "删除清算子步骤信息成功", null).toString();
	}
	
}
