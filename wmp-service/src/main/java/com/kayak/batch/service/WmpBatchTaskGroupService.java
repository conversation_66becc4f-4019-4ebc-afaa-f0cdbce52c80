package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.batch.dao.WmpBatchTaskGroupDao;
import com.kayak.batch.model.WmpBatchTaskGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc 清算任务组操作服务
 */
@Service
@APIDefine(desc = "清算任务组操作服务", model = WmpBatchTaskGroup.class)
public class WmpBatchTaskGroupService {

    @Autowired
    private WmpBatchTaskGroupDao wmpBatchTaskGroupDao;

    @API(desc = "查询清算任务组", auth = APIAuth.NO)
    public SqlResult<WmpBatchTaskGroup> findWmpBatchTaskGroups(SqlParam<WmpBatchTaskGroup> params) throws Exception {

        params.setMakeSql(true);

        return wmpBatchTaskGroupDao.queryWmpBatchTaskGroups(params);
    }

    @API(desc = "新增清算任务组", auth = APIAuth.NO)
    public String insertWmpBatchTaskGroup(SqlParam<WmpBatchTaskGroup> params) throws Exception {

        params.setMakeSql(false);
        if (!Tools.isEmpty(params.getModel().getPreGroupId())
                && (params.getModel().getPreGroupId().contains(params.getModel().getGroupId()))){
            return RequestSupport.updateReturnJson(false, "前置任务组不能包含本身", null).toString();
        }
        int count = wmpBatchTaskGroupDao.queryWmpBatchTaskGroupByID(params.getModel().getGroupId());
        if (count > 0){
            return RequestSupport.updateReturnJson(false, "任务组已存在", null).toString();
        }
        wmpBatchTaskGroupDao.insertWmpBatchTaskGroup(params);

        return RequestSupport.updateReturnJson(true, "新增任务组成功", null).toString();
    }

    @API(desc = "修改清算任务组", auth = APIAuth.NO)
    public String updateWmpBatchTaskGroup(SqlParam<WmpBatchTaskGroup> params) throws Exception {

        params.setMakeSql(false);

        wmpBatchTaskGroupDao.updateWmpBatchTaskGroup(params);

        return RequestSupport.updateReturnJson(true, "修改任务组成功", null).toString();
    }

    @API(desc = "删除清算任务组", auth = APIAuth.NO)
    public String deleteWmpBatchTaskGroup(SqlParam<WmpBatchTaskGroup> params) throws Exception {

        params.setMakeSql(false);

        int count = wmpBatchTaskGroupDao.queryWmpBatchTaskSetByGroupId(params.getModel().getGroupId());
        if (count > 0){
            return RequestSupport.updateReturnJson(false, "任务组存在任务设置，不允许删除", null).toString();
        }
        wmpBatchTaskGroupDao.deleteWmpBatchTaskGroup(params);

        return RequestSupport.updateReturnJson(true, "删除任务组成功", null).toString();
    }

    @API(desc = "查询清算任务组系统编号", auth = APIAuth.NO)
    public SqlResult<WmpBatchTaskGroup> findWmpBatchTaskGroupsSystemNo(SqlParam<WmpBatchTaskGroup> params) throws Exception {

        params.setMakeSql(false);

        return wmpBatchTaskGroupDao.queryWmpBatchTaskGroupsSystemNo(params);
    }
}
