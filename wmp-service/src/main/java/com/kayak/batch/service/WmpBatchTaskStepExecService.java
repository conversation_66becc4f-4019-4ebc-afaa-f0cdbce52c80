package com.kayak.batch.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.batch.model.B001;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.batch.dao.WmpBatchTaskStepExecDao;
import com.kayak.batch.model.WmpBatchTaskStepExec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "清算任务步骤", model = B001.class)
public class WmpBatchTaskStepExecService {
    @Autowired
    WmpBatchTaskStepExecDao wmpBatchTaskStepExecDao;
    @API(desc="清算任务步骤信息查询")
    public SqlResult<WmpBatchTaskStepExec> query(SqlParam<WmpBatchTaskStepExec> param)throws  Exception{
        return wmpBatchTaskStepExecDao.queryByTargeCode(param);
    }
}
