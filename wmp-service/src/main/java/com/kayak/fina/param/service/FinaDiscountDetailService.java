package com.kayak.fina.param.service;

import com.kayak.fina.param.model.M502;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.FinaDiscountDetailDao;
import com.kayak.fina.param.model.FinaDiscountDetail;

@Service
@APIDefine(desc = "折扣方案明细服务", model = M502.class)
public class FinaDiscountDetailService {

	@Autowired
	private FinaDiscountDetailDao finaDiscountDetailDao;

	@API(desc = "查询折扣方案明细信息", auth = APIAuth.YES)
	public SqlResult<FinaDiscountDetail> findFinaDiscountDetails(SqlParam<FinaDiscountDetail> params) throws Exception {
		params.setMakeSql(false);
		SqlResult sr =  finaDiscountDetailDao.findFinaDiscountDetails(params);

		return sr;
	}
}
