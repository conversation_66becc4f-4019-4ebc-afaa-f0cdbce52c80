package com.kayak.fina.param.service;

import com.kayak.core.sql.SqlRow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M511Dao;
import com.kayak.fina.param.model.M511;
/**
 * 托管行管理, 原名 FinaTruteeInfoService
 */
@Service
@APIDefine(desc = "托管行信息实体类服务", model = M511.class)
public class M511Service {

	@Autowired
	private M511Dao m511Dao;

	@API(desc = "查询托管行信息实体类信息", auth = APIAuth.YES)
	public SqlResult<M511> findFinaTruteeInfos(SqlParam<M511> params) throws Exception {
		params.setMakeSql(true);
		return m511Dao.findFinaTruteeInfos(params);
	}

	@API(desc = "查询托管行信息下拉数据")
	public SqlResult<SqlRow> findFinaTrutee(SqlParam<M511> params) throws Exception {
		return m511Dao.findFinaTrutee();
	}

	@API(desc = "添加托管行信息实体类", params = "trutee_code,legal_code,trutee_name,account_version,name_format,fax", auth = APIAuth.NO)
	public int addFinaTruteeInfo(SqlParam<M511> params) throws Exception {
		return m511Dao.addFinaTruteeInfo(params).getEffect();
	}
	
	@API(desc = "修改托管行信息实体类", params = "trutee_code,legal_code,trutee_name,account_version,name_format,fax", auth = APIAuth.NO)
	public int updateFinaTruteeInfo(SqlParam<M511> params) throws Exception {
		return m511Dao.updateFinaTruteeInfo(params).getEffect();
	}
	
	@API(desc = "删除托管行信息实体类", params = "trutee_code,legal_code,trutee_name,account_version,name_format,fax", auth = APIAuth.NO)
	public int deleteFinaTruteeInfo(SqlParam<M511> params) throws Exception {
		return m511Dao.deleteFinaTruteeInfo(params).getEffect();
	}

}
