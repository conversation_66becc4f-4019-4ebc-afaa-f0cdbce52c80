package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ExcelUtils;
import com.kayak.common.util.ParseExcelToJson;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.M516Dao;
import com.kayak.fina.param.model.M516;
import com.kayak.fina.param.model.M516JSONTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static com.kayak.base.dao.util.DaoUtil.doTrans;

/**
 * 鍘熷悕 Ta2013Service
 */
@Service
@RequiredArgsConstructor
@APIDefine(desc = "鎺ュ彛閰嶇疆鏂囦欢瑙ｆ瀽", model = M516.class)
@Slf4j
public class M516Service {
    @Autowired
    M516Dao m516Dao;
    @Value("${json.conf.path}")
    private String jsonPath;
    @Value("${excel.conf.path}")
    private String excelPath;

    @API(desc = "鏌ヨ鎺ュ彛淇℃伅琛?)
    public SqlResult<M516> query(SqlParam<M516> param) throws Exception {
        return m516Dao.query(param);
    }

    @API(desc = "涓轰笂浼爀xec")
    public SqlResult<M516> execl(SqlParam<M516> param) throws Exception {
        addExecl(param);
        return m516Dao.query(param);
    }

    public String add(SqlParam<M516> param) throws Exception {
        if (checkOne(param).getRows().size() > 0) {
            return RequestSupport.updateReturnJson(false, "閲嶅鎻掑叆", null).toString();
        }
        m516Dao.add(param);
        return RequestSupport.updateReturnJson(true, "鎻掑叆鎴愬姛", null).toString();
    }

    @API(desc = "鎻掑叆鏂规硶")
    public String addExecl(SqlParam<M516> param) throws Exception {
        Map<String, String> params = new HashMap();
        params.put("interface_file_route", param.getModel().getInterfaceFileRoute());
        params.put("outFilePath", jsonPath);
        String fileName = param.getModel().getInterfaceFileName();
        String version = fileName.substring(fileName.indexOf("_") + 1, fileName.lastIndexOf("."));
        param.getModel().setInterfaceVersion(version);
        params.put("interface_version", version);
        Map<String, Object> map = ParseExcelToJson.loadTemplate(params);
        if (map.get("code").equals("0000")) {
            Map<String, String[]> datas = (HashMap) map.get("data");
            String[] interface_json_type = datas.get("interface_json_type");
            String[] interface_json_name = datas.get("interface_json_name");
            String[] interface_json_route = datas.get("interface_json_route");
            M516JSONTemplate m516JSONTemplate = new M516JSONTemplate();
            m516JSONTemplate.setCreator("admin");
            m516JSONTemplate.setInterfaceCreateDate(DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()));
            m516JSONTemplate.setInterfaceVersion(version);
            doTrans(() -> {
                for (int i = 0; i < interface_json_name.length; i++) {
                    m516JSONTemplate.setInterfaceJsonName(interface_json_name[i]);
                    m516JSONTemplate.setInterfaceFileType(interface_json_type[i]);
                    m516JSONTemplate.setInterfaceJsonRoute(interface_json_route[i]);
                    m516Dao.addJSON(m516JSONTemplate);
                }
                m516Dao.add(param);
            });
        } else {
            return RequestSupport.updateReturnJson(false, (String) map.get("msg"), null).toString();
        }
        return RequestSupport.updateReturnJson(true, "鎻掑叆鎴愬姛", null).toString();
    }

    @API(desc = "妫€鏌ュ崟涓?)
    public SqlResult<M516> checkOne(SqlParam<M516> param) throws Exception {
        return m516Dao.checkOne(param);
    }

    public Map<String, String> checkAndReturn(SqlParam<M516> param) throws Exception {
        HashMap resultMap = new HashMap();
        String fileName = param.getModel().getInterfaceFileName();
        //鏂囦欢鍚庣紑
        String extension = null;
        if (fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf(".") + 1);
        }
        String version = fileName.substring(fileName.indexOf("_") + 1, fileName.lastIndexOf("."));
        param.getModel().setInterfaceVersion(version);
        if (m516Dao.checkOne(param).getRows().size() > 0) {
            resultMap.put("check", "false");
        } else {
            resultMap.put("check", "true");
            resultMap.put("path", excelPath);
            resultMap.put("uploadFileName", ExcelUtils.buildFilePathByExtension(excelPath, extension));
        }
        return resultMap;
    }

    public String stop(SqlParam<M516> param) throws Exception {
        doTrans(() -> {
            m516Dao.stop(param);
            m516Dao.stopJSON(param);
        });
        return RequestSupport.updateReturnJson(true, "鎿嶄綔鎴愬姛", null).toString();
    }

    public String start(SqlParam<M516> param) throws Exception {
        doTrans(() -> {
            m516Dao.start(param);
            m516Dao.startJSON(param);
        });
        return RequestSupport.updateReturnJson(true, "鎿嶄綔鎴愬姛", null).toString();
    }

    @API(desc = "淇敼鐘舵€?)
    public String changeStatus(SqlParam<M516> param) throws Exception {
        if (m516Dao.checkStatus(param).getRows().size() > 0) {
            return RequestSupport.updateReturnJson(false, "瀛樺湪妯℃澘涓庨攢鍞晢鍏宠仈锛屾棤娉曠鐢紒", null).toString();
        }
        if (param.getModel().getDataStatus().contains("E")) {
            return stop(param);
        } else {
            return start(param);
        }
    }
}


