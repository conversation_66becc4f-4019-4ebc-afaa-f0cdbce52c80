package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M551;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M551Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M551> findM551s(SqlParam<M551> param) throws  Exception{
        param.setMakeSql(true);
        String sql = " select t1.tano,\n" +
                "       t1.trans_date,\n" +
                "       t1.trans_orgno,\n" +
                "       t1.cust_manager,\n" +
                "       t1.prod_code,\n" +
                "       t1.prod_name,\n" +
                "       t1.stock_amt,\n" +
                "       t1.stock_num,\n" +
                "       t1.effe_num,\n" +
                "       t2.ta_name\n" +
                "  from fina_sale_stock_cm t1\n" +
                "  left join fina_ta_info t2 on t1.tano = t2.tano\n" +
                " where 1 = 1 \n ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,param);
    }
}
