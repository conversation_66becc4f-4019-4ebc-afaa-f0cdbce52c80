package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M506Dao;
import com.kayak.fina.param.model.M506;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "产品信息表服务", model = M506.class)
public class M506Service {

	@Autowired
	private M215Service m215Service;
	@Autowired
	private M506Dao m506Dao;
	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询产品代理关系信息", auth = APIAuth.NO)
	public SqlResult<M506> findFinaTFilec2(SqlParam<M506> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<M506> sqlResult = m506Dao.findFinaTFilec2(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		List<M506> volList = sqlResult.getRows();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		for (M506 m506 : volList){
			Map<String, Object> prodParam = new HashMap<>();
			prodParam.put("systemNo", SystemNo.FINA);
			prodParam.put("prodCode", m506.getFundcode());
			List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
			if (prodParaList != null && prodParaList.size() > 0){
				Map<String, String> prodPara = prodParaList.get(0);
				m506.setProdName(prodPara.get("prod_name"));
			}
		}
		return sqlResult;
	}

}
