package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.common.constants.SubDatabase;
import com.kayak.fina.param.model.M517;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M517Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M517> findFinaStocks(SqlParam<M517> params) throws Exception {
        String sql = "select t1.tano,\n" +
                "       t1.trans_date,\n" +
                "       t1.prod_code,\n" +
                "       sum(case\n" +
                "             when t1.cust_type = '1' then\n" +
                "              t1.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as per_amt,\n" +
                "       sum(case\n" +
                "             when t1.cust_type != '1' then\n" +
                "              t1.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as org_amt,\n" +
                "       sum(t1.stock_vol) as total_amt,\n" +
                "       t2.ta_name\n" +
                "  from fina_vol_stock t1 left join fina_ta_info t2 on t1.tano=t2.tano where 1=1 " +
                "group by t1.tano,t1.trans_date,t1.prod_code,t2.ta_name";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }


}
