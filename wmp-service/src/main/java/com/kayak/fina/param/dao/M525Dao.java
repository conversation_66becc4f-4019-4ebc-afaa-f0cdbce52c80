package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.common.constants.SubDatabase;
import com.kayak.fina.param.model.M525;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M525Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M525> findFinaTransDaySums(SqlParam<M525> params) throws Exception {
        return super.findRows("select a.orgno,so.orgname,a.prod_code,a.tano,t2.ta_name," +
                " sum(ifnull(a.ds_count, 0)) per_count," +
                " sum(ifnull(a.dg_count, 0)) org_count," +
                " sum(ifnull(a.ds_count, 0) + ifnull(a.dg_count, 0)) total_count," +
                " sum(ifnull(a.ds_amt, 0)) per_amt," +
                " sum(ifnull(a.dg_amt, 0)) org_amt," +
                " sum(ifnull(a.ds_amt, 0) + ifnull(a.dg_amt, 0)) total_amt" +
                " from fina_trans_day_sum a left join fina_ta_info t2 on a.tano=t2.tano" +
                " left join fdsprod.sys_org so on" +
                " a.orgno = so.orgno" +
                " group by a.orgno,a.prod_code,a.tano,t2.ta_name ,so.orgname", SubDatabase.DATABASE_FINA_CENTER, params);
    }
}
