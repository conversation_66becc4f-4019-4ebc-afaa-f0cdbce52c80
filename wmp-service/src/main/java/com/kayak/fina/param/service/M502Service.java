package com.kayak.fina.param.service;

import com.kayak.common.constants.DisoucntType;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.constants.SystemNo;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.FinaDiscountDetailDao;
import com.kayak.fina.param.model.FinaDiscountDetail;
import com.kayak.fina.param.model.M502;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M502Dao;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@APIDefine(desc = "折扣方案信息服务", model = M502.class)
public class M502Service {

    private static final Logger log = LoggerFactory.getLogger(RequestSupport.class);

    @Autowired
    private M502Dao m502Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private FinaDiscountDetailDao finaDiscountDetailDao;

    @API(desc = "查询折扣方案详情信息", auth = APIAuth.YES)
    public SqlResult<M502> findAllDiscountCode(SqlParam<M502> params) throws Exception {
        params.setMakeSql(false);
        return m502Dao.findAllDiscountCode(params);
    }

    @API(desc = "查询单个产品所有折扣方案信息", auth = APIAuth.YES)
    public SqlResult<M502> findFinaDiscountInfos(SqlParam<M502> params) throws Exception {
        params.setMakeSql(true);
        return m502Dao.findFinaDiscountInfos(params);
    }

    @API(desc = "添加折扣方案信息", params = "discount_code,discount_name,busi_type,remark,discount_role,legal_code", auth = APIAuth.NO)
    public String addFinaDiscountInfo(SqlParam<M502> params) throws Exception {

        DaoUtil.doTrans(() -> {
            if(null!=params.getModel().getConstantRate()&&StringUtils.isNotBlank(params.getModel().getConstantRate())){
                Double consRate=Double.valueOf(params.getModel().getConstantRate())/100;
                BigDecimal bd=BigDecimal.valueOf(consRate);
                params.getModel().setConstantRate(bd.toPlainString());
            }
            //生成折扣方案代码
            String dicountCode = genDiscountCode(params);
            //生成折扣类型
            if(null!=params.getModel().getConstantRate()&&StringUtils.isNotBlank(params.getModel().getConstantRate())){
                params.getModel().setDiscountType("0");
            }else{
                params.getModel().setDiscountType("1");
            }
            log.info("折扣率："+params.getModel().getConstantRate()+"------折扣类型："+params.getModel().getDiscountType());
            //插入折扣方案表
            params.getModel().setDiscountCode(dicountCode);
            m502Dao.addFinaDiscountInfo(params);
            params.getModel().getFinaDiscountDetailList().forEach(e -> {
                e.setDiscountCode(dicountCode);
                SqlParam<FinaDiscountDetail> finaDiscountDetailListParam =
                        null;
                try {
                    finaDiscountDetailListParam = new FetcherData<>(new HashMap<>(), FinaDiscountDetail.class);
                    BeanUtils.copyProperties(e, finaDiscountDetailListParam.getModel());
                    if(finaDiscountDetailListParam.getModel().getDim1Max() == null){
                        finaDiscountDetailListParam.getModel().setDim1Max("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim1Min() == null){
                        finaDiscountDetailListParam.getModel().setDim1Min("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim2Max() == null){
                        finaDiscountDetailListParam.getModel().setDim2Max("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim2Min() == null){
                        finaDiscountDetailListParam.getModel().setDim2Min("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim3Max() == null){
                        finaDiscountDetailListParam.getModel().setDim3Max("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim3Min() == null){
                        finaDiscountDetailListParam.getModel().setDim3Min("-");
                    }

                    finaDiscountDetailDao.addFinaDiscountDetail(finaDiscountDetailListParam);
                } catch (Exception exception) {
                    exception.printStackTrace();
                }
            });
        }, SubDatabase.DATABASE_FINA_CENTER);


        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }

    @API(desc = "修改折扣方案信息", params = "discount_code,discount_name,busi_type,remark,discount_role,legal_code", auth = APIAuth.NO)
    public String updateFinaDiscountInfo(SqlParam<M502> params) throws Exception {

        DaoUtil.doTrans(() -> {
            if(null!=params.getModel().getConstantRate()&&StringUtils.isNotBlank(params.getModel().getConstantRate())){
                Double consRate=Double.valueOf(params.getModel().getConstantRate())/100;
                BigDecimal bd=BigDecimal.valueOf(consRate);
                params.getModel().setConstantRate(bd.toPlainString());
            }
            //生成折扣类型
            if(null!=params.getModel().getConstantRate()&&StringUtils.isNotBlank(params.getModel().getConstantRate())){
                params.getModel().setDiscountType("0");
            }else{
                params.getModel().setDiscountType("1");
            }
            log.info("折扣率："+params.getModel().getConstantRate()+"------折扣类型："+params.getModel().getDiscountType());
            m502Dao.updateFinaDiscountInfo(params);
            //删除原数据
            finaDiscountDetailDao.deleteFinaDiscountDetail(params);

            params.getModel().getFinaDiscountDetailList().forEach(e -> {
                e.setDiscountCode(params.getModel().getDiscountCode());
                SqlParam<FinaDiscountDetail> finaDiscountDetailListParam = null;
                try {
                    finaDiscountDetailListParam = new FetcherData<>(new HashMap<>(), FinaDiscountDetail.class);
                    BeanUtils.copyProperties(e, finaDiscountDetailListParam.getModel());
                    if(finaDiscountDetailListParam.getModel().getDim1Max() == null){
                        finaDiscountDetailListParam.getModel().setDim1Max("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim1Min() == null){
                        finaDiscountDetailListParam.getModel().setDim1Min("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim2Max() == null){
                        finaDiscountDetailListParam.getModel().setDim2Max("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim2Min() == null){
                        finaDiscountDetailListParam.getModel().setDim2Min("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim3Max() == null){
                        finaDiscountDetailListParam.getModel().setDim3Max("-");
                    }
                    if(finaDiscountDetailListParam.getModel().getDim3Min() == null){
                        finaDiscountDetailListParam.getModel().setDim3Min("-");
                    }

                    finaDiscountDetailDao.addFinaDiscountDetail(finaDiscountDetailListParam);
                } catch (Exception exception) {
                    exception.printStackTrace();
                }
            });

        }, SubDatabase.DATABASE_FINA_CENTER);
        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    @API(desc = "删除折扣方案信息", params = "discount_code,discount_name,busi_type,remark,discount_role,legal_code", auth = APIAuth.YES)
    public String deleteFinaDiscountInfo(SqlParam<M502> params) throws Exception {
        SqlResult<M502> sqlResult = m502Dao.findProdDiscountInfo(params);
        if(sqlResult.getRows() != null && sqlResult.getRows().size() > 0){
            return RequestSupport.updateReturnJson(false, "删除失败,折扣率方案已使用", null).toString();
        }
        DaoUtil.doTrans(() -> {
            m502Dao.deleteFinaDiscountInfo(params);
            finaDiscountDetailDao.deleteFinaDiscountDetail(params);
        }, SubDatabase.DATABASE_FINA_CENTER);
        return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
    }

    @API(desc = "查询单个产品所有折扣方案信息", auth = APIAuth.YES)
    public SqlResult<M502> findDiscountInfosByProd(SqlParam<M502> params) throws Exception {
        return m502Dao.findDiscountInfosByProd(params);
    }

    @API(desc = "查询某个系统法人下的所有折扣方案", auth = APIAuth.YES)
    public SqlResult<M502> findDiscountInfos(SqlParam<M502> params) throws Exception {
        return m502Dao.findDiscountInfos(params);
    }

    @API(desc = "应用折扣方案")
    public String useDiscount(SqlParam<M502> params) throws Exception {
        DaoUtil.doTrans(() -> {
            M502 model = params.getModel();
            if (StringUtils.isNotBlank(model.getProdCode())){
                String [] prodList = params.getModel().getProdCode().split(",");
                for(int i=0;i<prodList.length;i++){
                    model.setProdCode(prodList[i]);
                    m502Dao.useDiscount(model);
                }
            }else {
                String [] tanos = params.getModel().getTano().split(",");
                if(tanos != null && tanos.length > 0){
                    for(int i=0;i<tanos.length;i++){
                        Map<String, Object> prodParam = new HashMap<>();
                        prodParam.put("systemNo", SystemNo.FINA);
                        prodParam.put("prodCode", model.getProdCode());
                        prodParam.put("supplyCode", tanos[i]);
                        prodParam.put("legalCode", "1000");
                        List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
                        if (prodParaList != null && prodParaList.size() > 0){
                            for (Map<String, String> map : prodParaList){
                                M502 m502 = new M502();
                                m502.setTano(tanos[i]);
                                m502.setProdCode(map.get("prod_code"));
                                m502.setBeginDate(model.getBeginDate());
                                m502.setBusiCode(model.getBusiCode());
                                m502.setDiscountCode(model.getDiscountCode());
                                m502Dao.useDiscount(m502);
                            }
                        }
                    }
                }

            }
        }, SubDatabase.DATABASE_FINA_CENTER);
        return RequestSupport.updateReturnJson(true, "处理成功", null).toString();
    }

    public String genDiscountCode(SqlParam<M502> params)throws Exception {
        String str = m502Dao.queryMaxDiscountCode(params);
        String code = "zkl"+0;
        if (StringUtils.isNotBlank(str)){
           code = "zkl"+String.valueOf(Long.parseLong(str.substring(3,str.length()))+1);
        }
        return code;
    }
}
