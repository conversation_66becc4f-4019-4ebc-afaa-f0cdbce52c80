package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.GlobalConstants;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.M515DataDao;
import com.kayak.fina.param.model.M515Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 涓櫥鏂囦欢琛紝鍘熷悕 Ta2016DataService
 */
@Service
@APIDefine(desc = "瀵煎叆鏂囦欢", model = M515Data.class)
@RequiredArgsConstructor
@Slf4j
public class M515DataService {
    @Autowired
    private M515DataDao m515DataDao;

    public final String ruleBef = "OFD_%s_%s_%s_" ;

    @API(desc = "涓櫥鏂囦欢鏌ヨ", auth = APIAuth.YES)
    public SqlResult<M515Data> findM515Data(SqlParam<M515Data> params) throws Exception {
        params.setMakeSql(true);
        return m515DataDao.findTaInterfaceDataFileDao(params);
    }

    @API(desc = "閫氳繃绱㈠紩鏌ヨ涓櫥鏂囦欢", auth = APIAuth.YES)
    public SqlResult<M515Data> findM515DataByNo(SqlParam<M515Data> params) throws Exception {

        return m515DataDao.findTaInterfaceDataFileByNo(params);
    }

    @API(desc = "涓櫥鏂囦欢娣诲姞", params = "file_id, filename, busi_code, index_no, name_rules, file_type, is_skip, is_vaildate_profile, datasource, data_status, create_time, create_user, update_time, update_user", auth = APIAuth.YES)
    public String addM515Data(SqlParam<M515Data> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛

        String loginname = (String) params.getAuthInfo().get("userid");
        params.getModel().setCreateUser(loginname);
        params.getModel().setUpdateUser(loginname);
        String nameRules = ruleBef+params.getModel().getInterfaceFileType()+".TXT";
        params.getModel().setNameRules(nameRules);
        params.getModel().setDataStatus("E");
        SqlResult<M515Data> result = m515DataDao.findTaInterfaceDataFileById(params);
        SqlResult<M515Data> resultOfBusi = m515DataDao.findTaInterfaceDataFileByBusiCode(params);


        if(result.getRows().size() == 0 && resultOfBusi.getRows().size() == 0){
            m515DataDao.addTaInterfaceDataFileDao(params);
        }else if(result.getRows().size() == 1){
            return RequestSupport.updateReturnJson(false, "娣诲姞閲嶅", null).toString();
        }else if(resultOfBusi.getRows().size() == 1){
            return RequestSupport.updateReturnJson(false, "鏂囦欢浠ｇ爜宸插瓨鍦?, null).toString();
        }

        return RequestSupport.updateReturnJson(true, "鎿嶄綔鎴愬姛", null).toString();
    }

    @API(desc = "涓櫥鏂囦欢鏇存柊", params = "file_id ,filename ,busi_code , datasource , data_status ,update_time ,update_user ", auth = APIAuth.YES)
    public String updateM515Data(SqlParam<M515Data> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛
//        Map<String, Object> sysUserParams = SysUtil.getSysUserParams();
//        params.getModel().setUpdateUser(params.getParams().get("userid").toString());
        String nameRules = ruleBef+params.getModel().getInterfaceFileType()+".TXT";
        params.getModel().setNameRules(nameRules);
        UpdateResult result =  m515DataDao.updateTaInterfaceDataFileDao(params);
        return RequestSupport.updateReturnJson(true, "鎿嶄綔鎴愬姛", null).toString();
    }
    @API(desc = "閿€鍞晢绱㈠紩鏂囦欢鍒犻櫎", params = "file_id", auth = APIAuth.YES)
    public String deleteM515Data(SqlParam<M515Data> params) throws Exception {
        UpdateResult result = m515DataDao.deleteTaInterfaceDataFileDao(params);
        return RequestSupport.updateReturnJson(true, "鍒犻櫎鎴愬姛", null).toString();
    }
}
