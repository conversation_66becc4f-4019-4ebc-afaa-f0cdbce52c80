package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M519;
import org.springframework.stereotype.Repository;


@Repository
public class M519Dao extends ComnDao {

	public SqlResult<M519> findFinaCustTransReqLog(SqlParam<M519> params) throws Exception {
		params.getModel().setLegalCode(null);
		params.setMakeSql(true);
		return super.findRows("SELECT app_serno,\n" +
				"       ori_app_serno,\n" +
				"       channel_serno,\n" +
				"       busi_code,\n" +
				"       busi_date,\n" +
				"       ack_date,\n" +
				"       channel_flag,\n" +
				"       trans_status,\n" +
				"       rtn_desc,\n" +
				"       cust_no,\n" +
				"       id_type,\n" +
				"       id_code,\n" +
				"       frozen_reason,\n" +
				"       cust_name,\n" +
				"       cust_type,\n" +
				"       tano,\n" +
				"       ta_name, \n" +
				"       ta_acct_no,\n" +
				"       prod_code,\n" +
				"       prod_name,\n" +
				"       trans_orgno,\n" +
				"       acct_no,\n" +
				"       trans_acct_no,\n" +
				"       cur,\n" +
				"       capital_status, \n" +
				"       expect_arrive_date,\n" +
				"       winding_date,\n" +
				"       plan_cycle_value,\n" +
				"       app_amt,\n" +
				"       app_vol,\n" +
				"       ack_amt,\n" +
				"       ack_vol,\n" +
				"       channel_date,\n" +
				"       channel_time,\n" +
				"       discount_rate,\n" +
				"       DEDUCTION_DATE,\n" +
				"       DEDUCTION_TYPE,\n" +
				"       cust_manager\n" +
				"  FROM fina_cust_trans_req_log  order by busi_date desc ", SubDatabase.DATABASE_FINA_CENTER,params);
	}



}
