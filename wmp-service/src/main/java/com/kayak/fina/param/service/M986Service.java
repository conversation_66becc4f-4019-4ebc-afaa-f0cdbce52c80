package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M986Dao;
import com.kayak.fina.param.model.M983;
import com.kayak.fina.param.model.M986;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * @ClassName M986Service
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/5 9:18
 * @Version 1.0
 **/
@Service
@APIDefine(desc = "理财账户类交易确认流水查询服务", model = M986.class)
public class M986Service {

    @Autowired
    private M986Dao M986Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财账户类交易确认历史流水查询", auth = APIAuth.YES)
    public SqlResult<M986> findFinaCustCfmParam(SqlParam<M986> params) throws Exception {
        SqlResult <M986> sqlResult = M986Dao.findFinaCustCfmParam(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
       /** sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            try{
                item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
            } catch (Exception e) {
                throw new RuntimeException("M986错误："+e.getMessage());
            }
            return item;
        }).collect(Collectors.toList()));*/
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

}
