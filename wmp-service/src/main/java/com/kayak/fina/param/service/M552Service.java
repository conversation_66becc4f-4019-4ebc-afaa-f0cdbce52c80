package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M552Dao;
import com.kayak.fina.param.model.M552;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财产品月报表", model = M552.class)
public class M552Service {

    @Autowired
    private M552Dao m552Dao;

    @Autowired
    private M001Dao m001Dao;
    
    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "理财产品月报表", auth = APIAuth.YES)
    public SqlResult<M552> findM552s(SqlParam<M552> param)throws Exception {
        SqlResult<M552> sqlResult = m552Dao.findM552s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M552> volList = sqlResult.getRows();
        for (M552 m552 : volList){
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m552.getProdCode());
            prodParam.put("supplyCode", m552.getTano());
            prodParam.put("legalCode", "1000");
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                m552.setProdName(prodPara.get("prod_name"));
            }
            M001 m001 = m001Dao.get(m552.getOrgno());
            if (m001 != null){
                m552.setOrgName(m001.getOrgname());
            }
            m552.setTaName(reportformUtil.getFinaTaName(m552.getTano()));
        }
        return sqlResult;
    }
}
