package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;

import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.FinaPrjFee;
import com.kayak.fina.param.model.FinaPrjFeeList;
import org.springframework.stereotype.Repository;

@Repository
public class FinaPrjFeeListDao extends ComnDao {

    private String property() {
        return "SELECT fee_code,dimension1_min,dimension1_max,dimension2_min,dimension2_max,dimension3_min,dimension3_max,rate*100 AS rate,max_fee,min_fee,constant_fee,crt_time,crt_user,upd_time,upd_user,remark";
    }

    public SqlResult<FinaPrjFeeList> findFinaPrjFeeLists(SqlParam<FinaPrjFeeList> params) throws Exception {
        String sql = property() + " FROM fina_prj_fee_list";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public int addFinaPrjFeeList(SqlParam<FinaPrjFeeList> params) throws Exception {
        String sql = "INSERT INTO fina_prj_fee_list(fee_code,dimension1_min,dimension1_max,dimension2_min,dimension2_max,dimension3_min,dimension3_max,rate,max_fee,min_fee,constant_fee,crt_time,crt_user,upd_time,upd_user,remark) VALUES($S{feeCode},$S{dimension1Min},$S{dimension1Max},$S{dimension2Min},$S{dimension2Max},$S{dimension3Min},$S{dimension3Max},$D{rate}*0.01,$D{maxFee},$D{minFee},$D{constantFee},current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark})";
        return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public int updateFinaPrjFeeList(SqlParam<FinaPrjFeeList> params) throws Exception {
        String sqlAll = "UPDATE fina_prj_fee_list SET fee_code=$S{feeCode} ,dimension1_min=$S{dimension1Min} ,dimension1_max=$S{dimension1Max} ,dimension2_min=$S{dimension2Min} ,dimension2_max=$S{dimension2Max} ,dimension3_min=$S{dimension3Min} ,dimension3_max=$S{dimension3Max} ,rate=$D{rate}*0.01 ,max_fee=$D{maxFee} ,min_fee=$D{minFee} ,constant_fee=$D{constantFee} ,current_timestamp ,upd_user=$S{updUser} ,remark=$S{remark}  WHERE ";
        String sqlDb2 = "UPDATE fina_prj_fee_list SET fee_code=$S{feeCode} ,dimension1_min=$S{dimension1Min} ,dimension1_max=$S{dimension1Max} ,dimension2_min=$S{dimension2Min} ,dimension2_max=$S{dimension2Max} ,dimension3_min=$S{dimension3Min} ,dimension3_max=$S{dimension3Max} ,rate=$D{rate}*0.01 ,max_fee=$D{maxFee} ,min_fee=$D{minFee} ,constant_fee=$D{constantFee} ,current timestamp ,upd_user=$S{updUser} ,remark=$S{remark}   WHERE ";
        Sql    sql    = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2).mysqlSql(sqlAll);

        return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public int deleteFinaPrjFeeList(SqlParam<FinaPrjFeeList> params) throws Exception {
        return super.update("DELETE FROM fina_prj_fee_list WHERE  fee_code = $S{feeCode} ", SubDatabase.DATABASE_SYS_CENTER,
                params.getModel()).getEffect();
    }

    //清除所有冗余数据
    public int deleteAllNoExits() throws Exception {
        return super.update("delete FROM fina_prj_fee_list l WHERE  not exists " +
                "( select t.fee_code from  fina_prj_fee t  where t.fee_code = l.fee_code   )", SubDatabase.DATABASE_SYS_CENTER, null).getEffect();
    }

    public int addHistNoExits(String openFlag) throws Exception {
        String sqlAll = "INSERT INTO fina_prj_fee_list_HIS " +
                "                (FEE_CODE, DIMENSION1_MIN, DIMENSION1_MAX, DIMENSION2_MIN, DIMENSION2_MAX, DIMENSION3_MIN, " +
                "                DIMENSION3_MAX, RATE, MAX_FEE, MIN_FEE, CONSTANT_FEE, CRT_TIME, CRT_USER, UPD_TIME, " +
                "                UPD_USER, REMARK,  OPER_USER, OPER_DATE, OPER_FLAG) " +
                "            select l.FEE_CODE, l.DIMENSION1_MIN, l.DIMENSION1_MAX, l.DIMENSION2_MIN, l.DIMENSION2_MAX, l.DIMENSION3_MIN, " +
                "                l.DIMENSION3_MAX, l.RATE, l.MAX_FEE, l.MIN_FEE, l.CONSTANT_FEE, l.CRT_TIME, l.CRT_USER, l.UPD_TIME, " +
                "                l.UPD_USER, l.REMARK,   $S{updUser}, current_timestamp,'" + openFlag + "' " +
                "            from fina_prj_fee_list l " +
                "            where not exists ( select t.fee_code from  fina_prj_fee t  where t.fee_code = l.fee_code   ) ";
        String sqlDb2 = "INSERT INTO fina_prj_fee_list_HIS " +
                "                (FEE_CODE, DIMENSION1_MIN, DIMENSION1_MAX, DIMENSION2_MIN, DIMENSION2_MAX, DIMENSION3_MIN, " +
                "                DIMENSION3_MAX, RATE, MAX_FEE, MIN_FEE, CONSTANT_FEE, CRT_TIME, CRT_USER, UPD_TIME, " +
                "                UPD_USER, REMARK,  OPER_USER, OPER_DATE, OPER_FLAG) " +
                "            select l.FEE_CODE, l.DIMENSION1_MIN, l.DIMENSION1_MAX, l.DIMENSION2_MIN, l.DIMENSION2_MAX, l.DIMENSION3_MIN, " +
                "                l.DIMENSION3_MAX, l.RATE, l.MAX_FEE, l.MIN_FEE, l.CONSTANT_FEE, l.CRT_TIME, l.CRT_USER, l.UPD_TIME, " +
                "                l.UPD_USER, l.REMARK,   $S{updUser}, current timestamp,'" + openFlag + "' " +
                "            from fina_prj_fee_list l " +
                "            where not exists ( select t.fee_code from  fina_prj_fee t  where t.fee_code = l.fee_code   ) ";
        Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
        int i   = super.update(sql.getSql(daoService.getDbType(0)), SubDatabase.DATABASE_SYS_CENTER, null).getEffect();
        return i;
    }


    public int addHist(SqlParam<FinaPrjFee> params, String openFlag) throws Exception {
        String sqlAll = "INSERT INTO fina_prj_fee_list_HIS " +
                "                (FEE_CODE, DIMENSION1_MIN, DIMENSION1_MAX, DIMENSION2_MIN, DIMENSION2_MAX, DIMENSION3_MIN, " +
                "                DIMENSION3_MAX, RATE, MAX_FEE, MIN_FEE, CONSTANT_FEE, CRT_TIME, CRT_USER, UPD_TIME, " +
                "                UPD_USER, REMARK,  OPER_USER, OPER_DATE, OPER_FLAG) " +
                "            select FEE_CODE, DIMENSION1_MIN, DIMENSION1_MAX, DIMENSION2_MIN, DIMENSION2_MAX, DIMENSION3_MIN, " +
                "                DIMENSION3_MAX, RATE, MAX_FEE, MIN_FEE, CONSTANT_FEE, CRT_TIME, CRT_USER, UPD_TIME, " +
                "                UPD_USER, REMARK,   $S{updUser}, current_timestamp,'" + openFlag + "' " +
                "            from fina_prj_fee_list " +
                "            where fee_code = $S{feeCode} ";
        String sqlDb2 = "INSERT INTO fina_prj_fee_list_HIS " +
                "                (FEE_CODE, DIMENSION1_MIN, DIMENSION1_MAX, DIMENSION2_MIN, DIMENSION2_MAX, DIMENSION3_MIN, " +
                "                DIMENSION3_MAX, RATE, MAX_FEE, MIN_FEE, CONSTANT_FEE, CRT_TIME, CRT_USER, UPD_TIME, " +
                "                UPD_USER, REMARK,  OPER_USER, OPER_DATE, OPER_FLAG) " +
                "            select FEE_CODE, DIMENSION1_MIN, DIMENSION1_MAX, DIMENSION2_MIN, DIMENSION2_MAX, DIMENSION3_MIN, " +
                "                DIMENSION3_MAX, RATE, MAX_FEE, MIN_FEE, CONSTANT_FEE, CRT_TIME, CRT_USER, UPD_TIME, " +
                "                UPD_USER, REMARK,   $S{updUser}, current timestamp,'" + openFlag + "' " +
                "            from fina_prj_fee_list " +
                "            where fee_code = $S{feeCode} ";
        Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
        int i   = super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
        return i;
    }

    public SqlResult<FinaPrjFeeList> findHistory(SqlParam<FinaPrjFeeList> params) throws Exception {
        return findRows(property() + " FROM fina_prj_fee_list_HIS ORDER BY TO_NUMBER(dimension1_min), TO_NUMBER(dimension2_min), TO_NUMBER(dimension3_min)", SubDatabase.DATABASE_SYS_CENTER, params);
    }
}
