package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.FinaTaInterfaceRelationDao;
import com.kayak.fina.param.model.FinaTaInterfaceRelation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "TA接口关联关系维护", model = FinaTaInterfaceRelation.class)
public class FinaTaInterfaceRelationService {

    @Autowired
    private FinaTaInterfaceRelationDao finaTaInterfaceRelationDao;

    @API(desc = "查询TA", auth = APIAuth.NO)
    public SqlResult<FinaTaInterfaceRelation> findTa(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        return finaTaInterfaceRelationDao.findTa(params);
    }

    @API(desc = "查询未绑定接口的TA", auth = APIAuth.NO)
    public SqlResult<FinaTaInterfaceRelation> findNewTa(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        params.setMakeSql(true);

        return finaTaInterfaceRelationDao.findNewTa(params);
    }

    @API(desc = "查询接口版本号", auth = APIAuth.NO)
    public SqlResult<FinaTaInterfaceRelation> findTaInterfaceVersion(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        params.setMakeSql(true);

        return finaTaInterfaceRelationDao.findTaInterfaceVersion(params);
    }

    @API(desc = "查询TA接口关联关系维护", auth = APIAuth.NO)
    public SqlResult<FinaTaInterfaceRelation> findTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        params.setMakeSql(true);

        return finaTaInterfaceRelationDao.findTaInterfaceRelation(params);
    }

    @API(desc = "添加TA接口关联关系维护", auth = APIAuth.NO)
    public String addTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        //判断TA接口是否存在
        SqlResult<FinaTaInterfaceRelation> onlyTano = finaTaInterfaceRelationDao.onlyTano(params);

        if (onlyTano.getRows().size() > 0){
            throw new PromptException("此TA接口关联关系已存在,请更换一个TA！");
        }

        //判断TA接口状态是否有效
        SqlResult<FinaTaInterfaceRelation> findInterfaceDataStatus = finaTaInterfaceRelationDao.findInterfaceDataStatus(params);

        if (findInterfaceDataStatus.getRows().size() > 0){

            //传值D 数据状态为无效
            params.getModel().setDataStatus("D");

            finaTaInterfaceRelationDao.addTaInterfaceRelation(params);

            return RequestSupport.updateReturnJson(true, "添加成功,此TA接口无效已停用接口状态！", null).toString();
        } else {

            //传值E 数据状态为有效
            params.getModel().setDataStatus("E");

            finaTaInterfaceRelationDao.addTaInterfaceRelation(params);

            return RequestSupport.updateReturnJson(true, "添加成功", null).toString();
        }

    }

    @API(desc = "修改TA接口关联关系维护", auth = APIAuth.NO)
    public String updateTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        finaTaInterfaceRelationDao.updateTaInterfaceRelation(params);

        //判断TA接口状态是否有效
        SqlResult<FinaTaInterfaceRelation> findInterfaceDataStatus = finaTaInterfaceRelationDao.findInterfaceDataStatus(params);

        if (findInterfaceDataStatus.getRows().size() > 0){

            finaTaInterfaceRelationDao.stopTaInterfaceRelation(params);

            return RequestSupport.updateReturnJson(true, "修改成功,此TA接口无效已停用接口状态！", null).toString();
        }

        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    @API(desc = "停用TA接口关联关系维护", auth = APIAuth.NO)
    public String stopTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        finaTaInterfaceRelationDao.stopTaInterfaceRelation(params);

        return RequestSupport.updateReturnJson(true, "TA接口关联关系已停用", null).toString();
    }

    @API(desc = "启用TA接口关联关系维护", auth = APIAuth.NO)
    public String startTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception{

        //判断TA接口状态是否有效
        SqlResult<FinaTaInterfaceRelation> findInterfaceDataStatus = finaTaInterfaceRelationDao.findInterfaceDataStatus(params);

        if (findInterfaceDataStatus.getRows().size() > 0){
            throw new PromptException("此TA接口状态无效,无法启用接口！");
        }

        finaTaInterfaceRelationDao.startTaInterfaceRelation(params);

        return RequestSupport.updateReturnJson(true, "TA接口关联关系已启用", null).toString();
    }

}