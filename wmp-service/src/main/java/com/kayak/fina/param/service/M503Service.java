package com.kayak.fina.param.service;

import com.kayak.common.constants.SystemNo;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M503;
import com.kayak.fina.param.model.M504;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M503Dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "产品日历服务", model = M503.class)
public class M503Service {

	@Autowired
	private M503Dao m503Dao;

	@Autowired
	private M215Service m215Service;

	@API(desc = "查询产品日历信息", auth = APIAuth.YES)
	public SqlResult<M503> findFinaProdCalendars(SqlParam<M503> params) throws Exception {
		params.setMakeSql(true);
		if (params.getModel().getProdCode().equals("")){
			params.getModel().setProdCode(null);
		}
		SqlResult<M503> sqlResult = m503Dao.findFinaProdCalendars(params);
//		if(sqlResult != null && sqlResult.getRows().size() > 0){
//			Map<String, Object> prodParam = new HashMap<>();
//			prodParam.put("systemNo", SystemNo.FINA);
//			prodParam.put("legalCode", "1000");
//			for (M503 m503 : sqlResult.getRows()){
//				prodParam.put("prodCode", m503.getProdCode());
//				prodParam.put("supplyCode", m503.getTano());
//				List<Map<String, String>> prodInfoList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
//				if (prodInfoList != null && prodInfoList.size() > 0){
//					Map<String, String> map = prodInfoList.get(0);
//					m503.setProdType(map.get("prod_type") == null ? "":map.get("prod_type"));
//				}
//			}
//		}
		return sqlResult;
	}

	@API(desc = "添加产品日历", params = "tano,prod_code,legal_code,sys_date,reserve_date_flag,reserve_invalid_da,order_date_flag,subs_date_flag,establish_date_flag,value_date_flag,apply_date_flag,apply_ack_date_flag,redeem_date_flag,redeem_ack_date_flag,registered_date_flag,convert_date_flag,winding_date_flag,pay_date_flag", auth = APIAuth.NO)
	public int addFinaProdCalendar(SqlParam<M503> params) throws Exception {
		return m503Dao.addFinaProdCalendar(params).getEffect();
	}
	
	@API(desc = "修改产品日历", params = "tano,prod_code,legal_code,sys_date,reserve_date_flag,reserve_invalid_da,order_date_flag,subs_date_flag,establish_date_flag,value_date_flag,apply_date_flag,apply_ack_date_flag,redeem_date_flag,redeem_ack_date_flag,registered_date_flag,convert_date_flag,winding_date_flag,pay_date_flag", auth = APIAuth.NO)
	public int updateFinaProdCalendar(SqlParam<M503> params) throws Exception {
		return m503Dao.updateFinaProdCalendar(params).getEffect();
	}
	
	@API(desc = "删除产品日历", params = "tano,prod_code,legal_code,sys_date,reserve_date_flag,reserve_invalid_da,order_date_flag,subs_date_flag,establish_date_flag,value_date_flag,apply_date_flag,apply_ack_date_flag,redeem_date_flag,redeem_ack_date_flag,registered_date_flag,convert_date_flag,winding_date_flag,pay_date_flag", auth = APIAuth.NO)
	public int deleteFinaProdCalendar(SqlParam<M503> params) throws Exception {
		return m503Dao.deleteFinaProdCalendar(params).getEffect();
	}

	@API(desc = "查询指定产品日历信息", auth = APIAuth.YES)
	public SqlResult<M503> findProdCalendars(SqlParam<M503> params) throws Exception {
		return m503Dao.findProdCalendars(params);
	}

}
