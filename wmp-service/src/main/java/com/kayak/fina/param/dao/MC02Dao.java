package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.MC02;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class MC02Dao extends ComnDao {

    public SqlResult<MC02> findMC02s(SqlParam<MC02> params) throws Exception {
        return super.findRows("SELECT * FROM TRFR_PROD_INFO ",
                SubDatabase.DATABASE_FINA_CENTER,params);
    }
    
    //添加
    public UpdateResult addMC02(SqlParam<MC02> params) throws Exception {
        String sql = "INSERT INTO TRFR_PROD_INFO\n" +
                "(system_no,tano,prod_code,legal_code,entrust_type,\n" +
                "trfr_prod_status,transfer_mode,part_buy_flag,min_transfer_unit,\n" +
                "min_transfer_vol,max_transfer_vol,min_buy_vol,break_limit_flag,\n" +
                "max_entrust_days,price_up_limit,price_down_limit,trfr_fee_obj,\n" +
                "trfr_fee_mode,fee_amt,max_fee,min_fee,\n" +
                "fee_precision_mode,amt_precision_mode,transfer_acct,transfer_fee_acct,\n" +
                "transfer_nav,buy_rate,sale_rate)\n" +
                "VALUES ($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{entrustType},\n" +
                "$S{trfrProdStatus},$S{transferMode},$S{partBuyFlag},$S{minTransferUnit},\n" +
                "$S{minTransferVol},$S{maxTransferVol},$S{minBuyVol},$S{breakLimitFlag},\n" +
                "$S{maxEntrustDays},$S{priceUpLimit},$S{priceDownLimit},$S{trfrFeeObj},\n" +
                "$S{trfrFeeMode},$S{feeAmt},$S{maxFee},$S{minFee},\n" +
                "$S{feePrecisionMode},$S{amtPrecisionMode},$S{transferAcct},$S{transferFeeAcct},\n" +
                "$S{transferNav},$S{buyRate},$S{saleRate})";
        return super.update(sql,SubDatabase.DATABASE_FINA_CENTER,params.getModel());
    }
    //修改
    public UpdateResult updateMC02(SqlParam<MC02> params) throws Exception {
        MC02 mc02 = params.getModel();
        String sql = "UPDATE TRFR_PROD_INFO SET\n" +
                "entrust_type=$S{entrustType},trfr_prod_status=$S{trfrProdStatus},transfer_mode=$S{transferMode},\n" +
                "part_buy_flag=$S{partBuyFlag},min_transfer_unit=$S{minTransferUnit},min_transfer_vol=$S{minTransferVol},\n" +
                "max_transfer_vol=$S{maxTransferVol},min_buy_vol=$S{minBuyVol},break_limit_flag=$S{breakLimitFlag},\n" +
                "max_entrust_days=$S{maxEntrustDays},price_up_limit=$S{priceUpLimit},price_down_limit=$S{priceDownLimit},\n" +
                "trfr_fee_obj=$S{trfrFeeObj},trfr_fee_mode=$S{trfrFeeMode},fee_amt=$S{feeAmt},\n" +
                "max_fee=$S{maxFee},min_fee=$S{minFee},fee_precision_mode=$S{feePrecisionMode},\n" +
                "amt_precision_mode=$S{amtPrecisionMode},transfer_acct=$S{transferAcct},transfer_fee_acct=$S{transferFeeAcct},\n" +
                "transfer_nav=$S{transferNav},buy_rate=$S{buyRate},sale_rate=$S{saleRate}\n" +
                "WHERE system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and legal_code=$S{legalCode} and entrust_type=$S{entrustType}";
        UpdateResult updateResult = super.update(sql,SubDatabase.DATABASE_FINA_CENTER,mc02);
        return updateResult;
    }

    //删除
    public UpdateResult deleteMC02(SqlParam<MC02> params) throws Exception {
        String sql = "DELETE FROM TRFR_PROD_INFO \n" +
                "WHERE system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and legal_code=$S{legalCode} and entrust_type=$S{entrustType}";
        return super.update(sql,SubDatabase.DATABASE_FINA_CENTER,params.getModel());
    }

    /**
     * 查询某个系统参数
     *
     * @param params
     * @return
     * @throws Exception
     */
    public MC02 findOne(SqlParam<MC02> params) throws Exception {
        // oracle 不支持limit 1
        String sql = "SELECT * FROM TRFR_PROD_INFO ";
        SqlResult<MC02> row = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            return row.getRows().get(0);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Description 获取挂单流水数据数量
     * @Date 2022/1/14
     * @Param [params]
     * @return int
     **/
    public int findEntrust(SqlParam<MC02> params) throws Exception {

        StringBuffer sb=new StringBuffer();
        sb.append(" SELECT count(0) as num FROM TRFR_CUST_ENTRUST_LOG where 1=1 ");
        if(StringUtils.isNotBlank(params.getModel().getSystemNo())){
            sb.append(" and system_no ='"+params.getModel().getSystemNo()+"'");
        }
        if(StringUtils.isNotBlank(params.getModel().getTano())){
            sb.append(" and tano ='"+params.getModel().getTano()+"'");
        }
        if(StringUtils.isNotBlank(params.getModel().getProdCode())){
            sb.append(" and prod_code ='"+params.getModel().getProdCode()+"'");
        }
        if(StringUtils.isNotBlank(params.getModel().getLegalCode())){
            sb.append(" and legal_code ='"+params.getModel().getLegalCode()+"'");
        }
        if(StringUtils.isNotBlank(params.getModel().getEntrustType())){
            sb.append(" and entrust_type ='"+params.getModel().getEntrustType()+"'");
        }
        int num=super.findRow(sb.toString()
                ,SubDatabase.DATABASE_FINA_CENTER, params).getInteger("num");

        return num;
    }

    /**
     * <AUTHOR>
     * @Description 获取买入流水数据
     * @Date 2022/1/14
     * @Param [params]
     * @return int
     **/
    public int findTrans(SqlParam<MC02> params) throws Exception {

        StringBuffer sb=new StringBuffer();
        sb.append(" SELECT count(0) as num FROM TRFR_CUST_TRANS_LOG a left join TRFR_CUST_ENTRUST_LOG b on a.entrust_serno = b.entrust_serno WHERE 1=1 ");
        if(StringUtils.isNotBlank(params.getModel().getSystemNo())){
            sb.append(" and a.system_no ='"+params.getModel().getSystemNo()+"'");
        }
        if(StringUtils.isNotBlank(params.getModel().getTano())){
            sb.append(" and a.tano ='"+params.getModel().getTano()+"'");
        }
        if(StringUtils.isNotBlank(params.getModel().getProdCode())){
            sb.append(" and a.prod_code ='"+params.getModel().getProdCode()+"'");
        }
        if(StringUtils.isNotBlank(params.getModel().getLegalCode())){
            sb.append(" and a.legal_code ='"+params.getModel().getLegalCode()+"'");
        }
        int num=super.findRow(sb.toString()
                ,SubDatabase.DATABASE_FINA_CENTER, params).getInteger("num");
        return num;
    }


}


