package com.kayak.fina.param.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.FinaDiscountDetail;
import com.kayak.fina.param.model.M502;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

@Repository
public class FinaDiscountDetailDao extends ComnDao {

	public SqlResult<FinaDiscountDetail> findFinaDiscountDetails(SqlParam<FinaDiscountDetail> params) throws Exception {
		return super.findRows("SELECT discount_code,dim1_min,dim1_max,dim2_min,dim2_max,dim3_min,dim3_max,cast(discount_rate*100 as decimal(5, 2)) as discount_rate FROM fina_discount_detail where discount_code=$S{discountCode} order by dim1_min ,dim2_min ,dim3_min ", SubDatabase.DATABASE_FINA_CENTER, params);
	}

	public UpdateResult addFinaDiscountDetail(SqlParam<FinaDiscountDetail> params) throws Exception {
		return super.update("INSERT INTO fina_discount_detail(discount_code,dim1_min,dim1_max,dim2_min,dim2_max,dim3_min,dim3_max,discount_rate) VALUES($S{discountCode},$S{dim1Min},$S{dim1Max},$S{dim2Min},$S{dim2Max},$S{dim3Min},$S{dim3Max},$D{discountRate}*0.01)",
				SubDatabase.DATABASE_FINA_CENTER, params.getModel());
	}
	
	public UpdateResult deleteFinaDiscountDetail(SqlParam<M502> params) throws Exception {
		return super.update("DELETE FROM fina_discount_detail WHERE  discount_code=$S{discountCode} ",
				SubDatabase.DATABASE_FINA_CENTER, params.getModel());
	}

}
