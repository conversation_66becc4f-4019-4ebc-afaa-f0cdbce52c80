package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.M508Dao;
import com.kayak.fina.param.model.M508;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "产品费率表服务", model = M508.class)
public class M508Service {

	@Autowired
	private M508Dao m508Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M215Service m215Service;


	@API(desc = "查询产品费率信息", auth = APIAuth.YES)
	public SqlResult<M508> findFinaProdRateFee(SqlParam<M508> params) throws Exception {
		SqlResult<M508> sqlResult  = m508Dao.findFinaProdRateFee(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
				if(Tools.isNotBlank(item.getProdCode())){
					Map<String, Object> prodParam = new HashMap<>();
					prodParam.put("systemNo", SystemNo.FINA);
					prodParam.put("prodCode", item.getProdCode());
					prodParam.put("supplyCode", item.getTano());
					prodParam.put("legalCode", "1000");
					List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
					if (prodParaList != null && prodParaList.size() > 0){
						Map<String, String> prodPara = prodParaList.get(0);
						item.setProdName(prodPara.get("prod_name"));
					}
				}
			} catch (Exception e) {
				throw new RuntimeException("M507错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		return sqlResult;
	}



}
