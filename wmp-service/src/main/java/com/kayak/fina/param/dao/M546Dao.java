package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M546;
import org.springframework.stereotype.Repository;

@Repository
public class M546Dao extends ComnDao {

    public SqlResult<M546> findM546s(SqlParam<M546> param) throws  Exception{
        param.setMakeSql(true);
        String sql = " select a.tano,\n" +
                "       a.prod_code,\n" +
                "       a.ack_date,\n" +
                "       a.cust_type,\n" +
                "       a.LEGAL_CODE,\n" +
                "       sum(case\n" +
                "             when a.busi_code = '125' and a.trans_status = '0' then\n" +
                "              a.app_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as pre_app_vol,\n" +
                "       sum(case\n" +
                "             when b.busi_code = '125' and b.trans_status = '3' then\n" +
                "              b.ack_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as pre_ack_vol,\n" +
                "       sum(case\n" +
                "             when a.busi_code = '098' and a.trans_code = 'T517' then\n" +
                "              a.app_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as rea_app_vol,\n" +
                "       sum(case\n" +
                "             when b.busi_code = '198' and a.trans_code = 'T517' and\n" +
                "                  b.trans_status = '3' then\n" +
                "              b.ack_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as rea_ack_vol,\n" +
                "           t.ta_name\n" +
                "  from FINA_CUST_TRANS_REQ_LOG a\n" +
                "  left join FINA_CUST_TRANS_CFM_LOG b on a.app_serno = b.app_serno\n" +
                "  left join fina_ta_info t on a.tano=t.tano\n" +
                " group by a.tano, a.prod_code, a.ack_date, a.cust_type, a.LEGAL_CODE,t.ta_name order by a.ack_date desc \n ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,param);
    }
}
