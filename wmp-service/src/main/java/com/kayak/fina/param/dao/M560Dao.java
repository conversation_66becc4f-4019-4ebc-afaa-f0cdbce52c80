package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M560;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class M560Dao extends ComnDao {

    public SqlResult<M560> findM560(SqlParam<M560> params) throws Exception {
        String custNo = params.getModel().getCustNo();
        params.getModel().setCustNo(null);
        String sql = "select t1.RULE_NO,t1.RULE_DESC,t2.cust_no,t2.trans_orgno" +
                "    from FINA_OLDCUST_RULE t1 left join FINA_OLDCUST_INFO t2" +
                "    on t1.RULE_NO = t2.RULE_NO where 1 = 1 ";
        if(Tools.isNotBlank(custNo)){
            sql = sql +" and t2.cust_no in ('"+custNo+"')";

        }
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
    }
    public M560 getCusts(String no)  throws Exception {
        String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
                " where cust_no = '"+no+"'";
        return super.findRow(M560.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }

    /**
     * <AUTHOR>
     * @Description 老客信息表，交易机构改为合并机构
     * @Date 2022/3/24
     * @Param [params]
     * @return com.kayak.core.sql.UpdateResult
     **/
    public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
        return super.update("UPDATE FINA_OLDCUST_INFO SET TRANS_ORGNO = $S{mergeOrgno} WHERE TRANS_ORGNO = $S{removeOrgno} ",
                SubDatabase.DATABASE_FINA_CENTER,params);
    }

}

