package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M563;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M563Dao extends ComnDao {

    public SqlResult<M563> findM563(SqlParam<M563> param) throws Exception{
        String prodCode = param.getModel().getProdCode();
        StringBuilder sql = new StringBuilder("select system_no, tano, legal_code, prod_code, quota_busi_type, redeem_vol, redeem_total_vol, create_time, update_time from fina_redeem_quota where 1=1 ");
        if(StringUtils.isNotBlank(prodCode)){
            sql.append(" and prod_code = '").append(prodCode).append("'");
        }
        return super.findRows(sql.toString(), SubDatabase.DATABASE_FINA_CENTER, param);
    }

}
