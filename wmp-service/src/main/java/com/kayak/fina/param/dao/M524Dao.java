package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M524;
import org.springframework.stereotype.Repository;


@Repository
public class M524Dao extends ComnDao {

	public SqlResult<M524> findFinaCustTransCfmLogH(SqlParam<M524> params) throws Exception {

		params.setMakeSql(true);
		String sql = "SELECT t1.app_serno,\n" +
				"       t1.ta_name,\n" +
				"       t1.busi_code,\n" +
				"       t1.mactime,\n" +
				"       t1.app_amt,\n" +
				"       t1.app_vol,\n" +
				"       t1.ack_amt,\n" +
				"       t1.ack_vol,\n" +
				"       t1.busi_date,\n" +
				"       t1.ack_date,\n" +
				"       t1.cust_manager,\n" +
				"       t1.cust_no,\n" +
				"       t1.trans_acct_no,\n" +
				"       t1.ta_acct_no,\n" +
				"       t1.acct_no,\n" +
				"       t1.target_acct_no,\n" +
				"       t1.cust_name,\n" +
				"       t1.cust_type,\n" +
				"       t1.cust_level,\n" +
				"       t1.prod_code,\n" +
				"       t1.nav,\n" +
				"       t1.nav_date,\n" +
				"       t1.cur,\n" +
				"       t1.frozen_reason,\n" +
				"       t1.frozen_end_date,\n" +
				"       t1.capital_status,\n" +
				"       t1.trans_status,\n" +
				"       t1.legal_code,\n" +
				"       t1.tano,\n" +
				"       t1.ta_ack_serno,\n" +
				"       t1.ta_deal_status,\n" +
				"       t1.ta_rtn_code,\n" +
				"       t1.ta_rtn_desc,\n" +
				"       t1.ta_busi_date,\n" +
				"       t1.trans_fee,\n" +
				"       t1.trans_fee_rate,\n" +
				"       t1.agency_fee,\n" +
				"       t1.total_trans_fee,\n" +
				"       t1.discount_rate,\n" +
				"       t1.manager_real_ratio,\n" +
				"       t1.stamp_tax,\n" +
				"       t1.interest_amt,\n" +
				"       t1.div_ratio,\n" +
				"       t1.interest_vol,\n" +
				"       t1.interest_tax,\n" +
				"       t1.taxes,\n" +
				"       t1.min_fee,\n" +
				"       t1.transfer_fee,\n" +
				"       t1.ta_flag,\n" +
				"       t1.force_redeem_reason,\n" +
				"       t1.force_redeem_type,\n" +
				"       t1.sale_percent,\n" +
				"       t1.merit_pay,\n" +
				"       t1.merit_compen,\n" +
				"       t1.breach_fee,\n" +
				"       t1.breach_fee_back_to_prod,\n" +
				"       t1.punish_fee,\n" +
				"       t1.float_fee,\n" +
				"       t1.target_trans_acct_no,\n" +
				"       t1.target_ta_acct_no,\n" +
				"       t1.charge_type,\n" +
				"       t1.region_code,\n" +
				"       t1.target_region_code,\n" +
				"       t1.target_sub_branch_code,\n" +
				"       t1.target_distributor_code,\n" +
				"       t1.def_div_method,\n" +
				"       t1.unit_div_amt,\n" +
				"       t1.div_unit,\n" +
				"       t1.div_vol,\n" +
				"       t1.div_amt,\n" +
				"       t1.div_date,\n" +
				"       t1.xr_date,\n" +
				"       t1.regist_date,\n" +
				"       t1.div_type,\n" +
				"       t1.div_frozen_vol,\n" +
				"       t1.benchmarks,\n" +
				"       t1.frozen_no,\n" +
				"       t1.cust_risk_level,\n" +
				"       t1.prod_risk_level,\n" +
				"       t1.contract_no,\n" +
				"       t1.elisor_name,\n" +
				"       t1.remark,\n" +
				"       t1.update_time,\n" +
				"       t1.target_prod_code,\n" +
				"       t1.target_nav,\n" +
				"       t1.target_vol\n" +
				"        FROM fina_cust_trans_cfm_log_h t1 \n" +
				"        WHERE 1=1 ";
		return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
	}



}
