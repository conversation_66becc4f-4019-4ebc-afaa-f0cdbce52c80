package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.ProdAcctInfoDao;
import com.kayak.fina.param.model.ProdAcctInfo;
import com.kayak.prod.model.M215;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "产品账户信息表服务", model = M215.class)
public class ProdAcctInfoService {

    @Autowired
    private ProdAcctInfoDao prodAcctInfoDao;

    /**
     * 以下是产品账号相关
     **/
    @API(desc = "查询账号类型、银行账号编号、银行账号名称", auth = APIAuth.YES)
    public SqlResult<ProdAcctInfo> findProdAcctInfos(SqlParam<ProdAcctInfo> params) throws Exception {
        return prodAcctInfoDao.queryProdAcctInfo(params);
    }

    @API(desc = "查询银行账号编号和名称 ", auth = APIAuth.YES)
    public SqlResult<ProdAcctInfo> findAcctNoAndName(SqlParam<ProdAcctInfo> params) throws Exception {
        return SqlResult.build(prodAcctInfoDao.queryAcctNoAndName(params));
    }


}
