package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.dao.DaoService;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.M513Dao;
import com.kayak.fina.param.dao.FinaTaPrjFeeDivideDao;
import com.kayak.fina.param.model.M513;

import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 原名 FinaTaInfoService，
 */
@Service
@APIDefine(desc = "TA信息表服务", model = M513.class)
public class M513Service {
	@Autowired
	private M513Dao finaTaInfoDao;
	@Autowired
	private M215Service m215Service;

	@Autowired
	private FinaTaPrjFeeDivideDao finaTaPrjFeeDivideDao;

	@API(desc = "查询TA信息表信息")
	public SqlResult<M513> findFinaTaInfos(SqlParam<M513> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<M513>  allList = finaTaInfoDao.findFinaTaInfos(params);
		return allList;
	}

	@API(desc = "添加TA信息", params = "tano,ta_name,ta_simplify_name,ta_type,status,n_legal_code,n_legal_type,n_legal_id_code,tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_version,is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct,present_confirm_num,crt_time,crt_user,upd_time,upd_user,remark,task_group,file_imp_flag,batch_no,is_holidays_send,fundday_file_path,cfm_file_path,req_file_path,freez_file_type, pgmno")
	public String addTaInfo(SqlParam<M513> params) throws Exception {
		//默认不支持预分配账号
		params.getModel().setIsPredistributionAcct("0");
		int count = finaTaInfoDao.findTaInfoCounts(Tools.makeParams()
				.put("tano",params.getModel().getTano())
				.build());
		if(count>0) {
			throw new PromptException("M51300", "TA代码已存在，或正处审核状态！");
			//return RequestSupport.updateReturnJson(false, "销售机构代码已存在", null).toString();
		}
		finaTaInfoDao.addTaInfo(params);
		return RequestSupport.updateReturnJson(true, "添加成功", null).toString();
	}




	@API(desc = "修改TA信息表", params = "tano,ta_name,ta_simplify_name,ta_type,status,n_legal_code,n_legal_type,n_legal_id_code,tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_version,is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct,present_confirm_num,crt_time,crt_user,upd_time,upd_user,remark,task_group,file_imp_flag,batch_no,is_holidays_send,fundday_file_path,cfm_file_path,req_file_path,freez_file_type, pgmno")
	public String updateTaInfo(SqlParam<M513> params) throws Exception {
		//默认不支持预分配账号
		params.getModel().setIsPredistributionAcct("0");
		finaTaInfoDao.updateTaInfo(params);
		return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
	}

	@API(desc = "删除销售商信息表", params = "tano,ta_name,ta_simplify_name,ta_type,status,n_legal_code,n_legal_type,n_legal_id_code,tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_version,is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct,present_confirm_num,crt_time,crt_user,upd_time,upd_user,remark,task_group,file_imp_flag,batch_no,is_holidays_send,fundday_file_path,cfm_file_path,req_file_path,freez_file_type")
	public int deleteTaDistributorInfo(SqlParam<M513> params) throws Exception {
		return finaTaInfoDao.deleteTaDistributorInfo(params);
	}


	@API(desc = "销售商产品设置", params = "tano,ta_name,ta_simplify_name,ta_type,status,n_legal_code,n_legal_type,n_legal_id_code,tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_version,is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct,present_confirm_num,crt_time,crt_user,upd_time,upd_user,remark,task_group,file_imp_flag,batch_no,is_holidays_send,fundday_file_path,cfm_file_path,req_file_path,freez_file_type")
	public String disprodformyAction(SqlParam<M513> params) throws Exception {


		if (params.getModel().getProdCode() == null && ((String) params.getModel().getProdCode()).trim() == "") {
			throw new PromptException("M51301", "产品代码参数不能为空");

		}
		String[] prodcodearray =params.getModel().getProdCode().toString().split(",");

		List<Map<String,Object>> updateParams = new ArrayList<Map<String,Object>>();
		for (String prod_code : prodcodearray) {
			updateParams.add(Tools.makeParams().put("distributorCode",params.getModel().getTano()).put("prodCode",prod_code).build());
		}

		finaTaInfoDao.disprodformyAction(updateParams);

		return RequestSupport.updateReturnJson(true, "保存成功", null).toString();
	}


	@API(desc = "停用TA设置", params = "tano,ta_name,ta_simplify_name,ta_type,status,n_legal_code,n_legal_type,n_legal_id_code,tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_version,is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct,present_confirm_num,crt_time,crt_user,upd_time,upd_user,remark,task_group,file_imp_flag,batch_no,is_holidays_send,fundday_file_path,cfm_file_path,req_file_path,freez_file_type")
	public String stopTaDistributorInfo(SqlParam<M513> params) throws Exception {
		Map<String,Object> map = new HashMap<>();
		map.put("systemNo","FINA");
		map.put("tano",params.getModel().getTano());
		map.put("prodStatus","1");
		SqlParam<M215> queryParams = new FetcherData<>(map, M215.class);
		List<Map<String, String>> m215List = m215Service.getProdInfoList(queryParams);
		if(m215List != null && m215List.size() > 0){
			String prodName = m215List.get(0).get("prod_name");
			return RequestSupport.updateReturnJson(false, "产品"+prodName+"还未下架，不可停用", null).toString();
		}
		int count = finaTaInfoDao.stopTaDistributorInfoBefor(Tools.makeParams().put("tano",params.getModel().getTano()).build());
		if(count>0) {
			throw new PromptException("M51302", "该TA存在客户份额，停用失败");
			//return RequestSupport.updateReturnJson(false, "销售机构代码已存在", null).toString();
		}
		 finaTaInfoDao.stopTaDistributorInfo(params);

		return RequestSupport.updateReturnJson(true, "停用成功", null).toString();
	}


	@API(desc = "恢复TA设置", params = "tano,ta_name,ta_simplify_name,ta_type,status,n_legal_code,n_legal_type,n_legal_id_code,tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_version,is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct,present_confirm_num,crt_time,crt_user,upd_time,upd_user,remark,task_group,file_imp_flag,batch_no,is_holidays_send,fundday_file_path,cfm_file_path,req_file_path,freez_file_type")
	public String startTaDistributorInfo(SqlParam<M513> params) throws Exception {
		 finaTaInfoDao.startTaDistributorInfo(params);
		return RequestSupport.updateReturnJson(true, "恢复成功", null).toString();
	}
	
	@API(desc="查询历史信息",auth = APIAuth.NO)
	public SqlResult<M513> findHistory(SqlParam<M513> param)throws Exception{
		return finaTaInfoDao.findHistory(param);
	}

	@API(desc="查询停用的ta账号",auth = APIAuth.NO)
	public SqlRow findTaNum(SqlParam<M513> param)throws Exception{
		String tano = param.getModel().getTano();
		return finaTaInfoDao.findTaNum(tano);
	}


}
