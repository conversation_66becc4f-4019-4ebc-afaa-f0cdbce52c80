package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M545Dao;
import com.kayak.fina.param.model.M545;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财产品存量统计表-按产品", model = M545.class)
public class M545Service {

    @Autowired
    private M545Dao m545Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财产品存量统计表-按产品", auth = APIAuth.YES)
    public SqlResult<M545> findM545s(SqlParam<M545> param)throws Exception {
        SqlResult<M545> sqlResult = m545Dao.findM545s(param);
        List<M545> volList = sqlResult.getRows();
        for (M545 m545 : volList){
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m545.getProdCode());
            prodParam.put("supplyCode", m545.getTano());
            prodParam.put("legalCode", "1000");
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                m545.setProdName(prodPara.get("prod_name"));
                m545.setEstablishDate(prodPara.get("establish_date"));
                m545.setIncomeStartDate(prodPara.get("income_start_date"));
                m545.setEndDate(prodPara.get("end_date"));
            }
            M545 raiAmt = m545Dao.coutRaiAmt(m545);
            if (raiAmt != null){
                m545.setRaiAmt(raiAmt.getRaiAmt());
            }
            m545.setTaName(reportformUtil.getFinaTaName(m545.getTano()));
        }
        return sqlResult;
    }
}
