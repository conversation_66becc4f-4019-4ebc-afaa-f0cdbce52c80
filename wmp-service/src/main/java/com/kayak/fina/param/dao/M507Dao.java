package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M507;
import org.springframework.stereotype.Repository;

@Repository
public class M507Dao extends ComnDao {

	/**
	 * 产品分红信息查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M507> findFinaProdDivInfo(SqlParam<M507> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("SELECT div_code,tano,prod_code,legal_code,charge_type,regist_date,div_date,xr_date,div_per_unit,draw_bonus_unit," +
				"xr_value,xr_value_flag,xr_type,specification,whole_flag,modify_way,download_date FROM fina_prod_div_info ", SubDatabase.DATABASE_FINA_CENTER, params);
	}





}
