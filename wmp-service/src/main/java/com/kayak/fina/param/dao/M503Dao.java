package com.kayak.fina.param.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.M503;
import com.kayak.fina.param.model.FinaProdInfo;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;

@Repository
public class M503Dao extends ComnDao {

	public SqlResult<M503> findFinaProdCalendars(SqlParam<M503> params) throws Exception {
		return super.findRows("SELECT t1.tano,\n" +
				"       t1.prod_code,\n" +
				"       t1.legal_code,\n" +
				"       t1.sys_date,\n" +
				"       t1.reserve_date_flag,\n" +
				"       t1.reserve_invalid_da,\n" +
				"       t1.order_date_flag,\n" +
				"       t1.subs_date_flag,\n" +
				"       t1.establish_date_flag,\n" +
				"       t1.value_date_flag,\n" +
				"       t1.apply_date_flag,\n" +
				"       t1.apply_ack_date_flag,\n" +
				"       t1.redeem_date_flag,\n" +
				"       t1.redeem_ack_date_flag,\n" +
				"       t1.registered_date_flag,\n" +
				"       t1.convert_date_flag,\n" +
				"       t1.winding_date_flag,\n" +
				"       t1.pay_date_flag\n" +
				"  FROM fina_prod_calendar t1", SubDatabase.DATABASE_FINA_CENTER, params);
	}

	public UpdateResult addFinaProdCalendar(SqlParam<M503> params) throws Exception {
		return super.update("INSERT INTO fina_prod_calendar(tano,prod_code,legal_code,sys_date,reserve_date_flag,reserve_invalid_da,order_date_flag,subs_date_flag,establish_date_flag,value_date_flag,apply_date_flag,apply_ack_date_flag,redeem_date_flag,redeem_ack_date_flag,registered_date_flag,convert_date_flag,winding_date_flag,pay_date_flag) VALUES($S{tano},$S{prodCode},$S{legalCode},$S{sysDate},$S{reserveDateFlag},$S{reserveInvalidDa},$S{orderDateFlag},$S{subsDateFlag},$S{establishDateFlag},$S{valueDateFlag},$S{applyDateFlag},$S{applyAckDateFlag},$S{redeemDateFlag},$S{redeemAckDateFlag},$S{registeredDateFlag},$S{convertDateFlag},$S{windingDateFlag},$S{payDateFlag})",
				SubDatabase.DATABASE_FINA_CENTER, params.getModel());
	}
	
	public UpdateResult updateFinaProdCalendar(SqlParam<M503> params) throws Exception {
		return super.update("UPDATE fina_prod_calendar SET reserve_date_flag=$S{reserveDateFlag} ,reserve_invalid_da=$S{reserveInvalidDa} ,order_date_flag=$S{orderDateFlag} ,subs_date_flag=$S{subsDateFlag} ,establish_date_flag=$S{establishDateFlag} ,value_date_flag=$S{valueDateFlag} ,apply_date_flag=$S{applyDateFlag} ,apply_ack_date_flag=$S{applyAckDateFlag} ,redeem_date_flag=$S{redeemDateFlag} ,redeem_ack_date_flag=$S{redeemAckDateFlag} ,registered_date_flag=$S{registeredDateFlag} ,convert_date_flag=$S{convertDateFlag} ,winding_date_flag=$S{windingDateFlag} ,pay_date_flag=$S{payDateFlag}  WHERE  tano=$S{tano} AND prod_code=$S{prodCode} AND legal_code=$S{legalCode} AND sys_date=$S{sysDate} ",
				SubDatabase.DATABASE_FINA_CENTER, params.getModel());
	}
	
	public UpdateResult deleteFinaProdCalendar(SqlParam<M503> params) throws Exception {
		return super.update("DELETE FROM fina_prod_calendar WHERE  tano=$S{tano} AND prod_code=$S{prodCode} AND legal_code=$S{legalCode} AND sys_date=$S{sysDate} ",
				SubDatabase.DATABASE_FINA_CENTER, params.getModel());
	}


    public UpdateResult deleteFinaProdAllCalendar(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("DELETE FROM fina_prod_calendar WHERE  tano=$S{tano} AND prod_code=$S{prodCode} AND legal_code=$S{legalCode}",
				SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

	public SqlResult<M503> findProdCalendars(SqlParam<M503> params) throws Exception {
		String sql = "SELECT tano, prod_code, legal_code, system_no, sys_date, reserve_date_flag, reserve_invalid_da, order_date_flag, subs_date_flag, establish_date_flag, value_date_flag, apply_date_flag, apply_ack_date_flag, redeem_date_flag, redeem_ack_date_flag, registered_date_flag, convert_date_flag, winding_date_flag, pay_date_flag" +
				" FROM fina_prod_calendar" +
				" WHERE system_no=$S{systemNo} AND legal_code=$S{legalCode} AND tano=$S{tano} AND prod_code=$S{prodCode}" +
				" ORDER BY sys_date";
		List<M503> rows = super.findRows(M503.class, sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
		return SqlResult.build(rows);
	}
}
