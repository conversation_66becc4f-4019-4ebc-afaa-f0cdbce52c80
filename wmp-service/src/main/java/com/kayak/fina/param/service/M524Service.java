package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M524Dao;
import com.kayak.fina.param.model.M523;
import com.kayak.fina.param.model.M524;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "客户交易确认历史实体类服务", model = M524.class)
public class M524Service {

	@Autowired
	private M524Dao m524Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M215Service m215Service;

	@API(desc = "查询客户交易确认历史信息", auth = APIAuth.YES)
	public SqlResult<M524> findFinaCustTransCfmLogH(SqlParam<M524> params) throws Exception {
		SqlResult<M524> sqlResult = m524Dao.findFinaCustTransCfmLogH(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		//获取产品名称、TA名称
		if(null!=sqlResult&&sqlResult.getRows().size()>0){
			List<M524> sqlList=sqlResult.getRows();
			for(M524 m524:sqlList){
				Map<String, String> prodInfoMap = m215Service.getProdInfo(m524.getLegalCode(), SystemNo.FINA,m524.getTano(),m524.getProdCode());
				if(prodInfoMap != null && prodInfoMap.size() > 0){
					m524.setProdName(prodInfoMap.get("prod_name"));
				}
				/**if(null!=m524){
					m524.setTaName(reportformUtil.getFinaTaName(m524.getTano()));
				}*/
			}
		}
		return sqlResult;
	}


}
