package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.constants.SystemParamConstants;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M509;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class M509Dao extends ComnDao {

    public SqlResult<M509> find(SqlParam<M509> params) throws Exception {
        return super.findRows("SELECT * FROM sys_param ", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    public void update(List<M509> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (M509 p : params) {
                super.update("UPDATE sys_param SET paravalue = $S{paravalue} WHERE paraid = $S{paraid}", SubDatabase.DATABASE_FINA_CENTER, p);
            }
        });
    }

    //gx
    public SqlResult<M509> findSysParams(SqlParam<M509> params) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder("SELECT " +
                " p.moduleid, " +
                " p.paraid, " +
                " p.paravalue, " +
                " p.paraname, " +
                " p.groupparaid, " +
                " p.dict, " +
                " p.functype, " +
                " p.confoption, " +
                " p.isedit, " +
                " p.isdisplay " +
                " FROM SYS_PARAM p " +
                " WHERE isdisplay = '" + SystemParamConstants.SHOW + "'  ");

        M509 model = params.getModel();


        /**if (Tools.isNotBlank(model.getParavalue())) {
         sqlBuilder.append(" AND ( p.dict IS NOT NULL AND p.paravalue LIKE '%$U{paravalue}%') ");
         }*/

        if (Tools.isNotBlank(model.getParaname())) {
            sqlBuilder.append(" AND p.paraname LIKE '%$U{paraname}%' ");
        }

        sqlBuilder.append(" order by PARAID ");

        return super.findRows(sqlBuilder.toString(), SubDatabase.DATABASE_FINA_CENTER, params);
    }

    //添加
    public UpdateResult addSysParam(SqlParam<M509> params) throws Exception {
        return super.update("INSERT INTO SYS_PARAM(moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay) VALUES($S{moduleid},$S{paraid},$S{paravalue},$S{paraname},$S{groupparaid},$S{dict},$S{functype},$S{confoption},$S{isdisplay})",
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    //修改
    public UpdateResult updateSysParam(SqlParam<M509> params) throws Exception {
        return super.update("UPDATE SYS_PARAM SET paravalue=$S{paravalue} ,paraname=$S{paraname}   WHERE  moduleid=$S{moduleid} and paraid=$S{paraid} ",
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    //删除
    public UpdateResult deleteSysParam(SqlParam<M509> params) throws Exception {
        return super.update("DELETE FROM SYS_PARAM WHERE moduleid=$S{moduleid} and paraid=$S{paraid} ",
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     * 查询某个系统参数
     *
     * @param params
     * @return
     * @throws Exception
     */
    public M509 findOne(SqlParam<M509> params) throws Exception {
        // oracle 不支持limit 1
        String sql = "SELECT paravalue ,paraname ,paraid ,moduleid ,isdisplay ,groupparaid ,graphql ,functype ,fieldtype ,execaction ,dict ,confoption FROM sys_param";
        SqlResult<M509> row = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            return row.getRows().get(0);
        }
        return null;
    }

    /**
     * @return com.kayak.core.sql.SqlResult<com.kayak.fina.param.model.M509>
     * <AUTHOR>
     * @Description 查询理财代销参数系统休市时间
     * @Date 2021/12/20
     * @Param []
     **/
    public SqlResult<M509> findParaValue(SqlParam<M509> params) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder("SELECT " +
                " p.moduleid, " +
                " p.paraid, " +
                " p.paravalue, " +
                " p.paraname, " +
                " p.groupparaid, " +
                " p.dict, " +
                " p.functype, " +
                " p.confoption, " +
                " p.isedit, " +
                " p.isdisplay " +
                " FROM SYS_PARAM p " +
                " WHERE PARANAME='系统休市时间' limit 1  ");

        return super.findRows(sqlBuilder.toString(), SubDatabase.DATABASE_FINA_CENTER, params);
    }

}
