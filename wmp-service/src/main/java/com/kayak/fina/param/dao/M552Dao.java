package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M552;
import org.springframework.stereotype.Repository;

@Repository
public class M552Dao extends ComnDao {

    public SqlResult<M552> findM552s(SqlParam<M552> param) throws  Exception{
        param.setMakeSql(true);
        String sql = "select * from FINA_PROD_MONTH_SUM";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,param);
    }
}
