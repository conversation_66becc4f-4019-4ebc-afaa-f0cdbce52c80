package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M518;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M518Dao extends ComnDao {

    public List<M518> findCusts(SqlParam<M518> param)  throws Exception {
        String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
                " where cust_name = $S{custName} and id_type = $S{idType} and id_code = $S{idCode}";
        return super.findRows(M518.class,sql, SubDatabase.DATABASE_CUST_CENTER,param.getModel());
    }
    public M518 getCusts(String no)  throws Exception {
        String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
                " where cust_no = '"+no+"'";
        return super.findRow(M518.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }
    
    public SqlResult<M518> findFinaCustFreezeDetails(SqlParam<M518> params) throws Exception {
        String custNo = params.getModel().getCustNo();
        params.getModel().setCustNo(null);
        String sql1 = " select * from (SELECT fr.frozen_no,\n" +
                "               fr.frozen_date,\n" +
                "               fr.cust_no,\n" +
                "               fr.prod_code,\n" +
                "               fr.tano,\n" +
                "               fr.frozen_cause,\n" +
                "               fr.contract_no,\n" +
                "               fr.frozen_enddate,\n" +
                "               fr.frozen_desc,\n" +
                "               fr.frozen_vol,\n" +
                "               fr.frozen_serno,\n" +
                "               fr.unfrozen_serno,\n" +
                "               fr.frozen_status,\n" +
                "               fr.unfrozen_date,\n" +
                "               fr.frozen_trans_orgno,\n" +
                "               fd.trans_acct_no,\n" +
                "               t3.cust_name,\n" +
                "               t3.id_type,\n" +
                "               t3.id_code,\n" +
                "               t4.ta_name,\n" +
                "               t3.acct_no\n" +
                "          FROM fina_cust_frozen_record fr\n" +
                "          left join fina_cust_frozen_detail fd on fr.frozen_no =\n" +
                "                                                  fd.frozen_no\n" +
                "          left join fina_cust_ta_acct t3  on fr.TRANS_ACCT_NO = t3.TRANS_ACCT_NO and fr.tano=t3.tano\n" +
                "          left join fina_ta_info t4 on fr.tano = t4.tano\n" +
                "         WHERE 1 = 1 ";
        if(Tools.isNotBlank(custNo)){
            sql1 = sql1 +" and fr.cust_no in ('"+custNo+"')";

        }
        sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(),M518.class);
        sql1 = sql1+" order by fr.frozen_date desc )tt1 ";
        String sql2 = " select * from (SELECT fr.frozen_no,\n" +
                "               fr.frozen_date,\n" +
                "               fr.cust_no,\n" +
                "               fr.prod_code,\n" +
                "               fr.tano,\n" +
                "               fr.frozen_cause,\n" +
                "               fr.contract_no,\n" +
                "               fr.frozen_enddate,\n" +
                "               fr.frozen_desc,\n" +
                "               fr.frozen_vol,\n" +
                "               fr.frozen_serno,\n" +
                "               fr.unfrozen_serno,\n" +
                "               fr.frozen_status,\n" +
                "               fr.unfrozen_date,\n" +
                "               fr.frozen_trans_orgno,\n" +
                "               fd.trans_acct_no,\n" +
                "               t3.cust_name,\n" +
                "               t3.id_type,\n" +
                "               t3.id_code,\n" +
                "               t4.ta_name,\n" +
                "               t3.acct_no\n" +
                "          FROM fina_cust_frozen_record_h fr\n" +
                "          left join fina_cust_frozen_detail_h fd on fr.frozen_no =\n" +
                "                                                    fd.frozen_no\n" +
                "          left join fina_cust_ta_acct t3  on fr.TRANS_ACCT_NO = t3.TRANS_ACCT_NO and fr.tano=t3.tano\n" +
                "          left join fina_ta_info t4 on fr.tano = t4.tano\n" +
                "         WHERE 1 = 1 ";
        if(Tools.isNotBlank(custNo)){
            sql2 = sql2 +" and fr.cust_no in ('"+custNo+"')";
        }

        sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M518.class);
        sql2 = sql2+" order by fr.frozen_date desc )tt2 ";
        String sql =
                "select * from (" + sql1 +
                        "union all \n" +
                sql2 + ")tt3 ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }
}
