package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M554;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M554Dao extends ComnDao {

    public SqlResult<M554> findM554s(SqlParam<M554> param) throws  Exception{

        String sql1 ="select * from (select t.tano,\n" +
                "               t.prod_code,\n" +
                "               t.prod_name,\n" +
                "               t.deduction_date,\n" +
                "               t.trans_orgno,\n" +
                "               t.legal_code,\n" +
                "               (select count(1) from FINA_CUST_TRANS_REQ_LOG ) as tra_num,\n" +
                "               count(DISTINCT t.cust_no) as cust_num,\n" +
                "               sum(t.app_amt) as total_amt,\n" +
                "               t2.ta_name\n" +
                "          from FINA_CUST_TRANS_REQ_LOG t left join fina_ta_info t2 on t.tano=t2.tano\n" +
                "         where busi_code = '020'\n" +
                "         group by t.tano,\n" +
                "                  t.prod_code,\n" +
                "                  t.prod_name,\n" +
                "                  t.deduction_date,\n" +
                "                  t.trans_orgno,\n" +
                "                  t.legal_code,\n" +
                "                  t2.ta_name  order by t.DEDUCTION_DATE desc )tt1 ";
        sql1 = MakeSqlUntil.makeSql(sql1,param.getParams(), M554.class);
        String sql2 ="select * from ( select t.tano,\n" +
                "               t.prod_code,\n" +
                "               t.prod_name,\n" +
                "               t.deduction_date,\n" +
                "               t.trans_orgno,\n" +
                "               t.legal_code,\n" +
                "               (select count(1) from FINA_CUST_TRANS_REQ_LOG_H ) as tra_num,\n" +
                "               count(DISTINCT t.cust_no) as cust_num,\n" +
                "               sum(t.app_amt) as total_amt,\n" +
                "               t2.ta_name\n" +
                "          from FINA_CUST_TRANS_REQ_LOG_H t left join fina_ta_info t2 on t.tano=t2.tano\n" +
                "         where busi_code = '020'\n" +
                "         group by t.tano,\n" +
                "                  t.prod_code,\n" +
                "                  t.prod_name,\n" +
                "                  t.deduction_date,\n" +
                "                  t.trans_orgno,\n" +
                "                  t.legal_code,\n" +
                "                  t2.ta_name  order by t.DEDUCTION_DATE desc )tt2 ";
        sql2 = MakeSqlUntil.makeSql(sql2,param.getParams(),M554.class);
        String sql = " select * from ( " + sql1 +
                "  union all " +
                sql2 + ")tt3";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,param);

    }

    public M554 getPeriodlist(M554 param)throws  Exception {
        String sql = "select periods,begin_date,end_date,income_rate\n"+
                "from fina_prod_periodlist where prod_code = $S{prodCode}";
        return super.findRow(M554.class,sql,SubDatabase.DATABASE_FINA_CENTER,param);
    }
}
