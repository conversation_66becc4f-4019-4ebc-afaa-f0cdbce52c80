package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.FinaPrjFee;

import org.springframework.stereotype.Repository;

import java.util.stream.Collectors;

@Repository
public class FinaPrjFeeDao extends ComnDao {

    private String property() {
        return "SELECT prod_code, fee_code, fee_type, enable_date, charge_type, buyfee_mode, buyfee_method, rate_calculate_method, rate_merge_method, backfee_calculate_method, redemfee_asset_method,  fee_role,data_status,crt_user,crt_time,remark,upd_user,upd_time";
    }

    public SqlResult<FinaPrjFee> findFinaPrjFees(SqlParam<FinaPrjFee> params) throws Exception {
        SqlResult<FinaPrjFee> sqlResult = super.findRows(property() + ",redemfee_asset_rat*100 redemfee_asset_rat  FROM fina_prj_fee", SubDatabase.DATABASE_SYS_CENTER, params);
        return approveParent(sqlResult);
    }

    public SqlResult<FinaPrjFee> select(SqlParam<FinaPrjFee> params) throws Exception {
        return super.findRows(property() + ",redemfee_asset_rat  FROM fina_prj_fee where PROD_CODE = $S{prodCode} and tano = $S{tano} and legal_code=$S{legalCode}", SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public int addFinaPrjFee(SqlParam<FinaPrjFee> params) throws Exception {
        String sql  = "INSERT INTO fina_prj_fee(" +
                "prod_code," +
                "tano," +
                "legal_code," +
                "fee_code," +
                "fee_type," +
                "enable_date," +
                "charge_type," +
                "buyfee_mode," +
                "buyfee_method," +
                "rate_calculate_method," +
                "rate_merge_method," +
                "backfee_calculate_method," +
                "redemfee_asset_method," +
                "redemfee_asset_rat," +
                "fee_role," +
                "crt_time," +
                "crt_user," +
                "upd_time," +
                "upd_user," +
                "remark," +
                "data_status" +
                ") VALUES(" +
                "$S{prodCode}," +
                "$S{tano}," +
                "$S{legalCode}," +
                "$S{feeCode}," +
                "$S{feeType}," +
                "$S{enableDate}," +
                "$S{chargeType}," +
                "$S{buyfeeMode}," +
                "$S{buyfeeMethod}," +
                "$S{rateCalculateMethod}," +
                "$S{rateMergeMethod}," +
                "$S{backfeeCalculateMethod}," +
                "$S{redemfeeAssetMethod}," +
                "$D{redemfeeAssetRat}," +
                "$S{feeRole}," +
                "current_timestamp," +
                "$S{crtUser}," +
                "current_timestamp," +
                "$S{updUser}," +
                "$S{remark}," +
                "$S{dataStatus})";
        return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public int updateFinaPrjFee(SqlParam<FinaPrjFee> params) throws Exception {
        String sql = "UPDATE fina_prj_fee SET " +
                "fee_code=$S{feeCode} ," +
                "fee_type=$S{feeType} ," +
                "enable_date=$S{enableDate} ," +
                "charge_type=$S{chargeType} ," +
                "buyfee_mode=$S{buyfeeMode} ," +
                "buyfee_method=$S{buyfeeMethod} ," +
                "rate_calculate_method=$S{rateCalculateMethod} ," +
                "rate_merge_method=$S{rateMergeMethod} ," +
                "backfee_calculate_method=$S{backfeeCalculateMethod} ," +
                "redemfee_asset_method=$S{redemfeeAssetMethod} ," +
                "redemfee_asset_rat=$D{redemfeeAssetRat} ," +
                "fee_role=$S{feeRole} ," +
                "upd_time=current_timestamp ," +
                "upd_user=$S{updUser} ," +
                "remark=$S{remark} ," +
                "data_status=$S{dataStatus} " +
                " WHERE " +
                "  PROD_CODE = $S{prodCode}  " +
                "  AND tano = $S{tano} " +
                "  AND legal_code = $S{legalCode} " +
                "  AND FEE_TYPE = $S{feeType} " +
                "  AND ENABLE_DATE = $S{enableDate} " ;
        return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public void deleteFinaPrjFee(SqlParam<FinaPrjFee> params) throws Exception {
        doTrans(() -> {
            delete(params);
            deleteList(params);
        });

    }

    public int delete(SqlParam<FinaPrjFee> params) throws Exception {
        return super.update("DELETE FROM fina_prj_fee WHERE " +
                        "				PROD_CODE = $S{prodCode} " +
                        "  AND FEE_CODE = $S{feeCode} " +
                        "  AND FEE_TYPE = $S{feeType} " +
                        "  AND ENABLE_DATE = $S{enableDate} " +
                        "  AND data_status = $S{dataStatus} ", SubDatabase.DATABASE_SYS_CENTER,
                params.getModel()).getEffect();
    }

    public int deleteList(SqlParam<FinaPrjFee> params) throws Exception {
        return super.update("DELETE FROM fina_prj_fee_list WHERE fee_code = $S{feeCode}  ", SubDatabase.DATABASE_SYS_CENTER,
                params.getModel()).getEffect();
    }


    /**
     * 检查方案是否存在
     *
     * @param params
     * @return
     * @throws Exception
     */
    public boolean checkExist(SqlParam<FinaPrjFee> params) throws Exception {
        SqlResult<FinaPrjFee> result = super.findRows("select fee_code from fina_prj_fee where " +
                "      prod_code = $S{prodCode} " +
                "      and tano = $S{tano} " +
                "      and legal_code = $S{legalCode} " +
                "      and fee_type = $S{feeType} " +
                "      and enable_date = $S{enableDate} ", SubDatabase.DATABASE_SYS_CENTER, params);
        if (result.getRows().size() > 0) {
            return true;
        }
        return false;
    }





    private SqlResult<FinaPrjFee> approveParent(SqlResult<FinaPrjFee> sqlResult) {
        sqlResult.setRows(sqlResult.getRows().stream().map(
                key -> {
                    String dataId   = key.getProdCode() + "-" + key.getFeeType() + "-" + key.getEnableDate() + "-" + key.getDataStatus();
                    String parentId = key.getProdCode() + "-" + key.getFeeType() + "-" + key.getEnableDate() + "-" + DataStatus.EFFECTED;
                    if (DataStatus.EFFECTED.equals(key.getDataStatus()) || DataStatus.ADD.equals(key.getDataStatus())) {
                        //修改新增和生效状态，作为父节点
                        parentId = "";
                    }
                    key.setDataParentId(parentId);
                    key.setDataId(dataId);
                    return key;
                }
        ).collect(Collectors.toList()));
        return sqlResult;
    }

}
