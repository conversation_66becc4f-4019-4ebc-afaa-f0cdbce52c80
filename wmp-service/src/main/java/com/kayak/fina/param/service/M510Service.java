package com.kayak.fina.param.service;

import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlRow;
import com.kayak.fina.param.model.M503;
import com.kayak.fina.param.model.ProdAcctInfo;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M204;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M510Dao;
import com.kayak.fina.param.model.M510;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 银行账号维护， 原名 FinaAcctInfoService
 */
@Service
@APIDefine(desc = "银行账号信息实体类服务", model = M510.class)
public class M510Service {

	@Autowired
	private M510Dao m510Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询银行账号信息实体类信息", auth = APIAuth.YES)
	public SqlResult<M510> findFinaAcctInfos(SqlParam<M510> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<M510> sqlResult=m510Dao.findFinaAcctInfos(params);
		//获取TA名称
		if(null!=sqlResult&&sqlResult.getRows().size()>0){
			List<M510> sqlList=sqlResult.getRows();
			for(M510 m510:sqlList){
				if(null!=m510){
					m510.setTaName(reportformUtil.getFinaTaName(m510.getTano()));
				}
			}
		}
		return sqlResult;
	}

	@API(desc = "添加银行账号信息实体类", params = "acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code,inter_code,acct_status,remark", auth = APIAuth.NO)
	public int addFinaAcctInfo(SqlParam<M510> params) throws Exception {
		List<M510> list = m510Dao.listFinaAcctInfosByNo(params.getModel());
		if (list != null && list.size() > 0){
			throw new PromptException("该银行账号已经存在");
		}
		/**List<M510> list1 = m510Dao.listFinaAcctInfosByName(params.getModel());
		if (list1 != null && list1.size() > 0){
			throw new PromptException("该银行账号名称已经存在");
		}*/
		return m510Dao.addFinaAcctInfo(params).getEffect();
	}
	
	@API(desc = "修改银行账号信息实体类", params = "acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code,inter_code,acct_status,remark", auth = APIAuth.NO)
	public int updateFinaAcctInfo(SqlParam<M510> params) throws Exception {
		/**List<M510> list1 = m510Dao.listFinaAcctInfosByName(params.getModel());
		if (list1 != null && list1.size() > 0 && !params.getModel().getAcctNo().equals(list1.get(0).getAcctNo())){
			throw new PromptException("该银行账号名称已经存在");
		}*/
		return m510Dao.updateFinaAcctInfo(params).getEffect();
	}
	
	@API(desc = "删除银行账号信息实体类", params = "acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code,inter_code,acct_status,remark", auth = APIAuth.NO)
	public int deleteFinaAcctInfo(SqlParam<M510> params) throws Exception {
/*		List<SqlRow> sqlRowList = m510Dao.getProdAcct(params.getModel().getAcctSerno());
		if (sqlRowList != null && sqlRowList.size() > 0){
			throw new PromptException("无法删除银行账户信息，原因：存在产品正在使用该银行账户");
//			Map<String, Object> prodParam = new HashMap<>();
//			prodParam.put("systemNo", SystemNo.FINA);
//			for (SqlRow sqlRow : sqlRowList){
//				prodParam.put("legalCode", sqlRow.get("legal_code"));
//				prodParam.put("prodCode", sqlRow.get("prod_code"));
//				prodParam.put("supplyCode", sqlRow.get("tano"));
//				List<Map<String, String>> prodInfoList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
//				if (prodInfoList != null && prodInfoList.size() > 0){
//					Map<String, String> map = prodInfoList.get(0);
//					String prodStatus = map.get("prod_status");
//					if ("1".equals(prodStatus)){
//						throw new PromptException("无法删除银行账户信息，原因：存在上架产品正在使用该银行账户");
//					}
//				}
//			}
		}*/
		return m510Dao.deleteFinaAcctInfo(params).getEffect();
	}

	@API(desc = "查询是否存在相同银行账号信息实体类信息", auth = APIAuth.YES)
	public SqlResult<ProdAcctInfo> getProdAcct(SqlParam<M510> params) throws Exception {
		Map<String,Object> map = new HashMap<>();
		map.put("acctSerno",params.getModel().getAcctSerno());
		SqlParam<ProdAcctInfo> dataParams = new FetcherData<>(map, ProdAcctInfo.class);
		dataParams.setMakeSql(true);
		SqlResult<ProdAcctInfo> sqlResult = m510Dao.getProdAcct(dataParams);
		return sqlResult;
	}

}
