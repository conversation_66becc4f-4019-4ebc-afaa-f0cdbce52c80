package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.M514List;
import org.springframework.stereotype.Repository;

import java.sql.SQLIntegrityConstraintViolationException;


/**
 * 接口版本与索引关系管理-接口文件列表，原名 TaInterfaceFileListDao
 */
@Repository
public class M514ListDao extends ComnDao {

    /**
     * 查询该interface_id对应的index_no结果集
     * @param params 传入参数
     * @return  查询单个文件列表结果
     * @throws Exception
     */
    public SqlResult<M514List> findTaInterfaceFileListById(SqlParam<M514List> params) throws Exception {
        return super.findRows("select index_no from wmp_interface_index_list where interface_id = $S{interfaceId} AND index_no = $S{indexNo}",
                SubDatabase.DATABASE_FINA_CENTER,params);
    }

    /**
     * @param params 传入参数
     * @return 根据接口id查询文件列表结果
     * @throws Exception
     */
    public SqlResult<M514List> findTaInterfaceFileListByNo(SqlParam<M514List> params) throws Exception {
        return super.findRows(
                "select t1.interface_id, t1.index_no, t2.index_name, t3.interface_name\n" +
                        "  from wmp_interface_index_list t1\n" +
                        "  left join wmp_interface_index_file t2\n" +
                        "    ON t1.index_no = t2.index_no \n" +
                        "  left join wmp_interface_info t3\n" +
                        "    ON t1.interface_id = t3.interface_id \n" , SubDatabase.DATABASE_FINA_CENTER,params);
    }

    /**
     *
     *
     * @param params 传入参数
     * @return  添加文件列表是否成功
     * @throws Exception
     */
    public UpdateResult addTaInterfaceFileListDao(SqlParam<M514List> params) throws Exception {
        return super.update("insert into wmp_interface_index_list(interface_id,index_no) values($S{interfaceId},$S{indexNo})",
                SubDatabase.DATABASE_FINA_CENTER,params.getModel());
    }

    /**
     *
     *
     * @param params 传入参数
     * @return  更新文件列表是否成功
     * @throws Exception
     */
    public UpdateResult updateTaInterfaceFileListDao(SqlParam<M514List> params) throws Exception {
        // 当前会报唯一性约束错误
        UpdateResult update = new UpdateResult();
        try{
            update = super.update("update wmp_interface_index_list set index_no = $S{indexName} where index_no =$S{indexNo}",
                    SubDatabase.DATABASE_FINA_CENTER, params.getModel());
        }catch (SQLIntegrityConstraintViolationException e) {
            update.setEffect(-1);
        }
        return update;
    }

    /**
     *
     * @param params 传入参数
     * @return   删除文件列表是否成功
     * @throws Exception
     */
    public UpdateResult deleteTaInterfaceFileListDao(SqlParam<M514List> params) throws Exception {
        return super.update("delete from wmp_interface_index_list where index_no = $S{indexNo}",
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }


}
