package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import com.kayak.fina.param.dao.FinaPrjTailingCommisionListDao;
import com.kayak.fina.param.model.FinaPrjTailingCommisionList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "费率明细服务", model = FinaPrjTailingCommisionList.class)
public class FinaPrjTailingCommisionListService {

	@Autowired
	private FinaPrjTailingCommisionListDao FinaPrjTailingCommisionListDao;

	@API(desc = "查询费率明细信息", auth = APIAuth.YES)
	public SqlResult<FinaPrjTailingCommisionList> findFinaPrjTailingCommisionLists(SqlParam<FinaPrjTailingCommisionList> params) throws Exception {
		params.setMakeSql(true);
		return FinaPrjTailingCommisionListDao.findFinaPrjTailingCommisionLists(params);
	}

	@API(desc = "添加费率明细", params = "tailing_commision_code,dimension1_min,dimension1_max,rate,crt_time,crt_user,upd_time,upd_user,remark", auth = APIAuth.NO)
	public int addFinaPrjTailingCommisionList(SqlParam<FinaPrjTailingCommisionList> params) throws Exception {
		return FinaPrjTailingCommisionListDao.addFinaPrjTailingCommisionList(params);
	}

	@API(desc = "删除费率明细", params = "tailing_commision_code,dimension1_min,dimension1_max,rate,crt_time,crt_user,upd_time,upd_user,remark", auth = APIAuth.NO)
	public int deleteFinaPrjTailingCommisionList(SqlParam<FinaPrjTailingCommisionList> params) throws Exception {
		return FinaPrjTailingCommisionListDao.deleteFinaPrjTailingCommisionList(params);
	}

	@API(desc = "查询费率明细信息", auth = APIAuth.YES)
	public SqlResult<FinaPrjTailingCommisionList> findHistory(SqlParam<FinaPrjTailingCommisionList> params) throws Exception {
		params.setMakeSql(true);
		return FinaPrjTailingCommisionListDao.findFinaPrjTailingCommisionLists(params);
	}

}
