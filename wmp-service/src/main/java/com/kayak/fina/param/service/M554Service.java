package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M554Dao;
import com.kayak.fina.param.model.M554;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财销售汇总表-按产品", model = M554.class)
public class M554Service {

    @Autowired
    private M554Dao m554Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "理财销售汇总表-按产品", auth = APIAuth.YES)
    public SqlResult<M554> findM554s(SqlParam<M554> param)throws Exception {
        SqlResult<M554> sqlResult = m554Dao.findM554s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M554> volList = sqlResult.getRows();
        for (M554 m554 : volList){
            M001 m0011 = m001Dao.get(m554.getTransOrgno());
            if (m0011 != null){
                m554.setOrgname(m0011.getOrgname());
            }
            //M554 period = m554Dao.getPeriodlist(m554);
            //if (period != null){
            //    m554.setPeriods(period.getPeriods());
            //    m554.setSubsBeginDate(period.getSubsBeginDate());
            //    m554.setSubsEndDate(period.getSubsEndDate());
            //    m554.setBenchmarks(period.getBenchmarks());
            //}
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m554.getProdCode());
            prodParam.put("supplyCode", m554.getTano());
            prodParam.put("legalCode", m554.getLegalCode());
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                String benchmarks = prodPara.get("benchmarks");
                if (StringUtils.isNotBlank(benchmarks)) {
                    DecimalFormat decimalFormat = new DecimalFormat("0.00%");
                    m554.setBenchmarks(decimalFormat.format(Double.parseDouble(benchmarks)));
                }
                m554.setProdName(prodPara.get("prod_name"));
                m554.setProdStatus(prodPara.get("prod_status"));
                m554.setInvestTerm(prodPara.get("invest_term"));
                m554.setWindingDate(prodPara.get("winding_date"));
                m554.setEstablishDate(prodPara.get("establish_date"));
                m554.setSubsBeginDate(prodPara.get("subs_begin_date"));
                m554.setSubsEndDate(prodPara.get("subs_end_date"));
            }
            //m554.setTaName(reportformUtil.getFinaTaName(m554.getTano()));
        }
        return sqlResult;
    }
}
