package com.kayak.fina.param.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.M512;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;

@Repository
public class M512Dao extends ComnDao {

	public SqlResult<M512> findFinaManagerInfos(SqlParam<M512> params) throws Exception {
		return super.findRows("SELECT 'FINA' as system_no, manager_code,legal_code,manager_name,address,connector,telno,remark,update_time FROM fina_manager_info", SubDatabase.DATABASE_FINA_CENTER,params);
	}

	public SqlResult<SqlRow> queryDict(SqlParam<M512> params) throws Exception {
		List<SqlRow> list = super.findRows(SqlRow.class,"SELECT manager_code as value,manager_name as label FROM fina_manager_info", SubDatabase.DATABASE_FINA_CENTER,null);
		SqlResult<SqlRow> sqlResult = new SqlResult();
		sqlResult.setRows(list);
		return sqlResult;
	}

	public UpdateResult addFinaManagerInfo(SqlParam<M512> params) throws Exception {
		return super.update("INSERT INTO fina_manager_info(manager_code,legal_code,manager_name,address,connector,telno,remark,update_time)" +
						" VALUES($S{managerCode},$S{legalCode},$S{managerName},$S{address},$S{connector},$S{telno},$S{remark},current_timestamp)",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult updateFinaManagerInfo(SqlParam<M512> params) throws Exception {
		return super.update("UPDATE fina_manager_info SET manager_name=$S{managerName},address=$S{address},connector=$S{connector},telno=$S{telno},remark=$S{remark},update_time=current_timestamp  WHERE  manager_code=$S{managerCode} AND legal_code=$S{legalCode} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult deleteFinaManagerInfo(SqlParam<M512> params) throws Exception {
		return super.update("DELETE FROM fina_manager_info WHERE  manager_code=$S{managerCode} AND legal_code=$S{legalCode} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}

}
