package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.common.constants.SystemNo;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.constants.SystemParamConstants;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.MC01Dao;
import com.kayak.fina.param.model.MC01;
import com.kayak.graphql.model.FetcherData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "杞涓氬姟鍙傛暟鏈嶅姟", model = MC01.class)
@RequiredArgsConstructor
@Slf4j
public class MC01Service {

    private final MC01Dao mC01Dao;

    @API(desc = "淇濆瓨杞涓氬姟鍙傛暟鍒楄〃")
    public String update(SqlParam<MC01> param) throws Exception {
        MC01 model = param.getModel();
        String paraid = model.getParaid();
        String paravalue = model.getParavalue();

        if (Tools.isBlank(paraid)) {
            return RequestSupport.updateReturnJson(true, "淇濆瓨鎴愬姛", null).toString();
        }
        // 绛夊彿鍒嗛殧杞崲涓簂ist
        List<MC01> params = new ArrayList<>();
        String[] paraidArr = paraid.split("=");
        String[] paravalueArr = paravalue.split("=");
        for (int i = 0; i < paraidArr.length; i++) {
            MC01 p = new MC01();
            p.setParaid(paraidArr[i]);
            p.setParavalue(paravalueArr[i]);
            params.add(p);
        }

        mC01Dao.update(params);
        refreshCache();
        return RequestSupport.updateReturnJson(true, "淇濆瓨鎴愬姛", null).toString();
    }

    @API(desc = "鏌ヨ杞涓氬姟鍙傛暟鍒楄〃", auth = APIAuth.NO)
    public SqlResult<MC01> find(SqlParam<MC01> params) throws Exception {
        MC01 model = params.getModel();
        model.setIsdisplay(SystemParamConstants.SHOW);
        return mC01Dao.find(params);
    }

    @API(desc = "娣诲姞杞涓氬姟鍙傛暟琛?, params = "moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay", auth = APIAuth.NO)
    public int addSysParam(SqlParam<MC01> params) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("paraid", params.getModel().getParaid());
        SqlParam<MC01> sqlParam = new FetcherData<MC01>(map, MC01.class);
        SqlResult<MC01> sqlResult = mC01Dao.find(sqlParam);
        if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0) {
            throw new PromptException("璇ュ弬鏁癷d宸茬粡琚娇鐢紝璇蜂慨鏀癸紒");
        }
        params.getModel().setIsdisplay("1");
        params.getModel().setModuleid("6");
        params.getModel().setGroupparaid(SystemNo.PLAN);
        int effect = mC01Dao.addSysParam(params).getEffect();
        refreshCache();
        return effect;
    }

    @API(desc = "淇敼杞涓氬姟鍙傛暟琛?, params = "system_no,moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay",
            auth = APIAuth.NO, operation = APIOperation.UPDATE)
    public int updateSysParam(SqlParam<MC01> params) throws Exception {
        int effect = mC01Dao.updateSysParam(params).getEffect();
        refreshCache();
        return effect;
    }

    @API(desc = "鍒犻櫎杞涓氬姟鍙傛暟琛?, params = "moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay", auth = APIAuth.NO)
    public int delete(SqlParam<MC01> params) throws Exception {
        int effect = mC01Dao.deleteSysParam(params).getEffect();
        refreshCache();
        return effect;
    }

    private void refreshCache() throws Exception {
        String content = Tools.getStringFromDate("yyyy-MM-dd hh:mm:ss", new Date());
        // ConfigUitl.publicNacosConfig("kcloud_system_params", content);
    }

    @API(desc = "鏌ヨ鏌愪釜鍙傛暟")
    public SqlResult<MC01> getSysParam(SqlParam<MC01> params) throws Exception {
        params.setMakeSql(true);
        MC01 one = mC01Dao.findOne(params);
        List<MC01> list = new ArrayList<>();
        list.add(one);
        return SqlResult.build(list);
    }

    @API(desc = "鏌ヨ鎵规鍙风敤浜庝笂浼犺浆璁╁崗璁埌褰卞儚骞冲彴")
    public MC01 getMC01(String paraId) throws Exception {
        MC01 mc01 = mC01Dao.findByContentId(paraId);
        return mc01;
    }

}


