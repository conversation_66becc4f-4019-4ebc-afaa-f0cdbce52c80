package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.fina.param.model.M513;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 原名 FinaTaInfoDao TA信息服务
 */
@Repository
public class M513Dao extends ComnDao {

    //校验销售商与产品是否可以关联
    public void disprodformyActionbefor(Map<String, Object> params) throws Exception {
        String sqlAll = "select decode (instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fina_ta_info" +
                "              where tano = $S{tano})),null,0,instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fina_ta_info" +
                "              where tano = $S{tano})) )result " +
                "  from DUAL";
        String sqlDb2 = "select decode (instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fina_ta_info" +
                "              where tano = $S{tano})),null,0,instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fina_ta_info" +
                "              where tano = $S{tano})) )result " +
                "  from sysibm.sysdummy1";
        Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
        List<SqlRow> datas = super.findRows(SqlRow.class, sql, SubDatabase.DATABASE_FINA_CENTER, params);

        if (datas.get(0).getInteger("result") < 1) {
            throw new PromptException("M51303", " 产品 [" + params.get("prodCode") + "] 不可与该销售商关联,请关闭当前页再试");
        }
    }


    public void disprodformyAction(List<Map<String, Object>> params) throws Exception {

        doTrans(() -> {
            for (Map<String, Object> param : params) {
                String sqlAll = "INSERT INTO ta_prod_distributor (" +
                        " tano,  prod_code," +
                        " status," +
                        " create_time," +
                        " update_time" +
                        " )" +
                        " VALUES(" +
                        " $S{tano}, $S{prodCode}," +
                        " '1'," +
                        " current_timestamp," +
                        " current_timestamp" +
                        ")";
                String sqlDb2 = "INSERT INTO ta_prod_distributor (" +
                        " tano,  prod_code," +
                        " status," +
                        " create_time," +
                        " update_time" +
                        " )" +
                        " VALUES(" +
                        " $S{tano}, $S{prodCode}," +
                        " '1'," +
                        " current timestamp," +
                        " current timestamp" +
                        ")";
                Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);

                super.update(sql, SubDatabase.DATABASE_FINA_CENTER, param);
            }
        });
    }

    //统计该tano记录数
    public int findTaInfoCounts(Map<String, Object> params) throws Exception {
        String sql = "SELECT COUNT(1) count FROM fina_ta_info WHERE tano = $S{tano} ";
        List<SqlRow> datas = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
        return datas.get(0).getInteger("count");
    }

    public SqlResult<M513> findTaDistributorInfosByTaskGroup(SqlParam<M513> params, String sql) throws Exception {
        return findRows("SELECT " +
                "tano,ta_name,ta_simplify_name,ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code," +
                "tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_id," +
                "is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list," +
                "check_type,is_predistribution_acct,present_confirm_num,create_time,create_user,update_time,update_user,task_group,file_imp_flag,is_holidays_send," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_import_c6_26_file,remark,pgmno,legal_code,is_own_ta," +
                "imp_task_group,exp_task_group" +
                " FROM fina_ta_info " + sql, SubDatabase.DATABASE_FINA_CENTER, params);
    }

    //查询TA信息
    public SqlResult<M513> findFinaTaInfos(SqlParam<M513> params) throws Exception {

        String sql = "SELECT  "
                + " ta.tano,ta.ta_name,ta.ta_simplify_name,ta.ta_status,ta.norm_legal_code,ta.norm_legal_type,ta.norm_legal_id_code,ta.tech_connector,"
                + "	ta.tech_connector_mobile,ta.busi_connector,ta.busi_connector_mobile,ta.address,ta.email,ta.postcode,ta.fax,ta.interface_type,ta.interface_id,"
                + "	ta.is_import_c1c5_file,ta.is_import_c6_26_file,ta.is_import_sale_fee_file,ta.allow_break_redeem,ta.is_trans_much_acct,ta.is_single_trust,"
                + "	ta.convert_ack_method,ta.is_vol_list,ta.check_type,ta.is_predistribution_acct,ta.present_confirm_num,ta.create_time,ta.create_user,ta.update_time,ta.update_user,ta.SUBS_TRANSFER_TIME,\n" +
                "       ta.APPLY_TRANSFER_TIME,\n" +
                "       ta.SUBS_REFUND_ACCT,\n" +
                "       ta.APPLY_REFUND_ACCT,"
                + "	ta.remark,ta.file_imp_flag,ta.is_holidays_send,ta.fundday_file_path,ta.cfm_file_path,ta.req_file_path,ta.freez_file_type,ta.pgmno,ta.is_own_ta ,tii.interface_name"
                + "	FROM fina_ta_info ta LEFT JOIN wmp_interface_info tii ON ta.interface_id = tii.interface_id "
                + " ORDER BY ta.create_time desc";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
    }
    
    //增加TA信息
    public int addTaInfo(SqlParam<M513> params) throws Exception {
        String sql = "INSERT INTO fina_ta_info (" +
                "     tano, ta_name," +
                "     ta_status," +
                "     norm_legal_code,      norm_legal_type," +
                "     norm_legal_id_code,   tech_connector," +
                "     tech_connector_mobile,  busi_connector,  busi_connector_mobile," +
                "     address,           email," +
                "     fax,               postcode," +
                "     interface_type,    interface_id," +
                "     is_import_c1c5_file, is_import_c6_26_file, is_import_sale_fee_file," +
                "     allow_break_redeem,   is_trans_much_acct," +
                "     is_single_trust,    convert_ack_method," +
                "     is_vol_list," +
                "     check_type," +
                "     remark,req_file_path,cfm_file_path," +
                "     fundday_file_path,is_holidays_send, create_time,create_user," +
                "     update_time,update_user,pgmno,imp_task_group,exp_task_group," +
                "     legal_code,is_own_ta,is_predistribution_acct ,SUBS_TRANSFER_TIME," +
                "     APPLY_TRANSFER_TIME," +
                "      SUBS_REFUND_ACCT," +
                "     APPLY_REFUND_ACCT)" +
                "     VALUES(" +
                "     $S{tano}, $S{taName}," +
                "     $S{taStatus}," +
                "     $S{normLegalCode},       $S{normLegalType}," +
                "     $S{normLegalIdCode}," +
                "     $S{techConnector},   $S{techConnectorMobile}," +
                "     $S{busiConnector},   $S{busiConnectorMobile}," +
                "     $S{address},          $S{email}," +
                "     $S{fax},              $S{postcode}," +
                "     $S{interfaceType},   $S{interfaceId}," +
                "     $S{isImportC1c5File},$S{isImportC626File},$S{isImportSaleFeeFile}," +
                "     $S{allowBreakRedeem},  $S{isTransMuchAcct}," +
                "     $S{isSingleTrust},     $S{convertAckMethod}," +
                "     $S{isVolList}," +
                "     $S{checkType}," +
                "     $S{remark}," +
                "     $S{reqFilePath},$S{cfmFilePath},$S{funddayFilePath}," +
                "     $S{isHolidaysSend}," +
                "     current_timestamp,$S{crtUser}," +
                "     current_timestamp,$S{updUser}," +
                "     $S{pgmno},$S{impTaskGroup},$S{expTaskGroup}, " +
                "     $S{superLegalCode}, $S{isOwnTa},$S{isPredistributionAcct},$S{subsTransferTime}," +
                "     $S{applyTransferTime}, $S{subsRefundAcct},$S{applyRefundAcct})";


        return super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel()).getEffect();
    }

    //修改TA信息
    public void updateTaInfo(SqlParam<M513> params) throws Exception {

        doTrans(() -> {
            this.updateTa(params);
//            this.addHistory(params, OpenFlag.UPDATE);
        });
    }

    //删除销售商信息和清算组成员配置表信息
    public int deleteTaDistributorInfo(SqlParam<M513> params) throws Exception {
        doTrans(() -> {
                    super.update("DELETE FROM TA_CLEAR_GROUP_MEMBER WHERE group_member = $S{tano}", SubDatabase.DATABASE_FINA_CENTER, params.getModel().getTano());
                    super.update("DELETE FROM fina_ta_info WHERE tano = $S{tano}", SubDatabase.DATABASE_FINA_CENTER,
                            params.getModel()).getEffect();
                }
        );
        return 1;
    }


    public void startTaDistributorInfo(SqlParam<M513> params) throws Exception {

        doTrans(() -> {
            super.update(
                    " UPDATE fina_ta_info" +
                        " SET ta_status = '1'" +
                        " WHERE tano = $S{tano} ", SubDatabase.DATABASE_FINA_CENTER, params.getModel());
//            this.addHis(params, OpenFlag.UPDATE);
        });
    }


    public int stopTaDistributorInfoBefor(Map<String, Object> params) throws Exception {

        List<SqlRow> datas = super.findRows("SELECT" +
                " count(*) as count" +
                " FROM prod_quota_info" +
                " WHERE tano = $S{tano}" +
                " AND total_quota > 0", SubDatabase.DATABASE_SYS_CENTER, params);
        return datas.get(0).getInteger("count");
    }


    public void stopTaDistributorInfo(SqlParam<M513> params) throws Exception {

        doTrans(() -> {
//            this.addHis(params, OpenFlag.DELETE);
            super.update(
                    " UPDATE fina_ta_info" +
                        " SET ta_status = '0'" +
                        " WHERE tano = $S{tano} ", SubDatabase.DATABASE_FINA_CENTER, params.getModel()).getEffect();
        });
    }

    //修改TA信息
    public void updateTa(SqlParam<M513> params) throws Exception {
        String sql = " UPDATE fina_ta_info \n" +
                "SET ta_name = $S{taName},\n" +
                "ta_status           = $S{taStatus},\n" +
                "norm_legal_code     = $S{normLegalCode},\n" +
                "norm_legal_type     = $S{normLegalType},\n" +
                "norm_legal_id_code  = $S{normLegalIdCode},\n" +
                "tech_connector   = $S{techConnector},\n" +
                "tech_connector_mobile = $S{techConnectorMobile},\n" +
                "busi_connector        = $S{busiConnector},\n" +
                "busi_connector_mobile = $S{busiConnectorMobile},\n" +
                "address            = $S{address},\n" +
                "email              = $S{email},\n" +
                "fax                = $S{fax},\n" +
                "postcode           = $S{postcode},\n" +
                "interface_type     = $S{interfaceType},\n" +
                "interface_id  = $S{interfaceId},\n" +
                "is_import_c1c5_file   = $S{isImportC1c5File},\n" +
                "is_import_c6_26_file   = $S{isImportC626File},\n" +
                "is_import_sale_fee_file   = $S{isImportSaleFeeFile},\n" +
                "allow_break_redeem = $S{allowBreakRedeem},\n" +
                "is_trans_much_acct = $S{isTransMuchAcct},\n" +
                "is_single_trust    = $S{isSingleTrust},\n" +
                "convert_ack_method = $S{convertAckMethod},\n" +
                "is_vol_list        = $S{isVolList},\n" +
                "check_type         = $S{checkType},\n" +
                "remark             = $S{remark},\n" +
                "req_file_path      = $S{reqFilePath},\n" +
                "cfm_file_path      = $S{cfmFilePath},\n" +
                "fundday_file_path  = $S{funddayFilePath},\n" +
                "is_holidays_send   = $S{isHolidaysSend},\n" +
                "pgmno              =$S{pgmno},\n" +
                "is_own_ta          =$S{isOwnTa},\n" +
                "update_user           = $S{createUser},\n" +
                "imp_task_group           = $S{impTaskGroup},\n" +
                "exp_task_group           = $S{expTaskGroup},\n" +
                "SUBS_TRANSFER_TIME           = $S{subsTransferTime},\n" +
                "SUBS_REFUND_ACCT           = $S{subsRefundAcct},\n" +
                "APPLY_TRANSFER_TIME           = $S{applyTransferTime},\n" +
                "APPLY_REFUND_ACCT           = $S{applyRefundAcct},\n" +
                "update_time           = current_timestamp\n" +
                "  WHERE tano = $S{tano}  ";
        super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    public void addHistory(SqlParam<M513> params, String openFlag) throws Exception {
        super.update(historySql(openFlag) + "  tano = $S{tano} ", SubDatabase.DATABASE_FINA_CENTER, params.getModel()).getEffect();

    }

    public SqlResult<M513> findHistory(SqlParam<M513> params) throws Exception {
        params.setMakeSql(false);
        String sql = "SELECT  "
                + "	dis.tano,dis.ta_name,dis.ta_simplify_name,dis.ta_status,dis.norm_legal_code,"
                + "	dis.norm_legal_type,dis.norm_legal_id_code,dis.tech_connector,dis.tech_connector_mobile,dis.busi_connector,dis.busi_connector_mobile,dis.address,"
                + "	dis.email,dis.postcode,dis.fax,dis.interface_type,dis.interface_id,dis.is_import_c1c5_file,dis.is_import_c6_26_file,dis.is_import_sale_fee_file,"
                + "	dis.allow_break_redeem,dis.is_trans_much_acct,dis.is_single_trust,dis.convert_ack_method,dis.is_vol_list,dis.check_type,"
                + "	dis.is_predistribution_acct,dis.present_confirm_num,dis.create_time,dis.create_user,dis.update_time,dis.update_user,"
                + "	dis.remark,dis.file_imp_flag,dis.is_holidays_send,dis.fundday_file_path,dis.cfm_file_path,dis.req_file_path,"
                + "	dis.freez_file_type, dis.pgmno FROM fina_ta_info_His dis where dis.process_instance_id = $S{processInstanceId} ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
    }

    public String historySql(String openFlag) {
        return "INSERT INTO fina_ta_info_his( tano,ta_name,ta_simplify_name," +
                "ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code,tech_connector,tech_connector_mobile,busi_connector," +
                "busi_connector_mobile,address,email,postcode,fax,interface_type,interface_id,is_import_c1c5_file,is_import_sale_fee_file," +
                "allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct," +
                "present_confirm_num,create_time,create_user,update_time,update_user,remark,task_group,file_imp_flag,batch_no,oper_user,oper_date,oper_flag," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_holidays_send,is_import_c6_26_file," +
                "process_ta_status,process_instance_id,pgmno,is_own_ta,exp_task_group,imp_task_group)" +
                "  SELECT " +
                "  tano,ta_name,ta_simplify_name," +
                "ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code,tech_connector,tech_connector_mobile,busi_connector," +
                "busi_connector_mobile,address,email,postcode,fax,interface_type,interface_id,is_import_c1c5_file,is_import_sale_fee_file," +
                "allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct," +
                "present_confirm_num,create_time,create_user,update_time,update_user,remark,task_group,file_imp_flag,null,$S{crtUser},current_timestamp,'" + openFlag + "' ," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_holidays_send,is_import_c6_26_file," +
                "process_status,$S{processInstanceId},pgmno,is_own_ta,exp_task_group,imp_task_group " +
                "  FROM fina_ta_info WHERE ";
    }

    public int addHis(SqlParam<M513> param, String openFlag) throws Exception {
        param.setMakeSql(false);
        return super.update(historySql(openFlag) + "  tano= $S{tano}", SubDatabase.DATABASE_FINA_CENTER, param.getModel()).getEffect();
    }

    public SqlResult<M513> select(SqlParam<M513> params) throws Exception {
        return super.findRows("SELECT " +
                "tano,ta_name,ta_simplify_name,ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code," +
                "tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type,interface_id," +
                "is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list," +
                "check_type,is_predistribution_acct,present_confirm_num,create_time,create_user,update_time,update_user,task_group,file_imp_flag,is_holidays_send," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_import_c6_26_file,remark,pgmno,is_own_ta," +
                "imp_task_group,exp_task_group" +
                " FROM fina_ta_info WHERE tano= $S{tano}", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    public SqlRow findTaNum(String tano) throws Exception {
        return super.findRow("SELECT count(1) num  from fina_ta_info where tano = '"+tano+"' and ta_status = '0' ",SubDatabase.DATABASE_FINA_CENTER, tano);
    }

}
