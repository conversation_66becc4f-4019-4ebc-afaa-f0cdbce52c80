package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M564;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <p>客户份额历史查询</p>
 *
 * <AUTHOR>
 * @date 20220923
 */

@Repository
public class M564Dao extends ComnDao {

    public SqlResult<M564> findM564(SqlParam<M564> params) throws Exception {
        params.setMakeSql(true);
        StringBuilder sql = new StringBuilder("select " +
                "bak_date,          acct_no,        cust_name,          id_type,           id_code,                prod_code,           prod_name,   " +
                "income_date,       end_date,       total_vol,          avail_vol,          redeem_frozen_vol + transfer_frozen_vol + notrans_frozen_vol trans_frozen_vol,      abn_frozen_vol + elisor_frozen_vol + ta_frozen_vol except_frozen_vol,  " +
                "unconvert_income,  breakmount,      mobile " +
                "from fina_cust_vol_day where 1=1 ");
        return super.findRows(sql.toString(), SubDatabase.DATABASE_FINA_CENTER, params);
    }
}
