package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M548;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M548Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M548> findM548s(SqlParam<M548> params) throws  Exception{
        params.setMakeSql(true);
        String sql = "select t1.tano,\n" +
                "       t1.trans_date,\n" +
                "       t1.cust_manager,\n" +
                "       t1.prod_code,\n" +
                "       t1.trans_orgno,\n" +
                "       sum(case\n" +
                "             when t1.cust_type = '1' then\n" +
                "              t1.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as per_vol,\n" +
                "       sum(case\n" +
                "             when t1.cust_type = '0' then\n" +
                "              t1.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as org_vol,\n" +
                "       sum(case\n" +
                "             when t1.cust_type = '0' or cust_type = '1' then\n" +
                "              t1.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as vol,\n" +
                "       sum(case\n" +
                "             when t1.cust_type = '1' then\n" +
                "              t1.effe_num\n" +
                "             else\n" +
                "              0\n" +
                "           end) as per_num,\n" +
                "       sum(case\n" +
                "             when t1.cust_type != '1' then\n" +
                "              t1.effe_num\n" +
                "             else\n" +
                "              0\n" +
                "           end) as org_num,\n" +
                "       sum(t1.effe_num) as total_num,\n" +
                "       t2.ta_name\n" +
                "  from fina_vol_stock_cm t1 left join fina_ta_info t2 on t1.tano=t2.tano\n" +
                " where 1 = 1 \n" +
                " group by t1.tano, t1.trans_date, t1.cust_manager, t1.prod_code, t1.trans_orgno,t2.ta_name\n";

        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }
}
