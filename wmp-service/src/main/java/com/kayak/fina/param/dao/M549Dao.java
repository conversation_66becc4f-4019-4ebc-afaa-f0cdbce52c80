package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M549;
import org.springframework.stereotype.Repository;

@Repository
public class M549Dao extends ComnDao {

    public SqlResult<M549> findM549s(SqlParam<M549> param) throws  Exception{
        param.setMakeSql(true);
        String sql = " select t1.tano,\n" +
                "       t2.ta_name,\n" +
                "       t1.prod_code,\n" +
                "       t1.prod_name,\n" +
                "       t1.busi_date,\n" +
                "       sum(case\n" +
                "             when t1.busi_code = '020' then\n" +
                "              t1.app_amt\n" +
                "             else\n" +
                "              0\n" +
                "           end) as sub_amt,\n" +
                "       sum(case\n" +
                "             when t1.busi_code = '022' then\n" +
                "              t1.app_amt\n" +
                "             else\n" +
                "              0\n" +
                "           end) as pur_amt,\n" +
                "       sum(case\n" +
                "             when t1.busi_code in ('024', '098') then\n" +
                "              t1.app_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as red_amt\n" +
                "  from FINA_CUST_TRANS_REQ_LOG t1 left join fina_ta_info t2 on t1.tano=t2.tano\n" +
                "  where 1=1 and t1.trans_status = '0'" +
                " group by t1.tano, t1.prod_code, t1.prod_name, t1.busi_date,t2.ta_name ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,param);
    }
}
