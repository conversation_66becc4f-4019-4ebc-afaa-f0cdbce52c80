package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.AddFrom;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.Ta3005;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Repository
public class FinaTaPrjFeeDivideDao extends ComnDao {

	private String property(){
		return "SELECT di.prod_code,di.tano,di.fee_type,di.enable_date,di.divid_type,"
				+" di.crt_time,di.crt_user,di.upd_time,di.upd_user,di.remark " ;
	}
	/**
	 * 2020/10/09   因为工作流新增筛选条件筛选状态为E的产品 和  E的销售商
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<Ta3005> findTaPrjFeeDivides(SqlParam<Ta3005> params) throws Exception {
		String sql = property() + ",info.prod_short_name,dis.ta_name,di.manager_divid_rate*100 AS manager_divid_rate,di.ta_divid_rate*100 AS ta_divid_rate "
				+" FROM fina_ta_prj_fee_divide di"
				+"   LEFT JOIN fina_prod_info info ON di.prod_code = info.prod_code "
				+ "  LEFT JOIN fina_ta_info dis ON di.tano=dis.tano "
				;

		return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
	}
	/**
	 * 2020/10/09   因为工作流新增筛选条件筛选状态为E的产品 和  E的销售商
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<Ta3005> findTaPrjFeeDividesByDisCode(SqlParam<Ta3005> params) throws Exception {
		String sql =	 property() + ",di.add_from,info.prod_short_name,dis.ta_name,di.manager_divid_rate*100 AS manager_divid_rate,di.ta_divid_rate*100 AS ta_divid_rate "
				+" FROM fina_ta_prj_fee_divide di"
				+"   LEFT JOIN fina_prod_info info ON di.prod_code = info.prod_code "
				+ "  LEFT JOIN fina_ta_info dis ON di.tano=dis.tano where 1=1"
				;
		if(!StringUtils.isEmpty(params.getModel().getProdCode())){
			sql += " and  di.prod_code like '%$U{prodCode}%'";
		}
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql += " and  di.tano = $S{tano}";
		}
		return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
	}
	/**
	 * * 2020/10/09   因为工作流新增筛选条件筛选状态为E的销售商 和 状态为E的产品
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<Ta3005> findTaPrjFeeDividesByProdCode(SqlParam<Ta3005> params) throws Exception {
		String sql =  property() +  ",di.add_from,info.prod_short_name,di.manager_divid_rate*100 AS manager_divid_rate,di.ta_divid_rate*100 AS ta_divid_rate,"
				+ " CASE WHEN di.tano = '-1' THEN '默认销售商' ELSE dis.ta_name END AS ta_name"
				+" FROM fina_ta_prj_fee_divide di"
				+"   LEFT JOIN fina_prod_info info ON di.prod_code = info.prod_code "
				+ "  LEFT JOIN fina_ta_info dis ON di.tano=dis.tano "
    			+ " WHERE di.prod_code=$S{prodCode} "
				+"  ";

    	if(!Tools.strIsEmpty(params.getModel().getTano())){
    		sql = sql + " AND di.tano LIKE '%$U{tano}%'";
    	}
		SqlResult<Ta3005> result = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
		return result;
	}
	public SqlResult<Ta3005> findTaPrjFeeDividesOnly(SqlParam<Ta3005> params) throws Exception {
		String sql = property()+" FROM fina_ta_prj_fee_divide di" +
				 " WHERE di.prod_code=$S{prodCode} and di.tano=$S{tano} AND di.fee_type=$S{feeType} and di.enable_date=$S{enableDate} ";
		return super.findRows(sql, 3, params);
	}

	public void addTa3005(List<Ta3005> params) throws Exception {
		//认购费
		final String SUBSCRIBE = "0";
		final String SALESSERVICE = "7";  //销售服务费
		final String MANAGE = "8";      //管理费
		List list = new ArrayList();
		//查询到所有封闭式产品的编号， 封闭式产品只允许插入认购费/销售服务费/管理费 1
		List<SqlRow> closeProd = super.findRows("SELECT prod_code FROM fina_prod_info where period_type = '1'");
		closeProd.stream().forEach(e->{ list.add(e.get("prod_code"));});

		doTrans(() -> {

			for (Ta3005 ta3005 : params) {
				//产品是否是封闭产品，插入类型是否是认购费、销售服务费、管理费
				if (list.contains(ta3005.getProdCode()) & !(SUBSCRIBE.equals(ta3005.getFeeType()) | SALESSERVICE.equals(ta3005.getFeeType()) | MANAGE.equals(ta3005.getFeeType()))){
					continue;
				}
				String sql = "SELECT 1 FROM fina_ta_prj_fee_divide WHERE prod_code = $S{prodCode} AND tano = $S{tano} "
						+ "	AND fee_type = $S{feeType} AND enable_date = $S{enableDate} ";

				List<SqlRow> findRows = super.findRows(sql, ta3005);
				if(findRows.size()>0){
					continue;
				}

				//插入产品交易费用费用分成方案表
				String sqlAll = "INSERT INTO fina_ta_prj_fee_divide(prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,ta_divid_rate,crt_time,crt_user,"
								+ "	upd_time,upd_user,remark,add_from) VALUES($S{prodCode},$S{tano},$S{feeType},$S{enableDate},$S{dividType},$D{managerDividRate},$D{distributorDividRate}, "
								+ "	current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark},$S{dataStatus},$S{addFrom})";
				super.update(sqlAll, SubDatabase.DATABASE_FINA_CENTER, ta3005);
			}
			
		});
		
		
	}
	
	public void updateTa3005(SqlParam<Ta3005> params) throws Exception {
			String sqlAll = "UPDATE fina_ta_prj_fee_divide SET prod_code=$S{prodCode} ,tano=$S{tano} , divid_type=$S{dividType} ,manager_divid_rate=$D{managerDividRate} ,"
					+ "	ta_divid_rate=$D{distributorDividRate} ,upd_time=current_timestamp ,upd_user=$S{updUser} ,	remark=$S{remark}   WHERE  prod_code = $S{prodCode}   AND tano = $S{tano} " +
							"                    AND fee_type = $S{feeType} AND enable_date = $S{enableDate} ";
			super.update(sqlAll, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
	}
	
	public void deleteTa3005(SqlParam<Ta3005> params) throws Exception {

				super.update("DELETE FROM fina_ta_prj_fee_divide WHERE prod_code=$S{prodCode} " +
								"   AND tano=$S{tano} " +
								"   AND fee_type=$S{feeType} " +
								"   AND enable_date=$S{enableDate} " , 3,
						params.getModel());
	}
	public int add(SqlParam<Ta3005> params) throws Exception {
		String sqlAll = "INSERT INTO fina_ta_prj_fee_divide(prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,ta_divid_rate,crt_time,crt_user,"
				+ "	upd_time,upd_user,remark,add_from) VALUES($S{prodCode},$S{tano},$S{feeType},$S{enableDate},$S{dividType},$D{managerDividRate},$D{distributorDividRate}, "
				+ "	current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark},$S{dataStatus},$S{addFrom})";
		return super.update(sqlAll, SubDatabase.DATABASE_FINA_CENTER, params.getModel()).getEffect();
	}
    public void addUpdateTa3005(List<Ta3005> params) throws Exception {
        //认购费
        final String SUBSCRIBE = "0";
        final String SALESSERVICE = "7";  //销售服务费
        final String MANAGE = "8";      //管理费
        List list = new ArrayList();
        //查询到所有封闭式产品的编号， 封闭式产品只允许插入认购费/销售服务费/管理费 1
        List<SqlRow> closeProd = super.findRows("SELECT prod_code FROM fina_prod_info where period_type = '1'");
        closeProd.stream().forEach(e->{ list.add(e.get("prod_code"));});

        doTrans(() -> {

            for (Ta3005 ta3005 : params) {
                //产品是否是封闭产品，插入类型是否是认购费、销售服务费、管理费
                if (list.contains(ta3005.getProdCode()) & !(SUBSCRIBE.equals(ta3005.getFeeType()) | SALESSERVICE.equals(ta3005.getFeeType()) | MANAGE.equals(ta3005.getFeeType()))){
                    continue;
                }
                List<SqlRow> findRows = super.findRows("SELECT 1 FROM fina_ta_prj_fee_divide WHERE prod_code = $S{prodCode} AND tano = $S{tano} "
                        + "	AND fee_type = $S{feeType} AND enable_date = $S{enableDate} ", ta3005);
                if(findRows.size()>0){
                    continue;
                }

                //插入产品交易费用费用分成方案表
				String sqlAll = "INSERT INTO fina_ta_prj_fee_divide(prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,ta_divid_rate,crt_time,crt_user"
                                + "	upd_time,upd_user,remark) VALUES($S{prodCode},$S{tano},$S{feeType},$S{enableDate},$S{dividType},$D{managerDividRate},$D{distributorDividRate}, "
                                + "	current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark},$S{dataStatus})";
                super.update(sqlAll,SubDatabase.DATABASE_FINA_CENTER,ta3005);
            }

        });
    }

	/**
	 * 这个方法要查询单边锁，如果销售商代码有从产品页面添加的数据责不允许修改表 如果产品代码有从销售商页面添加的数据不允许修改表
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<Ta3005> getLock(SqlParam<Ta3005> params)throws Exception{
		String sql = property() + ",di.manager_divid_rate,di.ta_divid_rate  FROM fina_ta_prj_fee_divide di WHERE ";
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql += " di.tano=$S{tano}  AND di.ADD_FROM =  '"+ AddFrom.PROD+"'  ";
		}else if(!StringUtils.isEmpty(params.getModel().getProdCode())){
			sql += " di.prod_code=$S{prodCode}  AND di.ADD_FROM = '"+AddFrom.DIS+"'  ";
		}
		return super.findRows( sql, SubDatabase.DATABASE_FINA_CENTER, params);
	}

    public int addHistory(SqlParam<Ta3005> params,String openFlag,String sql) throws Exception{
		String sqlAll = " insert into fina_ta_prj_fee_divide_HIS(  prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,ta_divid_rate,crt_time,crt_user,upd_time,upd_user,remark ,oper_user,oper_date,oper_flag,min_discount,resolve_way,process_instance_id,add_from)" +
                " select prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,ta_divid_rate,crt_time,crt_user,upd_time,upd_user,remark, $S{crtUser},current_timestamp,'"+ openFlag+"',min_discount,resolve_way, $S{processInstanceId},add_from from fina_ta_prj_fee_divide where " + sql;
        return super.update(sqlAll, SubDatabase.DATABASE_FINA_CENTER, params.getModel()).getEffect();
    }

    public void addHisApprove(SqlParam<Ta3005> params,String openFlag) throws Exception{
        String sql = null;
        if(!StringUtils.isEmpty(params.getModel().getTano())){
            sql = " tano=$S{tano}  AND ADD_FROM !=  '"+AddFrom.PROD+"'  ";
        }else if(!StringUtils.isEmpty(params.getModel().getProdCode())){
            sql = " prod_code=$S{prodCode}  AND ADD_FROM != '"+AddFrom.DIS+"'  ";
        }
        this.addHistory(params,openFlag,sql);
    }

	public int clearData(SqlParam<Ta3005> params,String openFlag,String clearSql) throws Exception {
	    this.addHisApprove(params,openFlag);
        String sql ="DELETE FROM fina_ta_prj_fee_divide WHERE ";
        if(!StringUtils.isEmpty(params.getModel().getTano())){
            sql += " tano=$S{tano}  AND ADD_FROM !=  '"+AddFrom.PROD+"'  ";
        }else{   //!StringUtils.isEmpty(params.getModel().getProdCode())
            sql += " and prod_code=$S{prodCode}  AND ADD_FROM != '"+AddFrom.DIS+"'  ";
        }
		return  super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel()).getEffect();
	}
	public SqlResult<Ta3005> select(SqlParam<Ta3005> params)throws Exception{
        String sql = property() + ",di.manager_divid_rate,di.ta_divid_rate  FROM fina_ta_prj_fee_divide di WHERE ";
        if(!StringUtils.isEmpty(params.getModel().getTano())){
            sql += " di.tano=$S{tano}  AND di.ADD_FROM !=  '"+AddFrom.PROD+"'  ";
        }else if(!StringUtils.isEmpty(params.getModel().getProdCode())){
            sql += " di.prod_code=$S{prodCode}  AND di.ADD_FROM != '"+AddFrom.DIS+"'  ";
        }
		return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
	}
}
