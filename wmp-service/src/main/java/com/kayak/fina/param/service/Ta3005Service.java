package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.AddFrom;
import com.kayak.common.model.FeeType;
import com.kayak.common.constants.GlobalConstants;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;

import com.kayak.fina.param.dao.FinaTaPrjFeeDivideDao;
import com.kayak.fina.param.model.Ta3005;
import com.kayak.graphql.model.FetcherData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "费用分成方案服务", model = Ta3005.class)
public class Ta3005Service {

	@Autowired
	private FinaTaPrjFeeDivideDao finaTaPrjFeeDivideDao;

	@API(desc = "查询费用分成方案信息", auth = APIAuth.YES)
	public SqlResult<Ta3005> findTa3005s(SqlParam<Ta3005> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<Ta3005>  allList = finaTaPrjFeeDivideDao.findTaPrjFeeDivides(params);
		return allList;
	}

	/**
	 * 会将 dataStatus!E&&addFrom !=1 的非销售商页面添加的过滤掉
	 * @param params
	 * @return
	 * @throws Exception
	 */
	@API(desc = "查询费用分成方案信息", auth = APIAuth.YES)
	public SqlResult<Ta3005> findTa3005sByDisCode(SqlParam<Ta3005> params) throws Exception {
		params.setMakeSql(false);
		SqlResult<Ta3005>  allList = finaTaPrjFeeDivideDao.findTaPrjFeeDividesByDisCode(params);
		allList.setRows( allList.getRows().stream().map(
			item-> {
				//db2的需要转化下科学计数法
				BigDecimal taDividRate = new BigDecimal(item.getTaDividRate());
				item.setTaDividRate(taDividRate.toPlainString());
				BigDecimal managerDividRate = new BigDecimal(item.getManagerDividRate());
				item.setManagerDividRate(managerDividRate.toPlainString());
				return item;
			}).filter(item-> AddFrom.DIS.equals(item.getAddFrom())).collect(Collectors.toList())
		);
		allList.setResults(allList.getRows().size());
		return allList;
	}

	/**
	 * 会将 dataStatus!E&&addFrom !=0 的非产品页面添加的过滤掉
	 * @param params
	 * @return
	 * @throws Exception
	 */
	@API(desc = "查询费用分成方案信息", auth = APIAuth.YES)
	public SqlResult<Ta3005> findTa3005sByProdCode(SqlParam<Ta3005> params) throws Exception {
		params.setMakeSql(false);
		SqlResult<Ta3005> ta3005s = finaTaPrjFeeDivideDao.findTaPrjFeeDividesByProdCode(params);
		//db2的需要转化下科学计数法
		ta3005s.setRows(ta3005s.getRows().stream().map(
				item->{
					BigDecimal managerDividRate = new BigDecimal(item.getManagerDividRate());
					item.setManagerDividRate(managerDividRate.toPlainString());
					BigDecimal taDividRate = new BigDecimal(item.getTaDividRate());
					item.setTaDividRate(taDividRate.toPlainString());
					return item;
				}
		).collect(Collectors.toList()));
		return ta3005s;
	}
	
	@API(desc = "添加费用分成方案", params = "prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
	public String addTa3005(SqlParam<Ta3005> params) throws Exception {
		
		Ta3005 model = params.getModel();
		
		//页面传过来的产品代码和费用类型是多选，需要进行循环匹配
		String[] prodCodes = model.getProdCode().split(GlobalConstants.MSELECT_SPLIT);
		String[] feeTypes = model.getFeeType().split(GlobalConstants.MSELECT_SPLIT);
		
		//数据库内存的分成比例需要除以100
		BigDecimal taDividRate = new BigDecimal(model.getTaDividRate());
		model.setTaDividRate(String.valueOf(taDividRate.divide(new BigDecimal(100))));
		BigDecimal managerDividRate = new BigDecimal(model.getManagerDividRate());
		model.setManagerDividRate(String.valueOf(managerDividRate.divide(new BigDecimal(100))));
		
		List<Ta3005> modelList = new ArrayList<>();

		StringBuilder Ta3005s = new StringBuilder();

		int loseNumber = 0;//用于记录插入失败条数

		for (String prodCode : prodCodes) {
			for (String feeType : feeTypes) {
				Ta3005 paramTemp = model.clone();
				paramTemp.setProdCode(prodCode);
				paramTemp.setFeeType(feeType);


				model.setProdCode(prodCode);
				model.setFeeType(feeType);

				boolean exist = isExist(params);
				if(exist){
					if(loseNumber > 0){
						Ta3005s.append(" 、"+prodCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}else {
						Ta3005s.append(" {  "+prodCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}
					loseNumber++;
				}

				modelList.add(paramTemp);
			}
		}
		finaTaPrjFeeDivideDao.addTa3005(modelList);

		if(loseNumber > 0){
			return RequestSupport.updateReturnJson(true, "新增失败"+loseNumber+"条，"+Ta3005s+" }   ,在 "+params.getModel().getEnableDate()+" 此启用日期下已有方案！",null).toString();
		}

		return  RequestSupport.updateReturnJson(true, "新增成功", null).toString();
	}
	

	@API(desc = "添加费用分成方案", params = "prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
	public String addTa3005ByProdCode(SqlParam<Ta3005> params) throws Exception {
		
		Ta3005 model = params.getModel();

		// 如果销售商未传入，则默认全部销售商，设置为-1
		if (model.getTano() == null || model.getTano() == ""){
			model.setTano("-1");
		}
		
		//页面传过来的销售商代码和费用类型是多选，需要进行循环匹配
		String[] distributorCodes = model.getTano().split(GlobalConstants.MSELECT_SPLIT);
		String[] feeTypes = model.getFeeType().split(GlobalConstants.MSELECT_SPLIT);
		
		//数据库内存的分成比例需要除以100
		BigDecimal taDividRate = new BigDecimal(model.getTaDividRate());
		model.setTaDividRate(String.valueOf(taDividRate.divide(new BigDecimal(100))));
		BigDecimal managerDividRate = new BigDecimal(model.getManagerDividRate());
		model.setManagerDividRate(String.valueOf(managerDividRate.divide(new BigDecimal(100))));
		
		List<Ta3005> modelList = new ArrayList<>();

		StringBuilder Ta3005s = new StringBuilder();

		int loseNumber = 0;//用于记录插入失败条数

		for (String distributorCode : distributorCodes) {
			for (String feeType : feeTypes) {
				Ta3005 paramTemp = model.clone();
				paramTemp.setTano(distributorCode);
				paramTemp.setFeeType(feeType);

				model.setTano(distributorCode);
				model.setFeeType(feeType);

				boolean exist = isExist(params);
				if(exist){
					if(loseNumber > 0){
						Ta3005s.append(" 、"+distributorCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}else {
						Ta3005s.append(" {  "+distributorCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}
					loseNumber++;
				}

				modelList.add(paramTemp);
			}
		}
		finaTaPrjFeeDivideDao.addTa3005(modelList);

		if(loseNumber > 0){
			return RequestSupport.updateReturnJson(true, "新增失败"+loseNumber+"条，"+Ta3005s+" }   ,在 "+params.getModel().getEnableDate()+" 此启用日期下已有方案！",null).toString();
		}
		return  RequestSupport.updateReturnJson(true, "新增成功", null).toString();
	}
	
	
	
	@API(desc = "修改费用分成方案", params = "prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
	public String updateTa3005(SqlParam<Ta3005> params) throws Exception {
		Ta3005 model = params.getModel();
		//数据库内存的分成比例需要除以100
		BigDecimal taDividRate = new BigDecimal(model.getTaDividRate());
		model.setTaDividRate(String.valueOf(taDividRate.divide(new BigDecimal(100))));
		BigDecimal managerDividRate = new BigDecimal(model.getManagerDividRate());
		model.setManagerDividRate(String.valueOf(managerDividRate.divide(new BigDecimal(100))));
		finaTaPrjFeeDivideDao.updateTa3005(params);

		return  RequestSupport.updateReturnJson(true, "修改成功", null).toString();
	}
	
	@API(desc = "删除费用分成方案", params = "prod_code,tano,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark", auth = APIAuth.NO)
	public String deleteTa3005(SqlParam<Ta3005> params) throws Exception {
		finaTaPrjFeeDivideDao.deleteTa3005(params);
		return  RequestSupport.updateReturnJson(true, "删除成功", null).toString();
	}

	private boolean isExist(SqlParam<Ta3005> params) throws Exception {
		SqlParam<Ta3005> queryParam =  new FetcherData<Ta3005>(new HashMap<>(),Ta3005.class);
		queryParam.setMakeSql(false);
		queryParam.getModel().setProdCode(params.getModel().getProdCode());
		queryParam.getModel().setTano(params.getModel().getTano());
		queryParam.getModel().setFeeType(params.getModel().getFeeType());
		queryParam.getModel().setEnableDate(params.getModel().getEnableDate());
		SqlResult<Ta3005> result = finaTaPrjFeeDivideDao.findTaPrjFeeDividesOnly(queryParam);
		if (result.getRows().size() > 0 ){
			return true;
		}
		return false;
	}
    /**
     * 撤销删除
     * @param params
     * @return
     * @throws Exception
     */
    public String cancelDeelteTa3005(SqlParam<Ta3005> params) throws Exception{
        finaTaPrjFeeDivideDao.deleteTa3005(params);
        return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
    }



    private List setTa3005List (SqlParam<Ta3005> params) throws Exception{
        Ta3005 model = params.getModel();
        // 如果销售商未传入，则默认全部销售商，设置为-1
        if (model.getTano() == null || model.getTano() == ""){
            model.setTano("-1");
        }

        //页面传过来的销售商代码和费用类型是多选，需要进行循环匹配
        String[] distributorCodes = model.getTano().split(GlobalConstants.MSELECT_SPLIT);
        String[] feeTypes = model.getFeeType().split(GlobalConstants.MSELECT_SPLIT);

        //数据库内存的分成比例需要除以100
        BigDecimal taDividRate = new BigDecimal(model.getTaDividRate());
        model.setTaDividRate(String.valueOf(taDividRate.divide(new BigDecimal(100))));
        BigDecimal managerDividRate = new BigDecimal(model.getManagerDividRate());
        model.setManagerDividRate(String.valueOf(managerDividRate.divide(new BigDecimal(100))));

        List<Ta3005> modelList = new ArrayList<>();

        StringBuilder Ta3005s = new StringBuilder();

        int loseNumber = 0;//用于记录插入失败条数

        for (String distributorCode : distributorCodes) {
            for (String feeType : feeTypes) {
                Ta3005 paramTemp = model.clone();
                paramTemp.setTano(distributorCode);
                paramTemp.setFeeType(feeType);

                model.setTano(distributorCode);
                model.setFeeType(feeType);

                boolean exist = isExist(params);
                if(exist){
                    if(loseNumber > 0){
                        Ta3005s.append(" 、"+distributorCode+"-"+ FeeType.codeOf(feeType).getDesc());
                    }else {
                        Ta3005s.append(" {  "+distributorCode+"-"+ FeeType.codeOf(feeType).getDesc());
                    }
                    loseNumber++;
                }

                modelList.add(paramTemp);
            }
        }
        return modelList;
    }

	@API(desc="单边锁",auth=APIAuth.NO)
	public SqlResult<Ta3005> getLock(SqlParam<Ta3005> param)throws Exception{
		return finaTaPrjFeeDivideDao.getLock(param);
	}
}
