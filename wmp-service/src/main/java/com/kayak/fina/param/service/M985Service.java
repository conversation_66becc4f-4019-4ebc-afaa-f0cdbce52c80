package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M985Dao;
import com.kayak.fina.param.model.M985;
import com.kayak.fina.param.model.M985;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName M985Service
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/5 9:18
 * @Version 1.0
 **/
@Service
@APIDefine(desc = "理财账户类交易确认流水查询服务", model = M985.class)
public class M985Service {

    @Autowired
    private M985Dao m985Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财账户类交易确认流水查询", auth = APIAuth.YES)
    public SqlResult<M985> findFinaCustCfmParam(SqlParam<M985> params) throws Exception {
        params.getModel().setLegalCode(null);
        SqlResult <M985> sqlResult = m985Dao.findFinaCustCfmParam(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
       /** sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            try{
                item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
            } catch (Exception e) {
                throw new RuntimeException("M985错误："+e.getMessage());
            }
            return item;
        }).collect(Collectors.toList()));*/
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

}
