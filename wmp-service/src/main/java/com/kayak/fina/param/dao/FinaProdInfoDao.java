package com.kayak.fina.param.dao;

import com.kayak.core.sql.UpdateResult;
import com.kayak.common.constants.SubDatabase;
import com.kayak.fina.param.model.FinaProdInfo;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

@Repository
public class FinaProdInfoDao extends ComnDao {

    public SqlResult<FinaProdInfo> findFinaProdInfos(SqlParam<FinaProdInfo> params) throws Exception {
        params.setMakeSql(true);
        return super.findRows("SELECT t1.tano, t1.prod_code, t1.legal_code, t1.prod_name, t1.prod_short_name, t1.regist_code, t1.parent_code, t1.prod_type, t1.sale_status, t1.prod_risk_level, t1.manager_code, t1.trutee_code, t1.rasie_type, t1.benchmarks, t1.cur, t1.def_div_method, t1.div_chg_flag, t1.face_value, t1.price, t1.nav, t1.nav_date, t1.can_reserve, t1.reserve_begin_date, t1.reserve_invalid_date, t1.subs_begin_date, t1.subs_begin_time, t1.subs_end_date, t1.subs_end_time, t1.establish_date, t1.income_date, t1.open_begin_date, t1.open_end_date, t1.actual_end_date, t1.end_date, t1.winding_date, t1.open_time, t1.close_time, t1.discount_code, t1.subs_pay_days, t1.apply_pay_days, t1.redeem_pay_days, t1.subs_payback_days, t1.div_pay_days, t1.fail_pay_days, t1.end_pay_days, t1.proxy_fee_flag, t1.prod_size, t1.service_fee_rate, t1.subs_capital_mode, t1.apply_capital_mode, t1.first_buy_flag, t1.prod_total_vol, t1.convert_status, t1.transfer_agency_status, t1.div_date, t1.equity_regist_date, t1.trans_start_time, t1.trans_end_time, t1.discount_flag, t1.change_days, t1.first_online_date, t1.first_online_time, t1.prod_order, t1.advance_days, t1.advance_begin_time, t1.impawn_frozen_flag, t1.realtime_redeem_flag, t1.close_realredeem_flag, t1.close_realredeem_repay_method, t1.workday_pgm, t1.invest_term, t1.subs_cut_flag, t1.crt_time, t1.upd_time, t1.double_flag, t1.year_days, t1.apply_cfm_days, t1.redeem_cfm_days, t1.expire_mode, t1.export_mode, t1.cool_days, t1.redeem_detail_order, t1.investment, t1.deptno, t1.fee_precision_mth, t1.money_precision_mth, t1.vol_precision_mth, t1.div_precision_mth, t1.inst_precision_mth, t1.profit_precision_mth, t1.rate_precision_mth, t1.period_type,t2.huge_redeem_ratio, t2.daily_redeem_vol, t2.max_single_self_booking, t2.max_hold_peoples, t2.min_hold_peoples, t2.max_hold_days, t2.min_hold_days, t2.min_age, t2.max_age, t2.max_real, t2.max_real_off, t2.max_prod_daily_buy_p, t2.max_total_buy_p, t2.max_daily_buy_p, t2.max_hold_amt_p, t2.min_subs_first_amt_p, t2.max_subs_single_amt_p, t2.min_subs_add_amt_p, t2.subs_amt_unit_p, t2.apply_amt_unit_p, t2.min_apply_first_amt_p, t2.min_apply_add_amt_p, t2.max_apply_amt_p, t2.max_prod_daily_redeem_p, t2.max_daily_redeem_p, t2.max_total_redeem_p, t2.min_redeem_single_vol_p, t2.max_redeem_single_vol_p, t2.redeem_vol_unit_p, t2.min_hold_vol_p, t2.max_real_single_amt_p, t2.max_real_daily_amt_p, t2.max_real_single_amt_off_p, t2.max_real_daily_amt_off_p, t2.min_convert_vol_p, t2.max_single_hold_amt_p, t2.max_single_hold_ratio_p, t2.max_prod_daily_buy_m, t2.max_total_buy_m, t2.max_daily_buy_m, t2.max_hold_amt_m, t2.min_subs_first_amt_m, t2.max_subs_single_amt_m, t2.min_subs_add_amt_m, t2.subs_amt_unit_m, t2.apply_amt_unit_m, t2.min_apply_first_amt_m, t2.min_apply_add_amt_m, t2.max_apply_amt_m, t2.max_prod_daily_redeem_m, t2.max_daily_redeem_m, t2.max_total_redeem_m, t2.min_redeem_single_vol_m, t2.max_redeem_single_vol_m, t2.redeem_vol_unit_m, t2.min_hold_vol_m, t2.max_real_single_amt_m, t2.max_real_daily_amt_m, t2.max_real_single_amt_off_m, t2.max_real_daily_amt_off_m, t2.min_convert_vol_m, t2.max_single_hold_amt_m, t2.max_single_hold_ratio_m FROM fina_prod_info t1 LEFT JOIN fina_prod_limit t2 ON t1.prod_code=t2.prod_code AND t1.tano=t2.tano AND t1.legal_code=t2.legal_code where 1=1", SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public SqlResult<FinaProdInfo> findFinaProdInfo(SqlParam<FinaProdInfo> params) throws Exception {
        params.setMakeSql(true);
        return super.findRows("SELECT t1.tano,t1.prod_code,t1.legal_code,t1.prod_name,t1.prod_type FROM fina_prod_info t1 ", SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public UpdateResult addFinaProdInfo(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("INSERT INTO fina_prod_info(tano,prod_code,legal_code,prod_name,prod_short_name,regist_code,parent_code,prod_type,sale_status,prod_risk_level,manager_code,trutee_code,rasie_type,benchmarks,cur,def_div_method,div_chg_flag,face_value,price,nav,nav_date,can_reserve,reserve_begin_date,reserve_invalid_date,subs_begin_date,subs_begin_time,subs_end_date,subs_end_time,establish_date,open_begin_date,open_end_date,actual_end_date,end_date,winding_date,open_time,close_time,discount_code,subs_pay_days,apply_pay_days,redeem_pay_days,subs_payback_days,div_pay_days,fail_pay_days,end_pay_days,proxy_fee_flag,prod_size,service_fee_rate,subs_capital_mode,apply_capital_mode,first_buy_flag,prod_total_vol,convert_status,transfer_agency_status,div_date,equity_regist_date,trans_start_time,trans_end_time,discount_flag,change_days,first_online_date,first_online_time,prod_order,advance_days,advance_begin_time,impawn_frozen_flag,realtime_redeem_flag,close_realredeem_flag,close_realredeem_repay_method,workday_pgm,invest_term,subs_cut_flag,crt_time,upd_time,double_flag,year_days,apply_cfm_days,redeem_cfm_days,expire_mode,export_mode,cool_days,redeem_detail_order,investment,deptno,fee_precision_mth,money_precision_mth,vol_precision_mth,div_precision_mth,inst_precision_mth,profit_precision_mth,rate_precision_mth,period_type)VALUES($S{tano},$S{prodCode},$S{legalCode},$S{prodName},$S{prodShortName},$S{registCode},$S{parentCode},$S{prodType},$S{saleStatus},$S{prodRiskLevel},$S{managerCode},$S{truteeCode},$S{rasieType},$D{benchmarks},$S{cur},$S{defDivMethod},$S{divChgFlag},$D{faceValue},$D{price},$D{nav},$S{navDate},$S{canReserve},$S{reserveBeginDate},$S{reserveInvalidDate},$S{subsBeginDate},$S{subsBeginTime},$S{subsEndDate},$S{subsEndTime},$S{establishDate},$S{openBeginDate},$S{openEndDate},$S{actualEndDate},$S{endDate},$S{windingDate},$S{openTime},$S{closeTime},$S{discountCode},$D{subsPayDays},$D{applyPayDays},$D{redeemPayDays},$D{subsPaybackDays},$D{divPayDays},$D{failPayDays},$D{endPayDays},$S{proxyFeeFlag},$D{prodSize},$D{serviceFeeRate},$S{subsCapitalMode},$S{applyCapitalMode},$S{firstBuyFlag},$D{prodTotalVol},$S{convertStatus},$S{transferAgencyStatus},$S{divDate},$S{equityRegistDate},$S{transStartTime},$S{transEndTime},$S{discountFlag},$D{changeDays},$S{firstOnlineDate},$S{firstOnlineTime},$S{prodOrder},$D{advanceDays},$S{advanceBeginTime},$S{impawnFrozenFlag},$S{realtimeRedeemFlag},$S{closeRealredeemFlag},$S{closeRealredeemRepayMethod},$S{workdayPgm},$D{investTerm},$S{subsCutFlag},NOW(),NOW(),$S{doubleFlag},$S{yearDays},$D{applyCfmDays},$D{redeemCfmDays},$S{expireMode},$S{exportMode},$D{coolDays},$S{redeemDetailOrder},$S{investment},$S{deptno},$S{fee_precision_mth},$S{money_precision_mth},$S{vol_precision_mth},$S{div_precision_mth},$S{inst_precision_mth},$S{profit_precision_mth},$S{rate_precision_mth},$S{period_type})",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult addFinaProdLimit(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("INSERT INTO fina_prod_limit(prod_code,tano,legal_code,huge_redeem_ratio,daily_redeem_vol,max_single_self_booking,max_hold_peoples,min_hold_peoples,max_hold_days,min_hold_days,min_age,max_age,max_real,max_real_off,max_prod_daily_buy_p,max_total_buy_p,max_daily_buy_p,max_hold_amt_p,min_subs_first_amt_p,max_subs_single_amt_p,min_subs_add_amt_p,subs_amt_unit_p,apply_amt_unit_p,min_apply_first_amt_p,min_apply_add_amt_p,max_apply_amt_p,max_prod_daily_redeem_p,max_daily_redeem_p,max_total_redeem_p,min_redeem_single_vol_p,max_redeem_single_vol_p,redeem_vol_unit_p,min_hold_vol_p,max_real_single_amt_p,max_real_daily_amt_p,max_real_single_amt_off_p,max_real_daily_amt_off_p,min_convert_vol_p,max_single_hold_amt_p,max_single_hold_ratio_p,max_prod_daily_buy_m,max_total_buy_m,max_daily_buy_m,max_hold_amt_m,min_subs_first_amt_m,max_subs_single_amt_m,min_subs_add_amt_m,subs_amt_unit_m,apply_amt_unit_m,min_apply_first_amt_m,min_apply_add_amt_m,max_apply_amt_m,max_prod_daily_redeem_m,max_daily_redeem_m,max_total_redeem_m,min_redeem_single_vol_m,max_redeem_single_vol_m,redeem_vol_unit_m,min_hold_vol_m,max_real_single_amt_m,max_real_daily_amt_m,max_real_single_amt_off_m,max_real_daily_amt_off_m,min_convert_vol_m,max_single_hold_amt_m,max_single_hold_ratio_m) VALUES($S{prodCode},$S{tano},$S{legalCode},$D{hugeRedeemRatio},$D{dailyRedeemVol},$D{maxSingleSelfBooking},$D{maxHoldPeoples},$D{minHoldPeoples},$D{maxHoldDays},$D{minHoldDays},$D{minAge},$D{maxAge},$D{maxReal},$D{maxRealOff},$D{maxProdDailyBuyP},$D{maxTotalBuyP},$D{maxDailyBuyP},$D{maxHoldAmtP},$D{minSubsFirstAmtP},$D{maxSubsSingleAmtP},$D{minSubsAddAmtP},$D{subsAmtUnitP},$D{applyAmtUnitP},$D{minApplyFirstAmtP},$D{minApplyAddAmtP},$D{maxApplyAmtP},$D{maxProdDailyRedeemP},$D{maxDailyRedeemP},$D{maxTotalRedeemP},$D{minRedeemSingleVolP},$D{maxRedeemSingleVolP},$D{redeemVolUnitP},$D{minHoldVolP},$D{maxRealSingleAmtP},$D{maxRealDailyAmtP},$D{maxRealSingleAmtOffP},$D{maxRealDailyAmtOffP},$D{minConvertVolP},$D{maxSingleHoldAmtP},$D{maxSingleHoldRatioP},$D{maxProdDailyBuyM},$D{maxTotalBuyM},$D{maxDailyBuyM},$D{maxHoldAmtM},$D{minSubsFirstAmtM},$D{maxSubsSingleAmtM},$D{minSubsAddAmtM},$D{subsAmtUnitM},$D{applyAmtUnitM},$D{minApplyFirstAmtM},$D{minApplyAddAmtM},$D{maxApplyAmtM},$D{maxProdDailyRedeemM},$D{maxDailyRedeemM},$D{maxTotalRedeemM},$D{minRedeemSingleVolM},$D{maxRedeemSingleVolM},$D{redeemVolUnitM},$D{minHoldVolM},$D{maxRealSingleAmtM},$D{maxRealDailyAmtM},$D{maxRealSingleAmtOffM},$D{maxRealDailyAmtOffM},$D{minConvertVolM},$D{maxSingleHoldAmtM},$D{maxSingleHoldRatioM})",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult updateFinaProdInfo(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("UPDATE fina_prod_info " +
                        "SET " +
                        "    prod_name=$S{prodName} ," +
                        "    prod_short_name=$S{prodShortName} ," +
                        "    regist_code=$S{registCode} ," +
                        "    parent_code=$S{parentCode} ," +
                        "    prod_type=$S{prodType} ," +
                        "    sale_status=$S{saleStatus} ," +
                        "    prod_risk_level=$S{prodRiskLevel} ," +
                        "    manager_code=$S{managerCode} ," +
                        "    trutee_code=$S{truteeCode} ," +
                        "    rasie_type=$S{rasieType} ," +
                        "    benchmarks=$D{benchmarks} ," +
                        "    cur=$S{cur} ," +
                        "    def_div_method=$S{defDivMethod} ," +
                        "    div_chg_flag=$S{divChgFlag} ," +
                        "    face_value=$D{faceValue} ," +
                        "    price=$D{price} ," +
                        "    nav=$D{nav} ," +
                        "    nav_date=$S{navDate} ," +
                        "    can_reserve=$S{canReserve} ," +
                        "    reserve_begin_date=$S{reserveBeginDate} ," +
                        "    reserve_invalid_date=$S{reserveInvalidDate} ," +
                        "    subs_begin_date=$S{subsBeginDate} ," +
                        "    subs_begin_time=$S{subsBeginTime} ," +
                        "    subs_end_date=$S{subsEndDate} ," +
                        "    subs_end_time=$S{subsEndTime} ," +
                        "    establish_date=$S{establishDate} ," +
                        "    open_begin_date=$S{openBeginDate} ," +
                        "    open_end_date=$S{openEndDate} ," +
                        "    income_date =$S{incomeDate}, " +
                        "    actual_end_date=$S{actualEndDate} ," +
                        "    end_date=$S{endDate} ," +
                        "    winding_date=$S{windingDate} ," +
                        "    open_time=$S{openTime} ," +
                        "    close_time=$S{closeTime} ," +
                        "    discount_code=$S{discountCode} ," +
                        "    subs_pay_days=$D{subsPayDays} ," +
                        "    apply_pay_days=$D{applyPayDays} ," +
                        "    redeem_pay_days=$D{redeemPayDays} ," +
                        "    subs_payback_days=$D{subsPaybackDays} ," +
                        "    div_pay_days=$D{divPayDays} ," +
                        "    fail_pay_days=$D{failPayDays} ," +
                        "    end_pay_days=$D{endPayDays} ," +
                        "    proxy_fee_flag=$S{proxyFeeFlag} ," +
                        "    prod_size=$D{prodSize} ," +
                        "    service_fee_rate=$D{serviceFeeRate} ," +
                        "    subs_capital_mode=$S{subsCapitalMode} ," +
                        "    apply_capital_mode=$S{applyCapitalMode} ," +
                        "    first_buy_flag=$S{firstBuyFlag} ," +
                        "    prod_total_vol=$D{prodTotalVol} ," +
                        "    convert_status=$S{convertStatus} ," +
                        "    transfer_agency_status=$S{transferAgencyStatus} ," +
                        "    div_date=$S{divDate} ," +
                        "    equity_regist_date=$S{equityRegistDate} ," +
                        "    trans_start_time=$S{transStartTime} ," +
                        "    trans_end_time=$S{transEndTime} ," +
                        "    discount_flag=$S{discountFlag} ," +
                        "    change_days=$D{changeDays} ," +
                        "    first_online_date=$S{firstOnlineDate} ," +
                        "    first_online_time=$S{firstOnlineTime} ," +
                        "    prod_order=$S{prodOrder} ," +
                        "    advance_days=$D{advanceDays} ," +
                        "    advance_begin_time=$S{advanceBeginTime} ," +
                        "    impawn_frozen_flag=$S{impawnFrozenFlag} ," +
                        "    realtime_redeem_flag=$S{realtimeRedeemFlag} ," +
                        "    close_realredeem_flag=$S{closeRealredeemFlag} ," +
                        "    close_realredeem_repay_method=$S{closeRealredeemRepayMethod} ," +
                        "    workday_pgm=$S{workdayPgm} ," +
                        "    invest_term=$D{investTerm} ," +
                        "    subs_cut_flag=$S{subsCutFlag} ," +
                        "    upd_time=now() ," +
                        "    double_flag=$S{doubleFlag} ," +
                        "    year_days=$S{yearDays} ," +
                        "    apply_cfm_days=$D{applyCfmDays} ," +
                        "    redeem_cfm_days=$D{redeemCfmDays} ," +
                        "    expire_mode=$S{expireMode} ," +
                        "    export_mode=$S{exportMode} ," +
                        "    cool_days=$D{coolDays} ," +
                        "    redeem_detail_order=$S{redeemDetailOrder} ," +
                        "    investment=$S{investment} ," +
                        "    deptno=$S{deptno}," +
                        "    fee_precision_mth = $S{feePrecisionMth}, " +
                        "    money_precision_mth = $S{moneyPrecisionMth}, " +
                        "    vol_precision_mth = $S{volPrecisionMth}, " +
                        "    div_precision_mth = $S{divPrecisionMth}, " +
                        "    inst_precision_mth = $S{instPrecisionMth}, " +
                        "    profit_precision_mth = $S{profitPrecisionMth}, " +
                        "    rate_precision_mth = $S{ratePrecisionMth}, " +
                        "    period_type = $S{periodType} " +
                        "WHERE  " +
                        "    tano=$S{tano} AND prod_code=$S{prodCode} AND legal_code=$S{legalCode} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult updateFinaProdLimit(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("UPDATE fina_prod_limit SET huge_redeem_ratio=$D{hugeRedeemRatio} ,daily_redeem_vol=$D{dailyRedeemVol} ,max_single_self_booking=$D{maxSingleSelfBooking} ,max_hold_peoples=$D{maxHoldPeoples} ,min_hold_peoples=$D{minHoldPeoples} ,max_hold_days=$D{maxHoldDays} ,min_hold_days=$D{minHoldDays} ,min_age=$D{minAge} ,max_age=$D{maxAge} ,max_real=$D{maxReal} ,max_real_off=$D{maxRealOff} ,max_prod_daily_buy_p=$D{maxProdDailyBuyP} ,max_total_buy_p=$D{maxTotalBuyP} ,max_daily_buy_p=$D{maxDailyBuyP} ,max_hold_amt_p=$D{maxHoldAmtP} ,min_subs_first_amt_p=$D{minSubsFirstAmtP} ,max_subs_single_amt_p=$D{maxSubsSingleAmtP} ,min_subs_add_amt_p=$D{minSubsAddAmtP} ,subs_amt_unit_p=$D{subsAmtUnitP} ,apply_amt_unit_p=$D{applyAmtUnitP} ,min_apply_first_amt_p=$D{minApplyFirstAmtP} ,min_apply_add_amt_p=$D{minApplyAddAmtP} ,max_apply_amt_p=$D{maxApplyAmtP} ,max_prod_daily_redeem_p=$D{maxProdDailyRedeemP} ,max_daily_redeem_p=$D{maxDailyRedeemP} ,max_total_redeem_p=$D{maxTotalRedeemP} ,min_redeem_single_vol_p=$D{minRedeemSingleVolP} ,max_redeem_single_vol_p=$D{maxRedeemSingleVolP} ,redeem_vol_unit_p=$D{redeemVolUnitP} ,min_hold_vol_p=$D{minHoldVolP} ,max_real_single_amt_p=$D{maxRealSingleAmtP} ,max_real_daily_amt_p=$D{maxRealDailyAmtP} ,max_real_single_amt_off_p=$D{maxRealSingleAmtOffP} ,max_real_daily_amt_off_p=$D{maxRealDailyAmtOffP} ,min_convert_vol_p=$D{minConvertVolP} ,max_single_hold_amt_p=$D{maxSingleHoldAmtP} ,max_single_hold_ratio_p=$D{maxSingleHoldRatioP} ,max_prod_daily_buy_m=$D{maxProdDailyBuyM} ,max_total_buy_m=$D{maxTotalBuyM} ,max_daily_buy_m=$D{maxDailyBuyM} ,max_hold_amt_m=$D{maxHoldAmtM} ,min_subs_first_amt_m=$D{minSubsFirstAmtM} ,max_subs_single_amt_m=$D{maxSubsSingleAmtM} ,min_subs_add_amt_m=$D{minSubsAddAmtM} ,subs_amt_unit_m=$D{subsAmtUnitM} ,apply_amt_unit_m=$D{applyAmtUnitM} ,min_apply_first_amt_m=$D{minApplyFirstAmtM} ,min_apply_add_amt_m=$D{minApplyAddAmtM} ,max_apply_amt_m=$D{maxApplyAmtM} ,max_prod_daily_redeem_m=$D{maxProdDailyRedeemM} ,max_daily_redeem_m=$D{maxDailyRedeemM} ,max_total_redeem_m=$D{maxTotalRedeemM} ,min_redeem_single_vol_m=$D{minRedeemSingleVolM} ,max_redeem_single_vol_m=$D{maxRedeemSingleVolM} ,redeem_vol_unit_m=$D{redeemVolUnitM} ,min_hold_vol_m=$D{minHoldVolM} ,max_real_single_amt_m=$D{maxRealSingleAmtM} ,max_real_daily_amt_m=$D{maxRealDailyAmtM} ,max_real_single_amt_off_m=$D{maxRealSingleAmtOffM} ,max_real_daily_amt_off_m=$D{maxRealDailyAmtOffM} ,min_convert_vol_m=$D{minConvertVolM} ,max_single_hold_amt_m=$D{maxSingleHoldAmtM} ,max_single_hold_ratio_m=$D{maxSingleHoldRatioM}  WHERE  prod_code=$S{prodCode} AND tano=$S{tano} AND legal_code=$S{legalCode}",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult updateFinaProdSaleOff(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("UPDATE fina_prod_info SET prod_base_status='1', upd_time=now() WHERE tano=$S{tano} AND prod_code=$S{prodCode} and legal_code=$S{legalCode}",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult updateFinaProdSaleOn(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("UPDATE fina_prod_info SET prod_base_status='2', upd_time=now() WHERE tano=$S{tano} AND prod_code=$S{prodCode} and legal_code=$S{legalCode}",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }


    public UpdateResult deleteFinaProdInfo(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("DELETE FROM fina_prod_info WHERE  tano=$S{tano} AND prod_code=$S{prodCode} AND legal_code=$S{legalCode} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult deleteFinaProdLimit(SqlParam<FinaProdInfo> params) throws Exception {
        return super.update("DELETE FROM fina_prod_limit WHERE  prod_code=$S{prodCode} AND tano=$S{tano} AND legal_code=$S{legalCode} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

}

