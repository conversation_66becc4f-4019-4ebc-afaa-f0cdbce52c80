package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M553;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M553Dao extends ComnDao {

    public SqlResult<M553> findM553s(SqlParam<M553> param) throws  Exception{
        param.getModel().setLegalCode(null);
        //param.setMakeSql(true);
        String sql1 = "select * from (select t1.tano,\n" +
                "               t1.prod_code,\n" +
                "               t1.deduction_date,\n" +
                "               t1.legal_code,\n" +
                "               count(t1.APP_SERNO) as tra_num,\n" +
                "               count(DISTINCT t1.cust_no) as cust_num,\n" +
                "               sum(t1.app_amt) as total_amt,\n" +
                "               t2.ta_name\n" +
                "          from FINA_CUST_TRANS_REQ_LOG t1 left join fina_ta_info t2 on t1.tano=t2.tano\n" +
                "         where t1.busi_code = '020'\n  " +
                "         group by t1.tano, t1.prod_code, t1.deduction_date, t1.legal_code,t2.ta_name order by t1.DEDUCTION_DATE desc )tt1";
        sql1 = MakeSqlUntil.makeSql(sql1,param.getParams(), M553.class);
        String sql2 = "select * from ( select t1.tano,\n" +
                "               t1.prod_code,\n" +
                "               t1.deduction_date,\n" +
                "               t1.legal_code,\n" +
                "               count(t1.APP_SERNO) as tra_num,\n" +
                "               count(DISTINCT t1.cust_no) as cust_num,\n" +
                "               sum(t1.app_amt) as total_amt,\n" +
                "               t2.ta_name\n" +
                "          from FINA_CUST_TRANS_REQ_LOG_h t1 left join fina_ta_info t2 on t1.tano=t2.tano\n" +
                "         where t1.busi_code = '020'\n" +
                "         group by t1.tano, t1.prod_code, t1.deduction_date, t1.legal_code,t2.ta_name order by t1.DEDUCTION_DATE desc )tt2";
        sql2 = MakeSqlUntil.makeSql(sql2,param.getParams(),M553.class);
        String sql = sql1 +
                "  union all " +
                sql2 ;

        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,param);
    }

    public M553 getPeriodlist(M553 param)throws  Exception {
        String sql = "select periods,begin_date,end_date,income_rate\n"+
                "from fina_prod_periodlist where prod_code = $S{prodCode}";
        return super.findRow(M553.class,sql,SubDatabase.DATABASE_FINA_CENTER,param);
    }
}
