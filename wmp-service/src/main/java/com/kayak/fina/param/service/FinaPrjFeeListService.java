package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.FinaPrjFeeListDao;
import com.kayak.fina.param.model.FinaPrjFeeList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "费用明细服务", model = FinaPrjFeeList.class)
public class FinaPrjFeeListService {

	@Autowired
	private FinaPrjFeeListDao finaPrjFeeListDao;

	@API(desc = "查询费用明细信息", auth = APIAuth.NO)
	public SqlResult<FinaPrjFeeList> findFinaPrjFeeLists(SqlParam<FinaPrjFeeList> params) throws Exception {
		params.setMakeSql(true);
		return finaPrjFeeListDao.findFinaPrjFeeLists(params);
	}

	@API(desc = "添加费用明细", params = "fee_code,dimension1_min,dimension1_max,dimension2_min,dimension2_max,dimension3_min,dimension3_max,rate,max_fee,min_fee,constant_fee,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
	public int addFinaPrjFeeList(SqlParam<FinaPrjFeeList> params) throws Exception {
		return finaPrjFeeListDao.addFinaPrjFeeList(params);
	}
	
	@API(desc = "修改费用明细", params = "fee_code,dimension1_min,dimension1_max,dimension2_min,dimension2_max,dimension3_min,dimension3_max,rate,max_fee,min_fee,constant_fee,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
	public int updateFinaPrjFeeList(SqlParam<FinaPrjFeeList> params) throws Exception {
		return finaPrjFeeListDao.updateFinaPrjFeeList(params);
	}
	
	@API(desc = "删除费用明细", params = "fee_code,dimension1_min,dimension1_max,dimension2_min,dimension2_max,dimension3_min,dimension3_max,rate,max_fee,min_fee,constant_fee,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
	public int deleteFinaPrjFeeList(SqlParam<FinaPrjFeeList> params) throws Exception {
		return finaPrjFeeListDao.deleteFinaPrjFeeList(params);
	}

	@API(desc = "查询费用明细信息", auth = APIAuth.NO)
	public SqlResult<FinaPrjFeeList> findHistory(SqlParam<FinaPrjFeeList> params) throws Exception {
		params.setMakeSql(true);
		return finaPrjFeeListDao.findFinaPrjFeeLists(params);
	}

}
