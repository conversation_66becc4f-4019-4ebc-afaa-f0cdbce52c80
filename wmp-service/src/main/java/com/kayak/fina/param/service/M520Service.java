package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.M520Dao;
import com.kayak.fina.param.model.M520;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@APIDefine(desc = "客户交易申请历史实体类服务", model = M520.class)
public class M520Service {

	@Autowired
	private M520Dao m520Dao;

	@Autowired
	private M215Service m215Service;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M001Dao m001Dao;

	@API(desc = "查询客户交易历史信息", auth = APIAuth.YES)
	public SqlResult<M520> findFinaCustTransReqLogH(SqlParam<M520> params) throws Exception {
		params.getModel().setLegalCode(null);
		SqlResult<M520> sqlResult = m520Dao.findFinaCustTransReqLogH(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		//获取产品名称、TA名称、交易机构名称
		if(null!=sqlResult&&sqlResult.getRows().size()>0){
			List<M520> sqlList=sqlResult.getRows();
			List<String> disdinctedOrgNoList = sqlList.stream().filter(s -> Tools.isNotBlank(s.getTransOrgno())).map(M520::getTransOrgno).distinct().collect(Collectors.toList());
			HashMap<String, String> orgNamesMap = new HashMap<>();
			Map<String,String> map = new HashMap<>();
			for (String orgNo : disdinctedOrgNoList) {
				map.put("orgno",orgNo);
				SqlResult<M001> m001Info = null;
				try {
					m001Info = m001Dao.find(new FetcherData(map, M001.class));
				} catch (Exception e) {
				}
				final List<M001> rows = m001Info.getRows();
				if (rows != null && rows.size() > 0) {
					orgNamesMap.put(orgNo, rows.get(0).getOrgname());
				}
			}

			for(M520 m520:sqlList){
				m520.setTransOrgnoName(orgNamesMap.get(m520.getTransOrgno()));
			}
		}
		return sqlResult;
	}
}
