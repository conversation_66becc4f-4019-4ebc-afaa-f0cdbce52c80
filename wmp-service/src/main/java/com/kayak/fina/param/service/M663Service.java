package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import com.kayak.cust.model.M101;
import com.kayak.fina.param.dao.M663Dao;
import com.kayak.fina.param.model.M663;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "账户类交易流水查询服务", model = M663.class)
public class M663Service {

	@Autowired
	private M663Dao m663Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "账户类交易申请流水查询", auth = APIAuth.YES)
	public SqlResult<M663> findFundProdBasicParam(SqlParam<M663> params) throws Exception {
		SqlResult <M663> sqlResult = m663Dao.findFundProdBasicParam(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
				if(StringUtils.isNotBlank(item.getBranchCode())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getBranchCode());
					map.put("userid",params.getParams().get("userid"));
					SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(dateParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setBranchName(m001Info.getRows().get(0).getOrgname());
					}
				}
				if(StringUtils.isNotBlank(item.getTransOrgno())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getTransOrgno());
					map.put("userid",params.getParams().get("userid"));
					SqlParam<M001> param = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(param);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setSubBranchName(m001Info.getRows().get(0).getOrgname());
					}
				}
			} catch (Exception e) {
				throw new RuntimeException("M663错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
