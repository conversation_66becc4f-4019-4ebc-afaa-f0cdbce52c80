package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M505;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "产品基本参数查询服务", model = M505.class)
public class M505Service {

	@Autowired
	private M215Service m215Service;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询产品基本参数信息", auth = APIAuth.YES)
	public SqlResult<M505> findFinaProdBasicParam(SqlParam<M505> params) throws Exception {

		Map<String, Object> prodParam = new HashMap<>();
		prodParam.put("systemNo", SystemNo.FINA);
		prodParam.put("tano", params.getModel().getTano());
		prodParam.put("prodCode", params.getModel().getProdCode());
		prodParam.put("legalCode", params.getModel().getLegalCode());
		prodParam.put("start", params.getStart());
		prodParam.put("limit", params.getLimit());

		List<Map<String, String>> prodParaList = m215Service.getProdInfoList2(new FetcherData<>(prodParam, M215.class));
		Integer counts = m215Service.findProdIndexInfoCounts(new FetcherData<>(prodParam, M215.class));
		List<M505> rowsList = new ArrayList<>();
		if(params.getLimit() == 0){
			reportformUtil.checkMaxExcel(prodParaList.size());
		}

		for(Map<String, String> prodPara : prodParaList) {

			M505 m505 = new M505();
			m505.setTano(prodPara.get("tano"));	//TA代码
			m505.setProdCode(prodPara.get("prod_code"));	//产品代码
			m505.setProdName(prodPara.get("prod_name"));	//产品名称
			m505.setProdType(prodPara.get("prod_sort"));	//产品类型(实际取的是产品类别)
			m505.setTruteeCode(prodPara.get("trutee_code"));	//托管人代码
			m505.setDefDivMethod(prodPara.get("def_div_method"));	//分红方式
			m505.setEstablishDate(prodPara.get("establish_date"));	//成立日
			m505.setFaceValue(prodPara.get("face_value"));	//产品面值
			m505.setRegistCode(prodPara.get("regist_code"));	//注册登记代码
			m505.setManagerCode(prodPara.get("manager_code"));	//管理人代码
			m505.setWholeFlag(prodPara.get("whole_flag"));	//全量标志
			if(Tools.isNotBlank(prodPara.get("tano"))){
				m505.setTaName(reportformUtil.getFinaTaName(prodPara.get("tano")));
			}
			rowsList.add(m505);
		}

		// 手动分页
		SqlResult<M505> sqlResult = new SqlResult<>();
		/*sqlResult.setRows(
			rowsList.subList(
				params.getStart(), Math.min(params.getStart() + params.getLimit(), rowsList.size())
			)
		);*/
		sqlResult.setResults(counts);
		sqlResult.setRows(rowsList);

		return sqlResult;
	}

	@API(desc = "查询产品基本参数信息", auth = APIAuth.YES)
	public SqlResult<M505> findFinaProd(SqlParam<M505> params) throws Exception {

		Map<String, Object> prodParam = new HashMap<>();
		prodParam.put("systemNo", SystemNo.FINA);
		prodParam.put("supplyCode", params.getModel().getTano());
		prodParam.put("prodCode", params.getModel().getProdCode());
		prodParam.put("legalCode", params.getModel().getLegalCode());


		List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
		List<M505> rowsList = new ArrayList<>();

		for(Map<String, String> prodPara : prodParaList) {

			M505 m505 = new M505();
			m505.setTano(prodPara.get("supply_code"));	//TA代码
			m505.setProdCode(prodPara.get("prod_code"));	//产品代码
			m505.setProdName(prodPara.get("prod_name"));	//产品名称
			m505.setProdType(prodPara.get("prod_type"));	//产品类型
			m505.setTruteeCode(prodPara.get("trutee_code"));	//托管人代码
			m505.setDefDivMethod(prodPara.get("def_div_method"));	//分红方式
			m505.setEstablishDate(prodPara.get("establish_date"));	//成立日
			m505.setFaceValue(prodPara.get("face_value"));	//产品面值
			m505.setRegistCode(prodPara.get("regist_code"));	//注册登记代码
			m505.setManagerCode(prodPara.get("manager_code"));	//管理人代码
			m505.setWholeFlag(prodPara.get("whole_flag"));	//全量标志
			rowsList.add(m505);
		}

		// 手动分页
		SqlResult<M505> sqlResult = new SqlResult<>();
		sqlResult.setRows(rowsList);
		sqlResult.setResults(rowsList.size());

		return sqlResult;
	}
}
