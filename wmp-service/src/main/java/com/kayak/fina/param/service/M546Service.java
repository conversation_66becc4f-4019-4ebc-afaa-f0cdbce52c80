package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M546Dao;
import com.kayak.fina.param.model.M546;
import com.kayak.fina.trans.model.M533;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@APIDefine(desc = "货币T0赎回统计查询", model = M546.class)
public class M546Service {

    @Autowired
    private M546Dao m546Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @Autowired
    private M215Service m215Service;

    @API(desc = "货币T0赎回统计查询", auth = APIAuth.YES)
    public SqlResult<M546> findM546s(SqlParam<M546> param)throws Exception {
        SqlResult<M546> sqlResult =  m546Dao.findM546s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());

        if(sqlResult != null && sqlResult.getRows() != null && sqlResult.getRows().size() > 0){
            for(M546 m546:sqlResult.getRows()){
               // m546.setTaName(reportformUtil.getFinaTaName(m546.getTano()));
                Map<String, String> prodInfoMap =m215Service.getProdInfo(m546.getLegalCode(), SystemNo.FINA,m546.getTano(),m546.getProdCode());
                if(prodInfoMap != null && prodInfoMap.size() > 0){
                    m546.setProdName(prodInfoMap.get("prod_name"));
                }
            }
        }
        return sqlResult;
    }
}
