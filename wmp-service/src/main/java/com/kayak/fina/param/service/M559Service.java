package com.kayak.fina.param.service;

import com.alibaba.fastjson.JSON;
import com.hundsun.jrescloud.common.exception.BaseBizException;
import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.M559Dao;
import com.kayak.fina.param.model.M559;
import com.kayak.prod.service.M215Service;
import com.kayakwise.fina.api.T545DubboDecorator;
import com.kayakwise.fina.api.T554DubboDecorator;
import com.kayakwise.fina.req.T554ServiceRequest;
import com.kayakwise.fina.resp.T554ServiceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "资金流水对账差错表实体类服务", model = M559.class)
public class M559Service {
	protected static final Logger log = LoggerFactory.getLogger(M559Service.class);

	@Autowired
	private M559Dao m559Dao;

	@CloudReference
	private T545DubboDecorator t545ServiceDecorator;

	@CloudReference
	private T554DubboDecorator t554ServiceDecorator;

	@Autowired
	private M215Service m215Service;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询资金流水对账差错表实体类信息", auth = APIAuth.YES)
	public SqlResult<M559> findM559s(SqlParam<M559> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<M559> sqlResult = m559Dao.findM559s(params);
		//获取产品名称、TA名称
		if(null!=sqlResult&&sqlResult.getRows().size()>0){
			List<M559> sqlList=sqlResult.getRows();
			for(M559 m559:sqlList){
				Map<String, String> prodInfoMap = m215Service.getProdInfo(m559.getLegalCode(), SystemNo.FINA,m559.getTano(),m559.getProdCode());
				if(prodInfoMap != null && prodInfoMap.size() > 0){
					m559.setProdName(prodInfoMap.get("prod_name"));
				}
				if(null!=m559){
					m559.setTaName(reportformUtil.getFinaTaName(m559.getTano()));
				}
			}
		}
		return sqlResult;
	}

	@API(desc = "调账",operation = APIOperation.UPDATE)
	public String dealError(SqlParam<M559> params) throws Exception {
		//获取管理台选中的数据
		String appSerno = params.getModel().getAppSerno();
		T554ServiceRequest t554ServiceRequest = new T554ServiceRequest();
		t554ServiceRequest.setAppSerno(appSerno);
		T554ServiceResponse t554ServiceResponse;
		try{
			t554ServiceResponse = t554ServiceDecorator.execute(t554ServiceRequest);
		}catch (BaseBizException e){
			return RequestSupport.updateReturnJson(false, e.getErrorMessage(), null).toString();
		}
		String rtnCode = t554ServiceResponse.getRtnCode();
		// 若modifySucessFlag为0，表示新增/修改失败
		if (!"000000".equals(t554ServiceResponse.getRtnCode())) {
			String rtnDesc = t554ServiceResponse.getRtnDesc();
			if (rtnDesc != null) {
				throw new PromptException("操作调账失败："+t554ServiceResponse.getRtnDesc());
			}
		}
		return RequestSupport.updateReturnJson(true, "操作调账完成", null).toString();
	}

	private byte[] changeMapToByte(Map<String,Object> map) throws Exception {

		byte[] bytes = null;
		try {
			bytes = JSON.toJSONString(map, true).getBytes();
		} catch (Exception e) {
			log.error("map到byte[]转换异常");
			throw new Exception("map到byte[]转换异常");
		}

		return bytes;
	}

}
