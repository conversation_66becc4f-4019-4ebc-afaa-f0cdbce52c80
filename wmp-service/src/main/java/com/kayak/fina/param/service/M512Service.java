package com.kayak.fina.param.service;

import com.kayak.core.sql.SqlRow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M512Dao;
import com.kayak.fina.param.model.M512;

@Service
@APIDefine(desc = "管理人信息实体类服务", model = M512.class)
public class M512Service {

	@Autowired
	private M512Dao m512Dao;

	@API(desc = "查询管理人信息实体类信息", auth = APIAuth.YES)
	public SqlResult<M512> findFinaManagerInfos(SqlParam<M512> params) throws Exception {
		params.setMakeSql(true);
		return m512Dao.findFinaManagerInfos(params);
	}

	@API(desc = "查询管理人下拉")
	public SqlResult<SqlRow> queryDict(SqlParam<M512> params) throws Exception {
		return m512Dao.queryDict(params);
	}

	@API(desc = "添加管理人信息实体类", params = "manager_code,legal_code,manager_name,address,connector,telno,remark,update_time", auth = APIAuth.NO)
	public int addFinaManagerInfo(SqlParam<M512> params) throws Exception {
		return m512Dao.addFinaManagerInfo(params).getEffect();
	}
	
	@API(desc = "修改管理人信息实体类", params = "manager_code,legal_code,manager_name,address,connector,telno,remark,update_time", auth = APIAuth.NO)
	public int updateFinaManagerInfo(SqlParam<M512> params) throws Exception {
		return m512Dao.updateFinaManagerInfo(params).getEffect();
	}
	
	@API(desc = "删除管理人信息实体类", params = "manager_code,legal_code,manager_name,address,connector,telno,remark,update_time", auth = APIAuth.NO)
	public int deleteFinaManagerInfo(SqlParam<M512> params) throws Exception {
		return m512Dao.deleteFinaManagerInfo(params).getEffect();
	}

}
