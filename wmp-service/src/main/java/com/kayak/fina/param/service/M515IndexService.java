package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.DataStatus;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.M515DataDao;
import com.kayak.fina.param.dao.M515IndexDao;
import com.kayak.fina.param.model.M514;
import com.kayak.fina.param.model.M515Index;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 鎺ュ彛绱㈠紩涓庢暟鎹枃浠剁鐞嗭紙閿€鍞晢瀵煎叆绱㈠紩鏂囦欢琛級锛屽師鍚?Ta2016IndexService
 */
@Service
@APIDefine(desc = "鏂囦欢绱㈠紩", model = M514.class)
@RequiredArgsConstructor
@Slf4j
public class M515IndexService {
    @Autowired
    private M515IndexDao m515IndexDao;

    @Autowired
    private M515DataDao m515DataDao;

    @API(desc = "绱㈠紩鏂囦欢鏌ヨ", auth = APIAuth.NO)
    public SqlResult<M515Index> findM515Index(SqlParam<M515Index> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M515Index> sqlResult= m515IndexDao.findTaInterfaceIndexFileDao(params);
        return sqlResult;
    }


    @API(desc = "绱㈠紩鏂囦欢娣诲姞", params = "file_id, filename, busi_code, index_no, name_rules, file_type, is_skip, is_vaildate_profile, datasource, data_status, create_time, create_user, update_time, update_user", auth = APIAuth.YES)
    public String addM515Index(SqlParam<M515Index> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛
        String loginname = (String) params.getAuthInfo().get("userid");
        params.getModel().setCreateUser(loginname);
        params.getModel().setUpdateUser((loginname));

        //params.getModel().setDirection("I");
        params.getModel().setDataStatus(DataStatus.EFFECTED);
        SqlResult<M515Index> result = m515IndexDao.findTaInterfaceIndexFileByNo(params);
        SqlResult<M515Index> resultName = m515IndexDao.findTaInterfaceIndexFileName(params);
        if(resultName.getRows().size()==1){
            return RequestSupport.updateReturnJson(false, "鍚嶅瓧閲嶅", null).toString();
        }
        if(result.getRows().size() == 0){
            m515IndexDao.addTaInterfaceIndexFileDao(params);
            return RequestSupport.updateReturnJson(true, "鎿嶄綔鎴愬姛", null).toString();
        }else if(result.getRows().size() == 1){
            return RequestSupport.updateReturnJson(false, "娣诲姞閲嶅", null).toString();
        }
        return RequestSupport.updateReturnJson(false, "娣诲姞澶辫触", null).toString();
    }



    @API(desc = "绱㈠紩鏂囦欢鏇存柊", params = "file_id ,filename ,busi_code , datasource , data_status ,update_time ,update_user ", auth = APIAuth.YES)
    public String updateM515Index(SqlParam<M515Index> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛
        params.getModel().setUpdateUser(((String) params.getAuthInfo().get("userid")));
        params.getModel().setDataStatus(DataStatus.EFFECTED);

        UpdateResult result =  m515IndexDao.updateTaInterfaceIndexFileDao(params);
        return RequestSupport.updateReturnJson(true, "鏇存柊鎴愬姛", null).toString();
    }


    @API(desc = "绱㈠紩鏂囦欢鍒犻櫎", params = "file_id", auth = APIAuth.YES)
    public String deleteM515Index(SqlParam<M515Index> params) throws Exception {
        UpdateResult result = m515IndexDao.deleteTaInterfaceIndexFileDao(params);
        m515DataDao.deleteTaInterfaceDataFileByNo(params);
        return RequestSupport.updateReturnJson(true, "鍒犻櫎鎴愬姛", null).toString();
    }


    @API(desc = "鏌ユ壘鏂囦欢绱㈠紩",  auth = APIAuth.YES)
    public SqlResult<M515Index> findIndexNoNotInFileList(SqlParam<M515Index> params) throws Exception {

        return m515IndexDao.findIndexNoNotInFileList(params);
    }
}


