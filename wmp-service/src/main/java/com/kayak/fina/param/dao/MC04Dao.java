package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.MC04;
import org.springframework.stereotype.Repository;

@Repository
public class MC04Dao extends ComnDao {
    public SqlResult<MC04> findMC04s(SqlParam<MC04> params) throws Exception {
        return super.findRows(" SELECT * FROM TRFR_ERROR_LOG ",
                SubDatabase.DATABASE_FINA_CENTER,params);
    }

    public UpdateResult deal(MC04 mc04) throws Exception {
        UpdateResult updateResult = super.update("UPDATE TRFR_ERROR_LOG SET trfr_error_mode=$S{trfrErrorMode} ,trfr_error_status=$S{trfrErrorStatus}   WHERE  error_serno=$S{errorSerno}",
                SubDatabase.DATABASE_FINA_CENTER,mc04);
        return updateResult;
    }
}
