package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M560Dao;
import com.kayak.fina.param.model.M560;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财老客信息查询", model = M560.class)
public class M560Service {

    @Autowired
    private M560Dao m560Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财老客信息查询", auth = APIAuth.YES)
    public SqlResult<M560> findM560(SqlParam<M560> params) throws Exception {
        params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),""));
        params.getModel().setIdCode(null);
        params.getModel().setIdType(null);
        params.getModel().setCustName(null);
        params.setMakeSql(true);
        SqlResult<M560> sqlResult = m560Dao.findM560(params);
        List<M560> list = sqlResult.getRows();
        if (list != null && list.size() > 0){
            for (M560 m560 : list){
                M560 cust = m560Dao.getCusts(m560.getCustNo());
                if (cust != null){
                    m560.setCustName(cust.getCustName());
                    m560.setIdType(cust.getIdType());
                    m560.setIdCode(cust.getIdCode());
                }
                if(StringUtils.isNotBlank(m560.getTransOrgno())){
                    Map<String,Object> map = new HashMap<>();
                    map.put("orgno",m560.getTransOrgno());
                    SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
                    SqlResult<M001> m001Info = m001Dao.find(dateParams);
                    if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
                        m560.setTransOrgname(m001Info.getRows().get(0).getOrgname());
                    }
                }
            }

        }
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

}
