package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M547Dao;
import com.kayak.fina.param.model.M547;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财产品存量统计表-按交易机构", model = M547.class)
public class M547Service {

    @Autowired
    private M547Dao m547Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "理财产品存量统计表-按交易机构", auth = APIAuth.YES)
    public SqlResult<M547> findM547s(SqlParam<M547> param)throws Exception {
        SqlResult<M547> sqlResult = m547Dao.findM547s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M547> volList = sqlResult.getRows();
        for (M547 m547 : volList){
            M001 m001 = m001Dao.get(m547.getOrgno());
            if (m001 != null){
                m547.setOrgName(m001.getOrgname());
            }
            //m547.setTaName(reportformUtil.getFinaTaName(m547.getTano()));
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m547.getProdCode());
            prodParam.put("supplyCode", m547.getTano());
            prodParam.put("legalCode", "1000");
            prodParam.put("userid",param.getAuthInfo().get("userid"));
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                m547.setProdName(prodPara.get("prod_name"));
            }
        }
        return sqlResult;
    }
}
