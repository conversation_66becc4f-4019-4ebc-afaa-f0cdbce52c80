package com.kayak.fina.param.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.M511;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;

/**
 * 托管行管理, 原名 FinaTruteeInfoService
 */
@Repository
public class M511Dao extends ComnDao {

	public SqlResult<M511> findFinaTruteeInfos(SqlParam<M511> params) throws Exception {
		return super.findRows("SELECT 'FINA' as system_no, trutee_code,legal_code,trutee_name,account_version,name_format,fax FROM fina_trutee_info", SubDatabase.DATABASE_FINA_CENTER,params);
	}

	public SqlResult<SqlRow> findFinaTrutee() throws Exception {
		List<SqlRow> list = super.findRows(SqlRow.class,"SELECT trutee_code as value,trutee_name as label FROM fina_trutee_info", SubDatabase.DATABASE_FINA_CENTER,null);
		SqlResult<SqlRow> sqlResult = new SqlResult();
		sqlResult.setRows(list);
		return sqlResult;
	}

	public UpdateResult addFinaTruteeInfo(SqlParam<M511> params) throws Exception {
		return super.update("INSERT INTO fina_trutee_info(trutee_code,legal_code,trutee_name,account_version,name_format,fax)" +
						" VALUES($S{truteeCode},$S{legalCode},$S{truteeName},$S{accountVersion},$S{nameFormat},$S{fax})",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult updateFinaTruteeInfo(SqlParam<M511> params) throws Exception {
		return super.update("UPDATE fina_trutee_info SET trutee_name=$S{truteeName} ,account_version=$S{accountVersion} ,name_format=$S{nameFormat} ,fax=$S{fax}  WHERE  trutee_code=$S{truteeCode} AND legal_code=$S{legalCode} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult deleteFinaTruteeInfo(SqlParam<M511> params) throws Exception {
		return super.update("DELETE FROM fina_trutee_info WHERE  trutee_code=$S{truteeCode} AND legal_code=$S{legalCode} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}

}
