package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.common.constants.SubDatabase;
import com.kayak.fina.param.model.M510;
import com.kayak.fina.param.model.ProdAcctInfo;
import com.kayak.fina.param.model.ProdAllAcctInfo;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class ProdAllAcctInfoDao extends ComnDao {

    /** 查询账号类型、银行账号、银行账号名称 **/
    public SqlResult<ProdAcctInfo> queryProdAcctInfo(SqlParam<ProdAcctInfo> params) throws Exception {
        return super.findRows("SELECT prod_acct_type,acct_no,acct_name FROM fina_prod_acct_info f1 LEFT JOIN fina_acct_info f2 on f1.acct_serno = f2.acct_serno WHERE f1.prod_code = $S{prodCode} AND f1.tano = $S{tano} AND (f1.legal_code = $S{legalCode}  )  "
                , SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /** 查询银行账号编号和名称 **/
    public SqlResult queryFinaAcctNoAndName(SqlParam<M510> params) throws Exception {
        String sql = "SELECT acct_serno,acct_no, acct_name FROM fina_acct_info ";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
    }

    /** 新增产品账号信息 **/
    public UpdateResult addProdAcctInfo(Map<String, Object> params) throws Exception {
        return super.update("INSERT INTO fina_prod_acct_info(prod_code,tano,legal_code,prod_acct_type,acct_serno) VALUES($S{prodCode},$S{tano},$S{legalCode},$S{prodAcctType},$S{acctSerno})"
                , SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /** 删除产品账号信息 **/
    public UpdateResult deleteProdAcctInfo(SqlParam<ProdAllAcctInfo> params) throws Exception {
        return super.update("DELETE FROM fina_prod_acct_info WHERE prod_code=$S{prodCode} AND tano=$S{tano} AND (legal_code = $S{legalCode} )"
                , SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

}
