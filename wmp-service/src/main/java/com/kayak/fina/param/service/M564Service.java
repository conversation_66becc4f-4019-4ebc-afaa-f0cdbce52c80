package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M564Dao;
import com.kayak.fina.param.model.M564;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@APIDefine(desc = "客户份额历史查询", model = M564.class)
public class M564Service {

    @Resource
    private M564Dao m564Dao;

    public SqlResult<M564> findM564(SqlParam<M564> params) throws Exception {
        return m564Dao.findM564(params);
    }

}
