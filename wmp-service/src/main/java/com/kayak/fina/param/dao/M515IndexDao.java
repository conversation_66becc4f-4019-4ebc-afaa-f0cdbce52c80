package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.global.utils.Tools;
import com.kayak.fina.param.model.M515Index;
import com.kayak.fina.param.model.M514;
import org.springframework.stereotype.Repository;


/**
 * 描述： 索引文件dao，原名 TaInterfaceIndexFileDao
 * 创建人： cy
 *
 */
@Repository
public class M515IndexDao extends ComnDao {

    /**
     *
     * @param params 传入参数
     * @return  查询结果
     * @throws Exception
     */
    public SqlResult<M515Index> findTaInterfaceIndexFileDao(SqlParam<M515Index> params) throws Exception {
        params.getModel().setDataStatus(DataStatus.EFFECTED);

        return super.findRows(
                "SELECT t1.index_no,\n" +
                        "       t1.index_name,\n" +
                        "       t1.direction,\n" +
                        "       t1.name_rules,\n" +
                        "       t1.is_vaildate_profile,\n" +
                        "       t2.task_name,\n" +
                        "       t1.task_id,t1.system_no\n" +
                        "  FROM wmp_interface_index_file t1\n" +
                        "  LEFT JOIN wmp_batch_task_info t2\n" +
                        "    ON t1.task_id = t2.task_id", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /**
     *
     * @param params 传入参数
     * @return 查询结果
     * @throws Exception
     */
    public SqlResult<M515Index> findTaInterfaceIndexFileByNo(SqlParam<M515Index> params) throws Exception {
        return super.findRows("SELECT index_no, index_name, direction, name_rules, is_vaildate_profile, create_time, create_user, update_time, update_user, data_status,system_no from wmp_interface_index_file where index_no = $S{indexNo}", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /**
     *
     * @param params 传入参数
     * @return 查询结果
     * @throws Exception
     */
    public SqlResult<M515Index> findTaInterfaceIndexFileName(SqlParam<M515Index> params) throws Exception {
        return super.findRows("SELECT index_no, index_name, direction, name_rules, is_vaildate_profile, create_time, create_user, update_time, update_user, data_status,system_no from wmp_interface_index_file where index_name = $S{indexName}", SubDatabase.DATABASE_FINA_CENTER, params);
    }




    /**
     *
     * @param params 传入参数
     * @return   添加是否成功
     * @throws Exception
     */
    public UpdateResult addTaInterfaceIndexFileDao(SqlParam<M515Index> params) throws Exception {

        // 索引名字，直接采用毫秒值，16位长度
        params.getModel().setIndexNo(Tools.getSeqValue());

        String sqlAll = "INSERT INTO wmp_interface_index_file\n" +
                "  (index_no,\n" +
                "   index_name,\n" +
                "   direction,\n" +
                "   name_rules,\n" +
                "   is_vaildate_profile,\n" +
                "   task_id,\n" +
                "   create_time,\n" +
                "   create_user,\n" +
                "   update_time,\n" +
                "   update_user,\n" +
                "   data_status,system_no)\n" +
                "VALUES\n" +
                "  ($S{indexNo},\n" +
                "   $S{indexName},\n" +
                "   $S{direction},\n" +
                "   $S{nameRules},\n" +
                "   $S{isVaildateProfile},\n" +
                "   $S{taskId},\n" +
                "   current_timestamp,\n" +
                "   $S{createUser},\n" +
                "   current_timestamp,\n" +
                "   $S{updateUser},\n" +
                "   $S{dataStatus},$S{systemNo})\n";
        String sqlDb2 = "INSERT INTO wmp_interface_index_file\n" +
                "  (index_no,\n" +
                "   index_name,\n" +
                "   direction,\n" +
                "   name_rules,\n" +
                "   is_vaildate_profile,\n" +
                "   task_id,\n" +
                "   create_time,\n" +
                "   create_user,\n" +
                "   update_time,\n" +
                "   update_user,\n" +
                "   data_status,system_no)\n" +
                "VALUES\n" +
                "  ($S{indexNo},\n" +
                "   $S{indexName},\n" +
                "   $S{direction},\n" +
                "   $S{nameRules},\n" +
                "   $S{isVaildateProfile},\n" +
                "   $S{taskId},\n" +
                "   current timestamp,\n" +
                "   $S{createUser},\n" +
                "   current timestamp,\n" +
                "   $S{updateUser},\n" +
                "   $S{dataStatus},$S{systemNo})\n";
        Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2).mysqlSql(sqlAll);
        return super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     *
     * @param params 传入参数
     * @return  更新是否成功
     * @throws Exception
     */
    public UpdateResult updateTaInterfaceIndexFileDao(SqlParam<M515Index> params) throws Exception {

        String sqlAll = "UPDATE wmp_interface_index_file\n" +
                "   set index_name          = $S{indexName},\n"+
                "       name_rules          = $S{nameRules},\n" +
                "       is_vaildate_profile = $S{isVaildateProfile},\n" +
                "       direction           = $S{direction},\n" +
                "       task_id             = $S{taskId},\n" +
                "       update_time         = current_timestamp,\n" +
                "       update_user         = $S{updateUser},\n" +
                "       data_status         = $S{dataStatus},\n" +
                "       system_no         = $S{systemNo}\n" +
                " WHERE index_no = $S{indexNo}\n";
        String sqlDb2 = "UPDATE wmp_interface_index_file\n" +
                "   set index_name          = $S{indexName},\n"+
                "       name_rules          = $S{nameRules},\n" +
                "       is_vaildate_profile = $S{isVaildateProfile},\n" +
                "       direction           = $S{direction},\n" +
                "       task_id             = $S{taskId},\n" +
                "       update_time         = current timestamp,\n" +
                "       update_user         = $S{updateUser},\n" +
                "       data_status         = $S{dataStatus},\n" +
                "       system_no         = $S{systemNo}\n" +
                " WHERE index_no = $S{indexNo}\n";
        Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2).mysqlSql(sqlAll);
        return super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     *
     * @param params 文件索引号
     * @return  删除是否成功
     * @throws Exception
     */
    public UpdateResult deleteTaInterfaceIndexFileDao(SqlParam<M515Index> params) throws Exception {
        return super.update("DELETE FROM wmp_interface_index_file where index_no = $S{indexNo}", SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     *
     * @param params 文件索引号
     * @return  删除是否成功
     * @throws Exception
     */
    public UpdateResult deleteTaInterfaceIndexFileByInfo(SqlParam<M514> params) throws Exception {
        return super.update("DELETE FROM wmp_interface_index_file where index_no = $S{indexNo}", SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     *
     * @param params
     * @return  查询索引表存在的，而file_list表不存在的indexNo
     * @throws Exception
     */
    public SqlResult<M515Index> findIndexNoNotInFileList(SqlParam<M515Index> params) throws Exception {
        return super.findRows("SELECT index_no,index_name from wmp_interface_index_file t where t.index_no not in (" +
                "select index_no from wmp_interface_index_list where interface_id = $S{interfaceId}) and t.system_no = $S{systemNo}", SubDatabase.DATABASE_FINA_CENTER, params);
    }
}

