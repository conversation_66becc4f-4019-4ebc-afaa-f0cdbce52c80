package com.kayak.fina.param.service;

import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.component.RedisUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M509Dao;
import com.kayak.fina.param.model.M509;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayakwise.prod.in.T289DubboDecorator;
import com.kayakwise.prod.req.T289ServiceRequest;
import com.kayakwise.wmp.base.util.Tools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@APIDefine(desc = "理财参数管理实体类服务", model = M215.class)
public class M509Service {

    @Autowired
    private M509Dao m509Dao;

    @Autowired
    RedisUtil redisUtil;
    @CloudReference
    private T289DubboDecorator t289DubboDecorator;


    @API(desc = "查询理财参数管理实体类信息", auth = APIAuth.NO)
    public SqlResult<M509> findSysParams(SqlParam<M509> params) throws Exception {
        params.setMakeSql(true);
        return m509Dao.findSysParams(params);
    }

    @API(desc = "添加理财参数管理实体类", params = "paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay,FIELDTYPE,ACTION,GRAPHQL", auth = APIAuth.NO)
    public int addSysParam(SqlParam<M509> params) throws Exception {
        if ("收市时间".equals(params.getModel().getParaname())) {
            Map<String, Object> map = new HashMap<>();
            map.put("paraname", "闭市时间");
            SqlParam<M509> sqlParam = new FetcherData<M509>(map, M509.class);
            SqlResult<M509> sqlResult = m509Dao.findSysParams(sqlParam);
            if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0) {
                String bssj = sqlResult.getRows().get(0).getParavalue();
                if (Integer.parseInt(params.getModel().getParavalue()) >= Integer.parseInt(bssj)) {
                    throw new PromptException("收市时间必须小于闭市时间");
                }
            }
        }
        if ("闭市时间".equals(params.getModel().getParaname())) {
            Map<String, Object> map = new HashMap<>();
            map.put("paraname", "收市时间");
            SqlParam<M509> sqlParam = new FetcherData<M509>(map, M509.class);
            SqlResult<M509> sqlResult = m509Dao.findSysParams(sqlParam);
            if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0) {
                String sssj = sqlResult.getRows().get(0).getParavalue();
                if (Integer.parseInt(params.getModel().getParavalue()) < Integer.parseInt(sssj)) {
                    throw new PromptException("收市时间必须小于闭市时间");
                }
            }
        }
        return m509Dao.addSysParam(params).getEffect();
    }

    @API(desc = "修改理财参数管理实体类", params = "paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay,FIELDTYPE,ACTION,GRAPHQL", auth = APIAuth.NO)
    public int updateSysParam(SqlParam<M509> params) throws Exception {
        if ("收市时间".equals(params.getModel().getParaname())) {
            Map<String, Object> map = new HashMap<>();
            map.put("paraname", "闭市时间");
            SqlParam<M509> sqlParam = new FetcherData<M509>(map, M509.class);
            SqlResult<M509> sqlResult = m509Dao.findSysParams(sqlParam);
            if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0) {
                String bssj = sqlResult.getRows().get(0).getParavalue();
                if (Integer.parseInt(params.getModel().getParavalue()) >= Integer.parseInt(bssj)) {
                    throw new PromptException("收市时间必须小于闭市时间");
                }
            }
        }
        if ("闭市时间".equals(params.getModel().getParaname())) {
            Map<String, Object> map = new HashMap<>();
            map.put("paraname", "收市时间");
            SqlParam<M509> sqlParam = new FetcherData<M509>(map, M509.class);
            SqlResult<M509> sqlResult = m509Dao.findSysParams(sqlParam);
            if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0) {
                String sssj = sqlResult.getRows().get(0).getParavalue();
                if (Integer.parseInt(params.getModel().getParavalue()) < Integer.parseInt(sssj)) {
                    throw new PromptException("收市时间必须小于闭市时间");
                }
            }
        }
        int updateSysParam = m509Dao.updateSysParam(params).getEffect();
        // 存入 redis
        if (null != params.getModel()) {
            // key
            String key = "wmp:cache:param:" + params.getModel().getModuleid() + ":" + params.getModel().getParaid() + ":getParavalByParaid";
            // 对象转json存储
            String value = params.getModel().getParavalue();
            // 存储
            redisUtil.set(key, value.replace("\"", ""));

            try {
                if (Tools.valueIn(params.getModel().getParaid(), "9999999999", "9999999998")) {
                    t289DubboDecorator.asyncExecute(new T289ServiceRequest() {{
                        setChannelSerno("web9999999999" + Tools.dt2yyyyMMddHHmmss(new Date()));
                    }});
                }
            } catch (Exception e) {
                log.info("报错信息:{}", e);
            }
        }


        return updateSysParam;
    }

    @API(desc = "删除理财参数管理实体类", params = "paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay,FIELDTYPE,ACTION,GRAPHQL", auth = APIAuth.NO)
    public int deleteSysParam(SqlParam<M509> params) throws Exception {
        return m509Dao.deleteSysParam(params).getEffect();
    }

    @API(desc = "查询理财代销参数系统休市时间", auth = APIAuth.YES)
    public SqlResult<M509> findParaValue(SqlParam<M509> params) throws Exception {
        params.setMakeSql(true);
        return m509Dao.findParaValue(params);
    }

}
