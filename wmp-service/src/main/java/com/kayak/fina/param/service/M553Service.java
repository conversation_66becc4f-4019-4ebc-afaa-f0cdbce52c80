package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M553Dao;
import com.kayak.fina.param.model.M553;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财销售汇总表-按产品", model = M553.class)
public class M553Service {

    @Autowired
    private M553Dao m553Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "理财销售汇总表-按产品", auth = APIAuth.YES)
    public SqlResult<M553> findM553s(SqlParam<M553> param)throws Exception {
        SqlResult<M553> sqlResult = m553Dao.findM553s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M553> volList = sqlResult.getRows();
        for (M553 m553 : volList){
            //M553 period = m553Dao.getPeriodlist(m553);
            //if (period != null){
            //    m553.setPeriods(period.getPeriods());
            //    m553.setSubsBeginDate(period.getSubsBeginDate());
            //    m553.setSubsEndDate(period.getSubsEndDate());
            //    m553.setBenchmarks(period.getBenchmarks());
            //}
            //m553.setTaName(reportformUtil.getFinaTaName(m553.getTano()));
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m553.getProdCode());
            prodParam.put("supplyCode", m553.getTano());
            prodParam.put("legalCode", m553.getLegalCode());
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                String benchmarks = prodPara.get("benchmarks");
                if (StringUtils.isNotBlank(benchmarks)) {
                    DecimalFormat decimalFormat = new DecimalFormat("0.00%");
                    m553.setBenchmarks(decimalFormat.format(Double.parseDouble(benchmarks)));
                }
                m553.setProdName(prodPara.get("prod_name"));
                m553.setProdStatus(prodPara.get("prod_status"));
                //投资期限
                //m553.setInvestTerm(prodPara.get("invest_term"));
                m553.setWindingDate(prodPara.get("winding_date"));
                m553.setEstablishDate(prodPara.get("establish_date"));
                m553.setSubsBeginDate(prodPara.get("subs_begin_date"));
                m553.setSubsEndDate(prodPara.get("subs_end_date"));
            }
        }
        return sqlResult;
    }
}
