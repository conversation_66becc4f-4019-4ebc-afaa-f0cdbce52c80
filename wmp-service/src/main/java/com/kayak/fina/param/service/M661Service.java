package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.fina.param.dao.M661Dao;
import com.kayak.fina.param.model.M661;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "客户冻结明细实体类服务", model = M661.class)
public class M661Service {

	@Autowired
	private M661Dao m661Dao;

	@Autowired
	private M101Dao m101Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M215Service m215Service;


	@API(desc = "查询客户冻结明细", auth = APIAuth.YES)
	public SqlResult<M661> findFinaCustFreezeDetails(SqlParam<M661> params) throws Exception {

		//如果是查询条件进来，必须预先判断是否存在客户信息
		if(Tools.isBlank(params.getModel().getCustNo())){
			params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()));
		}
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		params.getModel().setAcctNo(null);
		//查询客户冻结表信息
		SqlResult<M661> sr1 = m661Dao.findFinaCustFreezeDetails(params);
		reportformUtil.checkMaxExcel(sr1.getRows().size());
		//配置查询客户信息的SqlParam
		SqlParam<M101> custParams = null;
		try {
			custParams = new FetcherData<>(new HashMap<>(), M101.class);
			BeanUtils.copyProperties(params.getModel(),custParams.getModel());
		} catch (Exception exception) {
			exception.printStackTrace();
		}
		if (sr1 != null && sr1.getRows().size() > 0){
			for (M661 bean1 : sr1.getRows()) {
				/**bean1.setTaName(reportformUtil.getFinaTaName(bean1.getTano()));
				M661 bean2 = m661Dao.getCusts(bean1.getCustNo());
				if (bean2 != null){
					bean1.setCustName(bean2.getCustName());
					bean1.setIdType(bean2.getIdType());
					bean1.setIdCode(bean2.getIdCode());
					bean1.setAcctNo(bean2.getAcctNo());
				}*/
				Map<String, Object> prodParam = new HashMap<>();
				prodParam.put("systemNo", SystemNo.FINA);
				prodParam.put("prodCode", bean1.getProdCode());
				prodParam.put("supplyCode", bean1.getTano());
				prodParam.put("legalCode", "1000");
				prodParam.put("userid",params.getParams().get("userid"));
				List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
				if (prodParaList != null && prodParaList.size() > 0){
					Map<String, String> prodPara = prodParaList.get(0);
					bean1.setProdName(prodPara.get("prod_name"));
				}
			}
		}
		sr1.setDesensitized(false);
		return sr1;
	}
}
