package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.ProdAcctInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProdAcctInfoDao extends ComnDao {

    /** 查询账号类型、银行账号、银行账号名称 **/
    public SqlResult<ProdAcctInfo> queryProdAcctInfo(SqlParam<ProdAcctInfo> params) throws Exception {
        if ("FINA".equals(params.getModel().getSystemNo())){
            return super.findRows("SELECT f1.tano, f1.prod_code, f1.legal_code, f1.prod_acct_type, f1.acct_serno, f2.acct_no, f2.acct_name, f1.remark, f2.open_bank" +
                            " FROM fina_prod_acct_info f1 LEFT JOIN fina_acct_info f2 ON f1.acct_serno = f2.acct_serno" +
                            " WHERE f1.prod_code = $S{prodCode} AND f1.tano = $S{tano} AND (f1.legal_code = $S{legalCode}  )  "
                    , SubDatabase.DATABASE_FINA_CENTER,params);
        }else {
            return super.findRows("SELECT f1.tano, f1.prod_code, f1.legal_code, f1.prod_acct_type, f1.acct_serno, f2.acct_no, f2.acct_name, f1.remark, f2.open_bank" +
                            " FROM fund_prod_acct_info f1 LEFT JOIN fund_acct_info f2 ON f1.acct_serno = f2.acct_serno" +
                            " WHERE f1.prod_code = $S{prodCode} AND f1.tano = $S{tano} AND (f1.legal_code = $S{legalCode}  )  "
                    , SubDatabase.DATABASE_FUND_CENTER,params);
        }
    }

    /** 查询银行账号编号和名称 **/
    public List<ProdAcctInfo> queryAcctNoAndName(SqlParam<ProdAcctInfo> params) throws Exception {
        if ("FINA".equals(params.getModel().getSystemNo())){
            String sql = "SELECT acct_serno, acct_no, acct_name, open_bank FROM fina_acct_info where acct_status = '0'";
            return super.findRows(ProdAcctInfo.class, sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());

        }else {
            String sql = "SELECT acct_serno, acct_no, acct_name, open_bank FROM fund_acct_info where acct_status = '0'";
            return super.findRows(ProdAcctInfo.class, sql, SubDatabase.DATABASE_FUND_CENTER, params.getModel());

        }
    }

}
