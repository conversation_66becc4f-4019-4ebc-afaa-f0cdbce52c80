package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M111Dao;
import com.kayak.cust.model.M111;
import com.kayak.fina.param.dao.M551Dao;
import com.kayak.fina.param.model.M551;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财产品销售量统计表-按客户经理", model = M551.class)
public class M551Service {

    @Autowired
    private M551Dao m551Dao;

    @Autowired
    private M111Dao m111Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财产品销量统计表-按客户经理", auth = APIAuth.YES)
    public SqlResult<M551> findM551s(SqlParam<M551> param)throws Exception {
        SqlResult<M551> sqlResult = m551Dao.findM551s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M551> volList = sqlResult.getRows();
        for (M551 m551 : volList){
            M001 m001 = m001Dao.get(m551.getTransOrgno());
            if (m001 != null){
                m551.setOrgName(m001.getOrgname());
            }
            M111 params = new M111();
            params.setCustManager(m551.getCustManager());
            M111 m111 = m111Dao.getManagerInfos(params);
            if (m111 != null){
                m551.setCustManagerName(m111.getCustManagerName());
            }
           // m551.setTaName(reportformUtil.getFinaTaName(m551.getTano()));
        }
        return sqlResult;
    }
}
