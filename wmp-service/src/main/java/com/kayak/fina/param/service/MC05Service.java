package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.MC05Dao;
import com.kayak.fina.param.model.MC05;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "鎸傚崟娴佹按", model = MC05.class)
@RequiredArgsConstructor
@Slf4j
public class MC05Service {
    @Autowired
    private final MC05Dao mC05Dao;

    @Autowired
    private final M001Dao m001Dao;

    @Autowired
    private final ReportformUtil reportformUtil;

    @Autowired
    private M215Service m215Service;

    @API(desc = "鏌ヨ鎸傚崟娴佹按", auth = APIAuth.NO)
    public SqlResult<MC05> findMC05(SqlParam<MC05> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<MC05> sqlResult = mC05Dao.findMC05s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        Map<String,Object> map = new HashMap<>();
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            try{
                Map<String, String> prodInfoMap = m215Service.getProdInfo("1000", SystemNo.FINA,item.getTano(),item.getProdCode());
                if(prodInfoMap != null && prodInfoMap.size() > 0){
                    item.setProdName(prodInfoMap.get("prod_name"));
                }
                item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
                if(StringUtils.isNotBlank(item.getTransBankCode())){
                    map.put("orgno",item.getTransBankCode());
                    map.put("userid",params.getParams().get("userid"));
                    SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
                    SqlResult<M001> m001Info = m001Dao.find(dateParams);
                    if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
                        item.setTransBankName(m001Info.getRows().get(0).getOrgname());
                    }
                }
                if(StringUtils.isNotBlank(item.getTransBranchCode())){
                    map.put("orgno",item.getTransBranchCode());
                    map.put("userid",params.getParams().get("userid"));
                    SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
                    SqlResult<M001> m001Info = m001Dao.find(dateParams);
                    if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
                        item.setTransBranchName(m001Info.getRows().get(0).getOrgname());
                    }
                }
                if(StringUtils.isNotBlank(item.getTransSubBranch())){
                    map.put("orgno",item.getTransSubBranch());
                    map.put("userid",params.getParams().get("userid"));
                    SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
                    SqlResult<M001> m001Info = m001Dao.find(dateParams);
                    if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
                        item.setTransSubName(m001Info.getRows().get(0).getOrgname());
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException("M933閿欒锛?+e.getMessage());
            }
            return item;
        }).collect(Collectors.toList()));
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

}

