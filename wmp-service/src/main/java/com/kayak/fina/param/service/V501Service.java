package com.kayak.fina.param.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.PostUrl;
import com.kayak.common.util.HttpClientUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.V501Dao;
import com.kayak.fina.param.model.V501Model;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@APIDefine(desc = "（柜台）产品信息查询", model = V501Model.class)
public class V501Service {

    public static Logger log = LoggerFactory.getLogger(V501Service.class);

    @Autowired
    private V501Dao v501Dao;

    @API(desc = "查询（柜台）产品信息", auth = APIAuth.YES)
    public SqlResult<Map<String, String>> findProductInfos(SqlParam<V501Model> params) throws Exception {
        Map<String, Object> selectParams = params.getParams();
        SqlResult<Map<String, String>> finaProdInfoSqlResult = new SqlResult<>();
        // 默认设置分页值
        Object start = selectParams.get("start");
        Object limit = selectParams.get("limit");
        if (start == null && limit == null) {
            selectParams.put("start", 0);
            selectParams.put("limit", 10);
        }

        String post = HttpClientUtil.sendPost(PostUrl.prodUrl + "T202", changeMapToByte(selectParams), 120000);
        log.info("调用http查询产品信息的结果为：{}", post);
        // 判断是否为异常信息(防止空指针异常)
        Optional<String> optional = Optional.ofNullable(post);
        if (!optional.isPresent()) {
            throw new PromptException("V50101","查询产品信息出现网络错误！");
        }
        JSONObject jsonObject = JSONObject.parseObject(optional.get());
        String status = jsonObject.getString("status");
        if ("FAILED".equals(status)) {
            String rtnCode = jsonObject.getString("rtnCode");
            String rtnDesc = jsonObject.getString("rtnDesc");
            throw new PromptException(rtnCode, rtnDesc);
        }
        JSONArray prodParaList = jsonObject.getJSONArray("prodParaList");
        // JSONArray 转 List<Map<String, String>>
        List<Map<String, String>> mapList = prodParaList.toJavaObject(new TypeReference<List<Map<String, String>>>() {});
        Long results = jsonObject.getLong("results");
        finaProdInfoSqlResult.setRows(mapList);
        finaProdInfoSqlResult.setResults(results);
        return finaProdInfoSqlResult;
    }

    private byte[] changeMapToByte(Map<String,Object> map) throws Exception {

        byte[] bytes = null;
        try {
            bytes = JSON.toJSONString(map, true).getBytes();
        } catch (Exception e) {
            log.error("map到byte[]转换异常");
            throw new Exception("map到byte[]转换异常");
        }

        return bytes;
    }
}
