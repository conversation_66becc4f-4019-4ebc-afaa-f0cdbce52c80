package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.api.impl.CustInfoImpl;
import com.kayak.cust.model.M101;
import com.kayak.fina.param.dao.V507Dao;
import com.kayak.fina.param.model.V507;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 冻结记录查询
 *
 * <AUTHOR>
 * @date 2021-04-25 15:30
 */
@Service
@APIDefine(desc = "（柜台）冻结记录查询服务", model = V507.class)
public class V507Service {

    @Autowired
    private V507Dao v507Dao;

    @Autowired
    private CustInfoImpl custInfoImpl;


    @API(desc = "查询份额冻结实体类信息", auth = APIAuth.YES)
    public SqlResult<V507> findFrozenRecords(SqlParam<V507> params) throws Exception {

        String custNo = params.getModel().getCustNo();
        params.setMakeSql(true);
        params.getModel().setLegalCode(null);
        SqlResult<V507> frozenRecordList = v507Dao.findFrozenRecords(params);
        /**
         *  调取接口查询客户信息
         */
        if (frozenRecordList.getRows().size() > 0){
            M101 custInfo = custInfoImpl.getCustAcctInfo(custNo,"","","").get(0);
            //把客户信息填充到份额冻结记录
            for (V507 record : frozenRecordList.getRows()) {
                record.setCustNo(custNo);
                record.setCustName(custInfo.getCustName());
                record.setAcctNo(custInfo.getAcctNo());
            }
        }
        return frozenRecordList;
    }
}
