package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.RateType;
import com.kayak.core.dao.DaoService;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;

import com.kayak.fina.param.dao.FinaPrjTailingCommisionDao;
import com.kayak.fina.param.dao.FinaPrjTailingCommisionListDao;
import com.kayak.fina.param.model.FinaPrjTailingCommision;
import com.kayak.fina.param.model.FinaPrjTailingCommisionList;
import com.kayak.graphql.model.FetcherData;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.UUID;

@Service
@APIDefine(desc = "尾随佣金服务", model = FinaPrjTailingCommision.class)
public class FinaPrjTailingCommisionService {
    private final static Logger log = LoggerFactory.getLogger(FinaPrjTailingCommisionService.class);

    @Autowired
    private FinaPrjTailingCommisionDao FinaPrjTailingCommisionDao;
    @Autowired
    private FinaPrjTailingCommisionListDao FinaPrjTailingCommisionListDao;
    @Autowired
    protected DaoService daoService;


    /**
     * 会将 dataStatus!E&&addFrom !=0的非产品页面添加的过滤掉
     *
     * @param params
     * @return
     * @throws Exception
     */
    @API(desc = "查询尾随佣金信息", auth = APIAuth.YES)
    public SqlResult<FinaPrjTailingCommision> findPrjTailingCommisions(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        params.setMakeSql(false);
        return FinaPrjTailingCommisionDao.findPrjTailingCommisions(params);

    }

    @API(desc = "添加尾随佣金", params = "prod_code,distributor_code,tailing_commision_code,enable_date,tailing_calc_mode,tailing_commision_yeardays,min_pay_amt,graduated_calc,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
    public String addFinaPrjTailingCommision(SqlParam<FinaPrjTailingCommision> params) throws Exception {

        FinaPrjTailingCommision finaPrjTailingCommision = params.getModel();

        //数据库内存除以了100的数
        String constantRate = params.getModel().getConstantRate();
        if (!Tools.strIsEmpty(constantRate)) {
            params.getModel().setConstantRate(String.valueOf(new BigDecimal(constantRate).multiply(BigDecimal.valueOf(0.01))));
        }


        if (isExist(params)) {
            return RequestSupport.updateReturnJson(false, "新增失败,已存在相同数据！", null).toString();
        }


        finaPrjTailingCommision.setTailingCommisionCode(UUID.randomUUID().toString().substring(10));

        daoService.doTrans(() -> {
            //增加主方案
            FinaPrjTailingCommisionDao.addFinaPrjTailingCommision(params);
            //固定费率0分段费率1
            if (RateType.SEGMENT.equals(params.getModel().getRateType())) {
                //增加明细方案
                addTailingList(params);
            }
        });

        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }


    @API(desc = "添加尾随佣金（产品）", params = "prod_code,distributor_code,tailing_commision_code,enable_date,tailing_calc_mode,tailing_commision_yeardays,min_pay_amt,graduated_calc,crt_time,crt_user,upd_time,upd_user,remark，data_status", auth = APIAuth.NO)
    public String addFinaPrjTailingCommisionByProdCode(SqlParam<FinaPrjTailingCommision> params) throws Exception {

        FinaPrjTailingCommision FinaPrjTailingCommision = params.getModel();


        //数据库内存除以了100的数
        String constantRate = params.getModel().getConstantRate();
        if (!Tools.strIsEmpty(constantRate)) {
            params.getModel().setConstantRate(String.valueOf(new BigDecimal(constantRate).multiply(BigDecimal.valueOf(0.01))));
        }

        FinaPrjTailingCommision.setTailingCommisionCode(UUID.randomUUID().toString().substring(10));
        daoService.doTrans(() -> {
            //增加主方案
            FinaPrjTailingCommisionDao.addFinaPrjTailingCommision(params);
            //固定费率0分段费率1
            if (RateType.SEGMENT.equals(params.getModel().getRateType())) {
                //增加明细方案
                addTailingList(params);
            }
        });


        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }


    @API(desc = "修改尾随佣金", params = "prod_code,distributor_code,tailing_commision_code,enable_date,tailing_calc_mode,tailing_commision_yeardays,min_pay_amt,graduated_calc,crt_time,crt_user,upd_time,upd_user,remark", auth = APIAuth.NO)
    public String updateFinaPrjTailingCommision(SqlParam<FinaPrjTailingCommision> params) throws Exception {

            //数据库内存除以了100的数
            String constantRate = params.getModel().getConstantRate();
            if (!Tools.strIsEmpty(constantRate)) {
                params.getModel().setConstantRate(String.valueOf(new BigDecimal(constantRate).multiply(BigDecimal.valueOf(0.01))));
            }
            //固定费率0分段费率1
            if (RateType.FIX.equals(params.getModel().getRateType())) {
                daoService.doTrans(() -> {
                    //删除明细数据
                    SqlParam<FinaPrjTailingCommisionList> listParam = new FetcherData<>(new HashMap<>(), FinaPrjTailingCommisionList.class);
                    listParam.getModel().setTailingCommisionCode(params.getModel().getTailingCommisionCode());
                    FinaPrjTailingCommisionListDao.deleteFinaPrjTailingCommisionList(listParam);
                    //修改主方案
                    FinaPrjTailingCommisionDao.updateFinaPrjTailingCommision(params);
                });
            } else if (RateType.SEGMENT.equals(params.getModel().getRateType())) {
                daoService.doTrans(() -> {
                    //删除原有的明细
                    deleteTailingList(params);
                    //修改主方案
                    FinaPrjTailingCommisionDao.updateFinaPrjTailingCommision(params);
                    //增加新明细
                    addTailingList(params);
                });
            } else {
                throw new PromptException("费率类型不存在");
            }

        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    @API(desc = "删除尾随佣金", params = "prod_code,distributor_code,tailing_commision_code,enable_date,tailing_calc_mode,tailing_commision_yeardays,min_pay_amt,graduated_calc,crt_time,crt_user,upd_time,upd_user,remark", auth = APIAuth.NO)
    public String deleteFinaPrjTailingCommision(SqlParam<FinaPrjTailingCommision> params) throws Exception {

        FinaPrjTailingCommisionDao.deleteFinaPrjTailingCommision(params);

        return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
    }


    private boolean isExist(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        SqlParam<FinaPrjTailingCommision> queryParam = new FetcherData<FinaPrjTailingCommision>(new HashMap<>(), FinaPrjTailingCommision.class);
        queryParam.setMakeSql(false);
        queryParam.getModel().setProdCode(params.getModel().getProdCode());
        queryParam.getModel().setEnableDate(params.getModel().getEnableDate());
        SqlResult<FinaPrjTailingCommision> result = FinaPrjTailingCommisionDao.findFinaPrjTailingCommision(queryParam);
        if (result.getRows().size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 删除明细数据
     *
     * @param params
     * @throws Exception
     */
    private void deleteTailingList(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        SqlParam<FinaPrjTailingCommisionList> listParam = new FetcherData<>(new HashMap<>(), FinaPrjTailingCommisionList.class);
        listParam.getModel().setTailingCommisionCode(params.getModel().getTailingCommisionCode());
        FinaPrjTailingCommisionListDao.deleteFinaPrjTailingCommisionList(listParam);
    }


    /**
     * 新增明细数据
     *
     * @param params
     */
    private void addTailingList(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        for (FinaPrjTailingCommisionList item : params.getModel().getTailingList()) {
            SqlParam<FinaPrjTailingCommisionList> tailingListParam =
                    new FetcherData<FinaPrjTailingCommisionList>(new HashMap<>(), FinaPrjTailingCommisionList.class);
            BeanUtils.copyProperties(item, tailingListParam.getModel());
            tailingListParam.getModel().setTailingCommisionCode(params.getModel().getTailingCommisionCode());
            FinaPrjTailingCommisionListDao.addFinaPrjTailingCommisionList(tailingListParam);
        }
    }


}
