package com.kayak.fina.param.service;

import com.alibaba.fastjson.JSONObject;
import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M563Dao;
import com.kayak.fina.param.model.M563;
import com.kayakwise.prod.ProdInfoDubboDecorator;
import com.kayakwise.wmp.pub.pojo.CachePrefixConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "可赎回额度查询实体类服务", model = M563.class)
public class M563Service {

    @Resource
    private M563Dao m563Dao;

    @CloudReference
    private ProdInfoDubboDecorator prodInfoDubboDecorator;

    public SqlResult<M563> findM563(SqlParam<M563> params) throws Exception {
        SqlResult<M563> result = m563Dao.findM563(params);
        List<M563> rows = result.getRows();
        if (rows.isEmpty()) {
            return result;
        }
        List<String> prodCodes = rows.stream()
                .map(row -> buildProdKeyHash(row.getSystemNo(), row.getTano(), row.getProdCode(), row.getLegalCode()))
                .collect(Collectors.toList());
        String strMap = prodInfoDubboDecorator.getMoreProdParam(prodCodes, new ArrayList<String>(){{
            add("huge_redeem_ratio");
            add("prod_name");
        }});
        Map<String, Map<String, String>> map = JSONObject.parseObject(strMap, Map.class);
        Consumer<M563> consumer = row -> {
            BigDecimal redeemTotalVol = row.getRedeemTotalVol();
            Map<String, String> m = map.get(buildProdKeyHash(row.getSystemNo(), row.getTano(), row.getProdCode(), row.getLegalCode()));
            if(m == null){
                return;
            }
            String r = m.get("huge_redeem_ratio");
            String prodName = m.get("prod_name");
            row.setProdName(prodName);
            if (StringUtils.isNotBlank(r)) {
                row.setRedeemTotalVol(redeemTotalVol.multiply(new BigDecimal(r)).subtract(row.getRedeemVol()).setScale(2, RoundingMode.HALF_UP));
            } else {
                row.setRedeemTotalVol(redeemTotalVol.subtract(row.getRedeemVol()));
            }

        };
        rows.forEach(consumer);
        return result;
    }

    /**
     * 临时使用，组装产品信息的key值 (Hash结构)
     */
    public static String buildProdKeyHash(@NotBlank String systemNo, @NotBlank String targetCode, @NotBlank String prodCode, @NotBlank String legalCode) {
        return CachePrefixConstant.PROD_INFO_PREFIX_HASH +
                systemNo + CachePrefixConstant.LINE +
                targetCode + CachePrefixConstant.LINE +
                prodCode + CachePrefixConstant.LINE +
                legalCode;
    }

}
