package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.*;
import com.kayak.fina.param.model.MC01;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class MC01Dao extends ComnDao {

    public SqlResult<MC01> find(SqlParam<MC01> params) throws Exception {
        params.setMakeSql(true);
        return super.findRows("SELECT * FROM sys_param ",
                SubDatabase.DATABASE_FINA_CENTER, params);
    }

    public void update(List<MC01> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (MC01 p : params) {
                super.update("UPDATE sys_param SET paravalue = $S{paravalue} WHERE paraid = $S{paraid}",
                        SubDatabase.DATABASE_FINA_CENTER, p);
            }
        });
    }

    //添加
    public UpdateResult addSysParam(SqlParam<MC01> params) throws Exception {
        return super.update("INSERT INTO SYS_PARAM(moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay) VALUES($S{moduleid},$S{paraid},$S{paravalue},$S{paraname},$S{groupparaid},$S{dict},$S{functype},$S{confoption},$S{isdisplay})",
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    //修改
    public UpdateResult updateSysParam(SqlParam<MC01> params) throws Exception {
        UpdateResult updateResult = super.update("UPDATE SYS_PARAM SET paravalue=$S{paravalue} ,paraname=$S{paraname}   WHERE  moduleid=$S{moduleid} and paraid=$S{paraid}",
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
        return updateResult;
    }

    //删除
    public UpdateResult deleteSysParam(SqlParam<MC01> params) throws Exception {
        return super.update("DELETE FROM SYS_PARAM WHERE moduleid=$S{moduleid} and paraid=$S{paraid} ",
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     * 查询某个系统参数
     *
     * @param params
     * @return
     * @throws Exception
     */
    public MC01 findOne(SqlParam<MC01> params) throws Exception {
        // oracle 不支持limit 1
        String sql = "SELECT paravalue ,paraname ,paraid ,moduleid ,isdisplay ,groupparaid ,graphql ,functype ,fieldtype ,execaction ,dict ,confoption FROM sys_param";
        SqlResult<MC01> row = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            return row.getRows().get(0);
        }
        return null;
    }

    /**
     * 根据批次号查询系统参数
     *
     * @param contentId
     * @return MC01
     * @throws Exception
     */
    public MC01 findByContentId(String contentId) throws Exception {
        String sql = "SELECT paravalue ,paraname ,paraid ,moduleid ,isdisplay ,groupparaid ,graphql ,functype ,fieldtype ,execaction ,dict ,confoption FROM sys_param WHERE paraid = 'a0000009' ";
        List<MC01> rows = super.findRows(MC01.class, sql, SubDatabase.DATABASE_FINA_CENTER, null);
        return rows.isEmpty() ? null : rows.get(0);
    }

}


