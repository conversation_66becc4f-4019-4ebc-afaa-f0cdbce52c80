package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M547;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M547Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M547> findM547s(SqlParam<M547> params) throws  Exception{
        params.setMakeSql(true);
        String sql = "select t.tano,\n" +
                "       t.trans_date,\n" +
                "       t.orgno,\n" +
                "       t.prod_code,\n" +
                "       sum(case\n" +
                "             when t.cust_type = '1' then\n" +
                "              t.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as per_vol,\n" +
                "       sum(case\n" +
                "             when t.cust_type = '0' then\n" +
                "              t.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as org_vol,\n" +
                "       sum(case\n" +
                "             when t.cust_type = '0' or t.cust_type = '1' then\n" +
                "              t.stock_vol\n" +
                "             else\n" +
                "              0\n" +
                "           end) as vol,\n" +
                "           t2.ta_name\n" +
                "  from fina_vol_stock t left join fina_ta_info t2 on t.tano=t2.tano\n" +
                " where 1 = 1\n" +
                " group by t.tano, t.trans_date, t.orgno, t.prod_code,t2.ta_name order by trans_date desc\n";

        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }
}
