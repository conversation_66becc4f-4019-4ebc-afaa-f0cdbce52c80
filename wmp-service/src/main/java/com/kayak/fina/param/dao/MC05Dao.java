package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.MC05;
import org.springframework.stereotype.Repository;

@Repository
public class MC05Dao extends ComnDao {
    public SqlResult<MC05> findMC05s(SqlParam<MC05> params) throws Exception {
        String sql = "SELECT t1.entrust_serno,t1.channel_serno,t1.busi_date," +
                "t1.channel_date,t1.channel_time,t1.trans_counter,t1.trans_bank_code," +
                "t1.trans_branch_code,t1.trans_sub_branch,t1.channel_flag,t1.cust_no," +
                "t1.trans_acct_no,t1.acct_no,t1.cust_type,t1.cust_name,t1.id_type,t1.id_code," +
                "t1.system_no,t1.tano,t1.prod_code,t1.entrust_amt,t1.entrust_vol," +
                "t1.transfer_mode,t1.max_fee,t1.min_fee,t1.cfm_buy_vol,"+
                "t1.trfr_fee_obj,t1.trfr_fee_mode,t1.fee_amt,t1.buy_vol," +
                "(t1.entrust_vol - t1.buy_vol) res_vol,t1.invalid_date_plan,t1.entrust_status,t1.fee_precision_mode," +
                "t1.amt_precision_mode,t1.rtn_code,t1.rtn_desc, t1.entrust_type,\n" +
                "       t1.trfr_in_type,t2.capital_status " +
                "FROM TRFR_CUST_ENTRUST_LOG t1 left join paym_capital_log t2 ON t1.ENTRUST_SERNO=t2.TRANS_SERNO ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }
}
