package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.V508Model;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class V508Dao extends ComnDao {

    /** 查询客户信息和TA信息 **/
    public SqlResult<V508Model> findCustTA(SqlParam<V508Model> params) throws Exception {
        SqlResult<V508Model> rows;
        try {
            String sql = "SELECT * FROM fina_cust_ta_acct";
            params.setMakeSql(true);
            rows = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
        } catch (Exception ex) {
            System.out.println(ex);
            System.out.println(ex.getMessage());
            throw new PromptException("V50800", "查询柜台的客户信息和TA信息出错！");
        }
        return rows;
    }

    /**
     * <AUTHOR>
     * @Description 客户TA账号表，交易机构改为合并机构
     * @Date 2022/3/24
     * @Param [params]
     * @return com.kayak.core.sql.UpdateResult
     **/
    public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
        return super.update("UPDATE fina_cust_ta_acct SET TRANS_ORGNO = $S{mergeOrgno} WHERE TRANS_ORGNO = $S{removeOrgno} ",
                SubDatabase.DATABASE_FINA_CENTER,params);
    }

}
