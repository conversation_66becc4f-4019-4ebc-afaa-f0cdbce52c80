package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import com.kayak.fina.param.model.M663;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

/**
 *
 */
@Repository
public class M663Dao extends ComnDao {

	/**
	 * 理财账户类交易流水查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M663> findFundProdBasicParam(SqlParam<M663> params) throws Exception {
		//params.setMakeSql(true);
		String sql2 = " select APP_SERNO,trans_Code,\n" +
				"               busi_Code,\n" +
				"               tano,\n" +
				"               ta_Acct_No,\n" +
				"               acct_No,\n" +
				"               cust_Name,\n" +
				"               id_type,\n" +
				"               id_code,\n" +
				"               branch_Code,\n" +
				"               sub_Branch_Code,\n" +
				"               trans_Acct_No,\n" +
				"               busi_Date,\n" +
				"               DATE_FORMAT(mactime,'%Y-%m-%d %H:%i:%s') as mactime,\n" +
				"               cust_Manager,\n" +
				"               ack_Date,\n" +
				"               trans_Status,\n" +
				"               channel_Flag from fina_cust_acct_req_log ";
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M663.class);
		String sql =
				"select * from ( " + sql2 + ")tt1 where 1=1 ";
		return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
	}
}