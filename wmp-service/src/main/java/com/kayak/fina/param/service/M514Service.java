package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.GlobalConstants;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.SysUtil;
import com.kayak.fina.param.dao.M515IndexDao;
import com.kayak.fina.param.dao.M514Dao;
import com.kayak.fina.param.model.M514;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
/**
 * 鎺ュ彛鐗堟湰涓庣储寮曞叧绯荤鐞嗭紝鍘熷悕 Ta2017Service
 */
@Service
@APIDefine(desc = "TA鏂囦欢鎺ュ彛閰嶇疆", model = M514.class)
@RequiredArgsConstructor
@Slf4j
public class M514Service {
    @Autowired
    private M514Dao m514Dao;

    @Autowired
    private M515IndexDao m515IndexDao;

    @API(desc = "鏂囦欢鎺ュ彛鏌ヨ", auth = APIAuth.YES)
    public SqlResult<M514> findM514(SqlParam<M514> params) throws Exception {
        params.setMakeSql(true);

        return m514Dao.findTaInterfaceInfoDao(params);
    }


    @API(desc = "鏂囦欢鎺ュ彛娣诲姞", params = "interface_id,interface_name,interface_type,interface_version,index_no", auth = APIAuth.YES)
    public String addM514(SqlParam<M514> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛
        String loginname = (String) params.getAuthInfo().get("userid");
        params.getModel().setCreateUser(loginname);
        params.getModel().setUpdateUser(loginname);
        params.getModel().setDataStatus("E");
//        SqlResult<M514> result = taInterfaceInfoDao.findTaInterfaceInfoByNo(params);
        //鏍规嵁鎺ュ彛鍚嶇О鏌ヨ鎺ュ彛鏂囦欢淇℃伅
        SqlResult<M514> result = m514Dao.findTaInterfaceInfoByName(params);
        //濡傛灉鎺ュ彛淇℃伅琛ㄤ腑鏃犺鍚嶇О锛屽垯鍙互鏂板涓€涓帴鍙ｆ枃浠朵俊鎭?
        if(result.getRows().size() == 0){
            m514Dao.addTaInterfaceInfoDao(params);
            return RequestSupport.updateReturnJson(true, "鎿嶄綔鎴愬姛", null).toString();
        }else if(result.getRows().size() == 1){
            return RequestSupport.updateReturnJson(false, "娣诲姞閲嶅", null).toString();
        }
        return RequestSupport.updateReturnJson(false, "娣诲姞澶辫触", null).toString();
    }



    @API(desc = "鏂囦欢鎺ュ彛鏇存柊", params = "interface_id,interface_name,index_no", auth = APIAuth.YES)
    public String updateM514(SqlParam<M514> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛
        String loginname = (String) params.getAuthInfo().get("userid");
        params.getModel().setUpdateUser(loginname);
        UpdateResult result =  m514Dao.updateTaInterfaceInfoDao(params);
        return RequestSupport.updateReturnJson(true, "鏇存柊鍐呭鎴愬姛", null).toString();
    }

    @API(desc = "鍋滅敤閿€鍞晢鎺ュ彛鍏宠仈鍏崇郴缁存姢")
    public String startTaInterfaceInfo(SqlParam<M514> params) throws Exception{

        m514Dao.startTaInterfaceInfo(params);

        return RequestSupport.updateReturnJson(true, "閿€鍞晢鎺ュ彛鍏宠仈鍏崇郴宸插仠鐢?, null).toString();
    }

    @API(desc = "鍚敤閿€鍞晢鎺ュ彛鍏宠仈鍏崇郴缁存姢")
    public String stopTaInterfaceInfo(SqlParam<M514> params) throws Exception{

        m514Dao.startTaInterfaceInfo(params);

        return RequestSupport.updateReturnJson(true, "閿€鍞晢鎺ュ彛鍏宠仈鍏崇郴宸插惎鐢?, null).toString();
    }


    @API(desc = "鏂囦欢鎺ュ彛鍒犻櫎", params = "interface_id", auth = APIAuth.YES)
    public String deleteM514(SqlParam<M514> params) throws Exception {
        UpdateResult result = m514Dao.deleteTaInterfaceInfoDao(params);

        return RequestSupport.updateReturnJson(true, "鍒犻櫎鎴愬姛", null).toString();
    }

    @API(desc = "鏌ヨ鎺ュ彛鐗堟湰")
    public SqlResult<M514> findM514Version(SqlParam<M514> params) throws Exception {
        return m514Dao.findTaInterfaceInfoVersion(params);
    }
    @API(desc = "鏌ヨ鎺ュ彛淇℃伅")
    public SqlResult<M514> findInterfaceIdByType(SqlParam<M514> params) throws Exception {
        return m514Dao.findInterfaceIdByType(params);
    }
}

