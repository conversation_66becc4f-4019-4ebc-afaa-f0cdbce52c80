package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.MC03Dao;
import com.kayak.fina.param.model.MC03;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "杞娴佹按", model = MC03.class)
@RequiredArgsConstructor
@Slf4j
public class MC03Service {
    @Autowired
    private final MC03Dao mC03Dao;

    @Autowired
    private M215Service m215Service;
    @Autowired
    private ReportformUtil reportformUtil;


    @API(desc = "鏌ヨ杞娴佹按", auth = APIAuth.NO)
    public SqlResult<MC03> findMC03(SqlParam<MC03> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<MC03> sqlResult = mC03Dao.findMC03s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            try {
                Map<String,Object> map = new HashMap<>();
                map.put("systemNo","FINA");
                map.put("prodCode",item.getProdCode());
                List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(map, M215.class));
                if (prodParaList != null && prodParaList.size() > 0){
                    Map<String, String> prodPara = prodParaList.get(0);
                    item.setProdName(prodPara.get("prod_name"));
                }
            } catch (Exception e) {
                throw new RuntimeException("MC03锛?+e.getMessage());
            }
            return item;
        }).collect(Collectors.toList()));
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

}

