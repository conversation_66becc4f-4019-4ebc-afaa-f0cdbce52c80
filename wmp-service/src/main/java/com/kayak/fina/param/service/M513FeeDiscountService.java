package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.AddFrom;
import com.kayak.common.constants.GlobalConstants;
import com.kayak.common.model.FeeType;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.M513FeeDiscountDao;
import com.kayak.fina.param.model.M513;
import com.kayak.fina.param.model.M513FeeDiscount;
import com.kayak.graphql.model.FetcherData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 最大折扣率服务，原名 Ta3006Service
 */
@Service
@APIDefine(desc = "最大折扣率服务", model = M513.class)
public class M513FeeDiscountService {

	@Autowired
	private M513FeeDiscountDao m513FeeDiscountDao;

	@API(desc = "查询最大折扣率信息", auth = APIAuth.YES)
	public SqlResult<M513FeeDiscount> findM513FeeDiscounts(SqlParam<M513FeeDiscount> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<M513FeeDiscount>  allList =  m513FeeDiscountDao.findM513FeeDiscounts(params);
		return allList;
	}

	/**
	 * 会将 dataStatus!E&&addFrom !=1 的非销售商页面添加的过滤掉
	 * @param params
	 * @return
	 * @throws Exception
	 */
	@API(desc = "通过销售商Code查询最大折扣率信息", auth = APIAuth.YES)
	public SqlResult<M513FeeDiscount> findM513FeeDiscountsByDisCode(SqlParam<M513FeeDiscount> params) throws Exception {
		params.setMakeSql(false);
		SqlResult<M513FeeDiscount>  allList =  m513FeeDiscountDao.findM513FeeDiscountsByDisCode(params);
		allList.setRows( allList.getRows().stream().map(
			item-> {
				//db2的需要转化下科学计数法
				BigDecimal bd = new BigDecimal(item.getMinDiscount());
				item.setMinDiscount(bd.toPlainString());
				return item;
			}).filter(item-> AddFrom.DIS.equals(item.getAddFrom())).collect(Collectors.toList())
		);
		allList.setResults(allList.getRows().size());
		return allList;
	}

	/**
	 * 会将 dataStatus!E&&addFrom !=1 的非产品页面添加的过滤掉
	 * @param params
	 * @return
	 * @throws Exception
	 */
	@API(desc = "通过产品Code查询最大折扣率信息", auth = APIAuth.YES)
	public SqlResult<M513FeeDiscount> findM513FeeDiscountsByProdCode(SqlParam<M513FeeDiscount> params) throws Exception {
		params.setMakeSql(false);
		SqlResult<M513FeeDiscount>  allList =  m513FeeDiscountDao.findM513FeeDiscountsByProdCode(params);
		allList.setResults(allList.getRows().size());
		return allList;
	}

	@API(desc = "添加最大折扣率", params = "prod_code,distributor_code,fee_type,min_discount,crt_time,crt_user,upd_time,upd_user,remark,resolve_way", auth = APIAuth.NO)
	public String addM513FeeDiscount(SqlParam<M513FeeDiscount> params) throws Exception {

		M513FeeDiscount model =params.getModel();

		//页面传过来的产品代码是多选，需要进行循环匹配
		String[] prodCodes = params.getModel().getProdCode().split(GlobalConstants.MSELECT_SPLIT);
		String[] feeTypes = params.getModel().getFeeType().split(GlobalConstants.MSELECT_SPLIT);

		//折扣率数据库内放除了100的数据
		String minDiscount = params.getModel().getMinDiscount();
		params.getModel().setMinDiscount( String.valueOf(new BigDecimal(minDiscount).divide(BigDecimal.valueOf(100))));

		List<M513FeeDiscount> modelList = new ArrayList<>();

		StringBuilder M513FeeDiscounts = new StringBuilder();

		int loseNumber = 0;//用于记录插入失败条数

		for (String prodCode : prodCodes) {
			for (String feeType : feeTypes) {
				M513FeeDiscount paramTemp = params.getModel().clone();
				paramTemp.setProdCode(prodCode);
				paramTemp.setFeeType(feeType);

				model.setProdCode(prodCode);
				model.setFeeType(feeType);

				boolean exist = isExist(params);
				if(exist){
					if(loseNumber > 0){
						M513FeeDiscounts.append(" 、"+prodCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}else {
						M513FeeDiscounts.append(" {  "+prodCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}
					loseNumber++;
				}

				modelList.add(paramTemp);
			}
		}
		m513FeeDiscountDao.addM513FeeDiscount(modelList);

		if(loseNumber > 0){
			return RequestSupport.updateReturnJson(true, "新增失败"+loseNumber+"条，"+M513FeeDiscounts+" }  ,已存在！",null).toString();
		}

		return  RequestSupport.updateReturnJson(true, "新增成功", null).toString();
	}

	@API(desc = "添加最大折扣率", params = "prod_code,distributor_code,fee_type,min_discount,crt_time,crt_user,upd_time,upd_user,remark,resolve_way", auth = APIAuth.NO)
	public String addM513FeeDiscountByProdCode(SqlParam<M513FeeDiscount> params) throws Exception {

		M513FeeDiscount model =params.getModel();

		//页面传过来的销售商代码是多选，需要进行循环匹配
		String[] distributorCodes = params.getModel().getTano().split(GlobalConstants.MSELECT_SPLIT);
		String[] feeTypes = params.getModel().getFeeType().split(GlobalConstants.MSELECT_SPLIT);

		//折扣率数据库内放除了100的数据
		String minDiscount = params.getModel().getMinDiscount();
		params.getModel().setMinDiscount( String.valueOf(new BigDecimal(minDiscount).divide(BigDecimal.valueOf(100))));

		List<M513FeeDiscount> modelList = new ArrayList<>();

		StringBuilder M513FeeDiscounts = new StringBuilder();

		int loseNumber = 0;//用于记录插入失败条数

		for (String distributorCode : distributorCodes) {
			for (String feeType : feeTypes) {
				M513FeeDiscount paramTemp = params.getModel().clone();
				paramTemp.setTano(distributorCode);
				paramTemp.setFeeType(feeType);

				model.setTano(distributorCode);
				model.setFeeType(feeType);

				boolean exist = isExist(params);
				if(exist){
					if(loseNumber > 0){
						M513FeeDiscounts.append(" 、"+distributorCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}else {
						M513FeeDiscounts.append(" {  "+distributorCode+"-"+ FeeType.codeOf(feeType).getDesc());
					}
					loseNumber++;
				}

				modelList.add(paramTemp);
			}
		}
		m513FeeDiscountDao.addM513FeeDiscount(modelList);

		if(loseNumber > 0){
			return RequestSupport.updateReturnJson(true, "新增失败"+loseNumber+"条，"+M513FeeDiscounts+" }  ,已存在！",null).toString();
		}

		return  RequestSupport.updateReturnJson(true, "新增成功", null).toString();
	}

	@API(desc = "修改最大折扣率", params = "prod_code,distributor_code,fee_type,min_discount,crt_time,crt_user,upd_time,upd_user,remark,resolve_way", auth = APIAuth.NO)
	public String updateM513FeeDiscount(SqlParam<M513FeeDiscount> params) throws Exception {
		//折扣率数据库内放除了100的数据
		String minDiscount = params.getModel().getMinDiscount();
		params.getModel().setMinDiscount( String.valueOf(new BigDecimal(minDiscount).divide(BigDecimal.valueOf(100))));
		return m513FeeDiscountDao.updateM513FeeDiscount(params);
	}

	@API(desc = "删除最大折扣率", params = "prod_code,distributor_code,fee_type,min_discount,crt_time,crt_user,upd_time,upd_user,remark,resolve_way", auth = APIAuth.NO)
	public String deleteM513FeeDiscount(SqlParam<M513FeeDiscount> params) throws Exception {
		return m513FeeDiscountDao.deleteM513FeeDiscount(params);
	}
	public String cancelDeelteM513FeeDiscount(SqlParam<M513FeeDiscount> params) throws Exception {
		m513FeeDiscountDao.deleteM513FeeDiscount(params);
		return RequestSupport.updateReturnJson(true, "撤销删除成功", null).toString();
	}


	private boolean isExist(SqlParam<M513FeeDiscount> params) throws Exception {
		SqlParam<M513FeeDiscount> queryParam =  new FetcherData<M513FeeDiscount>(new HashMap<>(), M513FeeDiscount.class);
		queryParam.setMakeSql(false);
		queryParam.getModel().setProdCode(params.getModel().getProdCode());
		queryParam.getModel().setTano(params.getModel().getTano());
		queryParam.getModel().setFeeType(params.getModel().getFeeType());
		SqlResult<M513FeeDiscount> result = m513FeeDiscountDao.findM513FeeDiscount(queryParam);
		if (result.getRows().size() > 0 ){
			return true;
		}
		return false;
	}

	@API(desc="单边锁",auth=APIAuth.NO)
	public SqlResult<M513FeeDiscount> getLock(SqlParam<M513FeeDiscount> param)throws Exception{
		return m513FeeDiscountDao.getLock(param);
	}
}
