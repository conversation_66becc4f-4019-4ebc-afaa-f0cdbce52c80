package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.V508Dao;
import com.kayak.fina.param.model.V508Model;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@APIDefine(desc = "（柜台）客户TA账户实体类服务", model = V508Model.class)
public class V508Service {

    @Autowired
    private V508Dao v508Dao;

    @API(desc = "查询（柜台）客户TA账户实体类信息", auth = APIAuth.YES)
    public SqlResult<V508Model> findCustTA(SqlParam<V508Model> params) throws Exception {
        Map<String, Object> params1 = params.getParams();
        System.out.println("参数：");
        for (String key:params1.keySet()) {
            System.out.println(params1.get(key));
        }
        return v508Dao.findCustTA(params);
    }
}
