package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M525Dao;
import com.kayak.fina.param.model.M525;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "每日销量统计服务", model = M525.class)
public class M525Service {

    @Autowired
    private M525Dao m525Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询每日销量", auth = APIAuth.YES)
    public SqlResult<M525> findFinaTransDaySums(SqlParam<M525> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M525> sqlResult = m525Dao.findFinaTransDaySums(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        if(sqlResult != null && sqlResult.getRows().size() > 0){
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("legalCode", "1000");
            prodParam.put("userid",params.getParams().get("userid"));
            for (M525 m525 : sqlResult.getRows()){
                //m525.setTaName(reportformUtil.getFinaTaName(m525.getTano()));
                m525.setOrgname(reportformUtil.getOrgName(m525.getOrgno()));
                prodParam.put("prodCode", m525.getProdCode());
                prodParam.put("supplyCode", m525.getTano());
                List<Map<String, String>> prodInfoList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
                if (prodInfoList != null && prodInfoList.size() > 0){
                    Map<String, String> map = prodInfoList.get(0);
                    m525.setProdName(map.get("prod_name") == null ? "":map.get("prod_name"));
                }
            }
        }
        return sqlResult;
    }
}
