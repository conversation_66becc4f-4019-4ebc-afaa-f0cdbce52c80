package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.MC01Dao;
import com.kayak.fina.param.dao.MC02Dao;
import com.kayak.fina.param.model.MC02;
import com.kayak.graphql.model.FetcherData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "杞浜у搧鍙傛暟鏈嶅姟", model = MC02.class)
@RequiredArgsConstructor
@Slf4j
public class MC02Service {

    private final MC02Dao mC02Dao;

    private final MC01Dao mc01Dao;

    @API(desc = "鏌ヨ杞浜у搧鍙傛暟鍒楄〃", auth = APIAuth.NO)
    public SqlResult<MC02> findMC02(SqlParam<MC02> params) throws Exception {
        params.setMakeSql(true);
        return mC02Dao.findMC02s(params);
    }

    @API(desc = "娣诲姞杞浜у搧鍙傛暟琛?, auth = APIAuth.NO)
    public int addMC02(SqlParam<MC02> params) throws Exception {
        Map<String,Object> map = new HashMap<>();
        //鐧惧垎姣旇浆鏁板瓧
        MC02 model = params.getModel();
        model.setPriceUpLimit(percentToNum(model.getPriceUpLimit()));
        model.setPriceDownLimit(percentToNum(model.getPriceDownLimit()));
        model.setBuyRate(percentToNum(model.getBuyRate()));
        model.setSaleRate(percentToNum(model.getSaleRate()));
        model.setBenchmarks(percentToNum(model.getBenchmarks()));
        map.put("systemNo", model.getSystemNo());
        map.put("tano", model.getTano());
        map.put("prodCode", model.getProdCode());
        map.put("legalCode", model.getLegalCode());
        map.put("entrustType", model.getEntrustType());
        map.put("userid",params.getParams().get("userid"));
        SqlParam sqlParam = new FetcherData(map,MC02.class);
        SqlResult<MC02> sqlResult = this.findMC02(sqlParam);
        if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0){
            throw new PromptException("璇ヤ骇鍝佸凡瀛樺湪杞鍙傛暟");
        }
        int effect = mC02Dao.addMC02(params).getEffect();
        return effect;
    }

    @API(desc = "淇敼杞浜у搧鍙傛暟琛?, auth = APIAuth.NO, operation = APIOperation.UPDATE)
    public int updateMC02(SqlParam<MC02> params) throws Exception {
        int effect = mC02Dao.updateMC02(params).getEffect();
        return effect;
    }

    @API(desc = "鍒犻櫎杞浜у搧鍙傛暟", auth = APIAuth.NO)
    public int delete(SqlParam<MC02> params) throws Exception {
        Map<String,Object> map = new HashMap<>();
        map.put("systemNo",params.getModel().getSystemNo());
        map.put("tano",params.getModel().getTano());
        map.put("prodCode",params.getModel().getProdCode());
        map.put("legalCode",params.getModel().getLegalCode());
        map.put("entrustType",params.getModel().getEntrustType());
        SqlParam<MC02> sqlParam = new FetcherData(map,MC02.class);
        int numEntrust=mC02Dao.findEntrust(sqlParam);
        int numTrans=mC02Dao.findTrans(sqlParam);
        if (numEntrust > 0 || numTrans >0){
            throw new PromptException("璇ュ弬鏁板瓨鍦ㄦ寕鍗曟垨涔板叆娴佹按锛屼笉鑳藉垹闄わ紒");
        }
        int effect = mC02Dao.deleteMC02(params).getEffect();
        return effect;
    }

    @API(desc = "鏌ヨ鏌愪釜杞浜у搧鍙傛暟")
    public SqlResult<MC02> getMC02(SqlParam<MC02> params) throws Exception {
        params.setMakeSql(true);
        MC02 one = mC02Dao.findOne(params);
        List<MC02> list = new ArrayList<>();
        list.add(one);
        return SqlResult.build(list);
    }

    public static Double percentToNum(Double num){
        if (num==null) {
            return null;
        }
        return num/100;
    }


}


