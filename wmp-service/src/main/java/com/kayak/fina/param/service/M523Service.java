package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.M523Dao;
import com.kayak.fina.param.model.M520;
import com.kayak.fina.param.model.M523;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.service.M215Service;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "客户交易确认实体类服务", model = M523.class)
public class M523Service {

	@Autowired
	private M523Dao m523Dao;

	@Autowired
	private M215Service m215Service;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询客户交易确认信息", auth = APIAuth.YES)
	public SqlResult<M523> findFinaCustTransCfmLog(SqlParam<M523> params) throws Exception {
		params.getModel().setLegalCode(null);
		SqlResult<M523> sqlResult = m523Dao.findFinaCustTransCfmLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		//获取产品名称、TA名称
		if(null!=sqlResult&&sqlResult.getRows().size()>0){
			List<M523> sqlList=sqlResult.getRows();
			for(M523 m523:sqlList){
				Map<String, String> prodInfoMap = m215Service.getProdInfo(m523.getLegalCode(), SystemNo.FINA,m523.getTano(),m523.getProdCode());
				if(prodInfoMap != null && prodInfoMap.size() > 0){
					m523.setProdName(prodInfoMap.get("prod_name"));
				}
				/**if(null!=m523){
					m523.setTaName(reportformUtil.getFinaTaName(m523.getTano()));
				}*/
			}
		}
		return sqlResult;
	}


}
