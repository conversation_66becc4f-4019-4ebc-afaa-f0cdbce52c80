package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.FeeType;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.FinaPrjFeeDivideDao;
import com.kayak.fina.param.model.FinaPrjFeeDivide;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@APIDefine(desc = "费用分成方案服务", model = FinaPrjFeeDivide.class)
public class FinaPrjFeeDivideService {

    @Autowired
    private FinaPrjFeeDivideDao finaPrjFeeDivideDao;


    /**
     * @param params
     * @return
     * @throws Exception
     */
    @API(desc = "查询费用分成方案信息", auth = APIAuth.YES)
    public SqlResult<FinaPrjFeeDivide> findFinaPrjFeeDividesByProdCode(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        params.setMakeSql(false);
        return finaPrjFeeDivideDao.findFinaPrjFeeDividesByProdCode(params);
    }

    @API(desc = "添加费用分成方案", params = "prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
    public String addFinaPrjFeeDivide(SqlParam<FinaPrjFeeDivide> params) throws Exception {

        FinaPrjFeeDivide model = params.getModel();

        //页面传过来的产品代码和费用类型是多选，需要进行循环匹配
        String[] feeTypes = model.getFeeType().split(",");

        String prodCode = model.getProdCode();
        //数据库内存的分成比例需要除以100
        BigDecimal distributorDividRate = new BigDecimal(model.getDistributorDividRate());
        model.setDistributorDividRate(String.valueOf(distributorDividRate.divide(new BigDecimal(100))));
        BigDecimal managerDividRate = new BigDecimal(model.getManagerDividRate());
        model.setManagerDividRate(String.valueOf(managerDividRate.divide(new BigDecimal(100))));

        List<FinaPrjFeeDivide> modelList = new ArrayList<>();

        StringBuilder finaPrjFeeDivides = new StringBuilder();

        int loseNumber = 0;//用于记录插入失败条数
        for (String feeType : feeTypes) {
            FinaPrjFeeDivide paramTemp = model.clone();
            paramTemp.setFeeType(feeType);
            model.setFeeType(feeType);
            boolean exist = isExist(params);
            if (exist) {
                if (loseNumber > 0) {
                    finaPrjFeeDivides.append(" 、" + prodCode + "-" + FeeType.codeOf(feeType).getDesc());
                } else {
                    finaPrjFeeDivides.append(" {  " + prodCode + "-" + FeeType.codeOf(feeType).getDesc());
                }
                loseNumber++;
            }

            modelList.add(paramTemp);
        }
        finaPrjFeeDivideDao.addFinaPrjFeeDivide(modelList);

        if (loseNumber > 0) {
            return RequestSupport.updateReturnJson(true, "新增失败" + loseNumber + "条，" + finaPrjFeeDivides + " }   ,在 " + params.getModel().getEnableDate() + " 此启用日期下已有方案！", null).toString();
        }

        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }


    @API(desc = "添加费用分成方案", params = "prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
    public String addFinaPrjFeeDivideByProdCode(List<FinaPrjFeeDivide> params) throws Exception {

        finaPrjFeeDivideDao.addFinaPrjFeeDivide(params);

        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }


    @API(desc = "修改费用分成方案", params = "prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
    public String updateFinaPrjFeeDivide(SqlParam<FinaPrjFeeDivide> params) throws Exception {

        FinaPrjFeeDivide model = params.getModel();
        //数据库内存的分成比例需要除以100
        BigDecimal distributorDividRate = new BigDecimal(model.getDistributorDividRate());
        model.setDistributorDividRate(String.valueOf(distributorDividRate.divide(new BigDecimal(100))));
        BigDecimal managerDividRate = new BigDecimal(model.getManagerDividRate());
        model.setManagerDividRate(String.valueOf(managerDividRate.divide(new BigDecimal(100))));

        finaPrjFeeDivideDao.updateFinaPrjFeeDivide(params);


        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    @API(desc = "删除费用分成方案", params = "prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,upd_time,upd_user,remark", auth = APIAuth.NO)
    public String deleteFinaPrjFeeDivide(SqlParam<FinaPrjFeeDivide> params) throws Exception {

        finaPrjFeeDivideDao.deleteFinaPrjFeeDivide(params);

        return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
    }

    private boolean isExist(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        params.setMakeSql(false);
        SqlResult<FinaPrjFeeDivide> result = finaPrjFeeDivideDao.findFinaPrjFeeDividesOnly(params);
        if (result.getRows().size() > 0) {
            return true;
        }
        return false;
    }




}
