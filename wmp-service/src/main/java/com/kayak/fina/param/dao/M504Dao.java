package com.kayak.fina.param.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.M504;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

@Repository
public class M504Dao extends ComnDao {

	public SqlResult<M504> findFinaProdNavInfos(SqlParam<M504> params) throws Exception {
		return super.findRows("SELECT 'FINA' as system_no, ni.tano,ni.prod_code,ni.legal_code,ni.nav_date," +
				"ni.nav,ni.total_nav,convert(ni.seven_days_income,char) as seven_days_income,convert(ni.ten_thousand_income_amt,char) as ten_thousand_income_amt,ni.nav_type," +
				"ni.prod_total_vol,ni.total_income,ni.year_income_rate," +
				"ni.benchmarks,ni.achievement_pay_ratio,ni.float_manger_fee_ratio,ni.last_days_income_rate,ni.DAY_RISE,ni.WEEK_RISE,ni.THREE_MONTH_RISE,ni.HALF_YEAR_RISE,ni.NINE_MONTH_RISE,ni.YEAR_RISE,ni.ESTABLISH_RISE,ni.THIS_YEAR_RISE " +
				"FROM fina_nav_info ni", SubDatabase.DATABASE_FINA_CENTER, params);
	}

	public UpdateResult addFinaProdNavInfo(SqlParam<M504> params) throws Exception {
		return super.update("INSERT INTO fina_nav_info(tano,prod_code,legal_code,nav_date,nav,total_nav,seven_days_income,ten_thousand_income_amt,nav_type,prod_total_vol,total_income,year_income_rate,benchmarks,achievement_pay_ratio,float_manger_fee_ratio,last_days_income_rate,crt_date,upd_date) VALUES($S{tano},$S{prodCode},$S{legalCode},$S{navDate},$D{nav},$D{totalNav},$D{sevenDaysIncome},$D{tenThousandIncomeAmt},$S{navType},$D{prodTotalVol},$D{totalIncome},$D{yearIncomeRate},$D{benchmarks},$D{achievementPayRatio},$D{floatMangerFeeRatio},$D{lastDaysIncomeRate},date_format(curdate(),'%Y%m%d'),date_format(curdate(),'%Y%m%d'))",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult updateFinaProdNavInfo(SqlParam<M504> params) throws Exception {
		return super.update("UPDATE fina_nav_info SET nav=$D{nav} ,total_nav=$D{totalNav} ,seven_days_income=$D{sevenDaysIncome} ,ten_thousand_income_amt=$D{tenThousandIncomeAmt} ,nav_type=$S{navType} ,prod_total_vol=$D{prodTotalVol} ,total_income=$D{totalIncome} ,year_income_rate=$D{yearIncomeRate} ,benchmarks=$D{benchmarks} ,achievement_pay_ratio=$D{achievementPayRatio} ,float_manger_fee_ratio=$D{floatMangerFeeRatio} ,last_days_income_rate=$D{lastDaysIncomeRate},upd_date=date_format(curdate(),'%Y%m%d') WHERE  tano=$S{tano} AND prod_code=$S{prodCode} AND legal_code=$S{legalCode} AND nav_date=$S{navDate} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult deleteFinaProdNavInfo(SqlParam<M504> params) throws Exception {
		return super.update("DELETE FROM fina_nav_info WHERE  tano=$S{tano} AND prod_code=$S{prodCode} AND legal_code=$S{legalCode} AND nav_date=$S{navDate} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}

}
