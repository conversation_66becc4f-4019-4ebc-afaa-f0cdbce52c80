package com.kayak.fina.param.dao;

import com.alibaba.fastjson.JSONObject;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;

import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;

import com.kayak.fina.param.model.M559;
import org.springframework.stereotype.Repository;

@Repository
public class M559Dao extends ComnDao {

    public SqlResult<M559> findM559s(SqlParam<M559> params) throws Exception {
        return super.findRows("select t.app_serno,\n" +
                "       t.legal_code,\n" +
                "       t.busi_date,\n" +
                "       t.busi_code,\n" +
                "       t.tano,\n" +
                "       t.prod_code,\n" +
                "       t.trans_status,\n" +
                "       t.capital_status,\n" +
                "       t.cust_no,\n" +
                "       t.cust_name,\n" +
                "       t.id_type,\n" +
                "       t.id_code,\n" +
                "       DATE_FORMAT(t.mactime,'%H:%i:%s') as mactime,DATE_FORMAT(t.mactime,'%Y-%m-%d') as macdate,\n" +
                "       t.host_rtn_code,\n" +
                "       t.host_rtn_desc,\n" +
                "       t.host_trans_serno\n" +
                "  from FINA_CUST_TRANS_REQ_LOG t where 1=1 and t.capital_status in ('B','C','D','E','F','I')", SubDatabase.DATABASE_FINA_CENTER, params);
       // return null;
    }

    public UpdateResult updateErrorStatus(JSONObject params) throws Exception {
        String sql="UPDATE paym_capital_check_error SET error_status= $S{errorStatus}  WHERE  ERROR_SERNO=$S{errorSerno} ";
        return super.update(sql,SubDatabase.DATABASE_FINA_CENTER, params);
    }
}
