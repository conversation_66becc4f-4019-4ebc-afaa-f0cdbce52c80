package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.common.constants.BusiOfApply;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.Msg;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.M513BusiTypeDao;
import com.kayak.fina.param.model.M513;
import com.kayak.fina.param.model.M513BusiType;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "TA业务类型", model = M513.class)
public class M513BusiTypeService {
    @Autowired
    private M513BusiTypeDao m513BusiTypeDao;

    @API(desc="查询TA业务类型")
    public SqlResult<M513BusiType> findByDisCode(SqlParam<M513BusiType> param)throws Exception{

        SqlResult<M513BusiType> allList = m513BusiTypeDao.findByDisCode(param);
        allList.setRows(allList.getRows().stream().map(item->{
            String dataID = item.getTano() + "-"+item.getBusiCode()  + "-"+item.getDataStatus();
            String parentID = item.getTano() + "-"+item.getBusiCode()   + "-"+ DataStatus.EFFECTED;
            item.setDataId(dataID);
            item.setDataParentId(dataID.equals(parentID)?"":parentID);
            return item;
        }).collect(Collectors.toList()));
        allList.setResults(allList.getRows().size());
        return allList;
    }

    @API(desc="新增TA业务类型可批量")
    public SqlResult addBusis(SqlParam<M513BusiType> param)throws Exception{
        List<Msg> resultList = new ArrayList();
        if(BusiOfApply.ALL.getCode().equals(param.getModel().getBusiCode())){
            param.getModel().setBusiCode(BusiOfApply.getCodes());
        }
        String[] businesTypes = param.getModel().getBusiCode().split(",");
        SqlResult<M513BusiType> hasList = m513BusiTypeDao.checkIsExist(param);
        DaoUtil.doTrans(()->{
            for(String item: businesTypes){
                Msg msg = new Msg();
                param.getModel().setBusiCode(item);
                if(StringUtils.isNotBlank(item)) {
                    if (checkIsExist(item, hasList) == true) {//业务类型code不等于空并且校验唯一
                        m513BusiTypeDao.addBusiType(param);
                        msg.setMsg(String.format("TA代码：[%s],业务代码：[%s]。添加成功！", param.getModel().getTano(), BusiOfApply.codeOf(param.getModel().getBusiCode()).getDesc()));
                        msg.setType(Msg.Type.SUCCESS.getType());
                    } else {//code不为空的才有价值返回，这么写是因为枚举类里面获取所有code的时候会有逗号在前面
                        msg.setMsg(String.format("TA代码：[%s],业务代码：[%s]。添加失败!【已存在】", param.getModel().getTano(), BusiOfApply.codeOf(param.getModel().getBusiCode()).getDesc()));
                        msg.setType(Msg.Type.ERROR.getType());
                    }
                    resultList.add(msg);
                }
            }
        });
        return SqlResult.build(resultList);
    }


    @API(desc="删除TA业务")
    public String delBusinesType(SqlParam<M513BusiType> param)throws Exception{
        if(DataStatus.EFFECTED.equals(param.getModel().getDataStatus())){
            param.getModel().setDataStatus(DataStatus.DELETED);
            this.addBusis(param);
            return RequestSupport.updateReturnJson(true, "操作成功", null).toString();
        }else {
            m513BusiTypeDao.delBusiType(param);
            return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
        }

    }


    @API(desc="通过审批号查询历史信息",auth= APIAuth.NO)
    public SqlResult<M513BusiType> findHistory(SqlParam<M513BusiType> params)throws Exception{
        params.setMakeSql(false);
        SqlResult<M513BusiType> allList = m513BusiTypeDao.findHistory(params);
        allList.setRows(allList.getRows().stream().map(item->{
            String dataID = item.getTano() + "-"+item.getBusiCode()  + "-"+item.getDataStatus();
            String parentID = item.getTano() + "-"+item.getBusiCode()   + "-"+ DataStatus.EFFECTED;
            item.setDataId(dataID);
            item.setDataParentId(dataID.equals(parentID)?"":parentID);
            return item;
        }).collect(Collectors.toList()));
        return allList;
    }
    
    private boolean checkIsExist(String busiType,SqlResult<M513BusiType> hasList){
        for(M513BusiType item : hasList.getRows()){
            if(item.getBusiCode().equals(busiType)){
                return false;
            }
        }
        return true;
    }
}
