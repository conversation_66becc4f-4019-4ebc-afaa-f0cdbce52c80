package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M549Dao;
import com.kayak.fina.param.model.M549;
import com.kayak.fina.trans.model.M526;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "产品购买赎回即时查询报表", model = M549.class)
public class M549Service {

    @Autowired
    private M549Dao m549Dao;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "产品购买赎回即时查询报表", auth = APIAuth.YES)
    public SqlResult<M549> findM549s(SqlParam<M549> param)throws Exception {
        SqlResult<M549> sqlResult = m549Dao.findM549s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
       /** if(sqlResult != null && sqlResult.getRows().size() > 0){
            for (M549 m549 : sqlResult.getRows()){
                m549.setTaName(reportformUtil.getFinaTaName(m549.getTano()));
            }
        }*/
        return sqlResult;
    }
}
