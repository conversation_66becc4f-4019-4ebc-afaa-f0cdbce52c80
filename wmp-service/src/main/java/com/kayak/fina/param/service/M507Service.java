package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.M507Dao;
import com.kayak.fina.param.model.M507;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "产品分红表服务", model = M507.class)
public class M507Service {

	@Autowired
	private M507Dao m507Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M215Service m215Service;


	@API(desc = "查询产品分红信息", auth = APIAuth.YES)
	public SqlResult<M507> findFinaProdDivInfo(SqlParam<M507> params) throws Exception {
		SqlResult<M507> sqlResult = m507Dao.findFinaProdDivInfo(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
				if(Tools.isNotBlank(item.getProdCode())){
					Map<String, Object> prodParam = new HashMap<>();
					prodParam.put("systemNo", SystemNo.FINA);
					prodParam.put("prodCode", item.getProdCode());
					prodParam.put("supplyCode", item.getTano());
					prodParam.put("legalCode", "1000");
					List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
					if (prodParaList != null && prodParaList.size() > 0){
						Map<String, String> prodPara = prodParaList.get(0);
						item.setProdName(prodPara.get("prod_name"));
					}
				}
			} catch (Exception e) {
				throw new RuntimeException("M507错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		return sqlResult;
	}



}
