package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.M514ListDao;
import com.kayak.fina.param.model.M514;
import com.kayak.fina.param.model.M514List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 鎺ュ彛鐗堟湰涓庣储寮曞叧绯荤鐞?鎺ュ彛鏂囦欢鍒楄〃锛屽師鍚?Ta2017ListService
 */
@Service
@APIDefine(desc = "閿€鍞晢鍒楄〃鏂囦欢鍒楄〃", model = M514.class)
@RequiredArgsConstructor
@Slf4j
public class M514ListService {
    @Autowired
    private M514ListDao m514ListDao;

    @API(desc = "鏂囦欢鍒楄〃鏌ヨ", auth = APIAuth.NO)
    public SqlResult<M514List> findTaInterfaceFileListByNo(SqlParam<M514List> params) throws Exception {
        params.setMakeSql(true);

        return m514ListDao.findTaInterfaceFileListByNo(params);
    }

    @API(desc = "鏂囦欢鍒楄〃娣诲姞", params = "interface_id,index_no", auth = APIAuth.YES)
    public String addM514List(SqlParam<M514List> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛
        //鏌ヨ璇nterface_id瀵瑰簲鐨刬ndex_no缁撴灉闆?
        SqlResult<M514List> result = m514ListDao.findTaInterfaceFileListById(params);
        //濡傛灉娌℃煡鍑篿ndex_no锛岃鏄庤绱㈠紩鏂囦欢娌℃湁琚鎺ュ彛鏂囦欢鎵€缁戝畾锛屽垯鍙互杩涜娣诲姞
        if(result.getRows().size() == 0){
            m514ListDao.addTaInterfaceFileListDao(params);
            return RequestSupport.updateReturnJson(true, "鎿嶄綔鎴愬姛", null).toString();
        }else if(result.getRows().size() == 1){
            //褰撶粨鏋滈泦涓?锛岃鏄庡凡缁忕粦瀹氳繃
            return RequestSupport.updateReturnJson(false, "娣诲姞閲嶅", null).toString();
        }
        return RequestSupport.updateReturnJson(false, "娣诲姞澶辫触", null).toString();
    }


    @API(desc = "鏂囦欢鍒楄〃鏇存柊", params = "interface_id,index_no", auth = APIAuth.YES)
    public String updateM514List(SqlParam<M514List> params) throws Exception {
        //鑾峰彇褰撳墠鐢ㄦ埛
   
        UpdateResult result =  m514ListDao.updateTaInterfaceFileListDao(params);
        if(result.getEffect() > 0) {
            return RequestSupport.updateReturnJson(true, "鏇存柊鍐呭鎴愬姛", null).toString();
        } else if(result.getEffect() == -1) {
            return RequestSupport.updateReturnJson(false, "褰撳墠鎺ュ彛宸插瓨鍦ㄣ€? + params.getModel().getInterfaceName() + "绱㈠紩銆?, null).toString();
        } else {
            return RequestSupport.updateReturnJson(false, "鏇存柊鍐呭鍐呭澶辫触", null).toString();
        }
    }


    @API(desc = "鏂囦欢鍒楄〃鍒犻櫎", params = "index_no", auth = APIAuth.YES)
    public String deleteM514List(SqlParam<M514List> params) throws Exception {
        UpdateResult result = m514ListDao.deleteTaInterfaceFileListDao(params);

        return RequestSupport.updateReturnJson(true, "鍒犻櫎鎴愬姛", null).toString();
    }

   
}


