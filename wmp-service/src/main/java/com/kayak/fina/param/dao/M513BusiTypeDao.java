package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.OpenFlag;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M513BusiType;
import com.kayak.graphql.model.FetcherData;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M513BusiTypeDao extends ComnDao{

    private String property(){
        return "select  b.tano,b.BUSI_CODE,b.data_status ";
    }

    public SqlResult<M513BusiType> findByDisCode(SqlParam<M513BusiType> param)throws Exception{
        param.setMakeSql(true);
        return super.findRows(property() + " FROM fina_ta_busi_limit b ", SubDatabase.DATABASE_FINA_CENTER,param);
    }
    public SqlResult<M513BusiType> selectByDisCode(SqlParam<M513BusiType> param)throws Exception{
        param.setMakeSql(false);
        return super.findRows(property() + " FROM fina_ta_busi_limit b where b.tano =$S{tano} ",SubDatabase.DATABASE_FINA_CENTER,param);
    }

    public SqlResult<M513BusiType> checkIsExist(SqlParam<M513BusiType> param)throws Exception{
        param.setMakeSql(false);
        String sql = null;
        if(DataStatus.ADD.equals(param.getModel().getDataStatus())){
             sql = property() + " FROM fina_ta_busi_limit b  where b.tano=$S{tano} ";
        }else{
            sql = property() + " FROM fina_ta_busi_limit b  where b.tano=$S{tano} and data_status=$S{dataStatus} ";
        }
        return super.findRows(sql,SubDatabase.DATABASE_FINA_CENTER, param);
    }

    public void addBusiType(SqlParam<M513BusiType> param)throws Exception{
        super.update("insert into  fina_ta_busi_limit(tano,BUSI_CODE,data_status,create_time,update_time,legal_code,err_msg)values($S{tano},$S{busiCode},$S{dataStatus},CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,$S{legalCode},$S{errMsg})", SubDatabase.DATABASE_FINA_CENTER, param.getModel());
    }

    public void updateBusiType(SqlParam<M513BusiType> param)throws Exception{
        super.update("update fina_ta_busi_limit where tano=$S{tano} and BUSI_CODE=$S{busiCode} and data_status=$S{dataStatus} ", SubDatabase.DATABASE_FINA_CENTER, param.getModel());
    }

    public void delBusiType(SqlParam<M513BusiType> param)throws Exception{
        super.update("delete from fina_ta_busi_limit where tano=$S{tano} and BUSI_CODE=$S{busiCode} and data_status=$S{dataStatus} ", SubDatabase.DATABASE_FINA_CENTER, param.getModel());
    }

    public int addHis(SqlParam<M513BusiType> params , String openFlag , String sql) throws Exception{
        String sqlAll = "insert into TA_DISTRIBUTOR_BUSI_HIS(distributor_code,business_type,data_status,crt_user,crt_time,upd_time,upd_user,oper_user,oper_date,oper_flag,process_instance_id) " +
                "select distributor_code,business_type,data_status,crt_user,crt_time,upd_time,upd_user,$S{crtUser},current_timestamp,'"+openFlag+"',$S{processInstanceId} from TA_DISTRIBUTOR_BUSI  where "
                + sql ;
        String sqlDb2 = "insert into TA_DISTRIBUTOR_BUSI_HIS(distributor_code,business_type,data_status,crt_user,crt_time,upd_time,upd_user,oper_user,oper_date,oper_flag,process_instance_id) " +
                "select distributor_code,business_type,data_status,crt_user,crt_time,upd_time,upd_user,$S{crtUser},current timestamp,'"+openFlag+"',$S{processInstanceId} from TA_DISTRIBUTOR_BUSI  where "
                + sql ;
        Sql sqlExec = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
        return super.update(sqlExec,params.getModel()).getEffect();
    }
    public int clearData(SqlParam<M513BusiType> params, String openFlag, String clearSql) throws Exception {
        this.addHis(params,openFlag,"  distributor_code=$S{distributorCode}");
        return super.update("DELETE FROM TA_DISTRIBUTOR_BUSI WHERE DATA_STATUS in ("+clearSql+") and  DISTRIBUTOR_CODE=$S{distributorCode}"
                , params.getModel()).getEffect();
    }
    /**
     * 更新审批状态用 外层会有doTrans
     * */
    public void approveData(SqlParam<M513BusiType> params , String clearSql)throws Exception{
        SqlResult<M513BusiType> allList = this.selectByDisCode(params);
        SqlParam<M513BusiType> pojoParam =  new FetcherData<>(params.getParams() , M513BusiType.class);
        this.clearData(params, OpenFlag.APPROVE,clearSql);
        for(int i = 0;i < allList.getRows().size();i++){
            M513BusiType pojo = allList.getRows().get(i);
            if(DataStatus.EFFECTED .equals(pojo.getDataStatus())){
                continue;
            }
            BeanUtils.copyProperties(pojo,pojoParam.getModel());
            if(DataStatus.DELETED .equals(pojo.getDataStatus())){
                pojoParam.getModel().setDataStatus(DataStatus.EFFECTED);
                this.delBusiType(pojoParam);
            }
            if(DataStatus.APPROVE .equals(pojo.getDataStatus())||DataStatus.ADD.equals(pojo.getDataStatus())){
                pojoParam.getModel().setDataStatus(DataStatus.EFFECTED);
                this.addBusiType(pojoParam);
            }
            if(DataStatus.UPDATE.equals(pojo.getDataStatus())){
                pojoParam.getModel().setDataStatus(DataStatus.EFFECTED);
                this.updateBusiType(pojoParam);
            }
        }
    }
    public SqlResult<M513BusiType> findHistory(SqlParam<M513BusiType> params)throws Exception{
        return findRows(property() +"  FROM TA_DISTRIBUTOR_BUSI where process_instance_id=$S{processInstanceId}   ", params);
    }
}
