package com.kayak.fina.param.service;


import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.M513Dao;
import com.kayak.fina.param.model.M504;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M504Dao;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@APIDefine(desc = "产品行情信息服务", model = M504.class)
public class M504Service {

	@Autowired
	private M504Dao m504Dao;

	@Autowired
	private M513Dao m513Dao;


	@Autowired
	private M215Service m215Service;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询产品行情信息信息", auth = APIAuth.YES)
	public SqlResult<M504> findFinaProdNavInfos(SqlParam<M504> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<M504> sqlResult = m504Dao.findFinaProdNavInfos(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		List<M504> rows = sqlResult.getRows();
		Map<String, Object> prodParam = new HashMap<>();
		prodParam.put("systemNo", SystemNo.FINA);
		prodParam.put("legalCode", "1000");

		if (rows != null){
			for (M504 m504 : rows){

				if(Tools.isNotBlank(m504.getTano())){
					m504.setTaName(reportformUtil.getFinaTaName(m504.getTano()));
					prodParam.put("tano", m504.getTano());
				}
				if(Tools.isNotBlank(m504.getProdCode())){
					prodParam.put("prodCode", m504.getProdCode());
				}

				List<Map<String, String>> prodInfoList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));

				if (prodInfoList != null && prodInfoList.size() > 0){
					Map<String, String> map = prodInfoList.get(0);
					m504.setProdname(map.get("prod_name") == null ? "":map.get("prod_name"));
					m504.setProdAssType(map.get("prod_ass_type") == null ? "":map.get("prod_ass_type"));
				}
			}
		}
		return sqlResult;
	}

	@API(desc = "添加产品行情信息", params = "tano,prod_code,legal_code,nav_date,nav,total_nav,seven_days_income,ten_thousand_income_amt,nav_type,prod_total_vol,total_income,year_income_rate,benchmarks,achievement_pay_ratio,float_manger_fee_ratio,last_days_income_rate", auth = APIAuth.NO)
	public String addFinaProdNavInfo(SqlParam<M504> params) throws Exception {

		//通过Ta代码查询TA状态
		SqlRow sqlRow = m513Dao.findTaNum(params.getModel().getTano());
		if(Integer.parseInt(sqlRow.get("num").toString())>0){
			return   RequestSupport.updateReturnJson(false, "该Ta状态为停用状态", null).toString();
		}
		params.getModel().getNavDate();

		try {
			m504Dao.addFinaProdNavInfo(params).getEffect();
		}catch (Exception e){
			return   RequestSupport.updateReturnJson(false, "添加失败："+e.getMessage(), null).toString();
		}

		return RequestSupport.updateReturnJson(true, "添加成功", null).toString();
	}
	
	@API(desc = "修改产品行情信息", params = "tano,prod_code,legal_code,nav_date,nav,total_nav,seven_days_income,ten_thousand_income_amt,nav_type,prod_total_vol,total_income,year_income_rate,benchmarks,achievement_pay_ratio,float_manger_fee_ratio,last_days_income_rate", auth = APIAuth.NO)
	public int updateFinaProdNavInfo(SqlParam<M504> params) throws Exception {
		return m504Dao.updateFinaProdNavInfo(params).getEffect();
	}
	
	@API(desc = "删除产品行情信息", params = "tano,prod_code,legal_code,nav_date,nav,total_nav,seven_days_income,ten_thousand_income_amt,nav_type,prod_total_vol,total_income,year_income_rate,benchmarks,achievement_pay_ratio,float_manger_fee_ratio,last_days_income_rate", auth = APIAuth.NO)
	public int deleteFinaProdNavInfo(SqlParam<M504> params) throws Exception {
//		Date today = new Date();
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
//		String todayString = sdf.format(today);
//		String navDate = params.getModel().getNavDate();
//		if (StringUtils.isNotBlank(navDate)){
//			Integer todayInt = Integer.parseInt(todayString);
//			Integer navDateInt = Integer.parseInt(navDate);
//			if (navDateInt < todayInt){
//				throw new PromptException("无法删除：净值日期小于当前日期");
//			}
//		}
		return m504Dao.deleteFinaProdNavInfo(params).getEffect();
	}

}
