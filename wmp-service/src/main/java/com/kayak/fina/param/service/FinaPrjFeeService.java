package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.FeeType;
import com.kayak.common.constants.OpenFlag;
import com.kayak.core.dao.DaoService;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.FinaPrjFeeDao;
import com.kayak.fina.param.dao.FinaPrjFeeListDao;
import com.kayak.fina.param.model.FinaPrjFee;
import com.kayak.fina.param.model.FinaPrjFeeList;
import com.kayak.graphql.model.FetcherData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.UUID;

@Service
@APIDefine(desc = "交易费用方案服务", model = FinaPrjFee.class)
public class FinaPrjFeeService {
    private final static Logger log = LoggerFactory.getLogger(FinaPrjFeeService.class);

    @Autowired
    private FinaPrjFeeDao finaPrjFeeDao;
    @Autowired
    private FinaPrjFeeListDao finaPrjFeeListDao;
    @Autowired
    private DaoService daoService;

    @API(desc = "查询交易费用方案信息", auth = APIAuth.YES)
    public SqlResult<FinaPrjFee> findFinaPrjFees(SqlParam<FinaPrjFee> params) throws Exception {
        params.setMakeSql(true);
        //SqlResult<FinaPrjFee> finaPrjFees = finaPrjFeeDao.findFinaPrjFees(params);
        SqlResult<FinaPrjFee> finaPrjFees = finaPrjFeeDao.select(params);
        finaPrjFees.getRows().forEach(e -> {
            e.setFeeTypeDesc(FeeType.codeOf(e.getFeeType()).getDesc());
        });
        return finaPrjFees;
    }

    @API(desc = "添加交易费用方案", params = "prod_code,fee_code,fee_type,enable_date,charge_type,buyfee_mode,buyfee_method,rate_calculate_method,rate_merge_method,backfee_calculate_method,redemfee_asset_method,redemfee_asset_rat,fee_role,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
    public String addFinaPrjFee(SqlParam<FinaPrjFee> params) throws Exception {
        //校验是否存在
        if (finaPrjFeeDao.checkExist(params)) {
            throw new PromptException("方案已经存在");
        }

        //数据库内存除以了100的数
        String redemfeeAssetRat = params.getModel().getRedemfeeAssetRat();
        if (!Tools.strIsEmpty(redemfeeAssetRat)) {
            params.getModel().setRedemfeeAssetRat(String.valueOf(new BigDecimal(redemfeeAssetRat).multiply(BigDecimal.valueOf(0.01))));
        }

        params.getModel().setFeeCode(UUID.randomUUID().toString().substring(10));
        daoService.doTrans(() -> {
            //插入主方案
            finaPrjFeeDao.addFinaPrjFee(params);
            //插入明细方案
//			if(ChargeType.SCALE.equals(params.getModel().getChargeType())){
            //增加明细方案
            params.getModel().getFinaPrjFeeLists().forEach(e -> {
                try {
                    SqlParam<FinaPrjFeeList> finaPrjFeeListParam =
                            new FetcherData<>(new HashMap<>(), FinaPrjFeeList.class);
                    BeanUtils.copyProperties(e, finaPrjFeeListParam.getModel());
                    finaPrjFeeListParam.getModel().setFeeCode(params.getModel().getFeeCode());
                    //数据库内存的费率时除以100的数，在SQL中做了
                    finaPrjFeeListDao.addFinaPrjFeeList(finaPrjFeeListParam);
                } catch (Exception ex) {
                    log.warn("WarnMsg:[{}]", ex.getMessage(), ex);
                }
            });
//			}
        });

        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }

    @API(desc = "修改交易费用方案", params = "prod_code,tano,fee_code,fee_type,enable_date,charge_type,buyfee_mode,buyfee_method,rate_calculate_method,rate_merge_method,backfee_calculate_method,redemfee_asset_method,redemfee_asset_rat,fee_role,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
    public String updateFinaPrjFee(SqlParam<FinaPrjFee> params) throws Exception {

        //数据库内存除以了100的数
        String redemfeeAssetRat = params.getModel().getRedemfeeAssetRat();
        if (!Tools.strIsEmpty(redemfeeAssetRat)) {
            params.getModel().setRedemfeeAssetRat(String.valueOf(new BigDecimal(redemfeeAssetRat).multiply(BigDecimal.valueOf(0.01))));
        }
/*         分销审批流相关,先注释掉
        判断是修改   生效数据,还是修改  修改的数据
        if(DataStatus.EFFECTED.equals(params.getModel().getDataStatus())) {
            params.getModel().setDataStatus(DataStatus.UPDATE);
            daoService.doTrans(() -> {
                params.getModel().setFeeCode(UUID.randomUUID().toString().substring(10));
                //修改主方案
                finaPrjFeeDao.addFinaPrjFee(params);
                //增加新明细
                addFinaPrjFeeList(params);
            });
        }else{
            daoService.doTrans(() -> {
                //主方案插入历史表
                finaPrjFeeDao.addHist(params, OpenFlag.UPDATE);
                //明细插入历史表
                addFinaPrjFeeListToHis(params);
                //删除原有的明细
                deleteFinaPrjFeeList(params);
                //修改主方案
                finaPrjFeeDao.updateFinaPrjFee(params);
                //增加新明细
                addFinaPrjFeeList(params);
            });
        }*/
        daoService.doTrans(() -> {
            //删除原有的明细
            deleteFinaPrjFeeList(params);
            //修改主方案
            finaPrjFeeDao.updateFinaPrjFee(params);
            //增加新明细
            addFinaPrjFeeList(params);
        });

        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    private void addFinaPrjFeeList(SqlParam<FinaPrjFee> params) {

        params.getModel().getFinaPrjFeeLists().forEach(e -> {
            try {
                SqlParam<FinaPrjFeeList> listParam =
                        new FetcherData<>(new HashMap<>(), FinaPrjFeeList.class);
                BeanUtils.copyProperties(e, listParam.getModel());
                listParam.getModel().setFeeCode(params.getModel().getFeeCode());
                finaPrjFeeListDao.addFinaPrjFeeList(listParam);
            } catch (Exception ex) {
                log.warn("WarnMsg:[{}]", ex.getMessage(), ex);
            }
        });
    }

    private void deleteFinaPrjFeeList(SqlParam<FinaPrjFee> params) throws Exception {
        SqlParam<FinaPrjFeeList> listParam = new FetcherData<>(new HashMap<>(), FinaPrjFeeList.class);
        listParam.getModel().setFeeCode(params.getModel().getFeeCode());
        finaPrjFeeListDao.deleteFinaPrjFeeList(listParam);
    }

    @API(desc = "删除交易费用方案", params = "prod_code,fee_code,fee_type,enable_date,charge_type,buyfee_mode,buyfee_method,rate_calculate_method,rate_merge_method,backfee_calculate_method,redemfee_asset_method,redemfee_asset_rat,fee_role,crt_time,crt_user,upd_time,upd_user,remark,data_status", auth = APIAuth.NO)
    public String deleteFinaPrjFee(SqlParam<FinaPrjFee> params) throws Exception {
        if (DataStatus.EFFECTED.equals(params.getModel().getDataStatus())) {
            params.getModel().setDataStatus(DataStatus.DELETED);
            finaPrjFeeDao.addFinaPrjFee(params);

        } else {
            finaPrjFeeDao.deleteFinaPrjFee(params);
            deleteFinaPrjFeeList(params);
        }

        return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
    }



}
