package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M545;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M545Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M545> findM545s(SqlParam<M545> params) throws  Exception{
        params.setMakeSql(true);
        String sql = "select tano,trans_date,prod_code,\n"
                +"sum(case when cust_type = '1' then stock_vol else 0 end) as per_vol,\n"
                +"sum(case when cust_type != '1' then stock_vol else 0 end) as org_vol,\n"
                +"sum(stock_vol) as vol,\n"
                +"sum(case when cust_type = '1' then effe_num else 0 end) as per_num,\n"
                +"sum(case when cust_type != '1' then effe_num else 0 end) as org_num\n"
                +" from fina_vol_stock where 1=1 " +
                "group by tano,trans_date,prod_code";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }

    public M545 coutRaiAmt(M545 param)throws  Exception{
        String sql = "select sum(app_amt) as rai_amt from FINA_CUST_TRANS_REQ_LOG\n"+
                "where prod_code = $S{prodCode} and tano = $S{tano} and busi_code = '020'";
        return super.findRow(M545.class,sql,SubDatabase.DATABASE_FINA_CENTER,param);
    }
}
