package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;

import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.FinaPrjTailingCommisionList;
import org.springframework.stereotype.Repository;

@Repository
public class FinaPrjTailingCommisionListDao extends ComnDao {

	public SqlResult<FinaPrjTailingCommisionList> findFinaPrjTailingCommisionLists(SqlParam<FinaPrjTailingCommisionList> params) throws Exception {
		String sql = "SELECT tailing_commision_code,dimension1_min,dimension1_max,rate*100 AS rate,crt_time,crt_user,upd_time,upd_user,remark FROM fina_prj_tailing_commision_LIST WHERE tailing_commision_code=$S{tailingCommisionCode} ORDER BY dimension1_min";



		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER,params);
	}

	public int addFinaPrjTailingCommisionList(SqlParam<FinaPrjTailingCommisionList> params) throws Exception {
		String sql = "INSERT INTO fina_prj_tailing_commision_list  " +
                " (tailing_commision_code,  " +
                " dimension1_min,  " +
                " dimension1_max,  " +
                " rate,  " +
                " crt_time,  " +
                " crt_user,  " +
                " upd_time,  " +
                " upd_user,  " +
                " remark " +
                " ) " +
                " VALUES " +
                " ($S{tailingCommisionCode},  " +
                " $S{dimension1Min},  " +
                " $S{dimension1Max},  " +
                " $D{rate}/100,  " +
                " CURRENT_TIMESTAMP,  " +
                " $S{crtUser},  " +
                " CURRENT_TIMESTAMP,  " +
                " $S{updUser},  " +
                " $S{remark} " +
                " )";

		return super.update(sql,SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
	}
	

	public int deleteFinaPrjTailingCommisionList(SqlParam<FinaPrjTailingCommisionList> params) throws Exception {
		return super.update("DELETE FROM fina_prj_tailing_commision_LIST WHERE tailing_commision_code = $S{tailingCommisionCode} ",SubDatabase.DATABASE_SYS_CENTER,
				params.getModel()).getEffect();
	}



}
