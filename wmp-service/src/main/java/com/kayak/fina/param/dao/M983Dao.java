package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M983;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

/**
 * @ClassName M983Dao
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/2 15:25
 * @Version 1.0
 **/
@Repository
public class M983Dao extends ComnDao {

    /**
     * 理财账户类历史交易流水查询
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M983> findFinaCustParam(SqlParam<M983> params) throws Exception {
        //params.setMakeSql(true);
        String sql1 = " select APP_SERNO,trans_Code,\n" +
                "               busi_Code,\n" +
                "               tano,\n" +
                "               ta_Acct_No,\n" +
                "               acct_No,\n" +
                "               cust_Name,\n" +
                "               instrepr_Id_Type,\n" +
                "               instrepr_Id_Code,\n" +
                "               branch_Code,\n" +
                "               sub_Branch_Code,\n" +
                "               trans_Acct_No,\n" +
                "               busi_Date,\n" +
                "               DATE_FORMAT(mactime,'%Y-%m-%d %H:%i:%s') as mactime,\n" +
                "               cust_Manager,\n" +
                "               ack_Date,\n" +
                "               trans_Status,\n" +
                "               channel_Flag from fina_cust_acct_req_log_h ";
        sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M983.class);

        String sql =
                "select * from ( " + sql1 + ")tt1 where 1=1 ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
    }

}
