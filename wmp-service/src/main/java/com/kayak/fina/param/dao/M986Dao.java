package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M986;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

/**
 * @ClassName M985Dao
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/5 9:22
 * @Version 1.0
 **/
@Repository
public class M986Dao extends ComnDao {

    /**
     * 理财账户类交易确认流水查询
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M986> findFinaCustCfmParam(SqlParam<M986> params) throws Exception {
        //params.setMakeSql(true);
        String sql1 = " SELECT\n" +
                "   t1.TA_ACK_SERNO,\n" +
                "   t1.busi_Code,\n" +
                "   t1.tano,\n" +
                "   t1.ta_Acct_No,\n" +
                "   t1.acct_No,\n" +
                "   t1.cust_Name,\n" +
                "   t1.instrepr_Id_Type,\n" +
                "   t1.instrepr_Id_Code,\n" +
                "   t1.trans_Acct_No,\n" +
                "   t1.busi_Date,\n" +
                "   DATE_FORMAT(mactime,'%Y-%m-%d %H:%i:%s') AS mactime,\n" +
                "   t1.cust_Manager,\n" +
                "   t1.ack_Date,\n" +
                "   t1.trans_Status,\n" +
                "   t2.ta_name\n" +
                "   FROM\n" +
                "   FINA_CUST_ACCT_CFM_LOG_H t1 " +
                "   left join fina_ta_info t2 on t1.tano = t2.tano ";
        sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M986.class);
        String sql =
                "select * from ( " + sql1 + ")tt1 where 1=1 ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
    }

}
