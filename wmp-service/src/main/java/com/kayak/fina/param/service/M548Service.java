package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.dao.M548Dao;
import com.kayak.fina.param.model.M548;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财产品存量统计表-按客户经理", model = M548.class)
public class M548Service {

    @Autowired
    private M548Dao m548Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财产品存量统计表-按客户经理", auth = APIAuth.YES)
    public SqlResult<M548> findM548s(SqlParam<M548> param)throws Exception {
        SqlResult<M548> sqlResult = m548Dao.findM548s(param);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M548> volList = sqlResult.getRows();
        for (M548 m548 : volList){
            M001 m001 = m001Dao.get(m548.getTransOrgno());
            if (m001 != null){
                m548.setOrgName(m001.getOrgname());
            }
            m548.setTaName(reportformUtil.getFinaTaName(m548.getTano()));
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m548.getProdCode());
            prodParam.put("supplyCode", m548.getTano());
            prodParam.put("legalCode", "1000");
            prodParam.put("userid",param.getParams().get("userid"));
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                m548.setProdName(prodPara.get("prod_name"));
            }
        }
        return sqlResult;
    }
}
