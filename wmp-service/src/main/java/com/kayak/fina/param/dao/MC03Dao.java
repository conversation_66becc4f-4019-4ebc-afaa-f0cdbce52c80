package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.MC03;
import org.springframework.stereotype.Repository;

@Repository
public class MC03Dao extends ComnDao {
    public SqlResult<MC03> findMC03s(SqlParam<MC03> params) throws Exception {
        String sql = "SELECT a.trfr_serno,a.channel_serno,a.entrust_serno," +
                "a.busi_date,a.channel_date,a.channel_time," +
                "a.trans_counter,a.trans_bank_code,a.trans_branch_code," +
                "a.trans_sub_branch,a.channel_flag,a.cust_no,a.trans_acct_no," +
                "a.acct_no,a.cust_type,a.cust_name,a.id_type,a.id_code," +
                "a.entrust_trans_no,a.entrust_cust_no,a.entrust_capital_no," +
                "a.entrust_cust_type,a.entrust_cust_name,a.entrust_id_type," +
                "a.entrust_id_code,a.system_no,a.tano,a.prod_code," +
                "a.legal_code,a.trfr_amt,a.trfr_vol,a.trfr_fee_obj,a.trfr_fee_mode," +
                "a.fee_amt,a.trfr_status,a.trfr_in_type,b.fee_precision_mode," +
                "b.amt_precision_mode,a.rtn_code,a.rtn_desc,a.rtn_trans_serno\n " +
                "FROM TRFR_CUST_TRANS_LOG a left join TRFR_CUST_ENTRUST_LOG b on a.entrust_serno = b.entrust_serno";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }
}
