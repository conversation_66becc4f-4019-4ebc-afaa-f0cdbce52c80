package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.FinaTaInterfaceRelation;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;

@Repository
public class FinaTaInterfaceRelationDao extends ComnDao {
    /**
     * 查询TA
     */
    public SqlResult<FinaTaInterfaceRelation> findTa(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        String systemNo = params.getModel().getSystemNo();
        if ("FINA".equals(systemNo)) {
            return super.findRows("SELECT ta_name,tano " +
                    "FROM fina_ta_info " +
                    "WHERE ta_status = '1' ORDER BY tano DESC" , SubDatabase.DATABASE_FINA_CENTER, params);
        } else if ("FUND".equals(systemNo)) {
            return super.findRows("SELECT ta_name,tano " +
                    "FROM fund_ta_info " +
                    "WHERE ta_status = '1' ORDER BY tano DESC" , SubDatabase.DATABASE_FUND_CENTER, params);
        } else {
            SqlResult<FinaTaInterfaceRelation> result = new SqlResult<>();
            result.setRows(new ArrayList<>());
            result.setResults(0);
            return result;
        }
    }

    /**
     * 查询未绑定接口的TA
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<FinaTaInterfaceRelation> findNewTa(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        return super.findRows("SELECT ta_name,tano " +
                "FROM fina_ta_info " +
                "WHERE ta_status = '1' " +
                "AND tano not in (SELECT tano FROM fina_ta_interface_relation) " +
                "ORDER BY tano DESC", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /*
    查询接口版本号
    */
    public SqlResult<FinaTaInterfaceRelation> findTaInterfaceVersion(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        return super.findRows("SELECT DISTINCT INTERFACE_VERSION " +
                "FROM fina_ta_interface_detail_info " , SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /*
    查询----> TA接口关联关系维护信息
    */
    public SqlResult<FinaTaInterfaceRelation> findTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        return super.findRows("SELECT ta_name,tano,interface_type,interface_version,data_status,remark " +
                "FROM fina_ta_interface_relation " +
                "ORDER BY tano DESC" , SubDatabase.DATABASE_FINA_CENTER, params);
    }


    /*
    添加----> TA接口关联关系维护信息
    */
    public UpdateResult addTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        String sql = "INSERT INTO fina_ta_interface_relation ( tano, ta_name, interface_type, interface_version, data_status, remark, create_time, create_user, update_time, update_user ) " +
                "VALUES($S{tano},(SELECT ta_name FROM fina_ta_info WHERE tano = $S{tano})," +
                "$S{interfaceType},$S{interfaceVersion},$S{dataStatus},$S{remark},now(),$S{createUser},now(),$S{updateUser})";
        return super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /*
    修改----> TA接口关联关系维护信息
    */
    public UpdateResult updateTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        String sql = "UPDATE fina_ta_interface_relation " +
                "SET tano = $S{tano},ta_name = $S{taName},interface_type = $S{interfaceType},interface_version = $S{interfaceVersion}," +
                "remark = $S{remark},update_time = now(),update_user = $S{updateUser}" +
                "WHERE ta_name = $S{taName} " +
                "AND tano = $S{tano} ";
        return super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /*
    停用----> TA接口关联关系维护信息
    */
    public UpdateResult stopTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        String sql = "UPDATE fina_ta_interface_relation " +
                "SET DATA_STATUS = 'D' " +
                "WHERE ta_name = $S{taName} " +
                "AND tano = $S{tano} " +
                "AND interface_version = $S{interfaceVersion} ";
        return super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /*
    启用----> TA接口关联关系维护信息
    */
    public UpdateResult startTaInterfaceRelation(SqlParam<FinaTaInterfaceRelation> params) throws Exception {
        String sql = "UPDATE fina_ta_interface_relation " +
                "SET DATA_STATUS = 'E' " +
                "WHERE ta_name = $S{taName} " +
                "AND tano = $S{tano} " +
                "AND interface_version = $S{interfaceVersion} ";
        return super.update(sql, SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /*
    唯一性校验----> TA接口关联关系维护信息
    */
    public SqlResult<FinaTaInterfaceRelation> onlyTano(SqlParam<FinaTaInterfaceRelation> params) throws Exception{
        return super.findRows("SELECT * FROM fina_ta_interface_relation WHERE tano = $S{tano}", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /*
    判断接口是否正常有效----> 接口信息明细表
   */
    public SqlResult<FinaTaInterfaceRelation> findInterfaceDataStatus(SqlParam<FinaTaInterfaceRelation> params) throws Exception{
        return super.findRows("SELECT data_status FROM fina_ta_interface_detail_info WHERE data_status = 'D' AND interface_version = $S{interfaceVersion}", SubDatabase.DATABASE_FINA_CENTER, params);
    }
}