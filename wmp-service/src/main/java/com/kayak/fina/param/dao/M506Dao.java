package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M506;
import com.kayak.until.MakeSqlUntil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M506Dao extends ComnDao {
    @Autowired
    private ReportformUtil reportformUtil;
    /**
     * 底层交易确认流水查询
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M506> findFinaTFilec2(SqlParam<M506> params) throws Exception {
        return super.findRows("select t.sequenceno,\n" +
                "       t.oorgno,\n" +
                "       t.fundcode,\n" +
                "       t.shareclass,\n" +
                "       t.branchcode,\n" +
                "       t.buypayperiod,\n" +
                "       t.redemptionpayperiod,\n" +
                "       t.conversionpayperiod,\n" +
                "       t.chargepaymethod,\n" +
                "       t.wholeflag,\n" +
                "       t.modifyway,\n" +
                "       t.operatedate,\n" +
                "       t.downloaddate\n" +
                "  from fina_t_filec2 t\n", SubDatabase.DATABASE_FINA_CENTER, params);
    }
}
