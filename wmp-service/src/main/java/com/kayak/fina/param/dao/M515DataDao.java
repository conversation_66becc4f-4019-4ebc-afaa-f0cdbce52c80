package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.global.utils.Tools;
import com.kayak.fina.param.model.M515Data;
import com.kayak.fina.param.model.M515Index;
import org.springframework.stereotype.Repository;

/**
 * 描述： 中登文件dao，原名 TaInterfaceDataFileDao
 * 创建人： cy
 */
@Repository
public class M515DataDao extends ComnDao {
    /**
     * @param params 传入参数
     * @return 查询所有中登文件结果
     * @throws Exception
     */
    public SqlResult<M515Data> findTaInterfaceDataFileDao(SqlParam<M515Data> params) throws Exception {
        return super.findRows("SELECT file_id, file_name, interface_file_type, index_no, name_rules, file_type, is_skip, " +
                " is_vaildate_profile, datasource, data_status from WMP_INTERFACE_DATA_FILE", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /**
     * @param params 传入参数
     * @return 查询单个中登文件结果
     * @throws Exception
     */
    public SqlResult<M515Data> findTaInterfaceDataFileById(SqlParam<M515Data> params) throws Exception {
        return super.findRows("SELECT file_id, file_name, interface_file_type, index_no, name_rules, file_type, is_skip, " +
                        " is_vaildate_profile, datasource, data_status from WMP_INTERFACE_DATA_FILE where file_id = $S{fileId}",
                SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /**
     * @param params 传入参数
     * @return 根据索引查询中登文件结果
     * @throws Exception
     */
    public SqlResult<M515Data> findTaInterfaceDataFileByNo(SqlParam<M515Data> params) throws Exception {
        return super.findRows("SELECT file_id, interface_file_type, file_name, index_no, name_rules, file_type, is_skip, " +
                " is_vaildate_profile, data_status ,datasource " +
                "from WMP_INTERFACE_DATA_FILE where index_no = $S{indexNo}", SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /**
     * @param params 传入参数
     * @return 查询结果
     * @throws Exception
     */
    public SqlResult<M515Index> findTaInterfaceIndexFileName(SqlParam<M515Index> params) throws Exception {
        return super.findRows("SELECT index_no, index_name, direction, name_rules, is_vaildate_profile, create_time, " +
                " create_user, update_time, update_user, data_status " +
                " from WMP_INTERFACE_INDEX_FILE where index_name = $S{indexName}", SubDatabase.DATABASE_FINA_CENTER, params);
    }


    /**
     * @param params 传入参数
     * @return 添加中登文件是否成功
     * @throws Exception
     */
    public UpdateResult addTaInterfaceDataFileDao(SqlParam<M515Data> params) throws Exception {

        params.getModel().setFileId(Tools.getSeqValue());

        Sql sql = Sql.build()
                .mysqlSql("INSERT INTO WMP_INTERFACE_DATA_FILE (file_id, file_name, interface_file_type, index_no, name_rules, " +
                        " file_type, is_skip, is_vaildate_profile, datasource, data_status, create_user, " +
                        " update_user)  " +
                        "VALUES($S{fileId},$S{fileName},$S{interfaceFileType},$S{indexNo},$S{nameRules},$S{fileType},$S{isSkip}," +
                        " $S{isVaildateProfile},$S{datasource},$S{dataStatus},$S{createUser},$S{updateUser})")
                .oracleSql("INSERT INTO WMP_INTERFACE_DATA_FILE (file_id, file_name, interface_file_type, index_no, name_rules, " +
                        " file_type, is_skip, is_vaildate_profile, datasource, data_status, create_user, " +
                        " update_user)  " +
                        "VALUES($S{fileId},$S{fileName},$S{interfaceFileType},$S{indexNo},$S{nameRules},$S{fileType},$S{isSkip}," +
                        " $S{isVaildateProfile},$S{datasource},$S{dataStatus},$S{createUser},$S{updateUser})")
                .db2Sql("INSERT INTO WMP_INTERFACE_DATA_FILE (file_id, file_name, interface_file_type, index_no, name_rules, file_type, is_skip," +
                        " is_vaildate_profile, datasource, data_status, create_user, update_user)  " +
                        "VALUES($S{fileId},$S{fileName},$S{interfaceFileType},$S{indexNo},$S{nameRules},$S{fileType},$S{isSkip}," +
                        "$S{isVaildateProfile},$S{datasource},$S{dataStatus},$S{createUser},$S{updateUser})");

        return super.update(sql,
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     * @param params 传入参数
     * @return 更新中登文件是否成功
     * @throws Exception
     */
    public UpdateResult updateTaInterfaceDataFileDao(SqlParam<M515Data> params) throws Exception {
        Sql sql = Sql.build()
                .mysqlSql("UPDATE WMP_INTERFACE_DATA_FILE set is_skip = $S{isSkip},is_vaildate_profile = $S{isVaildateProfile},file_type=$S{fileType} ,file_id = $S{fileId},file_name = $S{fileName},interface_file_type=$S{interfaceFileType},datasource = $S{datasource},name_rules=$S{nameRules}, data_status = $S{dataStatus},update_time = current_timestamp,update_user = $S{updateUser} WHERE file_id = $S{fileId}")
                .oracleSql("UPDATE WMP_INTERFACE_DATA_FILE set is_skip = $S{isSkip},is_vaildate_profile = $S{isVaildateProfile},file_type=$S{fileType} ,file_id = $S{fileId},file_name = $S{fileName},interface_file_type=$S{interfaceFileType},datasource = $S{datasource},name_rules=$S{nameRules}, data_status = $S{dataStatus},update_time = current_timestamp,update_user = $S{updateUser} WHERE file_id = $S{fileId}")
                .db2Sql("UPDATE WMP_INTERFACE_DATA_FILE set is_skip = $S{isSkip},is_vaildate_profile = $S{isVaildateProfile},file_type=$S{fileType}, file_id = $S{fileId},file_name = $S{fileName},interface_file_type=$S{interfaceFileType},datasource = $S{datasource},name_rules=$S{nameRules}, data_status = $S{dataStatus},update_time = current timestamp,update_user = $S{updateUser} WHERE file_id = $S{fileId}");
        return super.update(sql,
                SubDatabase.DATABASE_FINA_CENTER, params.getModel());
    }

    /**
     * @param params 传入参数
     * @return 查询是否有这个文件代码的文件
     * @throws Exception
     */
    public SqlResult<M515Data> findTaInterfaceDataFileByBusiCode(SqlParam<M515Data> params) throws Exception {
        // 对于这条sql，脑子转不过来。。。
        return super.findRows("SELECT interface_file_type from WMP_INTERFACE_DATA_FILE " +
                "where interface_file_type = $S{interfaceFileType} and index_no = $S{indexNo}", SubDatabase.DATABASE_FINA_CENTER, params);

    }


    /**
     * @param params 传入参数
     * @return 删除中登文件是否成功
     * @throws Exception
     */
    public UpdateResult deleteTaInterfaceDataFileDao(SqlParam<M515Data> params) throws Exception {
        return super.update("DELETE FROM WMP_INTERFACE_DATA_FILE where file_id = $S{fileId}", SubDatabase.DATABASE_FINA_CENTER,
                params.getModel());
    }

    /**
     * @param params 传入参数
     * @return 根据索引号删除中登文件
     * @throws Exception
     */
    public UpdateResult deleteTaInterfaceDataFileByNo(SqlParam<M515Index> params) throws Exception {
        return super.update("DELETE FROM WMP_INTERFACE_DATA_FILE where index_no = $S{indexNo}", SubDatabase.DATABASE_FINA_CENTER,
                params.getModel());
    }
}


