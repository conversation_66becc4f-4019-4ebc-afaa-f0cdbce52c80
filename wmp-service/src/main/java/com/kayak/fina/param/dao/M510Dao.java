package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.M510;
import com.kayak.fina.param.model.ProdAcctInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 银行账号维护， 原名 FinaAcctInfoDao
 */
@Repository
public class M510Dao extends ComnDao {

	public SqlResult<M510> findFinaAcctInfos(SqlParam<M510> params) throws Exception {
		return super.findRows("SELECT 'FINA' as system_no, acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code," +
				"inter_code,acct_status,remark,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,TANO,PROD_ACCT_TYPE,IN_OUT_FLAG FROM fina_acct_info order by OPEN_PROVINCE,tano,acct_serno", SubDatabase.DATABASE_FINA_CENTER,params);
	}
	public List<M510> listFinaAcctInfosByNo(M510 param) throws Exception {
		return super.findRows(M510.class,"SELECT 'FINA' as system_no, acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code," +
				"inter_code,acct_status,remark,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,TANO,PROD_ACCT_TYPE,IN_OUT_FLAG FROM fina_acct_info where acct_no = '"+param.getAcctNo()+"' and prod_acct_type = '"+param.getProdAcctType()+"'", SubDatabase.DATABASE_FINA_CENTER,param);
	}
	public List<M510> listFinaAcctInfosByName(M510 param) throws Exception {
		return super.findRows(M510.class,"SELECT 'FINA' as system_no, acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code," +
				"inter_code,acct_status,remark,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO FROM fina_acct_info where acct_name = '"+param.getAcctName()+"'", SubDatabase.DATABASE_FINA_CENTER,param);
	}
	public UpdateResult addFinaAcctInfo(SqlParam<M510> params) throws Exception {
		return super.update("INSERT INTO fina_acct_info(acct_serno,legal_code,acct_no,acct_name,open_bank,open_province,open_city,exchange_code,inter_code,acct_status,remark" +
						",CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,TANO,PROD_ACCT_TYPE,IN_OUT_FLAG ) " +
						"VALUES($AUTOIDS{acctSerno},$S{legalCode},$S{acctNo},$S{acctName},$S{openBank},$S{openProvince},$S{openCity},$S{exchangeCode},$S{interCode}" +
						",$S{acctStatus},$S{remark},$S{cur},$S{hostProdType},$S{hostAcctSeqNo},$S{hostAcctType},$S{hostOrgno},$S{tano},$S{prodAcctType},$S{inOutFlag})",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult updateFinaAcctInfo(SqlParam<M510> params) throws Exception {
		return super.update("UPDATE fina_acct_info SET acct_no=$S{acctNo} ,acct_name=$S{acctName} ,open_bank=$S{openBank} ,open_province=$S{openProvince} ," +
						"open_city=$S{openCity} ,exchange_code=$S{exchangeCode} ,inter_code=$S{interCode}" +
						" ,acct_status=$S{acctStatus} ,remark=$S{remark},CUR=$S{cur},HOST_PROD_TYPE=$S{hostProdType} ,HOST_ACCT_SEQ_NO=$S{hostAcctSeqNo} ," +
						"HOST_ACCT_TYPE=$S{hostAcctType} ,HOST_ORGNO=$S{hostOrgno} ,TANO=$S{tano} ,PROD_ACCT_TYPE=$S{prodAcctType},IN_OUT_FLAG=$S{inOutFlag}   WHERE  acct_serno=$S{acctSerno} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}
	
	public UpdateResult deleteFinaAcctInfo(SqlParam<M510> params) throws Exception {
		return super.update("DELETE FROM fina_acct_info WHERE  acct_serno=$S{acctSerno} ",
				SubDatabase.DATABASE_FINA_CENTER,params.getModel());
	}

	/**
	 * <AUTHOR>
	 * @Description 获取银行账户是否有产品正在使用
	 * @Date 2022/4/7
	 * @Param [params]
	 * @return com.kayak.core.sql.SqlResult<com.kayak.fina.param.model.ProdAcctInfo>
	 **/
	public SqlResult<ProdAcctInfo> getProdAcct(SqlParam<ProdAcctInfo> params)throws Exception {
		return super.findRows("select f1.tano, f1.prod_code, f1.legal_code, f1.prod_acct_type, f1.acct_serno from fina_prod_acct_info f1 ", SubDatabase.DATABASE_FINA_CENTER,params);
	}

}
