package com.kayak.fina.param.service;

import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.param.dao.MC04Dao;
import com.kayak.fina.param.model.MC04;
import com.kayakwise.plan.api.TD28DubboDecorator;
import com.kayakwise.plan.req.TD28ServiceRequest;
import com.kayakwise.plan.resp.TD28ServiceResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@APIDefine(desc = "转让差错", model = MC04.class)
@Slf4j
public class MC04Service {

    @Autowired
    private MC04Dao mC04Dao;

    @CloudReference
    private TD28DubboDecorator td28ServiceDecorator;

    private List<String> list = Arrays.asList("1", "2", "3","4");

    public MC04Service() {
    }

    @API(desc = "查询转让差错", auth = APIAuth.NO)
    public SqlResult<MC04> findMC04(SqlParam<MC04> params) throws Exception {
        MC04 model = params.getModel();
        params.setMakeSql(true);
        return mC04Dao.findMC04s(params);
    }

    @API(desc = "拒绝")
    public String refuse(SqlParam<MC04> params) throws Exception {
        MC04 model = params.getModel();
        if (!"0".equals(model.getTrfrErrorStatus())){
            throw new PromptException("已经在处理中或处理成功禁止再次提交");
        }
        if (!list.contains(model.getTrfrErrorType())){
            throw new PromptException("该错误类型不能解冻");
        }
        //todo 调用核心接口
        TD28ServiceRequest td28ServiceRequest = new TD28ServiceRequest();
        td28ServiceRequest.setErrorSerno(params.getModel().getErrorSerno());
        TD28ServiceResponse td28ServiceResponse = td28ServiceDecorator.execute(td28ServiceRequest);
        if(!td28ServiceResponse.getRtnCode().equals("000000")){
            throw new PromptException("MC04","处理失败：失败原因："+td28ServiceResponse.getRtnDesc());
        }
        return RequestSupport.updateReturnJson(true, "返款成功", null).toString();
    }

    @API(desc = "线下处理")
    public int deal(SqlParam<MC04> params) throws Exception {
        MC04 model = params.getModel();
        if (!"0".equals(model.getTrfrErrorStatus())){
            throw new PromptException("已经在处理中或处理成功禁止再次提交");
        }
        if (list.contains(model.getTrfrErrorType())){
            model.setTrfrErrorMode("1");
        }else {
            model.setTrfrErrorMode("2");
        }

        model.setTrfrErrorStatus("4");
        int effect = mC04Dao.deal(model).getEffect();
        return effect;
    }

    @API(desc = "返款")
    public String refund(SqlParam<MC04> params) throws Exception {

        MC04 model = params.getModel();
        if (!"0".equals(model.getTrfrErrorStatus())){
            throw new PromptException("已经在处理中或处理成功禁止再次提交");
        }
        if (list.contains(model.getTrfrErrorType())){
            throw new PromptException("该错误类型不能还款");
        }
        //todo 调用核心接口
        TD28ServiceRequest td28ServiceRequest = new TD28ServiceRequest();
        td28ServiceRequest.setErrorSerno(params.getModel().getErrorSerno());
        TD28ServiceResponse td28ServiceResponse = td28ServiceDecorator.execute(td28ServiceRequest);
        if(!td28ServiceResponse.getRtnCode().equals("000000")){
            throw new PromptException("MC04","处理失败：失败原因："+td28ServiceResponse.getRtnDesc());
        }
        return RequestSupport.updateReturnJson(true, "返款成功", null).toString();
    }
}
