package com.kayak.fina.param.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.common.constants.SubDatabase;
import com.kayak.fina.param.dao.ProdAllAcctInfoDao;
import com.kayak.fina.param.model.M510;
import com.kayak.fina.param.model.ProdAcctInfo;
import com.kayak.fina.param.model.ProdAllAcctInfo;
import com.kayak.graphql.model.FetcherData;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "产品账户信息表服务", model = ProdAcctInfo.class)
public class ProdAllAcctInfoService {

    @Autowired
    private ProdAllAcctInfoDao finaProdAllAcctInfoDao;

    /**
     * 以下是产品账号相关
     **/
    @API(desc = "查询账号类型、银行账号编号、银行账号名称 ", auth = APIAuth.YES)
    public SqlResult<ProdAcctInfo> findProdAcctInfos(SqlParam<ProdAcctInfo> params) throws Exception {
        return finaProdAllAcctInfoDao.queryProdAcctInfo(params);
    }

    @API(desc = "查询银行账号编号和名称 ", auth = APIAuth.YES)
    public SqlResult<M510> findFinaAcctNoAndName(SqlParam<M510> params) throws Exception {
        return finaProdAllAcctInfoDao.queryFinaAcctNoAndName(params);
    }

    /**
     * 保存:修改产品账号信息
     **/
    @API(desc = "修改产品账号信息", params = "prod_code,tano,legal_code,prod_acct_type,acct_serno,remark", auth = APIAuth.NO)
    public int updateProdAcctInfo(SqlParam<ProdAllAcctInfo> params) throws Exception {
        List<ProdAcctInfo> acctInfoList = params.getModel().getAcctInfoList();
        FetcherData<T> fetcherData = (FetcherData) params;
        //获取参数
        String acctList = fetcherData.getParamsDirect().get("acctList").toString();
        String legalCode = fetcherData.getParamsDirect().get("legalCode").toString();
        String tano = fetcherData.getParamsDirect().get("tano").toString();
        String prodCode = fetcherData.getParamsDirect().get("prodCode").toString();
        JSONArray AcctInfoList = JSONArray.parseArray(acctList); //json字符串转为json数组

        Map<String, Object> map = params.getParams();

        DaoUtil.doTrans(() -> {
            //先清空表里该产品对应的所有账号信息
            finaProdAllAcctInfoDao.deleteProdAcctInfo(params);
            //再循环插入前端传递的所有账号信息
            if (AcctInfoList != null) {
                AcctInfoList.stream().forEach(item -> {
                    JSONObject _item = (JSONObject) item;
                    map.put("legalCode", legalCode);
                    map.put("tano", tano);
                    map.put("prodCode", prodCode);
                    map.put("prodAcctType", _item.get("prodAcctType"));
                    map.put("acctSerno", _item.get("acctSerno"));
                    try {
                        finaProdAllAcctInfoDao.addProdAcctInfo(map);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
//            for (ProdAcctInfo acctInfo : acctInfoList) {
//                acctInfo.setLegalCode(params.getModel().getLegalCode());
//                acctInfo.setProdCode(params.getModel().getProdCode());
//                acctInfo.setTano(params.getModel().getTano());
//                SqlParam<ProdAcctInfo> sqlParam = new FetcherData<>(new HashMap<>(), ProdAcctInfo.class);
//                BeanUtils.copyProperties(acctInfo, sqlParam.getModel());
//                finaProdAllAcctInfoDao.addProdAcctInfo(sqlParam);
//            }
        }, SubDatabase.DATABASE_FINA_CENTER);  //需要指定sharding，事务才生效
        return 2;
    }
}
