package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.V507;
import org.springframework.stereotype.Repository;

import java.util.Map;


/**
 * 冻结记录查询
 *
 * <AUTHOR>
 * @date 2021-04-25 15:13
 */
@Repository
public class V507Dao extends ComnDao {

    public SqlResult<V507> findFrozenRecords(SqlParam<V507> params) throws Exception {
        /**
         * 查询客户份额冻结：根据客户号查询
         */
        //根据客户号查询客户的份额汇总
        String volSql = " SELECT  frozen_serno , cust_no , prod_code , tano , legal_code ,  frozen_cause , frozen_vol , contract_no , frozen_enddate , frozen_desc , unfrozen_vol ,unfrozen_date, frozen_amt, trans_acct_no "+
                    " FROM fina_cust_frozen_record ";
        return super.findRows(volSql, SubDatabase.DATABASE_FINA_CENTER, params);
    }

    /**
     * <AUTHOR>
     * @Description 客户份额冻结登记表，FROZEN_TRANS_ORGNO(冻结交易机构)改为合并机构
     * @Date 2022/3/24
     * @Param [params]
     * @return com.kayak.core.sql.UpdateResult
     **/
    public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
        return super.update("UPDATE fina_cust_frozen_record SET FROZEN_TRANS_ORGNO = $S{mergeOrgno} WHERE FROZEN_TRANS_ORGNO = $S{removeOrgno} ",
                SubDatabase.DATABASE_FINA_CENTER,params);
    }


}
