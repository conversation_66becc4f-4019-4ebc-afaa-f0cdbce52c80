package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M550;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M550Dao extends ComnDao {
    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M550> findM550s(SqlParam<M550> params) throws  Exception{
        params.setMakeSql(true);
        String sql = "select t.tano,\n" +
                "       t.trans_date,\n" +
                "       t.orgno,\n" +
                "       t.prod_code,\n" +
                "       t.stock_amt,\n" +
                "       t.stock_num,\n" +
                "       t.effe_num,\n" +
                "       t2.ta_name\n" +
                "  from fina_sale_stock t\n" +
                "  left join fina_ta_info t2 on t.tano = t2.tano\n" +
                " where 1 = 1\n"+
                " order by t.trans_date desc ";

        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }
}
