package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import com.kayak.cust.model.M101;
import com.kayak.fina.param.dao.M519Dao;
import com.kayak.fina.param.model.M519;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "客户交易申请实体类服务", model = M519.class)
public class M519Service {

	@Autowired
	private M519Dao m519Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询客户交易信息", auth = APIAuth.YES)
	public SqlResult<M519> findFinaCustTransReqLog(SqlParam<M519> params) throws Exception {

		SqlResult<M519> sqlResult = m519Dao.findFinaCustTransReqLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				Map<String,Object> map2 = new HashMap<>();
				map2.put("orgno",item.getTransOrgno());
				map2.put("userid",params.getParams().get("userid"));
				SqlParam<M001> param = new FetcherData<>(map2, M001.class);
				param.setMakeSql(true);
				SqlResult<M001> m001Info1 = m001Dao.find(param);
				if(m001Info1.getRows() != null && m001Info1.getRows().size() > 0){
					item.setTransOrgnoName(m001Info1.getRows().get(0).getOrgname());
				}
			} catch (Exception e) {
				throw new RuntimeException("M519错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(false);
		return sqlResult;


	}


}
