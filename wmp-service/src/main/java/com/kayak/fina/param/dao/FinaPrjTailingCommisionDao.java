package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.FinaPrjTailingCommision;

import org.springframework.stereotype.Repository;

@Repository
public class FinaPrjTailingCommisionDao extends ComnDao {


    public SqlResult<FinaPrjTailingCommision> findPrjTailingCommisions(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        String sql = "SELECT  prod_code,  " +
                " tano,  " +
                " legal_code,  " +
                " tailing_commision_code,  " +
                " enable_date,  " +
                " tailing_calc_mode,  " +
                " tailing_commision_yeardays,  " +
                " min_pay_amt,  " +
                " graduated_calc,  " +
                " crt_time,  " +
                " crt_user,  " +
                " upd_time,  " +
                " upd_user,  " +
                " remark,  " +
                " rate_type,  " +
                " constant_rate*100 constant_rate " +
                " FROM  " +
                " fina_prj_tailing_commision  " +
                " WHERE prod_code=$S{prodCode} AND " +
                " tano=$S{tano} AND " +
                " legal_code=$S{legalCode} ";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);

    }

    public SqlResult<FinaPrjTailingCommision> findFinaPrjTailingCommision(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        String sql = "SELECT  prod_code,        " +
                " tano,                         " +
                " legal_code,                   " +
                " tailing_commision_code,       " +
                " enable_date,                  " +
                " tailing_calc_mode,            " +
                " tailing_commision_yeardays,   " +
                " min_pay_amt,                  " +
                " graduated_calc,               " +
                " crt_time,                     " +
                " crt_user,                     " +
                " upd_time,                     " +
                " upd_user,                     " +
                " remark,                       " +
                " rate_type,                    " +
                " constant_rate                 " +
                " FROM                          " +
                " fina_prj_tailing_commision    " +
                " WHERE prod_code=$S{prodCode} AND   " +
                " tano=$S{tano} AND             " +
                " legal_code=$S{legalCode} AND  " +
                " enable_date=$S{enableDate}    ";

        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public int addFinaPrjTailingCommision(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        String sql = "INSERT INTO fina_prj_tailing_commision   " +
                " (prod_code,   " +
                " tano,   " +
                " legal_code,   " +
                " tailing_commision_code,   " +
                " enable_date,   " +
                " tailing_calc_mode,   " +
                " tailing_commision_yeardays,   " +
                " min_pay_amt,   " +
                " graduated_calc,   " +
                " crt_time,   " +
                " crt_user,   " +
                " upd_time,   " +
                " upd_user,   " +
                " remark,   " +
                " rate_type,   " +
                " constant_rate  " +
                " )  " +
                " VALUES  " +
                " ($S{prodCode},   " +
                " $S{tano},   " +
                " $S{legalCode},   " +
                " $S{tailingCommisionCode},   " +
                " $S{enableDate},   " +
                " $S{tailingCalcMode},   " +
                " $S{tailingCommisionYeardays},   " +
                " $D{minPayAmt},   " +
                " $S{graduatedCalc},   " +
                " CURRENT_TIMESTAMP,   " +
                " $S{crtUser},   " +
                " CURRENT_TIMESTAMP,   " +
                " $S{updUser},   " +
                " $S{remark},   " +
                " $S{rateType},   " +
                " $D{constantRate}  " +
                " )";
        return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public int updateFinaPrjTailingCommision(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        String sql = "update fina_prj_tailing_commision set  " +
                "tailing_commision_code=$S{tailing_commision_code}, " +
                "enable_date=$S{enable_date}, " +
                "tailing_calc_mode=$S{tailing_calc_mode}, " +
                "tailing_commision_yeardays=$S{tailing_commision_yeardays}, " +
                "min_pay_amt=$D{min_pay_amt}, " +
                "graduated_calc=$S{graduated_calc}, " +
                "upd_time=CURRENT_TIMESTAMP, " +
                "upd_user=$S{upd_user}, " +
                "remark=$S{remark}, " +
                "rate_type=$S{rate_type}, " +
                "constant_rate=$D{constant_rate} " +
                "where  " +
                "prod_code=$S{prod_code} and " +
                "tano=$S{tano} and " +
                "legal_code=$S{legal_code}";
        return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public int update(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        String sql = "update fina_prj_tailing_commision set  " +
                "tailing_commision_code=$S{tailing_commision_code}, " +
                "enable_date=$S{enable_date}, " +
                "tailing_calc_mode=$S{tailing_calc_mode}, " +
                "tailing_commision_yeardays=$S{tailing_commision_yeardays}, " +
                "min_pay_amt=$D{min_pay_amt}, " +
                "graduated_calc=$S{graduated_calc}, " +
                "upd_time=CURRENT_TIMESTAMP, " +
                "upd_user=$S{upd_user}, " +
                "remark=$S{remark}, " +
                "rate_type=$S{rate_type}, " +
                "constant_rate=$D{constant_rate} " +
                "where  " +
                "prod_code=$S{prod_code} and " +
                "tano=$S{tano} and " +
                "legal_code=$S{legal_code}";
        return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public void deleteFinaPrjTailingCommision(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        doTrans(() -> {
            this.delete(params);
            this.deleteList(params);
        });

    }

    public void delete(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        super.update("DELETE FROM fina_prj_tailing_commision WHERE "
                        + "	tailing_commision_code=$S{tailingCommisionCode} AND enable_date=$S{enableDate} AND prod_code=$S{prodCode} AND legal_code = $S{legalCode} and tano=$S{tano} ",SubDatabase.DATABASE_SYS_CENTER,
                params.getModel());
    }


    public void deleteList(SqlParam<FinaPrjTailingCommision> params) throws Exception {
        super.update("DELETE FROM fina_prj_tailing_commision_list WHERE tailing_commision_code = $S{tailingCommisionCode} ",SubDatabase.DATABASE_SYS_CENTER,
                params.getModel());
    }


}
