package com.kayak.fina.param.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.fina.param.model.FinaPrjFeeDivide;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Repository
public class FinaPrjFeeDivideDao extends ComnDao {

    private String property() {
        return "SELECT div.prod_code,div.distributor_code,div.fee_type,div.enable_date,div.divid_type,"
                + " div.crt_time,div.crt_user,div.upd_time,div.upd_user,div.remark,div.data_status ";
    }

    /**
     * 2020/10/09   因为工作流新增筛选条件筛选状态为E的产品 和  E的销售商
     *
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<FinaPrjFeeDivide> findFinaPrjFeeDivides(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        String sql = property() + ",info.prod_name_short,dis.distributor_name,div.manager_divid_rate*100 AS manager_divid_rate,div.distributor_divid_rate*100 AS distributor_divid_rate "
                + " FROM fina_prj_fee_divide div"
                + "   LEFT JOIN fina_prod_info info ON div.prod_code = info.prod_code AND info.data_status='" + DataStatus.EFFECTED + "'"
                + "  LEFT JOIN ta_distributor_info dis ON div.distributor_code=dis.distributor_code  AND dis.data_status='" + DataStatus.EFFECTED + "'";

        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
    }

    /**
     * 2020/10/09   因为工作流新增筛选条件筛选状态为E的产品 和  E的销售商
     *
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<FinaPrjFeeDivide> findFinaPrjFeeDividesByDisCode(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        String sql = property() + ",div.add_from,info.prod_name_short,dis.distributor_name,div.manager_divid_rate*100 AS manager_divid_rate,div.distributor_divid_rate*100 AS distributor_divid_rate "
                + " FROM fina_prj_fee_divide div"
                + "   LEFT JOIN fina_prod_info info ON div.prod_code = info.prod_code AND info.data_status='" + DataStatus.EFFECTED + "'"
                + "  LEFT JOIN ta_distributor_info dis ON div.distributor_code=dis.distributor_code  AND dis.data_status='" + DataStatus.EFFECTED + "' where 1=1";
        if (!StringUtils.isEmpty(params.getModel().getProdCode())) {
            sql += " and  div.prod_code like '%$U{prodCode}%'";
        }
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
    }

    /**
     * * 2020/10/09   因为工作流新增筛选条件筛选状态为E的销售商 和 状态为E的产品
     *
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<FinaPrjFeeDivide> findFinaPrjFeeDividesByProdCode(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        String sql = "SELECT prod_code, " +
                "legal_code, " +
                "tano, " +
                "fee_type, " +
                "enable_date, " +
                "divid_type, " +
                "manager_divid_rate*100 manager_divid_rate, " +
                "distributor_divid_rate*100 distributor_divid_rate, " +
                "crt_time, " +
                "crt_user, " +
                "upd_time, " +
                "upd_user, " +
                "remark, " +
                "min_discount*100 min_discount, " +
                "resolve_way " +
                "FROM " +
                "fina_prj_fee_divide " +
                "WHERE prod_code=$S{prodCode} AND " +
                "tano=$S{tano} AND " +
                "legal_code=$S{legalCode}  ";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public SqlResult<FinaPrjFeeDivide> findFinaPrjFeeDividesOnly(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        String sql = "SELECT " +
                "prod_code, " +
                "legal_code, " +
                "tano, " +
                "fee_type, " +
                "enable_date, " +
                "divid_type, " +
                "manager_divid_rate, " +
                "distributor_divid_rate, " +
                "crt_time, " +
                "crt_user, " +
                "upd_time, " +
                "upd_user, " +
                "remark, " +
                "min_discount, " +
                "resolve_way " +
                "FROM " +
                "fina_prj_fee_divide " +
                "WHERE prod_code=$S{prodCode} AND " +
                "tano=$S{tano} AND " +
                "legal_code=$S{legalCode} AND " +
                "fee_type=$S{feeType} AND " +
                "enable_date=$S{enableDate}";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public void addFinaPrjFeeDivide(List<FinaPrjFeeDivide> params) throws Exception {
        doTrans(() -> {
            for (FinaPrjFeeDivide finaPrjFeeDivide : params) {
                //插入产品交易费用费用分成方案表
                String sql = "INSERT INTO fina_prj_fee_divide " +
                        "(prod_code, " +
                        "legal_code, " +
                        "tano, " +
                        "fee_type, " +
                        "enable_date, " +
                        "divid_type, " +
                        "manager_divid_rate, " +
                        "distributor_divid_rate, " +
                        "crt_time, " +
                        "crt_user, " +
                        "upd_time, " +
                        "upd_user, " +
                        "remark, " +
                        "min_discount, " +
                        "resolve_way" +
                        ")" +
                        "VALUES" +
                        "($S{prodCode}, " +
                        "$S{legalCode}, " +
                        "$S{tano}, " +
                        "$S{feeType}, " +
                        "$S{enableDate}, " +
                        "$S{dividType}, " +
                        "$D{managerDividRate}, " +
                        "$D{distributorDividRate}, " +
                        "CURRENT_TIMESTAMP, " +
                        "$S{crtUser}, " +
                        "CURRENT_TIMESTAMP, " +
                        "$S{updUser}, " +
                        "$S{remark}, " +
                        "$D{minDiscount}, " +
                        "$S{resolveWay}" +
                        ")";

                super.update(sql, SubDatabase.DATABASE_SYS_CENTER, finaPrjFeeDivide);
            }

        });


    }

    public void updateFinaPrjFeeDivide(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        String sql = "UPDATE fina_prj_fee_divide " +
                " SET " +
                "divid_type = $S{dividType}, " +
                "manager_divid_rate = $D{managerDividRate}, " +
                "distributor_divid_rate = $D{distributorDividRate}, " +
                "upd_time = CURRENT_TIMESTAMP, " +
                "upd_user = $S{updUser}, " +
                "remark = $S{remark}, " +
                "min_discount = $D{minDiscount}, " +
                "resolve_way = $S{resolveWay}" +
                " WHERE " +
                "prod_code = $S{prodCode} AND " +
                "legal_code = $S{legalCode} AND " +
                "tano = $S{tano} AND " +
                "fee_type = $S{feeType} AND " +
                "enable_date = $S{enableDate} ";

        super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public void deleteFinaPrjFeeDivide(SqlParam<FinaPrjFeeDivide> params) throws Exception {

        String sql = "DELETE FROM  fina_prj_fee_divide  " +
                " WHERE " +
                " prod_code = $S{prodCode} AND  " +
                " legal_code = $S{legalCode} AND  " +
                " tano = $S{tano} AND  " +
                " fee_type = $S{feeType} AND  " +
                " enable_date = $S{enableDate} ";
        super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public int add(SqlParam<FinaPrjFeeDivide> params) throws Exception {
        String sqlAll = "INSERT INTO fina_prj_fee_divide(prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,"
                + "	upd_time,upd_user,remark,data_status,add_from) VALUES($S{prodCode},$S{distributorCode},$S{feeType},$S{enableDate},$S{dividType},$D{managerDividRate},$D{distributorDividRate}, "
                + "	current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark},$S{dataStatus},$S{addFrom})";
        String sqlDb2 = "INSERT INTO fina_prj_fee_divide(prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,"
                + "	upd_time,upd_user,remark,data_status,add_from) VALUES($S{prodCode},$S{distributorCode},$S{feeType},$S{enableDate},$S{dividType},$D{managerDividRate},$D{distributorDividRate}, "
                + "	current timestamp,$S{crtUser},current timestamp,$S{updUser},$S{remark},$S{dataStatus},$S{addFrom})";
        Sql sqlExec = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
        return super.update(sqlExec, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
    }

    public void addUpdateFinaPrjFeeDivide(List<FinaPrjFeeDivide> params) throws Exception {
        //认购费
        final String SUBSCRIBE    = "0";
        final String SALESSERVICE = "7";  //销售服务费
        final String MANAGE       = "8";      //管理费
        List         list         = new ArrayList();
        //查询到所有封闭式产品的编号， 封闭式产品只允许插入认购费/销售服务费/管理费 1
        List<SqlRow> closeProd = super.findRows("SELECT prod_code FROM fina_prod_info where period_type = '1'");
        closeProd.stream().forEach(e -> {
            list.add(e.get("prod_code"));
        });

        doTrans(() -> {

            for (FinaPrjFeeDivide finaPrjFeeDivide : params) {
                //产品是否是封闭产品，插入类型是否是认购费、销售服务费、管理费
                if (list.contains(finaPrjFeeDivide.getProdCode()) & !(SUBSCRIBE.equals(finaPrjFeeDivide.getFeeType()) | SALESSERVICE.equals(finaPrjFeeDivide.getFeeType()) | MANAGE.equals(finaPrjFeeDivide.getFeeType()))) {
                    continue;
                }
                List<SqlRow> findRows = super.findRows("SELECT 1 FROM fina_prj_fee_divide WHERE prod_code = $S{prodCode} AND distributor_code = $S{distributorCode} "
                        + "	AND fee_type = $S{feeType} AND enable_date = $S{enableDate} and data_status=$S{dataStatus}", finaPrjFeeDivide);
                if (findRows.size() > 0) {
                    continue;
                }

                //插入产品交易费用费用分成方案表
                String sqlAll = "INSERT INTO fina_prj_fee_divide(prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,data_status"
                        + "	upd_time,upd_user,remark) VALUES($S{prodCode},$S{distributorCode},$S{feeType},$S{enableDate},$S{dividType},$D{managerDividRate},$D{distributorDividRate}, "
                        + "	current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark},$S{dataStatus})";
                String sqlDb2 = "INSERT INTO fina_prj_fee_divide(prod_code,distributor_code,fee_type,enable_date,divid_type,manager_divid_rate,distributor_divid_rate,crt_time,crt_user,data_status"
                        + "	upd_time,upd_user,remark) VALUES($S{prodCode},$S{distributorCode},$S{feeType},$S{enableDate},$S{dividType},$D{managerDividRate},$D{distributorDividRate}, "
                        + "	current timestamp,$S{crtUser},current timestamp,$S{updUser},$S{remark},$S{dataStatus})";
                Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
                super.update(sql, finaPrjFeeDivide);
            }

        });
    }


}
