package com.kayak.fina.param.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.M517Dao;
import com.kayak.fina.param.model.M517;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "存量信息服务", model = M517.class)
public class M517Service {

    @Autowired
    private M517Dao m517Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询存量信息", auth = APIAuth.YES)
    public SqlResult<M517> findFinaStocks(SqlParam<M517> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M517> sqlResult = m517Dao.findFinaStocks(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M517> volList = sqlResult.getRows();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        for (M517 m517 : volList){
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m517.getProdCode());
            prodParam.put("supplyCode", m517.getTano());
            prodParam.put("legalCode", "1000");
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                m517.setPeriodType(prodPara.get("period_type"));
                m517.setProdName(prodPara.get("prod_name"));
                m517.setSubsBeginDate(prodPara.get("subs_begin_date"));
                m517.setSubsEndDate(prodPara.get("subs_end_date"));
                m517.setEstablishDate(prodPara.get("establish_date"));
                m517.setIncomeDate(prodPara.get("income_start_date"));
                m517.setWindingDate(prodPara.get("end_date"));
                if (Tools.isNotBlank(m517.getEstablishDate()) && Tools.isNotBlank(m517.getWindingDate())){
                    Date start = sdf.parse(m517.getEstablishDate());
                    Date end = sdf.parse(m517.getWindingDate());
                    int days = getDayDiffer(start,end) + 1;
                }
            }
           // m517.setTaName(reportformUtil.getFinaTaName(m517.getTano()));
        }
        return sqlResult;
    }

    public static int getDayDiffer(Date startDate, Date endDate) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        long startDateTime = dateFormat.parse(dateFormat.format(startDate)).getTime();
        long endDateTime = dateFormat.parse(dateFormat.format(endDate)).getTime();
        return (int) ((endDateTime - startDateTime) / (1000 * 3600 * 24));
    }
}
