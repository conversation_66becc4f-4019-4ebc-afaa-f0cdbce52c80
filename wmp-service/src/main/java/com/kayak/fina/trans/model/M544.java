package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m544Service",table="")
public class M544 {
    @GraphQLField(label = "TA代码", field = "tano",sql = "tano = $S{tano}" )
    private String tano;
    @GraphQLField(label = "产品代码", field = "prod_code",sql = "prod_code = $S{prodCode}" )
    private String prodCode;
    @GraphQLField( kkhtml = "KFieldSelect", label = "客户类型", field = "cust_type",
            kkhtmlExt="{\"data-dict\":\"cust_type\"}",kkhtmlDefault=true)
    private String custType;
   // 红利/红利再投资基数
    @GraphQLField(label = "客户份额", field = "base_vol")
    private String baseVol;

    @GraphQLField(label = "分红方式", field = "def_div_method")
    private String defDivMethod;
    //红利资金
    @GraphQLField(label = "分红金额", field = "div_amt")
    private String divAmt;
    //红利再投资基金份数
    @GraphQLField(label = "分红份额", field = "div_vol")
    private String divVol;

    @GraphQLField(label = "日期", field = "div_date")
    private String divDate;

    @GraphQLField( kkhtml = "KFieldDate", label = "开始分红日期", field = "begin_date",kkhtmlDefault=true)
    private String beginDate;

    @GraphQLField(kkhtml = "KFieldDate",label = "结束分红日期", field = "end_date",kkhtmlDefault=true)
    private String endDate;

    @GraphQLField(label ="TA名称", field = "ta_name")
    private String taName;

    @GraphQLField(label ="产品名称", field = "prod_name")
    private String prodName;

    @GraphQLField(label ="法人代码", field = "legal_code")
    private String legalCode;

}
