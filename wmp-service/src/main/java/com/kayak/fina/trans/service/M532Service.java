package com.kayak.fina.trans.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.ErrorDealType;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M532Dao;
import com.kayak.fina.trans.model.M532;
import com.kayak.fund.model.M610;
import com.kayakwise.fina.api.T250DubboDecorator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "资金划拨", model = M532.class)
public class M532Service {
    protected static final Logger log = LoggerFactory.getLogger(M532Service.class);
    @CloudReference
    private T250DubboDecorator t250ServiceDecorator;

    @Autowired
    private M532Dao m532Dao;

    @API(desc = "查询资金划拨信息", auth = APIAuth.YES)
    public SqlResult<M532> findM532s(SqlParam<M532> params) throws Exception {
        params.setMakeSql(true);
        return m532Dao.findM532s(params);
    }

    @API(desc = "差错处理", auth = APIAuth.NO)
    public String dealError(SqlParam<M610> params) throws Exception {
        log.error("调用开始");
        //获取管理台选中的数据
        String chooseData = (String) params.getParams().get("chooseData");

        JSONArray json = JSONArray.parseArray(chooseData);

        if (json.size() <= 0) {
            log.error("所选数组长度为0");
            throw new Exception("所选数组长度为0");
        }

        //差错处理类型
        String errorDealType = "";

        if (params.getModel().getErrorDealType().equals("dealError")){
            errorDealType = ErrorDealType.DEALERROR.getType();
        }else{
            errorDealType = ErrorDealType.OFFLINE.getType();
        }

        //组装批量失败记录流水号
        List<String> errorSernoList = new ArrayList<>();

        for (int i=0;i<json.size();i++){
            JSONObject _json = (JSONObject) json.get(i);
            errorSernoList.add((String) _json.get("errorSerno"));
        }

//        T639ServiceRequest t639ServiceRequest = new T639ServiceRequest();
//        t639ServiceRequest.setErrorDealType(errorDealType);
//        t639ServiceRequest.setErrorSernoList(errorSernoList);
       /** T639ServiceResponse response = t639ServiceDecorator.execute(t639ServiceRequest);

        int count = 0;

        //返回成功
        if (!StringUtils.isEmpty(response) && "000000".equals(response.getRtnCode())){
            count = response.getSuccNum();
        }
*/
        //返回页面参数
        Map<String,Object> map1 = new HashMap<>();
       // map1.put("count",count);
        map1.put("length",json.size());
return null;
      // return RequestSupport.updateReturnJson(true, "后台"+(errorDealType.equals(ErrorDealType.DEALERROR.getType())?"调账":"线下处理")+"完成，共选择"+json.size()+"条差错，其中成功"+count+"条", map1).toString();
    }

}
