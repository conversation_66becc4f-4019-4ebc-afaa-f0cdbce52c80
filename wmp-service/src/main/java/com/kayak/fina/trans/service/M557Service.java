package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;

import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M557Dao;
import com.kayak.fina.trans.model.M557;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "理财聚合交易流水", model = M557.class)
public class M557Service {
    @Autowired
    private M557Dao m557Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财聚合交易流水", auth = APIAuth.YES)
    public SqlResult<M557> findM557(SqlParam<M557> params) throws Exception {
        SqlResult<M557> sqlResult = m557Dao.findM557(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        //sqlResult.setDesensitized(false);
        return sqlResult;
    }

}
