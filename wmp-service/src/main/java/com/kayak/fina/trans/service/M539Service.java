package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M539Dao;
import com.kayak.fina.trans.model.M539;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.model.M215S;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "产品份额查询", model = M539.class)
public class M539Service {
    @Resource
    private M215Service m215Service;
    @Autowired
    private M539Dao m539Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "产品份额查询", auth = APIAuth.YES)
    public SqlResult<M539> findCustVol(SqlParam<M539> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M539> sqlResult = m539Dao.findCustVol(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M539> volList = sqlResult.getRows();
        for (M539 m539:volList) {
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("supplyCode", m539.getTano());
            prodParam.put("prodCode", m539.getProdCode());
            prodParam.put("legalCode", m539.getLegalCode());


            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            for(Map<String, String> prodPara : prodParaList) {
                m539.setProdName(prodPara.get("prod_name"));	//产品名称
               }

        }

        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
