package com.kayak.fina.trans.service;

import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.RemainQuotaQueryType;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.trans.model.M537;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayakwise.prod.in.T203DubboDecorator;
import com.kayakwise.prod.model.RemainQuotaDetail;
import com.kayakwise.prod.req.T203ServiceRequest;
import com.kayakwise.prod.resp.T203ServiceResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@APIDefine(desc = "理财产品周期额度查询", model = M537.class)
public class M537Service {

    @Resource
    private M215Service m215Service;

    @CloudReference
    private T203DubboDecorator t203ServiceDecorator;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "理财产品周期额度查询", auth = APIAuth.YES)
    public SqlResult<M537> findProd(SqlParam<M537> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M537> sqlResult = new SqlResult<M537>();
        Map<String, Object> prodParam = new HashMap<>();
        prodParam.put("systemNo", SystemNo.FINA);
        List<M537> volList=new ArrayList<>();
        List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
        for(Map<String, String> prodPara : prodParaList) {

            M537 m537= new M537();
            m537.setProdCode(prodPara.get("prod_code"));    //产品代码
            m537.setProdName(prodPara.get("prod_name"));	//产品名称
            if (StringUtils.isNotBlank(prodPara.get("open_begin_date")) && StringUtils.isNotBlank(prodPara.get("open_end_date"))){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                Date open_begin_date = sdf.parse(prodPara.get("open_begin_date"));
                Date open_end_date = sdf.parse(prodPara.get("open_end_date"));
                Long days = (long) (open_end_date.getTime() - open_begin_date.getTime())/(1000*60*60*24);
                m537.setPeriods(days);	//开放周期  开放结束日-开放开始日
            }
            m537.setIpoStartDate(prodPara.get("subs_begin_date"));//募集开始日
            m537.setIpoEndDate(prodPara.get("subs_end_date"));//募集结束日
            m537.setEstablishDate(prodPara.get("establish_date"));//
            m537.setWindingDate(prodPara.get("winding_date"));


            T203ServiceRequest t203ServiceRequest = new T203ServiceRequest();
            t203ServiceRequest.setSystemNo("FINA");
            t203ServiceRequest.setTano(prodPara.get("tano"));
            t203ServiceRequest.setProdCode(prodPara.get("prod_code"));
            t203ServiceRequest.setLegalCode("1000");
            t203ServiceRequest.setQueryType(RemainQuotaQueryType.WEB_SYS);
            t203ServiceRequest.setQuotaBusiType("3");
            T203ServiceResponse t203ServiceResponse=null;
            try {
                t203ServiceResponse = t203ServiceDecorator.excute(t203ServiceRequest);
            }catch(Exception e){
                continue;
            }
            List<RemainQuotaDetail> remainQuotaDetailList = t203ServiceResponse.getRemainQuotaDetailList();

            BigDecimal countQuota = new BigDecimal(0.0);
            if(remainQuotaDetailList != null && remainQuotaDetailList.size() > 0){
                for(RemainQuotaDetail remainQuotaDetail : remainQuotaDetailList){
                    if (BigDecimal.ZERO.compareTo(remainQuotaDetail.getPersonRemainQuota()) != 0) {
                        countQuota.add(remainQuotaDetail.getPersonRemainQuota());  // 个人 剩余额度
                    } else if (BigDecimal.ZERO.compareTo(remainQuotaDetail.getPublicRemainQuota()) != 0) {
                        countQuota.add(remainQuotaDetail.getPublicRemainQuota());  // 对公 剩余额度
                    } else if (BigDecimal.ZERO.compareTo(remainQuotaDetail.getTradeRemainQuota()) != 0) {
                        countQuota.add(remainQuotaDetail.getTradeRemainQuota());    // 同业 剩余额度
                    } else if (BigDecimal.ZERO.compareTo(remainQuotaDetail.getProdRemainQuota()) != 0) {
                        countQuota.add(remainQuotaDetail.getProdRemainQuota());      // 产品 剩余额度
                    }else if(remainQuotaDetail.getShareRemainQuota() != null){
                        countQuota.add(remainQuotaDetail.getShareRemainQuota());
                    }
                }

            }

            m537.setSoldVol(countQuota);
            if(Tools.isNotBlank(prodPara.get("total_quota"))){
                BigDecimal totalQuota =  new BigDecimal(prodPara.get("total_quota"));
                m537.setAvailableVol(totalQuota.subtract(countQuota));
            }



            //条件过滤
            if(Tools.isBlank(params.getModel().getProdCode()) && Tools.isBlank(params.getModel().getProdName())){
                volList.add(m537);
            }
            if(Tools.isNotBlank(params.getModel().getProdCode()) && Tools.isBlank(params.getModel().getProdName())){
                if(prodPara.get("prod_code").equals(params.getModel().getProdCode())){
                    System.out.println("2");
                    volList.add(m537);
                }
            }
            if(Tools.isBlank(params.getModel().getProdCode()) && Tools.isNotBlank(params.getModel().getProdName())){
                if(m537.getProdName().contains(params.getModel().getProdName())){
                    System.out.println("3");
                    volList.add(m537);
                }
            }
            if(Tools.isNotBlank(params.getModel().getProdCode()) && Tools.isNotBlank(params.getModel().getProdName())) {
                if (prodPara.get("prod_code").equals(params.getModel().getProdCode())
                        && m537.getProdName().contains(params.getModel().getProdName())) {
                    volList.add(m537);
                    System.out.println("4");
                }
            }
        }
        if (params.getLimit() == 0){
            sqlResult.setRows(volList);
        }else {
            sqlResult.setRows(volList.subList(
                    params.getStart(), Math.min(params.getStart() + params.getLimit(), volList.size())
            ));
        }

        sqlResult.setResults(volList.size());
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
