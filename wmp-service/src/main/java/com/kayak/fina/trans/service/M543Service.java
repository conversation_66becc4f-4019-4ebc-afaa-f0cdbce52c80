package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.common.util.SimpleDataUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M543Dao;
import com.kayak.fina.trans.model.M543;
import com.kayak.prod.service.M215Service;
import com.kayak.until.AsyncUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Service
@APIDefine(desc = "产品到期查询报表", model = M543.class)
public class M543Service {
    @Resource
    private M215Service m215Service;
    @Autowired
    private M543Dao m543Dao;
    @Autowired
    private ReportformUtil reportformUtil;
    @Autowired
    private AsyncUtils asyncUtils;

    private String prodInfoPrefix = "ProdInfoHash:";

    @API(desc = "产品到期查询报表", auth = APIAuth.YES)
    public SqlResult<M543> findFinaCustVolDetail(SqlParam<M543> params) throws Exception {
        SqlResult<M543> sqlResult = new SqlResult<M543>();
        List<M543> finaCustVolDetails = m543Dao.findFinaCustVolDetail(params);
        sqlResult.setResults(finaCustVolDetails.size());
        if (params.getLimit() != 0) {
            finaCustVolDetails = finaCustVolDetails.subList(
                    params.getStart(), Math.min(params.getStart() + params.getLimit(), finaCustVolDetails.size())
            );
        }

        //收集prodCode集合(prodCode--prodInfoMap)
        Map<String, Map<String, String>> prodInfoMap = new HashMap<>();
        //收集prodInfo集合(prodCode--prodCode)
        Map<String, String> prodCodeMap = new HashMap<>();
        finaCustVolDetails.parallelStream().forEach(finaCustVolDetail -> {
            //组装redis查询产品信息的key
            String prodInfoHashFormat = prodInfoPrefix + SystemNo.FINA + "-" + finaCustVolDetail.getTano() + "-" + "%s" + "-" + finaCustVolDetail.getLegalCode();
            prodCodeMap.put(finaCustVolDetail.getProdCode(), String.format(prodInfoHashFormat, finaCustVolDetail.getProdCode()));
        });

        Future<Map<String, Map<String, String>>> prodInfoFuture = asyncUtils.getProdInfoFuture(prodInfoMap, prodCodeMap);
        prodInfoFuture.get();//此处异步意义不大,仅复用
        Map<String, String> prodInfo;
        DecimalFormat format = new DecimalFormat("0.00%");
        DecimalFormat format1 = new DecimalFormat("0.00");
        for (M543 m543 : finaCustVolDetails) {
            //m543.setTaName(reportformUtil.getFinaTaName(m543.getTano()));
            prodInfo = prodInfoMap.get(m543.getProdCode());
            //预期兑付金额：总份额*净值
            String totalAmt = format1.format(Double.parseDouble(prodInfo.get("nav")) * Double.parseDouble(m543.getSumTotalVol()));
            m543.setSumAmt(totalAmt);
            m543.setProdName(prodInfo.get("prod_name"));
            //封闭型产品取产品信息表到期日为到期日
            //期限
            m543.setEadline(SimpleDataUtil.daysBetween(m543.getIncomeDate(), m543.getCanRedeemDate()));
            //业绩比较基准
            m543.setBenchmarks(format.format(Double.parseDouble(prodInfo.get("benchmarks"))));//转化为百分号字符串
        }
        sqlResult.setRows(finaCustVolDetails);

        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
