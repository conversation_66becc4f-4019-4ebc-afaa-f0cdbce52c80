package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m227Service",table="fina_cust_vol_detail",firstLineNotDesensitized = true)
public class M227 {

    @GraphQLField(kkhtml = "KFieldCascader", kkhtmlDefault = true, kkhtmlExt="{\"data-diffcondition\":\"orgno,parentorgno\"," +
            "\"data-graphql\":\"{queryM001(action:\\\"find\\\") {rows{orgno, orgname, parentorgno, orgid},results}}\"," +
            "\"data-display-child\":\"children\",\"data-check-strictly\":true," +
            "\"data-show-num\":true,\"data-props\":\"{ expandTrigger: 'hover'}\",\"data-size\":\"medium\"," +
            "\"data-clearable\":true,\"data-fileterable\":true,\"data-display-field\":\"orgname\",\"data-value-field\":\"orgno\"}",
            label = "机构代码", sql = "f1.trans_orgno = $S{transOrgno}", field = "trans_orgno")
    private String transOrgno;

    @GraphQLField(label = "分/区域行", field = "branch_code",sql="f1.branch_code = $S{branchCode}")
    private String branchCode;
    @GraphQLField(label = "社区支行", field = "sub_branch_code")
    private String subBranchCode;
    @GraphQLField(label = "法人代码", field = "legal_code", sql="f1.legal_code = $S{legalCode}")
    private String legalCode;
    @GraphQLField(label = "客户号", field = "cust_no", sql="f1.cust_no = $S{custNo}")
    private String custNo;
    @GraphQLField(label = "客户名称", field = "cust_name")
    private String custName;
    @GraphQLField(label = "交易账号", field = "trans_acct_no")
    private String transAcctNo;
    @GraphQLField(sql = "f1.tano = $S{tano}",label = "Ta代码", field = "tano")
    private String tano;
    @GraphQLField( label = "TA名称", field = "ta_name")
    private String taName;
    @GraphQLField(sql = "f1.prod_code = $S{prodCode}",label = "产品代码", field = "prod_code")
    private String prodCode;
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "开放周期序号", field = "INCOME_RATE")
    private String incomeRate;
    //   起息日
    @GraphQLField(label = "起息日", field = "income_date")
    private String incomeDate;
    //   到期日开始时间
    @GraphQLField(key = true ,kkhtmlDefault=true, kkhtml = "KFieldDate", label = "到期日", sql = "f1.can_redeem_date >= $S{beginDate}", field = "can_redeem_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'endBeginDate','dataAllowblank':false}")
    private String beginDate;
    //    到期日结束时间
    @GraphQLField(sql = "f1.can_redeem_date <= $S{endBeginDate}",label = "到期日结束时间", field = "can_redeem_date")
    private String endBeginDate;
    //   到期日
    @GraphQLField(label = "到期日", field = "end_date")
    private String endDate;
    //   到期日
    @GraphQLField(label = "客户周期到期日", field = "can_redeem_date")
    private String canRedeemDate;
    //    期限
    @GraphQLField(label = "期限", field = "deadline")
    private String deadline;
    //    预计兑付本金
    @GraphQLField(label = "预计兑付本金", field = "total_cost")
    private String totalCost;
    //    预计兑付金额
    @GraphQLField(label = "预计兑付金额", field = "vol")
    private String totalAmt;
    //    预计兑付份额
    @GraphQLField(label = "预计兑付份额", field = "total_vol")
    private String totalVol;
    //    客户预期收益率
    @GraphQLField(label = "客户预期收益率/业绩比较基准", field = "benchmarks")
    private String benchmarks;
}
