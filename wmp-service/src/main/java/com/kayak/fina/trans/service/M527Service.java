package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M527Dao;
import com.kayak.fina.trans.model.M527;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "货币型产品每日收益查询", model = M527.class)
public class M527Service {

	@Autowired
	private M527Dao m527Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "货币型产品每日收益查询", auth = APIAuth.YES)
	public SqlResult<M527> findFinaCurProdDayProf(SqlParam<M527> params) throws Exception {
		//查询客户交易流水确认表
		params.setMakeSql(true);
		SqlResult<M527> sqlResult = m527Dao.queryFinaCurProdDayProf(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
