package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M562Dao;
import com.kayak.fina.trans.model.M562;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 资金补发信息报表
 * xsh
 */
@Service
@APIDefine(desc = "资金补发信息报表", model = M562.class)
public class M562Service {

	@Autowired
	private M562Dao m562Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	public M562Service() {
	}

	@API(desc = "分红汇总报表", auth = APIAuth.YES)
	public SqlResult<M562> findPayAgain(SqlParam<M562> params) throws Exception {
		SqlResult<M562> sqlResult = m562Dao.findPayAgain(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
