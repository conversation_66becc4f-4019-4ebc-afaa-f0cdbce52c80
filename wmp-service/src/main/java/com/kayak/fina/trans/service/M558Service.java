package com.kayak.fina.trans.service;

import com.hundsun.jrescloud.common.exception.BaseBizException;
import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.RtnCodeStatus;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.SysUtil;
import com.kayak.fina.trans.model.M558;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M006Dao;
import com.kayak.system.model.M006;
import com.kayakwise.fina.api.T577DubboDecorator;
import com.kayakwise.fina.req.T577ServiceRequest;
import com.kayakwise.fina.resp.T577ServiceResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@APIDefine(desc = "理财TA开户", model = M558.class)
public class M558Service {
    @Autowired
    private M006Dao m006Dao;

    @CloudReference
    private T577DubboDecorator t577ServiceDecorator;

    @API(desc = "理财TA开户", auth = APIAuth.YES)
    public String openTaInfo(SqlParam<M558> params) throws Exception {
        T577ServiceRequest t577ServiceRequest = new T577ServiceRequest();
        t577ServiceRequest.setAcctNo(params.getModel().getAcctNo());
        //t577ServiceRequest.setCur(params.getModel().getCur());
        t577ServiceRequest.setAgentIdCode(params.getModel().getAgentIdCode());
        t577ServiceRequest.setAgentIdType(params.getModel().getAgentIdType());
        t577ServiceRequest.setAgentName(params.getModel().getAgentName());
        t577ServiceRequest.setCustNo(params.getModel().getCustNo());
        t577ServiceRequest.setTano(params.getModel().getTano());
        t577ServiceRequest.setTransAcctNo(params.getModel().getTransAcctNo());
        t577ServiceRequest.setTransOrgno(params.getModel().getOrgno());
        t577ServiceRequest.setCustName(params.getModel().getCustName());
        t577ServiceRequest.setIdCode(params.getModel().getIdCode());
        t577ServiceRequest.setIdType(params.getModel().getIdType());
        t577ServiceRequest.setInstreprName(params.getModel().getInstreprName());
        t577ServiceRequest.setInstreprIdCode(params.getModel().getInstreprIdCode());
        t577ServiceRequest.setInstreprIdType(params.getModel().getInstreprIdType());
        String userId = params.getAuthInfo().get("userid").toString();
        Map<String, Object> map = new HashMap<>();
        map.put("userid", userId);
        SqlParam<M006> sqlParam = new FetcherData<M006>(map, M006.class);
        SqlResult<M006> users = m006Dao.findUsers(sqlParam);
        if (users != null && users.getRows().size() > 0) {
            t577ServiceRequest.setInputuser(users.getRows().get(0).getLoginname());
        }
        t577ServiceRequest.setCustType(params.getModel().getCustType());

        T577ServiceResponse response =null;
        try {
            response = t577ServiceDecorator.execute(t577ServiceRequest);
        }catch (BaseBizException e){
            if (e.getMessage().contains("T57715")) {
                return RequestSupport.updateReturnJson(false,"开户机构只能是支行",null).toString();
            }
        }
        // 若modifySucessFlag为0，表示新增/修改失败
        String rtnCode = response.getRtnCode();
        if (rtnCode != null && !rtnCode.equals(RtnCodeStatus.RTNCODE)) {
            throw new PromptException(rtnCode, response.getRtnDesc());
        }
        return RequestSupport.updateReturnJson(true, "已经申请开户", null).toString();
    }

}
