package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M535Dao;
import com.kayak.fina.trans.model.M535;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "认、申购赎回申请成功查询报表", model = M535.class)
public class M535Service {

    @Autowired
    private M535Dao m535Dao;

    @Autowired
    private ReportformUtil reportformUtil;


    @API(desc = "认、申购赎回申请成功查询", auth = APIAuth.YES)
    public SqlResult<M535> findTransReqlist(SqlParam<M535> params) throws Exception {
        SqlResult<M535> sqlResult = m535Dao.findTransReqlist(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        return sqlResult;
    }
}
