package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

//客户TA账号信息查询
@Data
@GraphQLModel(fetcher = "m521Service",firstLineNotDesensitized = true)
public class M521 {
    @GraphQLField(key = true , label = "交易账号", sql = "trans_acct_no = $S{transAcctNo}" ,field = "trans_acct_no")
    private String transAcctNo;
    @GraphQLField(kkhtml = "KFieldSelect",kkhtmlExt = "{\"data-action\":\"M215.findTano\",\"data-params\":\"{systemNo : 'FINA'}\" ,\"data-value-field\":\"no\",\"data-display-field\":\"no,name\"}",key = true ,label = "TA代码",  sql = " tano = $S{tano}",field = "tano",kkhtmlDefault = true)
    private String tano;
    @GraphQLField(key = true , label = "法人代码", sql = "legal_code = $S{legalCode}" ,field = "legal_code")
    private String legalCode;
    @GraphQLField(label = "TA账号", sql = "ta_acct_no = $S{taAcctNo}" ,field = "ta_acct_no")
    private String taAcctNo;
    @GraphQLField(label = "账户状态", sql = "acct_status = $S{acctStatus}" ,field = "acct_status")
    private String acctStatus;
    @GraphQLField(label = "TA名称",field = "ta_name")
    private String taName;

    @GraphQLField(kkhtml = "KFieldText", label = "客户号", kkhtmlDefault = true, kkhtmlExt = "{\"ref\": \"custNo\"}",  sql = "cust_no = $S{custNo}", field = "cust_no")
    private String custNo;
    @GraphQLField(kkhtml = "KFieldText",  label = "客户名称",  sql = "cust_name like '%$U{custName}%'", field = "cust_name")
    private String custName;
    @GraphQLField(kkhtml = "KFieldSelect", label = "证件类型", kkhtmlDefault = true, kkhtmlExt="{\"data-dict\": \"id_type\",\"ref\": \"idType\"}",  sql = "id_type = $S{idType}", field = "id_type")
    private String idType;
    @GraphQLField(kkhtml = "KFieldText",label = "证件号码", kkhtmlDefault = true,kkhtmlExt = "{\"ref\": \"idCode\"}",sql = "id_code = $S{idCode}", field = "id_code", desensitized = IdCardDesensitized.class)
    private String idCode;
    @GraphQLField(kkhtml = "KFieldText",label = "资金账号", kkhtmlDefault = true,kkhtmlExt = "{\"ref\": \"acctNo\"}", sql = "acct_no = $S{acctNo}", field = "acct_no", desensitized = Blankcarddesensitized.class)
    private String acctNo;
    @GraphQLField(key = true, kkhtmlDefault = true, kkhtml = "KFieldDate", label = "开户日期", sql = "open_date >= $S{openDate}", field = "open_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'openEndDate','dataAllowblank':false}")
    private String openDate;
    @GraphQLField(label = "开户日期（区间结束时间）", sql = "open_date <= $S{openEndDate}", field = "open_date")
    private String openEndDate;


}
