package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import lombok.Data;

import java.util.List;

/**
 * 客户信息实体
 */
@Data
public class CustInfoVo {
    @GraphQLField(label = "客户号", field = "eadline")
    private String custNo;
    @GraphQLField(label = "法人代码", field = "legal_code")
    private String legalCode;
    @GraphQLField(label = "证件类型", field = "id_type")
    private String idType;
    @GraphQLField(label = "证件号码", field = "id_code")
    private String idCode;
    @GraphQLField(label = "客户名称", field = "cust_name")
    private String custName;
    @GraphQLField(label = "客户类型", field = "cust_type")
    private String custType;

    private List<String>  custNos;
    private List<String>  legalCodes;
}
