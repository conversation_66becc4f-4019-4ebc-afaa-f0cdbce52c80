package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m539Service",table="fina_cust_vol")
public class M539 {
    @GraphQLField(label = "TA代码", field = "tano")
    private String tano;
    @GraphQLField(label = "法人代表",field = "legal_code")
    private String legalCode;
    @GraphQLField(label = "产品代码", field = "prod_code")
    private String prodCode;
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "总份额", field = "total_vol")
    private String totalVol;
    @GraphQLField(label = "可用份额", field = "avail_vol")
    private String availVol;
    @GraphQLField(label = "交易冻结份额", field = "redeem_frozen_vol")
    private String redeemFrozenVol;
    @GraphQLField(label = "异常冻结份额", field = "abnormal_frozen_vol")
    private String abnormalFrozenVol;
    @GraphQLField(label = "客户未转结收益", field = "unconvert_income")
    private String unconvertIncome;
    @GraphQLField(label = "产品默认分红方式", field = "def_div_method")
    private String defDivMethod;
}
