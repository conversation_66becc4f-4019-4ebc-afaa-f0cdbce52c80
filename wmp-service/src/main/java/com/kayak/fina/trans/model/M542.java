package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m542Service",table="")
public class M542 {
     /**TA代码 */
    @GraphQLField(label = "TA代码", field = "tano",sql = "tano = $S{tano}")
    private String tano;
    @GraphQLField(label = "法人代码",  field = "legal_code")
    private String legalCode;
    /**@GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "产品代码",
            sql = "prod_code = $S{prodCode}",
            field = "prod_code",
            kkhtmlExt="{\"data-action\":\"M215.queryProdInfoList\",\"data-value-field\":\"prod_code\",\"data-display-field\":\"prod_code,prod_name\", \"data-params\":{'systemNo':'FINA'}}"
    )*/
    @GraphQLField(label = "产品代码", field = "prod_code", sql = "prod_code = $S{prodCode}")
    private String prodCode;
    @GraphQLField(kkhtml = "KFieldSelect", label = "客户类型", sql = "cust_type = $S{custType}" , kkhtmlExt = "{\"data-dict\":\"cust_type\"}", field = "cust_type",kkhtmlDefault=true)
    private String custType;
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "募集结束日期",field = "subs_end_date" )
    private String subsEndDate;
    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "起息日", sql = "", field = "income_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'endDate'}")
    private String incomeDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "到期日", sql = "", field = "end_date")
    private String endDate;
    @GraphQLField(label = "清盘日" ,field = "winding_date")
    private String windingDate;
    //@GraphQLField(label = "开放周期" ,field = "periods")
    //private String periods;
    @GraphQLField(label = "期限" ,field = "hold_days")
    private String holdDays;
    @GraphQLField(label = "申请金额", field = "app_amt")
    private String appAmt;
    @GraphQLField(label = "确认金额", field = "ack_amt")
    private String ackAmt;


}
