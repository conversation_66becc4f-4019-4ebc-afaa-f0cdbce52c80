package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.trans.dao.M526Dao;
import com.kayak.fina.trans.model.M522;
import com.kayak.fina.trans.model.M526;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "当日机构销售额查询", model = M526.class)
public class M526Service {

    @Autowired
    private M526Dao m526Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @Autowired
    private M001Dao m001Dao;

    @API(desc = "当日机构销售额查询" , auth = APIAuth.YES)
    public SqlResult<M526> findDayOrgSalAmt(SqlParam<M526> params) throws Exception {
        params.setMakeSql(false);
        SqlResult<M526> sqlResult =  m526Dao.queryDayOrgSalAmt(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        if(sqlResult != null && sqlResult.getRows().size() > 0){
            for (M526 m526 : sqlResult.getRows()){
                m526.setTaName(reportformUtil.getFinaTaName(m526.getTano()));
                //获取交易机构名称
                if(Tools.isNotBlank(m526.getTransOrgno())){
                    Map<String,Object> map = new HashMap<>();
                    map.put("orgno",m526.getTransOrgno());
                    SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
                    SqlResult<M001> m001Info = m001Dao.find(dateParams);
                    if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
                        m526.setTransOrgName(m001Info.getRows().get(0).getOrgname());
                    }
                }
            }
        }
        return sqlResult;
    }

    @API(desc = "查TA信息", auth = APIAuth.YES)
    public SqlResult<M526> findTA(SqlParam<M526> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M526> sqlResult = m526Dao.queryTA(params);
        return sqlResult;
    }

}

