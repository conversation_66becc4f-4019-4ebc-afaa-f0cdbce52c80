package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@GraphQLModel(fetcher = "m537Service",table="fina_prod_periodlist")
public class M537 {
    @GraphQLField(label = "TA代码", field = "tano")
    private String tano;
    @GraphQLField(label = "法人代表",field = "legal_code")
    private String legalCode;
    /**@GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "产品代码",
            sql = "prod_code = $S{prodCode}",
            field = "prod_code",
            kkhtmlExt="{\"data-action\":\"M215.queryProdInfoList\",\"data-value-field\":\"prod_code\",\"data-display-field\":\"prod_code,prod_name\", \"data-params\":{'systemNo':'FINA'}}"
    )*/
    @GraphQLField(label = "产品代码",field = "prod_code",sql = "prod_code = $S{prodCode}" )
    private String prodCode;
    @GraphQLField( kkhtml = "KFieldText",label = "产品名称", field = "prod_name",kkhtmlDefault=true)
    private String prodName;
    @GraphQLField(label = "开放周期", field = "periods")
    private long periods;
    @GraphQLField(label = "募集开始日期",field = "ipo_start_date" )
    private String ipoStartDate;
    @GraphQLField(label = "募集结束日期",field = "ipo_end_date" )
    private String ipoEndDate;
    @GraphQLField(label = "成立日",field = "establish_date" )
    private String establishDate;
    @GraphQLField(label = "清盘日期",field = "winding_date" )
    private String windingDate;
    @GraphQLField(label = "回款支付日",field = "a1" )
    private String a1;
    @GraphQLField(label = "可用额度", field = "available_vol")
    private BigDecimal availableVol;
    @GraphQLField(label = "已售额度", field = "sold_vol")
    private BigDecimal soldVol;

}
