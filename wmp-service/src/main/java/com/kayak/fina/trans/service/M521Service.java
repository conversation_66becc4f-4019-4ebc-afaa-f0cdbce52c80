package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.util.Tools;
import com.kayak.cust.model.M101;
import com.kayak.fina.trans.dao.M521Dao;
import com.kayak.fina.trans.model.M521;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M206Detail;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "客户TA账号信息查询", model = M521.class)
public class M521Service {

    @Autowired
    private M521Dao m521Dao;

    @Autowired
    private ReportformUtil reportformUtil;


    @API(desc = "客户和TA账号信息查询", auth = APIAuth.YES)
    public SqlResult<M521> findCustTA(SqlParam<M521> params) throws Exception {
        Map<String,Object> map = new HashMap<>();
        params.getModel().setCustNo(reportformUtil.getCustNoForCustInfos(params.getModel().getCustNo(),params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()));
        if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
            map.put("acctNo",params.getModel().getAcctNo());
        }
        if(StringUtils.isNotBlank(params.getModel().getCustName())){
            map.put("custName",params.getModel().getCustName());
        }
        if(StringUtils.isNotBlank(params.getModel().getIdCode())){
            map.put("idCode",params.getModel().getIdCode());
        }
        if(StringUtils.isNotBlank(params.getModel().getIdType())){
            map.put("idType",params.getModel().getIdType());
        }
        SqlParam<M521> paras = new FetcherData<>(map, M521.class);
        params.getModel().setCustName(null);
        params.getModel().setIdCode(null);
        params.getModel().setIdType(null);
        params.getModel().setAcctNo(null);
        SqlResult<M521> sqlResult = m521Dao.findCustTAInfo((FetcherData<M521>) params,(FetcherData<M521>) paras);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

    @API(desc = "查TA信息", auth = APIAuth.YES)
    public SqlResult<M521> findTA(SqlParam<M521> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M521> sqlResult = m521Dao.queryTA(params);
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

}

