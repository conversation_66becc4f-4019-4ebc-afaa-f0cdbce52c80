package com.kayak.fina.trans.service;

import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlRow;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M522Dao;
import com.kayak.fina.trans.model.M522;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "客户份额明细实体类服务", model = M522.class)
public class M522Service {

	@Autowired
	private M522Dao m522Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private M215Service m215Service;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询客户份额明细实体类信息", auth = APIAuth.YES)
	public SqlResult<M522> findFinaCustVolDetails(SqlParam<M522> params) throws Exception {
		SqlResult<M522> sqlResult = m522Dao.findFinaCustVolDetails(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		if(sqlResult != null && sqlResult.getRows().size() > 0){
			Map<String, Object> prodParam = new HashMap<>();
			prodParam.put("systemNo", SystemNo.FINA);
			prodParam.put("legalCode", "1000");
			prodParam.put("userid",params.getParams().get("userid"));
			for (M522 m522 : sqlResult.getRows()){
				//获取交易机构名称
				M001 m001 = m001Dao.get(m522.getTransOrgno());
				if (m001 != null){
					m522.setOrgName(m001.getOrgname());
				}
				prodParam.put("prodCode", m522.getProdCode());
				prodParam.put("supplyCode", m522.getTano());
				List<Map<String, String>> prodInfoList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
				if (prodInfoList != null && prodInfoList.size() > 0){
					Map<String, String> map = prodInfoList.get(0);
					m522.setProdName(map.get("prod_name") == null ? "":map.get("prod_name"));
				}
			}
		}
		sqlResult.setDesensitized(false);
		return sqlResult;
	}




}
