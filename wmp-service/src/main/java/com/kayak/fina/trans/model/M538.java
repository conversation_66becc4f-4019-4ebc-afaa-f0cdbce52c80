package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.util.List;

@Data
@GraphQLModel(fetcher = "m538Service",table="fina_prod_periodlist",firstLineNotDesensitized = true)
public class M538 {

    @GraphQLField(label = "Ta代码", field = "tano")
    private String tano;

    @GraphQLField(label = "TA名称", field = "ta_name")
    private String taName;

    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;

   /** @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "产品代码",
            sql = "prod_code = $S{prodCode}",
            field = "prod_code",
            kkhtmlExt="{\"data-action\":\"M539.findProdList\",\"data-value-field\":\"prodName\",\"data-display-field\":\"prodCode\"}"
    )*/
   @GraphQLField(
           label = "产品代码", sql = " prod_code like '%$U{prodCode}%' " ,field = "prod_code")
    private String prodCode;
    @GraphQLField(label = "客户号", field = "cust_no", sql = "cust_no in ( $S{custNo} )")
    private String custNo;
    @GraphQLField(label = "法人代码", field = "legal_code")
    private String legalCode;

    @GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true,
            label = "客户名称", sql = "cust_name like '%${custName}%'" ,field = "cust_name")
    private String custName;
    //, "data-allowblank":false
    @GraphQLField(kkhtml="KFieldSelect", label = "证件类型", kkhtmlExt="{\"data-dict\": \"id_type\",\"ref\": \"idType\"}",
            kkhtmlDefault = true, sql = "id_type = $S{idType}", field = "id_type")
    private String idType;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
    private String idCode;

    @GraphQLField(label = "证件号码",  sql = " id_code = $S{queryIdCode} ",field = "id_code")
    private String queryIdCode;

    @GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true,
            label = "交易账号", sql = "trans_acct_no = $S{transAcctNo} " ,field = "trans_acct_no")
    private String transAcctNo;
    @GraphQLField(label = "总份额",field = "total_vol")
    private String totalVol;
    @GraphQLField(label = "最新金额",field = "new_vol")
    private String newVol;
    @GraphQLField(label = "其他金额",field = "other_vol")
    private String otherVol;
    //总份额*最新净值+累计赎回金额+累计分红-总成本
    @GraphQLField(label = "客户损益",field = "cust_profit_loss")
    private String custProfitLoss;
//    @GraphQLField(label = "客户号", field = "eadline")
//    private String custNo;
//    @GraphQLField(label = "法人代码", field = "legal_code")
//    private String legalCode;
//    @GraphQLField(label = "证件类型", field = "id_type")
//    private String idType;
//    @GraphQLField(label = "证件号码", field = "id_code")
//    private String idCode;
//    @GraphQLField(label = "客户名称", field = "cust_name")
//    private String custName;
    @GraphQLField(label = "客户类型", field = "cust_type")
    private String custType;

    @GraphQLField(label = "总成本", field = "total_cost")
    private String totalCost;


    private List<String> custNos;
    private List<String>  legalCodes;
}
