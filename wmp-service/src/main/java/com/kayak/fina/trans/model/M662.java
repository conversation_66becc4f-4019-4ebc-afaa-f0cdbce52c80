package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m662Service",table="fina_capital_transfer_info")
public class M662 {



    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            key = true , label = "TA代码", sql = "t.tano = $S{tano}" ,field = "tano",
            kkhtmlExt="{\"data-action\":\"M662.findTano\",\"data-display-field\":\"tano,taName\",\"data-value-field\":\"tano\"}")
    private String tano;


    @GraphQLField(label = "TA名称",field = "taName")
    private String taName;

    /**@GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            key = true , label = "资金类型", sql = "t.CAPITALTYPE = $S{capitaltype}" ,field = "capitaltype",
            kkhtmlExt="{\"data-dict\": \"fina_capital_type\"}")*/
    @GraphQLField(label = "资金类型",field = "capitaltype", kkhtmlExt = "{\"data-dict\": \"fina_capital_type\"}")
    private String capitaltype;

    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "划拨日期", sql = "t.PAYMENTDAY >= $S{paymentday}", field = "paymentday", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'paymentdayEnd'}")
    private String paymentday;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = " t.PAYMENTDAY <= $S{paymentdayEnd}", field = "paymentdayEnd")
    private String paymentdayEnd;

    @GraphQLField(label = "划拨金额",field = "PAYMENTAMOUNT")
    private String paymentamount;

    @GraphQLField(label = "出账账号",field = "OUT_ACCT_NO")
    private String outAcctNo;

    @GraphQLField(label = "入账账号",field = "IN_ACCT_NO")
    private String inAcctNo;

    @GraphQLField(label = "备注",field = "SUMMARY")
    private String summary;

    @GraphQLField(label = "流水号",field = "APPLY_SERNO")
    private String applySerno;


    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            key = true ,label = "执行状态",field = "EXEC_STATUS", kkhtmlExt = "{\"data-dict\": \"fund_exec_status\",\"data-default-value\": \"0\"}", sql = "t.EXEC_STATUS = $S{execStatus}")
    private String execStatus;

    @GraphQLField(label = "返回码",field = "RETURNCODE")
    private String returncode;

    @GraphQLField(label = "返回描述",field = "RETURNMSG")
    private String returnmsg;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            key = true ,label = "划拨状态",field = "TRANSFER_STATUS", kkhtmlExt = "{\"data-dict\": \"transfer_status\",\"data-default-value\": \"0\"}", sql = "t.TRANSFER_STATUS = $S{transferStatus}")
    private String transferStatus;




}
