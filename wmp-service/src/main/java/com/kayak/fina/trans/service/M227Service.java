package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.component.RedisUtil;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.common.util.SimpleDataUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M227Dao;
import com.kayak.fina.trans.dao.M543Dao;
import com.kayak.fina.trans.model.M227;
import com.kayak.prod.service.M215Service;
import com.kayak.until.AsyncUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

@Slf4j
@Service
@APIDefine(desc = "产品到期客户明细", model = M227.class)
public class M227Service {

    @Resource
    private AsyncUtils asyncUtils;
    @Resource
    private M215Service m215Service;
    @Autowired
    private M227Dao m227Dao;
    @Autowired
    private M543Dao m543Dao;
    @Autowired
    private ReportformUtil reportformUtil;
    @Autowired
    private RedisUtil redisUtil;

    private String prodInfoPrefix = "ProdInfoHash:";

    @API(desc = "产品到期客户明细", auth = APIAuth.YES)
    public SqlResult<M227> findFinaCustVolDetail(SqlParam<M227> params) throws Exception {
        SqlResult<M227> sqlResult = m227Dao.findFinaCustVolDetail(params);
        List<M227> finaCustVolDetail = sqlResult.getRows();
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());

        //收集prodCode集合(prodCode--prodInfoMap)
        Map<String, Map<String, String>> prodInfoMap = new HashMap<>();
        //收集tano集合(prodCode--tano)
        Map<String, String> prodTanoMap = new HashMap<>();
        //收集custNo集合(prodCode--custName)
        Map<String, String> prodCustNoMap = new ConcurrentHashMap<>();
        //收集prodInfo集合(prodCode--prodCode)
        Map<String, String> prodCodeMap = new HashMap<>();
        finaCustVolDetail.parallelStream().forEach(item -> {
            String prodCode = item.getProdCode();
            String tano = item.getTano();
            String prodInfoHashFormat = prodInfoPrefix + SystemNo.FINA + "-" + tano + "-" + "%s" + "-" + item.getLegalCode();
            prodTanoMap.put(tano, prodCode);
            prodCustNoMap.put(item.getCustNo(), prodCode);
            prodCodeMap.put(prodCode, String.format(prodInfoHashFormat, prodCode));
            //prodCodeMap.put(String.format(prodInfoHashFormat, prodCode),prodCode);
        });
        //供应商数量少数,不需要异步处理
        prodTanoMap.keySet().stream().forEach(tano -> {
            try {
                prodTanoMap.put(tano, m227Dao.findTaNameByTaCode(tano).get(0).getString("ta_name"));
            } catch (Exception e) {
                log.error("供应商名称获取失败：{}", tano);
                prodTanoMap.remove(tano);
            }
        });

        //异步遍历map的keyset作为条件查询数据库,结果放入当前的key中
        Future<Map<String,String>> CustNameFuture = asyncUtils.getCustNameFuture(prodCustNoMap);
        Future<Map<String,Map<String,String>>> prodInfoFuture = asyncUtils.getProdInfoFuture(prodInfoMap, prodCodeMap);

        CustNameFuture.get();//阻塞获取客户名称map
        prodInfoFuture.get();//阻塞获取产品信息map
        Map<String, String> prodInfo;
        for (M227 m227 : finaCustVolDetail) {
            //填充Ta名称
            m227.setTaName(prodTanoMap.get(m227.getTano()));

            //填充custName
            m227.setCustName(prodCustNoMap.get(m227.getCustNo()));

            //填充产品信息
            prodInfo = prodInfoMap.get(m227.getProdCode());

            m227.setProdName(prodInfo.get("prod_name"));
            String endDate = m227.getCanRedeemDate();//客户周期产品取份额明细表的可赎回日期为到期日
            String incomeDate = m227.getIncomeDate();
            String benchmarks = prodInfo.get("benchmarks");
            String nav = prodInfo.get("nav");
            String totalVol = m227.getTotalVol();
            DecimalFormat format = new DecimalFormat("0.00%");
            DecimalFormat format1 = new DecimalFormat("0.00");
            if (StringUtils.isNotBlank(benchmarks)) {
                //业绩比较基准
                m227.setBenchmarks(format.format(Double.parseDouble(benchmarks)));//转化为百分号字符串
            }
            //到期日
            m227.setEndDate(endDate);
            //起息日
            m227.setIncomeDate(incomeDate);
            //期限
            if (StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(incomeDate)) {
                String days = String.valueOf(SimpleDataUtil.daysBetween2(incomeDate, endDate));
                m227.setDeadline(days);
            }
            if (StringUtils.isNotBlank(nav) && StringUtils.isNotBlank(totalVol)) {
                //预计兑付金额
                String totalAmt = format1.format(Double.parseDouble(nav) * Double.parseDouble(totalVol));
                m227.setTotalAmt(totalAmt);
            } else m227.setTotalAmt("0");
        }
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
