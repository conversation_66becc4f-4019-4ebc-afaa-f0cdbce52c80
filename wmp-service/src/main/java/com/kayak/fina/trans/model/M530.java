package com.kayak.fina.trans.model;

import com.kayak.common.base.BaseModel;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;

import java.math.BigDecimal;

/**
 * 资金延后处理 实体类
 *
 * <AUTHOR>
 * @date 2021-04-28 10:06
 */
@GraphQLModel(fetcher = "m530Service")
public class M530 extends BaseModel {

    @GraphQLField(label = "产品代码",field = "prod_code", sql = "t1.prod_code = $S{prodCode}")
    private String prodCode;
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "TA代码", field = "tano",sql = " t1.tano= $S{tano} ")
    private String tano;
    @GraphQLField(label = "TA名称", field = "ta_name")
    private String taName;

    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "资金处理日期", sql =  " t1.capital_deal_date >= $S{capitalDealDate}", field = "capital_deal_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'capitalDealEndDate'}")
    private String capitalDealDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = " t1.capital_deal_date <= $S{capitalDealEndDate}", field = "capitalDealEndDate")
    private String capitalDealEndDate;


    @GraphQLField(label = "延后资金处理日期", field = "delay_capital_deal_date")
    private String delayCapitalDealDate;
    @GraphQLField(label = "总金额", field = "total_amt")
    private BigDecimal totalAmt;
    @GraphQLField(label = "业务代码", field = "busi_code")
    private String busiCode;
    @GraphQLField(label = "法人代码", field = "legal_code")
    private String legalCode;

    public String getCapitalDealEndDate() {
        return capitalDealEndDate;
    }

    public void setCapitalDealEndDate(String capitalDealEndDate) {
        this.capitalDealEndDate = capitalDealEndDate;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public String getTano() {
        return tano;
    }

    public void setTano(String tano) {
        this.tano = tano;
    }

    public String getTaName() {
        return taName;
    }

    public void setTaName(String taName) {
        this.taName = taName;
    }

    public String getCapitalDealDate() {
        return capitalDealDate;
    }

    public void setCapitalDealDate(String capitalDealDate) {
        this.capitalDealDate = capitalDealDate;
    }

    public String getDelayCapitalDealDate() {
        return delayCapitalDealDate;
    }

    public void setDelayCapitalDealDate(String delayCapitalDealDate) {
        this.delayCapitalDealDate = delayCapitalDealDate;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getLegalCode() {
        return legalCode;
    }

    public void setLegalCode(String legalCode) {
        this.legalCode = legalCode;
    }
}
