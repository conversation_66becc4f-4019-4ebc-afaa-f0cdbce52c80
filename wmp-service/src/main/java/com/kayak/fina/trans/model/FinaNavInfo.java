package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import lombok.Data;

/**
 * 理财产品行情信息表实体
 */
@Data
public class FinaNavInfo {
    @GraphQLField(sql = "tano = $S{tano}",label = "TA代码", field = "tano")
    private String tano;

    @GraphQLField(sql = "legal_code = $S{legalCode}",label = "法人代码", field = "legal_code")
    private String legalCode;

    @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "产品代码",
            sql = "prod_code = $S{prodCode}",
            field = "prod_code",
            kkhtmlExt="{\"data-action\":\"M539.findProdList\",\"data-value-field\":\"prodName\",\"data-display-field\":\"prodCode\"}"
    )
    private String prodCode;
    @GraphQLField(label = "净值日期", field = "nav_date")
    private String navDate;
    @GraphQLField(label = "单位净值", field = "nav")
    private String nav;
    @GraphQLField(label = "累计净值", field = "total_nav")
    private String totalNav;
    @GraphQLField(label = "7日年化收益率", field = "seven_days_income")
    private String sevenDaysIncome;
    @GraphQLField(label = "万份收益", field = "ten_thousand_income_amt")
    private String tenThousandIncomeAmt;
    @GraphQLField(label = "净值类型", field = "nav_type")
    private String navType;
    @GraphQLField(label = "产品总份数", field = "prod_total_vol")
    private String prodTotalVol;
    @GraphQLField(label = "产品当日总收益", field = "total_income")
    private String totalIncome;
    @GraphQLField(label = "货币产品年化收益率", field = "year_income_rate")
    private String yearIncomeRate;
    @GraphQLField(label = "业绩比较基准", field = "benchmarks")
    private String benchmarks;
    @GraphQLField(label = "业绩报酬比例", field = "achievement_pay_ratio")
    private String achievementPayRatio;
    @GraphQLField(label = "浮动管理费比例", field = "float_manger_fee_ratio")
    private String floatMangerFeeRatio;
    @GraphQLField(label = "上一日年化收益率", field = "last_days_income_rate")
    private String lastDaysIncomeRate;
    @GraphQLField(label = "创建日期", field = "crt_date")
    private String crtDate;
    @GraphQLField(label = "更新日期", field = "upd_date")
    private String updDate;
}
