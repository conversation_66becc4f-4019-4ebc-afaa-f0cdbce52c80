package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M520;
import com.kayak.fina.trans.dao.M544Dao;
import com.kayak.fina.trans.model.M544;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.service.M215Service;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "理财代销-客户分红汇总表", model = M544.class)
public class M544Service {

    @Autowired
    private M544Dao m544Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "理财代销-客户分红汇总查询", auth = APIAuth.YES)
    public SqlResult<M544> findSumcCustDivDetail(SqlParam<M544> params) throws Exception {
        SqlResult<M544> sqlResult= m544Dao.findSumcCustDivDetail(params);
        //获取产品名称、TA名称
        if(null!=sqlResult&&sqlResult.getRows().size()>0){
            List<M544> sqlList=sqlResult.getRows();
            for(M544 m544:sqlList){
                Map<String, String> prodInfoMap = m215Service.getProdInfo(m544.getLegalCode(), SystemNo.FINA,m544.getTano(),m544.getProdCode());
                if(prodInfoMap != null && prodInfoMap.size() > 0){
                    m544.setProdName(prodInfoMap.get("prod_name"));
                }
                /**if(null!=m544){
                    m544.setTaName(reportformUtil.getFinaTaName(m544.getTano()));
                }*/
            }
        }
        return sqlResult;
    }
}
