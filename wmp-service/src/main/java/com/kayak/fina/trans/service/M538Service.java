package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.fina.trans.dao.M538Dao;
import com.kayak.fina.trans.dao.M543Dao;
import com.kayak.fina.trans.model.CustInfoVo;
import com.kayak.fina.trans.model.FinaNavInfo;
import com.kayak.fina.trans.model.M538;
import com.kayak.fina.trans.model.M543;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "客户损益查询", model = M538.class)
public class M538Service {
    @Resource
    private M215Service m215Service;
    @Autowired
    private M538Dao m538Dao;
    @Autowired
    private M543Dao m543Dao;
    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "客户损益查询报表", auth = APIAuth.YES)
    public SqlResult<M538> findCustProfitLoss(SqlParam<M538> params) throws Exception {
        if(Tools.isNotBlank(params.getModel().getQueryIdCode())){
            params.getModel().setIdCode(params.getModel().getQueryIdCode());
            params.getModel().setQueryIdCode(null);
        }
        params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),""));
        params.getModel().setCustName(null);
        params.getModel().setIdCode(null);
        params.getModel().setIdType(null);

        params.setMakeSql(true);
        SqlResult<M538> sqlResult = m538Dao.findCustVol(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            try{

                String vol = "0";
                FinaNavInfo finaNavInfo = new FinaNavInfo();
                finaNavInfo.setTano(item.getTano());
                finaNavInfo.setLegalCode(item.getLegalCode());
                finaNavInfo.setProdCode(item.getProdCode());
                String totalCost = "0";
                if(Tools.isNotBlank(item.getTotalCost())){
                    totalCost = item.getTotalCost();
                }
                String otherVol = "0";
                if(Tools.isNotBlank(item.getOtherVol())){
                    otherVol = item.getOtherVol();
                }

                List<FinaNavInfo> finaNewNavInfoList = m543Dao.findFinaNewNavInfo(finaNavInfo);
                if(finaNewNavInfoList != null && finaNewNavInfoList.size() > 0 && Tools.isNotBlank(item.getTotalVol()) && Tools.isNotBlank(finaNewNavInfoList.get(0).getNav())) {
                     Double totalVol = Double.parseDouble(item.getTotalVol());
                     Double nav = Double.parseDouble(finaNewNavInfoList.get(0).getNav());
                     vol = String.valueOf(totalVol * nav);
                }
                item.setNewVol(vol);
                //item.setCustProfitLoss(String.valueOf(Double.parseDouble(item.getNewVol())+Double.parseDouble(otherVol)-Double.parseDouble(totalCost)));


                CustInfoVo custInfoVo=new CustInfoVo();
                if (StringUtils.isNotBlank(item.getCustNo())){
                    custInfoVo.setCustNo(item.getCustNo());
                    List<M538> custInfo = m538Dao.findCustInfo(custInfoVo);
                    if(custInfo != null && custInfo.size() >0){
                        item.setIdType(custInfo.get(0).getIdType());
                        item.setIdCode(custInfo.get(0).getIdCode());
                        item.setCustName(custInfo.get(0).getCustName());
                        item.setQueryIdCode(custInfo.get(0).getIdCode());
                    }
                }
                item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
                item.setProdName(reportformUtil.getProdName(SystemNo.FINA,item.getTano(),item.getProdCode()));
            } catch (Exception e) {
                throw new RuntimeException("M933错误："+e.getMessage());
            }
            return item;
        }).collect(Collectors.toList()));
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
