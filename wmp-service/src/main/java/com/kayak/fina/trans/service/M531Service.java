package com.kayak.fina.trans.service;

import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.common.constants.RtnCodeStatus;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.fina.trans.dao.M531Dao;
import com.kayak.fina.trans.model.M531;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayakwise.fina.api.T250DubboDecorator;
import com.kayakwise.fina.req.T250ServiceRequest;
import com.kayakwise.fina.resp.T250ServiceResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "资金划拨处理服务", model = M531.class)
public class M531Service {

    @Autowired
    private M531Dao m531Dao;

    @Autowired
    private M215Service m215Service;

    @CloudReference
    private T250DubboDecorator t250ServiceDecorator;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询资金划拨信息", auth = APIAuth.YES)
    public SqlResult<M531> findM531s(SqlParam<M531> params) throws Exception {
       // params.setMakeSql(true);
        SqlResult<M531> sqlResult = m531Dao.findM531s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M531> volList = sqlResult.getRows();
        for (M531 m531 : volList){
            Map<String, Object> prodParam = new HashMap<>();
            prodParam.put("systemNo", SystemNo.FINA);
            prodParam.put("prodCode", m531.getProdCode());
            prodParam.put("supplyCode", m531.getTano());
            prodParam.put("legalCode", "1000");
            prodParam.put("userid", params.getParams().get("userid"));
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                m531.setProdName(prodPara.get("prod_name"));
            }
            Map<String, Object> map = new HashMap<>();
            map.put("tano", m531.getTano());
            map.put("userid",params.getParams().get("userid"));
            SqlParam<M531> sqlParam = new FetcherData<>(map,M531.class);
            SqlResult<M531> taResult = findTano(sqlParam);
            if (taResult.getRows() != null && taResult.getRows().size() > 0){
                m531.setTaName(taResult.getRows().get(0).getTaName());
            }
        }
        return sqlResult;
    }

    @API(desc = "查询供应商信息", auth = APIAuth.YES)
    public SqlResult<M531> findTano(SqlParam<M531> params) throws Exception {
        params.setMakeSql(true);
        return m531Dao.findTano(params);
    }

    @API(desc = "核对", auth = APIAuth.YES,operation = APIOperation.UPDATE)
    public String updateCheck(SqlParam<M531> params) throws Exception {
        if(Tools.isBlank(params.getModel().getBusiCode()) || Tools.isBlank(params.getModel().getProdCode()) || Tools.isBlank(params.getModel().getTano())
            || Tools.isBlank(params.getModel().getAckDate())){
            throw new PromptException("状态更新失败：数据缺失！");
        }else{
            try{
                m531Dao.updateCheckFlag(params);
            }catch (Exception ex){
                throw new PromptException("核对保存失败："+ex.getMessage());
            }

        }
        return RequestSupport.updateReturnJson(true, "核对保存成功，状态已更新", null).toString();
    }

    @API(desc = "划拨", auth = APIAuth.YES)
    public String updateM531(SqlParam<M531> params) throws Exception {
        T250ServiceRequest t250ServiceRequest = new T250ServiceRequest();
        t250ServiceRequest.setProdCode(params.getModel().getProdCode());
        t250ServiceRequest.setTano(params.getModel().getTano());
        t250ServiceRequest.setInAcctNo(params.getModel().getInAcctNo());
        t250ServiceRequest.setOutAcctNo(params.getModel().getOutAcctNo());
        t250ServiceRequest.setTransferAmt(params.getModel().getTransferAmt());
        T250ServiceResponse t250ServiceResponse = t250ServiceDecorator.execute(t250ServiceRequest);
        String status = t250ServiceResponse.getRtnCode();
        if (status != null && !status.equals(RtnCodeStatus.RTNCODE)) {
            throw new PromptException("T250接口错误"+status+"：错误原因", t250ServiceResponse.getRtnDesc());
        }
        return RequestSupport.updateReturnJson(true, "已经划拨成功", null).toString();
    }
}
