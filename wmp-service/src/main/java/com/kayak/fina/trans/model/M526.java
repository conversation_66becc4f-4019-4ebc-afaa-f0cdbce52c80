package com.kayak.fina.trans.model;

import com.kayak.common.base.BaseModel;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

/**
 * 当日机构销售额查询
 */
@Data
@GraphQLModel(fetcher = "m526Service")
public class M526 extends BaseModel {
    @GraphQLField( key = true, label = "份额明细流水号", sql = "vol_trans_serno = $S{volTransSerno}" ,field = "vol_trans_serno")
    private String volTransSerno;
    @GraphQLField( label = "TA代码", sql = "t1.tano = $S{tano}" ,field = "tano")
    private String tano;
    @GraphQLField( label = "法人代码", sql = "(legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode})", field = "legal_code")
    private String legalCode;
    @GraphQLField(label = "产品代码", sql = "t1.prod_code = $S{prodCode}" )
    private String prodCode;
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "TA名称", field = "ta_name")
    private String taName;
    @GraphQLField(label = "交易机构", field = "trans_orgno")
    private String transOrgno; //即机构代码
    @GraphQLField(label = "机构名称", field = "orgno_name")
    private String orgnoName; //表无此字段
    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true, kkhtmlExt = "{'dataAllowblank':false}", label = "扣款日期", sql = "t1.busi_date = $S{busiDate}", field = "busi_date")
    private String busiDate;
    @GraphQLField(label = "总销售额(万元)", field = "total_amt")
    private String totalAmt;
    @GraphQLField(label = "个人户数", field = "pers_count")
    private String persCount;
    @GraphQLField(label = "个人销售额(万元)", field = "pers_amt")
    private String persAmt;
    @GraphQLField(label = "企业户数", field = "comp_count")
    private String compCount;
    @GraphQLField(label = "企业销售额(万元)", field = "comp_amt")
    private String compAmt;
    @GraphQLField(label = "交易机构名称",field = "trans_org_name")
    private String transOrgName;

}
