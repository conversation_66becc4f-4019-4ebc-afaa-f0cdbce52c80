package com.kayak.fina.trans.model;

import com.kayak.common.base.BaseModel;
import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

/**
 * @Description 货币型产品每日收益查询
 * <AUTHOR>
 * @Date 2020/12/23 11:42
 **/
@Data
@GraphQLModel(fetcher = "m527Service",table = "fina_cust_trans_cfm_log",firstLineNotDesensitized = true)
public class M527 extends BaseModel {
   @GraphQLField(label = "原申请单编号", field = "app_serno")
   private String appSerno;
   @GraphQLField(label = "TA确认交易流水号", field = "ta_ack_serno")
   private String taAckSerno;
   @GraphQLField(kkhtml = "KFieldDate", label = "确认日期", sql = "ack_date = $S{ackDate}" ,field = "ack_date", kkhtmlDefault = true)
   private String ackDate;
   @GraphQLField(kkhtml = "KFieldText",  label = "客户号", sql = "cust_no LIKE '%$U{custNo}%'" ,field = "cust_no", kkhtmlDefault = true)
   private String custNo;
   @GraphQLField(kkhtml = "KFieldText", label = "客户名称", field = "cust_name", sql = "cust_name LIKE '%$U{custName}%'" , kkhtmlDefault = true)
   private String custName;
   @GraphQLField(kkhtml = "KFieldSelect", label = "客户类型", sql = "cust_type = $S{custType}" , kkhtmlExt = "{\"data-dict\":\"cust_type\"}", field = "cust_type", kkhtmlDefault = true)
   private String custType;
   @GraphQLField( label = "TA代码", field = "tano")
   private String tano;
   @GraphQLField( label = "TA名称", field = "ta_name")
   private String taName;
   @GraphQLField( label = "TA账号", field = "ta_acct_no")
   private String taAcctNo;
   @GraphQLField( label = "交易账号" , field = "trans_acct_no")
   private String transAcctNo;
   @GraphQLField(label = "产品代码", sql = "prod_code LIKE '%$U{prodCode}%'", field = "prod_code")
   private String prodCode;
   @GraphQLField( label = "产品名称", field = "prod_name")
   private String prodName;
   @GraphQLField(kkhtml = "KFieldText", sql = "acct_no LIKE '%$U{acctNo}%'", label = "资金账号", field = "acct_no", desensitized = Blankcarddesensitized.class, kkhtmlDefault = true)
   private String acctNo;
   @GraphQLField( label = "币种", field = "cur")
   private String cur;
   @GraphQLField( label = "确认金额", field = "ack_amt")
   private String ackAmt;
   @GraphQLField( label = "法人代码", sql = "(legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode})", field = "legal_code")
   private String legalCode;
}