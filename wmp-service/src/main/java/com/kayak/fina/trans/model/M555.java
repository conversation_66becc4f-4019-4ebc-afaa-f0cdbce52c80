package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@GraphQLModel(fetcher = "m555Service",table = "fina_sale_fee_divide")
@Data
public class M555 {
    @GraphQLField(label = "TA代码", field = "tano")
    private String tano;
    @GraphQLField(label = "产品编号", field = "prod_code")
    private String prodCode;
    @GraphQLField( sql = "orgno = $S{orgno}",
            label = "机构代码", field = "orgno")
    private String orgno;
    @GraphQLField(label ="TA名称", field = "ta_name")
    private String taName;
    @GraphQLField(label ="产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "机构名称",field = "org_name")
    private String orgName;
    @GraphQLField( kkhtml = "KFieldDate",sql = "begin_date = $S{beginDate}",
            label = "开始日期", field = "begin_date")
    private String beginDate;
    @GraphQLField( kkhtml = "KFieldDate",sql = "end_date = $S{endDate}",
            label = "结束日期", field = "end_date")
    private String endDate;
    @GraphQLField( label = "总金额", field = "total_amount")	
    private	BigDecimal	totalAmount;
    @GraphQLField( label = "总行比例", field = "bank_ratio")	
    private	BigDecimal	bankRatio;
    @GraphQLField( label = "分配参考类型", field = "refer_divide_type")	
    private String referDivideType;
    @GraphQLField( label = "总存量/销量", field = "total_stock")	
    private BigDecimal totalStock;
    @GraphQLField( label = "机构存量/销量", field = "org_stock")	
    private	BigDecimal	orgStock;
    @GraphQLField( label = "参考分配金额", field = "refer_divide_amt")	
    private	BigDecimal	referDivideAmt;
    @GraphQLField( label = "调整金额", field = "adjust_amt")	
    private	BigDecimal	adjustAmt;
    @GraphQLField( label = "实际分配金额", field = "actual_divide_amt")	
    private	BigDecimal	actualDivideAmt;
    @GraphQLField( label = "划款状态", field = "transfer_status")	
    private String transferStatus;
    @GraphQLField( label = "出账账号", field = "out_acct_no")	
    private String outAcctNo;
    @GraphQLField( label = "入账账号", field = "in_acct_no")	
    private String inAcctNo;
    @GraphQLField( label = "应划款日期", field = "trans_date")
    private String transDate;
    @GraphQLField( label = "实际划款日期", field = "actual_trans_date")
    private String actualTransDate;
    @GraphQLField( label = "费用类型", field = "fee_type")
    private String feeType;
    @GraphQLField( label = "币种", field = "cur")
    private String cur;
    @GraphQLField( label = "返回描述", field = "RTN_DESC")
    private String rtnDesc;
    @GraphQLField( label = "划拨批次号", field = "TASK_EXECID")
    private String taskExecid;
    @GraphQLField( label = "返回码", field = "RTN_CODE")
    private String rtnCode;
    @GraphQLField( label = "划拨流水号", field = "TRANSFER_SERNO")
    private String transferSerno;
    @GraphQLField( label = "划拨业务流水号", field = "BUSS_SERNO")
    private String bussSerno;
    @GraphQLField( label = "划拨全局流水号", field = "GLOBAL_SERNO")
    private String globalSerno;
    @GraphQLField( label = "客户类型", field = "CUST_TYPE")
    private String custType;
    @GraphQLField()
    private List<M555> m555List;
}
