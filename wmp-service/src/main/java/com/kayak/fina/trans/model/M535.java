package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m535Service",table="fina_cust_trans_req_log")
public class M535 {
    @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "交易类型",
            sql = "busi_code = $S{transCode}",
            field = "busi_code",
            kkhtmlExt="{\"data-dict\": \"busi_code\"}")
    private String busiCode;
    @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldDate",
            label = "交易申请日期",
            sql = "busi_date = $S{busiDate}",
            field = "busi_date"
    )
    private String busiDate;
   /** @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "产品代码",
            sql = "prod_code = $S{prodCode}",
            field = "prod_code",
            kkhtmlExt="{\"data-action\":\"M215.queryProdInfoList\",\"data-value-field\":\"prod_code\",\"data-display-field\":\"prod_code,prod_name\", \"data-params\":{'systemNo':'FINA'}}"
    )*/
    @GraphQLField(label = "产品代码", field = "prod_code",sql = "prod_code = $S{prodCode}")
    private String prodCode;
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "总金额", field = "sum_app_amt")
    private String sumAppAmt;
    @GraphQLField(label = "总份额", field = "sum_app_vol")
    private String sumAppVol;
    @GraphQLField(label = "申购时收益率", field = "crt_time")
    private String ao;
    @GraphQLField(label = "客户类型", field = "cust_type")
    private String custType;
    @GraphQLField(label = "购买人数", field = "count_cust_no")
    private String countCustNo;
    @GraphQLField(sql = "tano = $S{tano}",label = "TA代码" ,field = "tano")
    private String tano;
}
