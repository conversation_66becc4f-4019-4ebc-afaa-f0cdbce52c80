package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M228Dao;
import com.kayak.fina.trans.model.M228;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "开放周期查询", model = M228.class)
public class M228Service {

    @Autowired
    private M228Dao m228Dao;
    @Autowired
    private M215Service m215Service;
    @API(desc = "开放周期查询", auth = APIAuth.YES)
    public SqlResult<M228> findPeriodlist(SqlParam<M228> params) throws Exception {
        SqlResult<M228> sqlRowSqlResult = m228Dao.findPeriodlist(params);
        List<M228> volList = sqlRowSqlResult.getRows();
        for (M228 m228 : volList){
            Map<String,Object> map = new HashMap<>();
            map.put("systemNo","FINA");
            map.put("prodCode",m228.getProdCode());
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(map, M215.class));
            if (prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                m228.setProdName(prodPara.get("prod_name"));
            }
        }
        sqlRowSqlResult.setDesensitized(false);
        return sqlRowSqlResult;
    }
}
