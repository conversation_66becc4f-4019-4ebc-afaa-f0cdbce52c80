package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M566Dao;
import com.kayak.fina.trans.model.M566;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 客户分红明细历史报表
 * xsh
 */
@Service
@APIDefine(desc = "客户分红明细历史报表", model = M566.class)
public class M566Service {

	@Autowired
	private M566Dao m566Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "分红汇总报表", auth = APIAuth.YES)
	public SqlResult<M566> findFinaProdBasicParam(SqlParam<M566> params) throws Exception {
		SqlResult<M566> sqlResult = m566Dao.findFundBonusSum(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
