package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m556Service",table = "fina_cust_vol",firstLineNotDesensitized = true)
public class M556 {
   @GraphQLField(label = "TA名称", field = "ta_name")
   private String taName;

   @GraphQLField(label = "产品名称", field = "prod_name")
   private String prodName;
   @GraphQLField(label = "TA代码" ,sql = "t.tano = $S{tano}" ,field = "tano")
   private String tano;
   @GraphQLField(label = "产品代码", sql = "t.prod_code = $S{prodCode}" ,field = "prod_code")
   private String prodCode;
   @GraphQLField(kkhtml = "KFieldText", label = "客户号", sql = "t.cust_no = $S{custNo}" , field = "cust_no")
   private String custNo;
   @GraphQLField(kkhtml = "KFieldSelect", label = "证件类型", kkhtmlDefault = true, kkhtmlExt="{\"data-dict\": \"id_type\",\"ref\": \"idType\"}", field = "id_type")
   private String idType;
   @GraphQLField(kkhtml = "KFieldText",label = "证件号码", kkhtmlDefault = true,kkhtmlExt = "{\"ref\": \"idCode\"}",sql = "t.id_code like '%$U{idCode}%'", field = "id_code", desensitized = IdCardDesensitized.class)
   private String idCode;
   @GraphQLField(kkhtml = "KFieldText",label = "客户姓名",  field = "cust_name")
   private String custName;
   @GraphQLField(kkhtml = "KFieldSelect", label = "客户类型", sql = "t.cust_type = $S{custType}" , kkhtmlExt = "{\"data-dict\":\"cust_type\"}", field = "cust_type")
   private String custType;
   @GraphQLField(kkhtml = "KFieldText", label = "交易账号", sql = "t.trans_acct_no = $S{transAcctNo}" ,field = "trans_acct_no")
   private String transAcctNo;


   @GraphQLField(label = "资金账号" ,field = "acct_no", desensitized = Blankcarddesensitized.class)
   private String acctNo;
   @GraphQLField( label = "总份额", sql = "t.total_vol = $S{totalVol}" ,field = "total_vol")
   private String totalVol;
   @GraphQLField( label = "可用份额", sql = "t.AVAIL_VOL = $S{availableVol}" ,field = "AVAIL_VOL")
   private String availVol;
   @GraphQLField( label = "赎回冻结份额", sql = "t.redeem_frozen_vol = $S{redeemFrozenVol}" ,field = "redeem_frozen_vol")
   private String redeemFrozenVol;
   @GraphQLField( label = "质押冻结份额", sql = "t.abn_frozen_vol = $S{abnFrozenVol}" ,field = "abn_frozen_vol")
   private String abnFrozenVol;
   @GraphQLField(label = "司法冻结份额", sql = "t.elisor_frozen_vol = $S{elisorFrozenVol}" ,field = "elisor_frozen_vol")
   private String elisorFrozenVol;
   @GraphQLField( label = "转让冻结份额", sql = "t.transfer_frozen_vol = $S{transferFrozenVol}" ,field = "transfer_frozen_vol")
   private String transferFrozenVol;
   @GraphQLField( label = "非交易过户冻结份额", sql = "t.NOTRANS_FROZEN_VOL = $S{notransFrozenVol}" ,field = "notrans_frozen_vol")
   private String notransFrozenVol;
   @GraphQLField( label = "总成本", field = "total_cost")
   private String totalCost;
   @GraphQLField( label = "当前分红方式", field = "DEF_DIV_METHOD")
   private String defDivMethod;
   @GraphQLField( label = "交易变更的分红方式", field = "CHG_DIV_METHOD")
   private String chgDivMethod;
   @GraphQLField( label = "分红方式变更日期", field = "CHG_DIV_DATE")
   private String chgDivDate;
   @GraphQLField( label = "上一日收益", sql = "t.LAST_DAY_INCOME = $S{lastDayIncome}" ,field = "LAST_DAY_INCOME")
   private String lastDayIncome;
   @GraphQLField( label = "最新收益日期", sql = "t.LAST_INCOME_DAY = $S{lastIncomeDay}" ,field = "LAST_INCOME_DAY")
   private String lastIncomeDay;
   @GraphQLField(label = "近一周收益", sql = "t.LAST_WEEK_INCOME = $S{lastWeekIncome}" ,field = "LAST_WEEK_INCOME")
   private String lastWeekIncome;
   @GraphQLField( label = "近一月收益", sql = "t.LAST_MONTH_INCOME = $S{lastMonthIncome}" ,field = "LAST_MONTH_INCOME")
   private String lastMonthIncome;
   @GraphQLField(label = "累计总收益", sql = "t.total_fee_back = $S{totalFeeBack}" ,field = "TOTAL_INCOME")
   private String totalIncome;


}