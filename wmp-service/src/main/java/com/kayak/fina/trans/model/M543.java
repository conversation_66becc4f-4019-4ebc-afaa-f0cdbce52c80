package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m543Service",table="fina_cust_vol")
public class M543 {
    @GraphQLField (label = "TA代码", field = "tano")
    private String tano;

    @GraphQLField (label = "TA名称", field = "ta_name")
    private String taName;

    @GraphQLField(label = "法人代码", field = "legal_code")
    private String legalCode;

   /** @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "产品代码",
            sql = "prod_code = $S{prodCode}",
            field = "prod_code",
            kkhtmlExt="{\"data-action\":\"M215.queryProdInfoList\",\"data-value-field\":\"prod_code\",\"data-display-field\":\"prod_code,prod_name\", \"data-params\":{'systemNo':'FINA'}}"
    )*/
    @GraphQLField(label = "产品代码", field = "prod_code", sql = "prod_code = $S{prodCode}")
    private String prodCode;
    //     产品名称
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    //    开放周期序号
    @GraphQLField(label = "开放周期序号", field = "periods")
    private String periods;
    //   起息日
    @GraphQLField(label = "起息日", field = "income_date")
    private String incomeDate;
    //   到期日

    @GraphQLField(key = true ,kkhtmlDefault=true, kkhtml = "KFieldDate", label = "到期日", sql = "can_redeem_date >= $S{canRedeemDate}", field = "can_redeem_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'canRedeemEndDate','dataAllowblank':false}")
    private String canRedeemDate;
    @GraphQLField(label = "结束日期", sql = "can_redeem_date <= $S{canRedeemEndDate}", field = "can_redeem_date")
    private String canRedeemEndDate;
    //    期限
    @GraphQLField(label = "期限", field = "eadline")
    private int eadline;
    //    预计兑付金额
    @GraphQLField(label = "预计兑付金额", field = "app_amt")
    private String sumAmt;
    //    预计兑付份额
    @GraphQLField(label = "预计兑付份额", field = "sum_total_vol")
    private String sumTotalVol;
    //    客户预期收益率/业绩比较基准
    @GraphQLField(label = "客户预期收益率", field = "benchmarks")
    private String benchmarks;
}
