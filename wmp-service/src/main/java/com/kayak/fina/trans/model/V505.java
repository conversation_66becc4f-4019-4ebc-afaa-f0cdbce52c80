package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;

/**
 * 客户份额查询
 *
 * <AUTHOR>
 * @date 2021-04-14 10:11
 */
@GraphQLModel(fetcher = "v505Service",table = "fina_cust_vol")
public class V505 {

    @GraphQLField(label = "TA代码", sql = "tano = $S{tano}" ,field = "tano")
    private String tano;
    @GraphQLField( label = "产品代码", sql = "prod_code = $S{prodCode}" ,field = "prod_code")
    private String prodCode;
    @GraphQLField(label = "法人代码", sql = "(legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode})",field = "legal_code")
    private String legalCode;
    @GraphQLField( label = "总份额", sql = "total_vol = $S{totalVol}" ,field = "total_vol")
    private String totalVol;
    @GraphQLField( label = "可用份额", sql = "available_vol = $S{availableVol}" ,field = "available_vol")
    private String availableVol;
    @GraphQLField( label = "赎回冻结份额", sql = "redeem_frozen_vol = $S{redeemFrozenVol}" ,field = "redeem_frozen_vol")
    private String redeemFrozenVol;
    @GraphQLField( label = "质押冻结份额", sql = "abn_frozen_vol = $S{abnFrozenVol}" ,field = "abn_frozen_vol")
    private String abnFrozenVol;
    @GraphQLField(label = "司法冻结份额", sql = "elisor_frozen_vol = $S{elisorFrozenVol}" ,field = "elisor_frozen_vol")
    private String elisorFrozenVol;
    @GraphQLField( label = "转让冻结份额", sql = "transfer_frozen_vol = $S{transferFrozenVol}" ,field = "transfer_frozen_vol")
    private String transferFrozenVol;
    @GraphQLField( label = "份额确认日期", sql = "ack_date = $S{ackDate}" ,field = "ack_date")
    private String ackDate;
    @GraphQLField( label = "TA账号", sql = "ta_acct_no = $S{taAcctNo}" ,field = "ta_acct_no")
    private String taAcctNo;
    @GraphQLField( label = "交易账号", sql = "trans_acct_no = $S{transAcctNo}" ,field = "trans_acct_no")
    private String transAcctNo;
    @GraphQLField( label = "交易总行代码", sql = "bank_code = $S{bankCode}" ,field = "bank_code")
    private String bankCode;
    @GraphQLField( label = "交易分行代码", sql = "branch_code = $S{branchCode}" ,field = "branch_code")
    private String branchCode;
    @GraphQLField(label = "支行网点代码", sql = "sub_branch_code = $S{subBranchCode}" ,field = "sub_branch_code")
    private String subBranchCode;
    @GraphQLField( label = "交易机构", sql = "trans_orgno = $S{transOrgno}" ,field = "trans_orgno")
    private String transOrgno;
    @GraphQLField(label = "交易后端收费总额", sql = "total_fee_back = $S{totalFeeBack}" ,field = "total_fee_back")
    private String totalFeeBack;
    @GraphQLField( label = "收费方式", sql = "shareclass = $S{shareclass}" ,field = "shareclass")
    private String shareclass;
    @GraphQLField(label = "明细标志", sql = "detail_flag = $S{detailFlag}" ,field = "detail_flag")
    private String detailFlag;
    @GraphQLField(label = "份额注册日期", sql = "register_date = $S{registerDate}" ,field = "register_date")
    private String registerDate;
    @GraphQLField( label = "未结转收益", sql = "unconvert_income = $S{unconvertIncome}" ,field = "unconvert_income")
    private String unconvertIncome;
    @GraphQLField( label = "份额原始来源", sql = "source_type = $S{sourceType}" ,field = "source_type")
    private String sourceType;
    @GraphQLField( label = "可赎回日期", sql = "can_redeem_date = $S{canRedeemDate}" ,field = "can_redeem_date")
    private String canRedeemDate;
    @GraphQLField(label = "份额最后变动日", sql = "upd_time = $S{updTime}" ,field = "upd_time")
    private String updTime;
    @GraphQLField( label = "周期到期处理方式", sql = "cycle_expire = $S{cycleExpire}" ,field = "cycle_expire")
    private String cycleExpire;

    @GraphQLField(kkhtml = "KFieldText", label = "客户号",kkhtmlDefault = true, field = "cust_no", kkhtmlExt = "{\"dataAllowblank\":false}")
    private String custNo;
    @GraphQLField( label = "客户名称",  field = "cust_name")
    private String custName;
    @GraphQLField( label = "客户类型", field = "cust_type")
    private String custType;
    @GraphQLField( label = "资金账号", field = "acct_no")
    private String acctNo;
    @GraphQLField( label = "TA名称", field = "ta_name")
    private String taName;
    @GraphQLField( label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField( label = "冻结总份数", field = "total_frozen_vol")
    private String totalFrozenVol;
    @GraphQLField( label = "总成本", field = "total_cost")
    private String totalCost;

    public String getTano() {
        return tano;
    }

    public void setTano(String tano) {
        this.tano = tano;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getLegalCode() {
        return legalCode;
    }

    public void setLegalCode(String legalCode) {
        this.legalCode = legalCode;
    }

    public String getTotalVol() {
        return totalVol;
    }

    public void setTotalVol(String totalVol) {
        this.totalVol = totalVol;
    }

    public String getAvailableVol() {
        return availableVol;
    }

    public void setAvailableVol(String availableVol) {
        this.availableVol = availableVol;
    }

    public String getRedeemFrozenVol() {
        return redeemFrozenVol;
    }

    public void setRedeemFrozenVol(String redeemFrozenVol) {
        this.redeemFrozenVol = redeemFrozenVol;
    }

    public String getAbnFrozenVol() {
        return abnFrozenVol;
    }

    public void setAbnFrozenVol(String abnFrozenVol) {
        this.abnFrozenVol = abnFrozenVol;
    }

    public String getElisorFrozenVol() {
        return elisorFrozenVol;
    }

    public void setElisorFrozenVol(String elisorFrozenVol) {
        this.elisorFrozenVol = elisorFrozenVol;
    }

    public String getTransferFrozenVol() {
        return transferFrozenVol;
    }

    public void setTransferFrozenVol(String transferFrozenVol) {
        this.transferFrozenVol = transferFrozenVol;
    }

    public String getAckDate() {
        return ackDate;
    }

    public void setAckDate(String ackDate) {
        this.ackDate = ackDate;
    }

    public String getTaAcctNo() {
        return taAcctNo;
    }

    public void setTaAcctNo(String taAcctNo) {
        this.taAcctNo = taAcctNo;
    }

    public String getTransAcctNo() {
        return transAcctNo;
    }

    public void setTransAcctNo(String transAcctNo) {
        this.transAcctNo = transAcctNo;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getSubBranchCode() {
        return subBranchCode;
    }

    public void setSubBranchCode(String subBranchCode) {
        this.subBranchCode = subBranchCode;
    }

    public String getTransOrgno() {
        return transOrgno;
    }

    public void setTransOrgno(String transOrgno) {
        this.transOrgno = transOrgno;
    }

    public String getTotalFeeBack() {
        return totalFeeBack;
    }

    public void setTotalFeeBack(String totalFeeBack) {
        this.totalFeeBack = totalFeeBack;
    }

    public String getShareclass() {
        return shareclass;
    }

    public void setShareclass(String shareclass) {
        this.shareclass = shareclass;
    }

    public String getDetailFlag() {
        return detailFlag;
    }

    public void setDetailFlag(String detailFlag) {
        this.detailFlag = detailFlag;
    }

    public String getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(String registerDate) {
        this.registerDate = registerDate;
    }

    public String getUnconvertIncome() {
        return unconvertIncome;
    }

    public void setUnconvertIncome(String unconvertIncome) {
        this.unconvertIncome = unconvertIncome;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getCanRedeemDate() {
        return canRedeemDate;
    }

    public void setCanRedeemDate(String canRedeemDate) {
        this.canRedeemDate = canRedeemDate;
    }

    public String getUpdTime() {
        return updTime;
    }

    public void setUpdTime(String updTime) {
        this.updTime = updTime;
    }

    public String getCycleExpire() {
        return cycleExpire;
    }

    public void setCycleExpire(String cycleExpire) {
        this.cycleExpire = cycleExpire;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustType() {
        return custType;
    }

    public void setCustType(String custType) {
        this.custType = custType;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getTaName() {
        return taName;
    }

    public void setTaName(String taName) {
        this.taName = taName;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public String getTotalFrozenVol() {
        return totalFrozenVol;
    }

    public void setTotalFrozenVol(String totalFrozenVol) {
        this.totalFrozenVol = totalFrozenVol;
    }

    public String getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(String totalCost) {
        this.totalCost = totalCost;
    }
}
