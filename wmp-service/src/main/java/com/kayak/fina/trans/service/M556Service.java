package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M101Dao;
import com.kayak.fina.trans.dao.M556Dao;
import com.kayak.fina.trans.model.M556;
import com.kayak.fund.dao.M230Dao;
import com.kayak.prod.service.M215Service;
import com.kayakwise.wmp.base.exception.TransException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "客户份额查询", model = M556.class)
@Slf4j
public class M556Service {

	@Autowired
	private M556Dao m556Dao;

	@Autowired
	private M101Dao m101Dao;

	@Autowired
	private M215Service m215Service;

	@Autowired
	private M230Dao m230Dao;

	@Autowired
	private ReportformUtil reportformUtil;


	@API(desc = "查询客户份额明细实体类信息", auth = APIAuth.YES)
	public SqlResult<M556> findM556(SqlParam<M556> params) throws Exception {
		//如果是查询条件进来，必须预先判断是否存在客户信息
		if(params.getModel().getCustNo() ==null){
			params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),""));
		}
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		SqlResult<M556> sqlResult = m556Dao.findM556(params);
		if (sqlResult.getRows().size() <= 0) {
			return sqlResult;
		}
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		List<M556> vollist = sqlResult.getRows();
		String tano = vollist.get(0).getTano();
		if (StringUtils.isBlank(tano)) {
			throw new TransException("M55601","tano不能为空--"+vollist.get(0).getProdCode());
		}
		//将产品代码去重后再查询redis,数据量大的时候会有很大的效率差  collect(Collectors.toMap(o -> o, o -> o)
		HashMap<String, String> prodNameMap = new HashMap<>();
		List<String> prodCodeList = vollist.stream().map(m556 -> m556.getProdCode()).distinct().collect(Collectors.toList());
		for (String prodCode : prodCodeList) {
			prodNameMap.put(prodCode,reportformUtil.getProdName(SystemNo.FINA,tano,prodCode));
		}
		for(M556 m556:vollist){
			//m556.setTaName(reportformUtil.getFinaTaName(m556.getTano()));
			m556.setProdName(prodNameMap.get(m556.getProdCode()));
			/**M230 m230=m230Dao.getCusts(m556.getCustNo());
			if(m230 !=null && !"".equals(m230.getCustName())){
				m556.setCustName(m230.getCustName());
				m556.setIdCode(m230.getIdCode());
				m556.setIdType(m230.getIdType());
				m556.setCustType(m230.getCustType());
			}
			if(m556 !=null && !"".equals(m556.getTransAcctNo())){
				m556.setAcctNo(m556Dao.getAcctNoByTransAcctNo(m556.getTransAcctNo()));
			}*/

		}
		stopWatch.stop();
		log.info("M556总查询耗时: " + stopWatch.getTotalTimeMillis());
		sqlResult.setDesensitized(false);
		return sqlResult;
	}




}
