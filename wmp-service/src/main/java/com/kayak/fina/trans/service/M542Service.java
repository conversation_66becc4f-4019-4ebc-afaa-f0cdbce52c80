package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.common.util.SimpleDataUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M228Dao;
import com.kayak.fina.trans.dao.M542Dao;
import com.kayak.fina.trans.model.M542;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "募集资金查询报表", model = M542.class)
public class M542Service {
    @Autowired
    private M542Dao m542Dao;
    @Autowired
    private M228Dao m228Dao;
    @Resource
    private M215Service m215Service;

    @Autowired
    private ReportformUtil reportformUtil;


    @API(desc = "募集资金查询报表", auth = APIAuth.YES)
    public SqlResult<M542> findCustTransReq(SqlParam<M542> params) throws Exception {
        SqlResult<M542> sqlResult = new SqlResult<M542>();
        List<M542> custTransReq = m542Dao.findCustTransReq(params);
        reportformUtil.checkMaxExcel(custTransReq.size());
        List<M542> list = new ArrayList<>();

        for (M542 m542:custTransReq) {
            if (StringUtils.isNotBlank(m542.getTano()) && StringUtils.isNotBlank(m542.getProdCode())){
                Map<String, Object> prodParam = new HashMap<>();
                prodParam.put("systemNo", SystemNo.FINA);
                prodParam.put("tano", m542.getTano());
                prodParam.put("userid",params.getParams().get("userid"));
                prodParam.put("prodCode", m542.getProdCode());
                //List<M228> m228s = m228Dao.findPeriodlist(new FetcherData<>(prodParam, M228.class)).getRows();
                //
                //if (m228s != null && m228s.size() > 0){
                //    //开放周期
                //    m542.setPeriods(m228s.get(0).getPeriods());
                //    //周期天数
                //    m542.setHoldDays(m228s.get(0).getHoldDays());
                //}
                List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
                for (Map<String, String> prodPara : prodParaList) {
                    String incomeDate = prodPara.get("income_date");
                    String endDate = prodPara.get("end_date");
                    if (StringUtils.isNotBlank(incomeDate)&&StringUtils.isNotBlank(endDate)) {
                        //产品期限（到期日-起息日）
                        String days = String.valueOf(SimpleDataUtil.daysBetween2(incomeDate, endDate));
                        m542.setHoldDays(days);
                    }
                    //募集结束日
                    m542.setSubsEndDate(prodPara.get("subs_end_date"));
                    //起息日
                    m542.setIncomeDate(incomeDate);
                    //到期日
                    m542.setEndDate(endDate);
                    if (StringUtils.isNotBlank(params.getModel().getIncomeDate())){
                        if (StringUtils.isNotBlank(incomeDate)){
                            int importDate = Integer.parseInt(incomeDate);
                            int beginDate = Integer.parseInt(params.getModel().getIncomeDate());
                            int endDateInt = Integer.parseInt(params.getModel().getEndDate());
                            if (importDate >= beginDate && importDate <= endDateInt){
                                list.add(m542);
                            }
                        }
                    }else {
                        list.add(m542);
                    }
                }
            }
        }
        // 手动分页
        int count = list.size();
        int start = params.getStart();
        int limit = params.getLimit();
        if (limit == 0){
            sqlResult.setRows(list);
        }else {
            sqlResult.setRows(list.subList(start, Math.min(start + limit, count)));
        }
        sqlResult.setResults(count);
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
