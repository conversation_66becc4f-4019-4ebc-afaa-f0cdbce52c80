package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.core.desensitized.PhoneDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m540Service",table="fina_cust_vol",firstLineNotDesensitized = true)
public class M540 {
     /**TA代码 */
    @GraphQLField(label = "TA代码", field = "tano",sql = "tano = $S{tano}")
    private String tano;
    @GraphQLField(label = "法人代码",  field = "legal_code")
    private String legalCode;
    //    机构名称
    @GraphQLField(label = "TA名称", field = "ta_name")
    private String taName;
    //    机构名称
    @GraphQLField(label = "机构名称", field = "org_name")
    private String orgName;
    @GraphQLField(kkhtml = "KFieldCascader", kkhtmlExt="{\"data-diffcondition\":\"orgno,parentorgno\",\"data-graphql\":\"{queryM001(action:\\\"find\\\") {rows{orgno, orgname, parentorgno, orgid},results}}\",\"data-display-child\":\"children\",\"data-check-strictly\":true,\"data-show-num\":true,\"data-props\":\"{ expandTrigger: 'hover'}\",\"data-size\":\"medium\",\"data-clearable\":true,\"data-fileterable\":true,\"data-display-field\":\"orgname\",\"data-value-field\":\"orgno\"}",
            label = "所属机构", sql = "trans_orgno = $S{transOrgno}", field = "trans_orgno",kkhtmlDefault=true)
    private String transOrgno;
    //    交易流水号
    @GraphQLField(kkhtml = "KFieldText", label = "交易流水号", field = "app_serno", sql = "app_serno = $S{appSerno}",kkhtmlDefault=true)
    private String appSerno;

    @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "业务代码",
            sql = "busi_code = $S{busiCode}",
            field = "busi_code",
            kkhtmlExt="{\"data-dict\": \"busi_code\"}" )
    private String busiCode;
    //    银行账号
    @GraphQLField(kkhtml = "KFieldText",label = "银行账号", field = "acct_no", sql = "acct_no = $S{acctNo}", desensitized = Blankcarddesensitized.class,kkhtmlDefault=true)
    private String acctNo;
    //    客户名称
    @GraphQLField(kkhtml = "KFieldText",label = "客户名称", field = "cust_name", sql = "cust_name like '%$U{custName}%'",kkhtmlDefault=true)
    private String custName;

    @GraphQLField(kkhtml = "KFieldText",label = "客户号", field = "cust_no", sql = "cust_no = $S{custNo}",kkhtmlDefault=true)
    private String custNo;

    //    手机号-关联客户信息表查询
    @GraphQLField(label = "手机号", field = "mobile", desensitized = PhoneDesensitized.class)
    private String mobile;
    //    证件类型
    @GraphQLField(kkhtml = "KFieldSelect",
            kkhtmlExt="{\"data-dict\": \"id_type\"}",
            kkhtmlDefault = true,
            sql = "id_type = $S{idType}",
            label = "证件类型",
            field = "id_type")
    private String idType;
    //    证件号码
    @GraphQLField(kkhtml = "KFieldText",
                  label = "证件号码",
                   field = "id_code",
            sql = "id_code = $S{idCode}", desensitized = IdCardDesensitized.class ,kkhtmlDefault=true   )
    private String idCode;

    @GraphQLField(label = "产品代码", sql = "prod_code = $S{prodCode}", field = "prod_code")
    private String prodCode;
    //     产品名称

    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    //    开放周期

    //    产品状态
    @GraphQLField(label = "产品状态", field = "prod_status")
    private String prodStatus;

    //    申请金额
    @GraphQLField(label = "申请金额", field = "app_amt")
    private String appAmt;
    //    确认金额
    @GraphQLField(label = "确认金额", field = "ack_amt")
    private String ackAmt;
    //    申请份额
    @GraphQLField(label = "申请份额", field = "app_vol")
    private String appVol;
    //    确认份额
    @GraphQLField(label = "确认份额", field = "ack_vol")
    private String ackVol;
    //    机器日期
    @GraphQLField(label = "机器日期", field = "mactime")
    private String macDate;
    //    机器时间
    @GraphQLField(label = "机器时间", field = "mactime")
    private String macTime;
    //    交易日期

    @GraphQLField(key = true ,kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "busi_date >= $S{busiDate}", field = "busi_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'busiEndDate','dataAllowblank':false}")
    private String busiDate;
    @GraphQLField(label = "结束日期", sql = "busi_date <= $S{busiEndDate}", field = "busi_date")
    private String busiEndDate;

    //     确认日期
    @GraphQLField(key = true ,kkhtmlDefault=true, kkhtml = "KFieldDate", label = "确认日期", sql = "ack_date >= $S{ackDate}", field = "ack_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'ackEndDate','dataAllowblank':false}")
    private String ackDate;
    @GraphQLField(label = "结束日期", sql = "ack_date <= $S{ackEndDate}", field = "ack_date")
    private String ackEndDate;
    //    预期收益率（%）
    //    业绩比较基准
    //     交易状态
    @GraphQLField(kkhtml = "KFieldSelect",
            kkhtmlExt="{\"data-dict\": \"trans_status\"}",
            kkhtmlDefault = true,
            sql = "trans_status = $S{transStatus}",
            label = "交易状态",
            field = "trans_status")

    private String transStatus;
    @GraphQLField(kkhtml = "KFieldSelect",
            kkhtmlExt="{\"data-dict\": \"channel_flag\"}",
            kkhtmlDefault = true,
            sql = "CHANNEL_FLAG = $S{channelFlag}",
            label = "交易渠道",
            field = "CHANNEL_FLAG")

    private String channelFlag;
    //    份额控制原因
    //    返回信息
    @GraphQLField(label = "返回信息", field = "rtn_desc")
    private String rtnDesc;
//    核心返回信息
//    终端类型
//    推荐人代码
    @GraphQLField(label = "推荐人代码", field = "cust_manager")
    private String custManager;
//    推荐人所属机构
    @GraphQLField(label = "推荐人代码", field = "cust_manager")
    private String custManagerOrg;
    //  业绩比较基准
    @GraphQLField(label = "业绩比较基准", field = "benchmarks")
    private String benchmarks;
    //  业绩比较基准
    @GraphQLField(label = "周期天数", field = "cycle_days")
    private String cycleDays;

    //  业绩比较基准
    @GraphQLField(label = "巨额赎回标识", field = "HUGE_REDEEM_FLAG")
    private String hugeRedeemFlag;

}
