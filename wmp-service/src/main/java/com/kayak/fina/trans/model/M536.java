package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m536Service",table="fina_cust_trans_cfm_log")
public class M536 {
    @GraphQLField(kkhtmlDefault=true,
            kkhtml = "KFieldSelect",
            label = "交易类型",
            sql = "busi_code = $S{transCode}",
            field = "prod_code",
            kkhtmlExt="{\"data-dict\": \"busi_code\"}")
    private String busiCode;


    @GraphQLField(key = true ,kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易申请日期", sql = "busi_date >= $S{busiDate}", field = "busi_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'busiEndDate','dataAllowblank':false}")
    private String busiDate;
    @GraphQLField(label = "结束日期", sql = "busi_date <= $S{busiEndDate}", field = "busi_date")
    private String busiEndDate;

    @GraphQLField(label = "产品代码", field = "prod_code")
    private String prodCode;
    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "总金额", field = "sum_app_amt")
    private String sumAppAmt;
    @GraphQLField(label = "总份额", field = "sum_app_vol")
    private String sumAppVol;
    @GraphQLField(label = "申购时收益率", field = "ao")
    private String ao;
    @GraphQLField(label = "客户类型", field = "cust_type")
    private String custType;
    @GraphQLField(label = "购买人数", field = "count_cust_no")
    private String countCustNo;
    @GraphQLField(sql = "tano = $S{tano}",label = "TA代码" ,field = "tano")
    private String tano;
}
