package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户分红明细历史报表
 * xsh
 */
@GraphQLModel(fetcher = "m566Service",table = "fina_cust_div_detail_h",firstLineNotDesensitized = true )
@Data
public class M566 {


    @GraphQLField(label = "TA确认交易流水号", field = "ta_ack_serno")
    private String taAckSerno;

    @GraphQLField(label = "TA代码",field = "tano", sql = "tano= $S{tano}")
    private String tano;

    @GraphQLField(label = "TA名称", field = "ta_name", sql = "ta_name= $S{taName}")
    private String taName;

    @GraphQLField(label = "产品代码" , field = "prod_code", sql = "prod_code = $S{prodCode}")
    private String prodCode;

    @GraphQLField(label = "产品名称", field = "prod_name", sql = "prod_name= $S{prodName}")
    private String prodName;

    @GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true , label = "客户号", field = "cust_no",sql = "cust_no= $S{custNo}")
    private String custNo;

    @GraphQLField(label = "交易账号", field = "trans_acct_no", desensitized = Blankcarddesensitized.class)
    private String transAcctNo;

    @GraphQLField(label = "资金账号", field = "acct_no",desensitized = Blankcarddesensitized.class)
    private String acctNo;

    @GraphQLField(label = "TA账号", field = "ta_acct_no")
    private String taAcctNo;

    @GraphQLField(label = "客户类型",field = "cust_type")
    private String custType;

    @GraphQLField(label = "客户名称", field = "cust_name")
    private String custName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = '$U{idType}'",field = "id_type",kkhtmlDefault = true)
    private String idType;

    @GraphQLField(kkhtml = "KFieldText",label = "证件号码", kkhtmlDefault = true ,field = "id_code", sql = "id_code= $S{idCode}", desensitized = IdCardDesensitized.class)
    private String idCode;

    @GraphQLField(label = "业务代码", field = "busi_code")
    private String busiCode;

    @GraphQLField(label = "币种", field = "cur")
    private String cur;

    @GraphQLField(label = "权益登记日期", field = "regist_date")
    private String registDate;

    @GraphQLField(label = "分红日/发放日", field = "div_date")
    private String divDate;

    @GraphQLField(label = "脱敏查询", field = "acct_no")
    private String queryAcctNo;

    @GraphQLField(key = true ,kkhtmlDefault=true, kkhtml = "KFieldDate", label = "分红日期", sql = "div_date >= $S{transDate}", field = "transDate", kkhtmlExt = "{\"data-allowblank\":false,'data-type':'daterange',endDateFeild:'transEndDate'}")
    private String transDate;
    @GraphQLField(label = "结束日期", sql = "div_date <= $S{transEndDate}", field = "trans_date",kkhtmlDefault=true)
    private String transEndDate;


    @GraphQLField(label = "除权日", field = "xr_date")
    private String xrDate;

    @GraphQLField(label = "单位分红金额", field = "div_per_unit")
    private BigDecimal divPerUnit;

    @GraphQLField(label = "分红单位", field = "draw_bonus_unit")
    private BigDecimal drawBonusUnit;

    @GraphQLField(label = "收费方式", field = "charge_type")
    private String chargeType;

    @GraphQLField(label = "默认分红方式", field = "def_div_method")
    private String defDivMethod;

    @GraphQLField(label = "红利/红利再投资基数", field = "base_vol")
    private BigDecimal baseVol;

    @GraphQLField(label = "红利资金", field = "div_amt")
    private BigDecimal divAmt;

    @GraphQLField(label = "红利再投资基金份数", field = "div_vol")
    private BigDecimal divVol;

    @GraphQLField(label = "每笔交易确认金额", field = "ack_amt")
    private BigDecimal ackAmt;

    @GraphQLField(label = "冻结总份数", field = "frozen_vol")
    private BigDecimal frozenVol;

    @GraphQLField(label = "冻结金额", field = "frozen_amt")
    private BigDecimal frozenAmt;

    @GraphQLField(label = "冻结再投资份额", field = "frozen_again_vol")
    private BigDecimal frozenAgainVol;

    @GraphQLField(label = "确认日期", field = "ack_date")
    private String ackDate;

    @GraphQLField(label = "红利比例", field = "div_ratio")
    private BigDecimal divRatio;

    @GraphQLField(label = "交易手续费", field = "trans_fee")
    private BigDecimal transFee;

    @GraphQLField(label = "代理手续费", field = "agency_fee")
    private BigDecimal agencyFee;

    @GraphQLField(label = "印花税", field = "stamp_tax")
    private BigDecimal stampTax;

    @GraphQLField(label = "过户费", field = "transfer_fee")
    private BigDecimal transferFee;

    @GraphQLField(label = "分红类型", field = "div_type")
    private String divType;

    @GraphQLField(label = "业绩报酬", field = "merit_pay")
    private BigDecimal meritPay;

    @GraphQLField(label = "业绩补偿", field = "merit_compen")
    private BigDecimal meritCompen;

    @GraphQLField(label = "法人代码", field = "legal_code")
    private String legalCode;

    @GraphQLField(label = "单位净值", field = "nav")
    private BigDecimal nav;

    @GraphQLField(label = "计费人", field = "fee_calculator")
    private String feeCalculator;

    @GraphQLField(label = "收市后赎回收益下发标志", field = "close_flag")
    private String closeFlag;

    @GraphQLField(label = "其他费用1", field = "other_fee1")
    private BigDecimal otherFee1;

    @GraphQLField(label = "其他费用2", field = "other_fee2")
    private BigDecimal otherFee2;
}