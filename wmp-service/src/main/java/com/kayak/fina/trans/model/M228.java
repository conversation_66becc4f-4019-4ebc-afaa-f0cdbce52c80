package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m228Service",table="fina_prod_periodlist")
public class M228 {

  @GraphQLField(sql = "tano = $S{tano}",label = "TA代码" ,field = "tano")
  private String tano;

   @GraphQLField(label = "产品代码", field = "prod_code",  sql = "prod_code = $S{prodCode}")
    private String prodCode;

    @GraphQLField(label = "开放周期", field = "prod_name")
    private String prodName;

    @GraphQLField(label = "开放周期", field = "periods")
    private String periods;
    @GraphQLField(
            kkhtml = "KFieldDate",
            label = "开放起始日",
            sql = "begin_date = $S{beginDate}",
            field = "begin_date"
            ,kkhtmlDefault=true)
    private String beginDate;
    @GraphQLField(
            kkhtml = "KFieldDate",
            label = "开放结束日",
            sql = "end_date = $S{endDate}",
            field = "end_date",kkhtmlDefault=true
    )
    private String endDate;
    @GraphQLField(label = "产品工作日", field = "prod_workdate")
    private String prodWorkdate;


    @GraphQLField( kkhtml = "KFieldDate", label = "确认日", sql = "open_cfm_date >= $S{openCfmDate}", field = "openCfmDate", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'openCfmEndDate'}",kkhtmlDefault=true)
    private String openCfmDate;
    @GraphQLField(label = "结束日期", sql = "open_cfm_date <= $S{openCfmEndDate}", field = "openCfmEndDate")
    private String openCfmEndDate;


    @GraphQLField( kkhtml = "KFieldDate", label = "兑付确认日", sql = "redeem_cfm_date >= $S{redeemCfmDate}", field = "redeemCfmDate", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'redeemCfmEndDate'}",kkhtmlDefault=true)
    private String redeemCfmDate;
    @GraphQLField(label = "结束日期", sql = "redeem_cfm_date <= $S{redeemCfmEndDate}", field = "redeemCfmEndDate")
    private String redeemCfmEndDate;

    @GraphQLField(label = "周期天数", field = "hold_days")
    private String holdDays;
    @GraphQLField(label = "投资周期到期日", field = "CYCLE_DUE_DATE")
    private String cycleDueDate;
    @GraphQLField(label = "预期收益率", field = "income_rate")
    private String incomeRate;
    @GraphQLField(label = "创建日期", field = "crt_date")
    private String crtDate;
    @GraphQLField(label = "创建时间", field = "crt_time")
    private String crtTime;
    @GraphQLField(label = "备注", field = "remark")
    private String remark;
    @GraphQLField(label = "更新日期", field = "upd_date")
    private String updDate;
    @GraphQLField(label = "更新时间", field = "upd_time")
    private String updTime;
    @GraphQLField(label = "开放允许交易", field = "open_allow_trade_list")
    private String openAllowTradeList;
    @GraphQLField(label = "业绩比较基准", field = "business_rate")
    private String businessRate;
    @GraphQLField(label = "超额计提基准", field = "max_business_rate")
    private String maxBusinessRate;
}
