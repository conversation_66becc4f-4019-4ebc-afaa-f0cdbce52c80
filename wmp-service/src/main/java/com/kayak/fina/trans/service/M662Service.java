package com.kayak.fina.trans.service;

import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.common.constants.RtnCodeStatus;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.fina.trans.dao.M662Dao;
import com.kayak.fina.trans.model.M662;
import com.kayak.prod.service.M215Service;
import com.kayakwise.fina.api.T250DubboDecorator;
import com.kayakwise.fina.req.T250ServiceRequest;
import com.kayakwise.fina.resp.T250ServiceResponse;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;
@Service
@APIDefine(desc = "资金划拨处理服务", model = M662.class)
public class M662Service {

    @Autowired
    private M662Dao m662Dao;

    @Autowired
    private M215Service m215Service;

    @CloudReference
    private T250DubboDecorator t250ServiceDecorator;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询资金划拨信息", auth = APIAuth.YES)
    public SqlResult<M662> findM662s(SqlParam<M662> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M662> sqlResult = m662Dao.findM662s(params);
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            try{
                item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
            } catch (Exception e) {
                throw new RuntimeException("M507错误："+e.getMessage());
            }
            return item;
        }).collect(Collectors.toList()));
        return sqlResult;
    }

    @API(desc = "查询供应商信息", auth = APIAuth.YES)
    public SqlResult<M662> findTano(SqlParam<M662> params) throws Exception {
        params.setMakeSql(true);
        return m662Dao.findTano(params);
    }

    @API(desc = "划拨", auth = APIAuth.YES,operation = APIOperation.UPDATE)
    public String updateM662(SqlParam<M662> params) throws Exception {
        params.setMakeSql(true);
        Map<String, Object> map = new HashedMap();
        map.put("applySerno", params.getModel().getApplySerno());
        int i = m662Dao.findNotCheckedCounts(map);
        if (i > 0) {
            throw new PromptException("存在未核对的划拨明细共" + i + "条");
        }
        T250ServiceRequest t250ServiceRequest = new T250ServiceRequest();
        t250ServiceRequest.setApplySerno(params.getModel().getApplySerno());
        T250ServiceResponse t250ServiceResponse = t250ServiceDecorator.execute(t250ServiceRequest);
        String status = t250ServiceResponse.getRtnCode();
        if (status != null && !status.equals(RtnCodeStatus.RTNCODE)) {
            throw new PromptException("T250接口错误"+status+"：错误原因", t250ServiceResponse.getRtnDesc());
        }
        return RequestSupport.updateReturnJson(true, "已经划拨成功", null).toString();
    }
}
