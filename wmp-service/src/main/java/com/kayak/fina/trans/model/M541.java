package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m541Service",table="fina_cust_vol")
public class M541 {
     /**TA代码 */
    @GraphQLField(label = "TA代码", field = "tano",sql = "f1.tano = $S{tano}")
    private String tano;
    @GraphQLField(
            label = "产品代码",
            sql = "f1.prod_code = $S{prodCode}",
            field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;

    @GraphQLField(kkhtml = "KFieldDate",sql = "f1.div_date >= $S{beginDivDate}",
            label = "估值日期范围开始日", field = "begin_div_date",kkhtmlDefault=true)
    private String beginDivDate;
    @GraphQLField(kkhtml = "KFieldDate",sql = "f1.div_date <= $S{endDivDate}",
            label = "估值日期范围结束日", field = "end_div_date",kkhtmlDefault=true)
    private String endDivDate;

    @GraphQLField(label = "估值日期", field = "div_date")
    private String divDate;

    @GraphQLField(label = "分红金额", field = "total_div_amt")
    private String totalDivAmt;

    @GraphQLField(label = "每日万份收益", field = "ten_thousand_income_amt")
    private String tenThousandIncomeAmt;

    @GraphQLField(label = "七日年化收益率", field = "seven_days_income")
    private String sevenDaysIncome;

    @GraphQLField(label = "法人代码", field = "legal_code",sql = "f1.legal_code = $S{legalCode}")
    private String legalCode;

}
