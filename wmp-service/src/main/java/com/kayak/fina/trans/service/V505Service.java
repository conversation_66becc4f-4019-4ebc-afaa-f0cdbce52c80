package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.fina.trans.dao.V505Dao;
import com.kayak.fina.trans.model.V505;
import com.kayak.graphql.model.FetcherData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2021-04-14 11:11
 */
@Service
@APIDefine(desc = "客户份额实体类服务", model = V505.class)
public class V505Service {

    @Autowired
    private V505Dao v505Dao;

    @API(desc = "查询客户份额实体类信息", auth = APIAuth.YES)
    public SqlResult<SqlRow> findFinaCustVol(SqlParam<V505> params) throws Exception {
        params.setMakeSql(true);
        return v505Dao.findFinaCustVol((FetcherData<V505>) params);
    }
}
