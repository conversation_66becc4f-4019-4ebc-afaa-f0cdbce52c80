package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M536Dao;
import com.kayak.fina.trans.model.M536;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "认、申购赎回确认成功查询报表", model = M536.class)
public class M536Service {

    @Autowired
    private M536Dao m536Dao;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "认、申购赎回确认成功查询", auth = APIAuth.YES)
    public SqlResult<M536> findTransCrmlist(SqlParam<M536> params) throws Exception {
        SqlResult<M536> sqlResult = m536Dao.findTransCrmlist(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        return sqlResult;
    }
}
