package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m532Service", table = "fina_capital_transfer_info")
@Data
public class M532 {
    //@GraphQLField(kkhtml = "KFieldSelect",kkhtmlExt = "{\"data-action\":\"M902.findTano\" ,\"data-value-field\":\"tano\",\"data-display-field\":\"tano\"}",key = true ,label = "TA代码",  sql = " tano like '%$U{tano}%'",field = "tano",kkhtmlDefault = true)
    @GraphQLField(label = "TA代码",field = "tano")
    private String tano;

   /** @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "产品代码", sql = "prod_code = $S{prodCode}" ,field = "prod_code",
            kkhtmlExt="{\"data-action\":\"M902.findProdCode\" ,\"data-value-field\":\"prodCode\",\"data-display-field\":\"prodCode\"}")*/
   @GraphQLField(label = "产品代码",field = "prodCode", sql = "prod_code = $S{prodCode}")
    private String prodCode;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "业务代码", sql = "busi_code = $S{busiCode}" ,field = "busi_code",
            kkhtmlExt="{\"data-dict\": \"busi_code\"}")
    private String busiCode;


    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
            label = "应划款日期", sql = "should_transfer_date = $S{shouldTransferDate}" ,field = "should_transfer_date")
    private String shouldTransferDate;

    @GraphQLField(label = "划款金额",field = "transfer_amt")
    private BigDecimal transferAmt;

    @GraphQLField(label = "出账账号",field = "out_acct_no")
    private String outAcctNo;

    @GraphQLField(label = "入账账号",field = "in_acct_no")
    private String inAcctNo;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "划款状态",field = "transfer_status",sql = "transfer_status = $S{transferStatus}" ,
            kkhtmlExt="{\"data-dict\": \"transfer_status\"}")
    private String transferStatus;

    @GraphQLField(label = "实际划款日期",field = "actual_transfer_date")
    private String actualTransferDate;

    @GraphQLField(label = "备注",field = "remark")
    private String remark;

    @GraphQLField(label = "收付标志",field = "RECE_PAY_FLAG")
    private String recePayFlag;

}
