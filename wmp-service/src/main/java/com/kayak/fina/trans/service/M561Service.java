package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;

import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M561Dao;
import com.kayak.fina.trans.model.M561;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 客户分红明细报表
 * lsy
 */
@Service
@APIDefine(desc = "客户分红明细报表", model = M561.class)
public class M561Service {

	@Autowired
	private M561Dao m561Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "分红汇总报表", auth = APIAuth.YES)
	public SqlResult<M561> findFinaProdBasicParam(SqlParam<M561> params) throws Exception {
		SqlResult<M561> sqlResult = m561Dao.findFundBonusSum(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
