package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m531Service",table="fina_capital_transfer_info")
public class M531 {

    @GraphQLField(key = true ,label = "法人代码", sql = "t1.legal_code = $S{legalCode}" , field = "legal_code")
    private String legalCode;

    @GraphQLField(label = "TA代码", sql = "t1.tano = $S{tano}" ,field = "tano")
    private String tano;

    @GraphQLField(label = "供应商名称",field = "taName")
    private String taName;

    @GraphQLField(label = "核对标记",field = "check_flag",kkhtmlExt = "{\"data-dict\": \"check_flag\"}")
    private String checkFlag;

    @GraphQLField(label = "产品代码", sql = "t1.prod_code = $S{prodCode}" ,field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称",field = "prod_name")
    private String prodName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            key = true, label = "业务代码", sql = "t1.busi_code = $S{busiCode}", field = "busi_code",
            kkhtmlExt = "{\"data-dict\": \"m531_busi_code\"}")
    private String busiCode;

    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
            key = true , label = "应划款日期", sql = "t1.should_transfer_date = $S{shouldTransferDate}" ,field = "transfer_date")
    private String shouldTransferDate;

    @GraphQLField(label = "划款金额", field = "transfer_amt")
    private String transferAmt;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "划款状态", field = "transfer_status", sql = "t1.transfer_status = $S{transferStatus}",
            kkhtmlExt = "{\"data-dict\": \"transfer_status\"}")
    private String transferStatus;

    @GraphQLField(kkhtml = "KFieldSelect",kkhtmlDefault = true,kkhtmlExt = "{\"data-dict\": \"rece_orpay_flag\"}",label = "出入账标志", field = "rece_pay_flag",sql = "t1.rece_pay_flag = $S{recePayFlag}")
    private String recePayFlag;

    @GraphQLField(label = "转出账号", field = "out_acct_no")
    private String outAcctNo;

    @GraphQLField(label = "转入账号", field = "in_acct_no")
    private String inAcctNo;

    @GraphQLField(label = "转出账号名称", field = "out_acct_name")
    private String outAcctName;

    @GraphQLField(label = "转入账号名称",field = "in_acct_name")
    private String inAcctName;

    @GraphQLField(label = "资金类型",field = "capital_type",kkhtmlExt = "{\"data-dict\": \"capital_type\"}")
    private String capitalType;

    @GraphQLField(label = "确认日期",field = "ack_date")
    private String ackDate;

}
