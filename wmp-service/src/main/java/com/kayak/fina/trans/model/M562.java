package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 资金补发信息报表
 * xsh
 */
@GraphQLModel(fetcher = "m562Service",table = "fina_pay_again_log",firstLineNotDesensitized = true )
@Data
public class M562 {

/*
    @GraphQLField(label = "申请流水号", field = "app_serno")
    private String appSerno;

    @GraphQLField(label = "原资金流水号", field = "ori_capital_serno")
    private String oriCapitalSerno;

    @GraphQLField(label = "原确认流水号", field = "ori_cfm_serno")
    private String oriCfmSerno;

    @GraphQLField(label = "客户号", field = "cust_no")
    private String custNo;
*/

    @GraphQLField(label = "TA代码",field = "tano",sql = "pbcl.tano = $S{tano}")
    private String tano;

    @GraphQLField(label = "产品代码" , field = "prod_code", sql = "pbcl.prod_code = $S{prodCode}")
    private String prodCode;


    @GraphQLField(label = "业务代码", field = "busi_code")
    private String busiCode;

    @GraphQLField(label = "交易确认流水号", field = "cfm_serno")
    private String cfmSerno;

    @GraphQLField(kkhtml = "KFieldDate",  label = "交易日期", sql = "date_format(fpal.create_time,'%Y%m%d') >= $S{transDate}" ,
            field = "transDate", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'ackEndDate'}",kkhtmlDefault=true)
    private String transDate;
    @GraphQLField(label = "结束日期", sql = "date_format(fpal.create_time,'%Y%m%d') <= $S{transEndDate}", field = "trans_date")
    private String transEndDate;
    @GraphQLField(label = "交易机构", field = "orgno" , sql = "pbcl.orgno = $S{orgno}")
    private String orgno;
    @GraphQLField(label = "应发金额", field = "should_send_amount")
    private String shouldSendAmount;
    @GraphQLField(label = "原下发金额", field = "ori_send_amount")
    private String oriSendAmount;
    @GraphQLField(label = "差额金额", field = "diff_amount")
    private String diffAmount;








}