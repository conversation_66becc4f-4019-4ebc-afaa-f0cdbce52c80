package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m522Service",table = "fina_cust_vol_detail",firstLineNotDesensitized = true)
public class M522 {

    @GraphQLField(key = true , label = "份额明细流水号", sql = "vol_trans_serno = $S{volTransSerno}" ,field = "vol_trans_serno")
    private String volTransSerno;
    @GraphQLField(label = "TA代码", sql = "tano = $S{tano}" ,field = "tano")
    private String tano;
    @GraphQLField( label = "产品代码", sql = "prod_code = $S{prodCode}" ,field = "prod_code")
    private String prodCode;
    @GraphQLField(label = "法人代码", sql = "(legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode})",field = "legal_code")
    private String legalCode;
    @GraphQLField( label = "TA确认交易流水号", sql = "ta_ack_serno = $S{taAckSerno}" ,field = "ta_ack_serno")
    private String taAckSerno;
    @GraphQLField( label = "总份额", sql = "total_vol = $S{totalVol}" ,field = "total_vol")
    private String totalVol;
    @GraphQLField( label = "可用份额", sql = "available_vol = $S{availableVol}" ,field = "available_vol")
    private String availableVol;
    @GraphQLField( label = "赎回冻结份额", sql = "redeem_frozen_vol = $S{redeemFrozenVol}" ,field = "redeem_frozen_vol")
    private String redeemFrozenVol;
    @GraphQLField( label = "质押冻结份额", sql = "abn_frozen_vol = $S{abnFrozenVol}" ,field = "abn_frozen_vol")
    private String abnFrozenVol;
    @GraphQLField(label = "司法冻结份额", sql = "elisor_frozen_vol = $S{elisorFrozenVol}" ,field = "elisor_frozen_vol")
    private String elisorFrozenVol;
    @GraphQLField( label = "转让冻结份额", sql = "transfer_frozen_vol = $S{transferFrozenVol}" ,field = "transfer_frozen_vol")
    private String transferFrozenVol;

    @GraphQLField(key = true, kkhtmlDefault = true, kkhtml = "KFieldDate", label = "份额确认日期", sql = "ack_date >= $S{ackDate}", field = "ack_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'EndAckDate','dataAllowblank':false}")
    private String ackDate;
    @GraphQLField(label = "份额确认日期", sql = "ack_date <= $S{EndAckDate}", field = "ack_date" )
    private String EndAckDate;

    @GraphQLField( label = "TA账号", sql = "ta_acct_no = $S{taAcctNo}" ,field = "ta_acct_no")
    private String taAcctNo;
    @GraphQLField( label = "交易账号", sql = "trans_acct_no = $S{transAcctNo}" ,field = "trans_acct_no"  )
    private String transAcctNo;
    @GraphQLField( label = "交易总行代码", sql = "bank_code = $S{bankCode}" ,field = "bank_code")
    private String bankCode;
    @GraphQLField( label = "交易分行代码", sql = "branch_code = $S{branchCode}" ,field = "branch_code")
    private String branchCode;
    @GraphQLField(label = "支行网点代码", sql = "sub_branch_code = $S{subBranchCode}" ,field = "sub_branch_code")
    private String subBranchCode;
    @GraphQLField( label = "交易机构", sql = "trans_orgno = $S{transOrgno}" ,field = "trans_orgno")
    private String transOrgno;
    @GraphQLField( label = "交易机构名称",  field = "org_name")
    private String orgName;
    @GraphQLField(label = "交易后端收费总额", sql = "total_fee_back = $S{totalFeeBack}" ,field = "total_fee_back")
    private String totalFeeBack;
    @GraphQLField( label = "收费方式", sql = "shareclass = $S{shareclass}" ,field = "shareclass")
    private String shareclass;
    @GraphQLField(label = "明细标志", sql = "detail_flag = $S{detailFlag}" ,field = "detail_flag")
    private String detailFlag;
    @GraphQLField(label = "份额注册日期", sql = "register_date = $S{registerDate}" ,field = "register_date")
    private String registerDate;
    @GraphQLField( label = "未结转收益", sql = "unconvert_income = $S{unconvertIncome}" ,field = "unconvert_income")
    private String unconvertIncome;
    @GraphQLField( label = "份额原始来源", sql = "source_type = $S{sourceType}" ,field = "source_type")
    private String sourceType;
    @GraphQLField( label = "可赎回日期", sql = "can_redeem_date = $S{canRedeemDate}" ,field = "can_redeem_date")
    private String canRedeemDate;
    @GraphQLField(label = "份额最后变动日", sql = "upd_time = $S{updTime}" ,field = "upd_time")
    private String updTime;
    @GraphQLField( label = "周期到期处理方式", sql = "cycle_expire = $S{cycleExpire}" ,field = "cycle_expire")
    private String cycleExpire;

    @GraphQLField(kkhtml = "KFieldText", label = "客户号", field = "cust_no")
    private String custNo;
    @GraphQLField( label = "客户名称",  field = "cust_name")
    private String custName;
    @GraphQLField( label = "客户类型", field = "cust_type")
    private String custType;
    @GraphQLField( label = "资金账号", field = "acct_no", desensitized = Blankcarddesensitized.class)
    private String acctNo;
    @GraphQLField( label = "TA名称", field = "ta_name")
    private String taName;
    @GraphQLField( label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField( label = "冻结总份数", field = "total_frozen_vol")
    private String totalFrozenVol;
    @GraphQLField( label = "总成本", field = "total_cost")
    private String totalCost;
    @GraphQLField( label = "份额最后变动日", field = "update_time")
    private String updateTime;
    @GraphQLField( label = "非交易过户冻结份额", field = "notrans_frozen_vol")
    private String notransFrozenVol;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}