package com.kayak.fina.trans.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m534Service", table = "fina_cust_trans_cfm_log",firstLineNotDesensitized = true)
@Data
public class M534 {
    @GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true,
            label = "客户名称", sql = "cust_name like '%$U{custName}%'" ,field = "cust_name")
    private String custName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "证件类型", sql = "id_type = $S{idType}" ,field = "id_type",
            kkhtmlExt="{\"data-dict\": \"id_type\"}")
    private String idType;

    @GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true,
            label = "证件号码", sql = "id_code = $S{idCode}" ,field = "id_code")
    private String idCode;

    @GraphQLField(
            label = "产品代码",
            sql = "prod_code = $S{prodCode}",
            field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "TA代码", sql = "tano = $S{tano}", field = "tano")
    private String tano;

    @GraphQLField(label = "账号",field = "trans_acct_no", sql = "trans_acct_no = $S{transAcctNo}")
    private String transAcctNo;

    @GraphQLField(label = "客户本金",field = "ack_amt")
    private BigDecimal ackAmt;

    @GraphQLField(label = "收益",field = "ack_inc")
    private BigDecimal ackInc;

    @GraphQLField(label = "合计",field = "ack_count")
    private BigDecimal ackCount;

}
