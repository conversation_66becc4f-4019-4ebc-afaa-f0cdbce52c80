package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;


@GraphQLModel(fetcher = "m533Service", table = "fina_cust_trans_cfm_log")
@Data
public class M533 {


    @GraphQLField(key = true ,kkhtmlDefault=true, kkhtml = "KFieldDate", label = "产品兑付日", sql = "t.ack_date >= $S{ackDate}", field = "ack_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'ackEndDate','dataAllowblank':false}")
    private String ackDate;
    @GraphQLField(label = "结束日期", sql = "t.ack_date <= $S{ackEndDate}", field = "ack_date")
    private String ackEndDate;

    @GraphQLField(label = "产品代码", field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;

    @GraphQLField(label = "TA代码", field = "tano")
    private String tano;
    @GraphQLField(label = "TA名称", field = "ta_name")
    private String taName;
    @GraphQLField(label = "应付本金",field = "should_amt")
    private BigDecimal shouldAmt;

    @GraphQLField(label = "实付本金",field = "ack_amt")
    private BigDecimal ackAmt;

    @GraphQLField(label = "应付收益",field = "should_inc")
    private BigDecimal shouldInc;

    @GraphQLField(label = "实付收益",field = "ack_inc")
    private BigDecimal ackInc;

    @GraphQLField(label = "应付客户数",field = "should_cust_num")
    private Long shouldCustNum;

    @GraphQLField(label = "实付客户数",field = "ack_cust_num")
    private Long ackCustNum;

    public String getAckDate() {
        return ackDate;
    }
}
