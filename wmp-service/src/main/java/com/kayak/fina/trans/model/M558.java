package com.kayak.fina.trans.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@GraphQLModel(fetcher = "m558Service")
public class M558 implements Cloneable{


    @GraphQLField(label = "TA代码",field = "tano")
    private String tano;

    @GraphQLField(label = "客户号",field = "cust_no")
    private String custNo;

    @GraphQLField(label = "账号",field = "acct_no")
    private String acctNo;

    @GraphQLField(label = "币种",field = "cur")
    private String cur;

    @GraphQLField(label = "核心产品类型",field = "prod_type")
    private String prodType;

    @GraphQLField(label = "账号序号", field = "acct_num")
    private String acctNum;

    @GraphQLField(label = "经办人姓名",field = "agent_name")
    private String agentName;

    @GraphQLField(label = "经办人证件类型",field = "agent_id_type")
    private String agentIdType;

    @GraphQLField(label = "经办人证件号码",field = "agent_id_code")
    private String agentIdCode;

    @GraphQLField(label = "手机号码",field = "phone")
    private String phone;

    @GraphQLField(label = "交易账号",field = "trans_acct_no")
    private String transAcctNo;

    @GraphQLField(label = "开户机构",field = "orgno")
    private String orgno;

    @GraphQLField(label = "客户类型",field = "cust_type")
    private String custType;

    @GraphQLField(label = "客户名称",field = "cust_name")
    private String custName;

    @GraphQLField(label = "证件号码",field = "id_code")
    private String idCode;

    @GraphQLField(label = "证件类型",field = "id_type")
    private String idType;

    @GraphQLField(label = "法人名称",field = "instrepr_name")
    private String instreprName;

    @GraphQLField(label = "法人证件号码",field = "instrepr_id_code")
    private String instreprIdCode;

    @GraphQLField(label = "法人证件类型",field = "instrepr_id_type")
    private String instreprIdType;

}