package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M541Dao;
import com.kayak.fina.trans.model.M541;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.Map;

@Service
@APIDefine(desc = "理财分销分红查询报表", model = M541.class)
public class M541Service {
    @Autowired
    private M541Dao m541Dao;
    @Autowired
    private ReportformUtil reportformUtil;
    @Autowired
    private M215Service m215Service;

    @API(desc = "分红查询", auth = APIAuth.YES)
    public SqlResult<M541> findFinaProDivInfo(SqlParam<M541> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M541> sqlResult = m541Dao.findFinaProDivInfo(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        if(sqlResult != null && sqlResult.getRows() != null && sqlResult.getRows().size() > 0){
            for(M541 m541:sqlResult.getRows()){
                Map<String, String> prodInfoMap = m215Service.getProdInfo(m541.getLegalCode(), SystemNo.FINA,m541.getTano(),m541.getProdCode());
                DecimalFormat decimalFormat1 = new DecimalFormat("0.00%");
                DecimalFormat decimalFormat2 = new DecimalFormat("0.0000");
                DecimalFormat decimalFormat3 = new DecimalFormat("0.00");
                String sevenDaysIncome = m541.getSevenDaysIncome();
                String tenThousandIncomeAmt = m541.getTenThousandIncomeAmt();
                String totalDivAmt = m541.getTotalDivAmt();
                if (StringUtils.isNotBlank(sevenDaysIncome)) {
                    m541.setSevenDaysIncome(decimalFormat1.format(Double.parseDouble(sevenDaysIncome)));
                }
                if (StringUtils.isNotBlank(tenThousandIncomeAmt)) {
                    m541.setTenThousandIncomeAmt(decimalFormat2.format(Double.parseDouble(sevenDaysIncome)));
                }
                if (StringUtils.isNotBlank(totalDivAmt)) {
                    m541.setTotalDivAmt(decimalFormat3.format(Double.parseDouble(totalDivAmt)));
                }
                if(prodInfoMap != null && prodInfoMap.size() > 0){
                    m541.setProdName(prodInfoMap.get("prod_name"));
                }
            }
        }
        return sqlResult;
    }
}
