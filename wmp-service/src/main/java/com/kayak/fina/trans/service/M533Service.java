package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.cache.util.RedisUtils;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M533Dao;
import com.kayak.fina.trans.model.M533;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "理财产品兑付", model = M533.class)
public class M533Service {
    @Autowired
    private M533Dao m533Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @Autowired
    private RedisUtils redisUtils;



    @API(desc = "查询理财产品兑付信息", auth = APIAuth.YES)
    public SqlResult<M533> findM533s(SqlParam<M533> params) throws Exception {
        SqlResult<M533> sqlResult = m533Dao.findM533s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        //for (M533 row : sqlResult.getRows()) {
        //    redisUtils.hmget(SystemNo.FINA+row.getTano()+row.getProdCode()+"1000")
        //}

        /**if(sqlResult != null && sqlResult.getRows() != null && sqlResult.getRows().size() > 0){
            for(M533 m533:sqlResult.getRows()){
                m533.setTaName(reportformUtil.getFinaTaName(m533.getTano()));
            }
        }*/
        return sqlResult;
    }
}
