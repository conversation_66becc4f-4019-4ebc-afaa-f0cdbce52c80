package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M111Dao;
import com.kayak.cust.model.M101;
import com.kayak.cust.model.M111;
import com.kayak.fina.trans.dao.M540Dao;
import com.kayak.fina.trans.model.M540;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "查询客户交易流水", model = M540.class)
public class M540Service {
    @Autowired
    private M540Dao m540Dao;

    @Autowired
    private M111Dao m111Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;


    @API(desc = "查询客户交易流水", auth = APIAuth.YES)
    public SqlResult<M540> findCustTrans(SqlParam<M540> params) throws Exception {
        SqlResult<M540> sqlResult = m540Dao.findCustTrans(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());

        if(sqlResult.getRows() != null && sqlResult.getRows().size() >0){
            sqlResult.setRows(sqlResult.getRows().stream().map(item->{
                try{
                    item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
                    Map<String,Object> map = new HashMap<>();
                    map.put("systemNo","FINA");
                    map.put("prodCode",item.getProdCode());
                    List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(map, M215.class));
                    if (prodParaList != null && prodParaList.size() > 0){
                        Map<String, String> prodPara = prodParaList.get(0);
                        item.setBenchmarks(prodPara.get("benchmarks"));
                        item.setCycleDays(prodPara.get("cycle_days"));
                    }
                    M111 m111Param = new M111();
                    m111Param.setCustManager(item.getCustManager());
                    M111 m111 = m111Dao.getManagerInfos(m111Param);
                    if(m111 != null){
                        item.setCustManagerOrg(m111.getTransOrgno());
                    }
                    M001 m001 = m001Dao.get(item.getTransOrgno());
                    if (m001 != null){
                        item.setOrgName(m001.getOrgname());
                    }

                    M540 cust = m540Dao.getCusts(item.getCustNo());
                    if (cust != null){
                        item.setMobile(cust.getMobile());
                    }
                } catch (Exception e) {
                    try {
                        throw new PromptException("M540错误", "错误信息："+e.getMessage());
                    } catch (PromptException promptException) {
                        promptException.printStackTrace();
                    }
                    //throw new RuntimeException("M933错误："+e.getMessage());
                }
                return item;
            }).collect(Collectors.toList()));
        }
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
