package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.trans.dao.M530Dao;
import com.kayak.fina.trans.model.M530;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 资金延后处理服务类
 *
 * <AUTHOR>
 * @date 2021-04-28 10:05
 */
@Service
@APIDefine(desc = "资金延后处理服务", model = M530.class)
public class M530Service {

    @Autowired
    private M530Dao m530DaoDao;

    @API(desc = "查询资金延后处理信息", auth = APIAuth.YES)
    public SqlResult<M530> findAll(SqlParam<M530> params) throws Exception {
        params.setMakeSql(true);
        return m530DaoDao.findAll(params);
    }

    @API(desc = "资金延后处理", auth = APIAuth.NO)
    public int updateM530(SqlParam<M530> params) throws Exception {
        return m530DaoDao.updateM530(params).getEffect();
    }
}
