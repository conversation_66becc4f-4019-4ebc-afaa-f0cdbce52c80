package com.kayak.fina.trans.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.fina.trans.dao.M534Dao;
import com.kayak.fina.trans.model.M534;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "理财产品兑付明细", model = M534.class)
public class M534Service {
    @Autowired
    private M534Dao m534Dao;

    @Autowired
    private ReportformUtil reportformUtil;



    @API(desc = "查询理财产品兑付明细信息", auth = APIAuth.YES)
    public SqlResult<M534> findM534s(SqlParam<M534> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M534> sqlResult = m534Dao.findM534s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
