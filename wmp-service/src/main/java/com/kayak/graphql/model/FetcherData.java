package com.kayak.graphql.model;

import java.util.HashMap;
import java.util.Map;

/**
 * GraphQL获取器数据模型
 */
public class FetcherData<T> {

    private String id;
    private String name;
    private String type;
    private Map<String, Object> data;
    private T model;
    private boolean makeSql;

    public FetcherData() {
        this.data = new HashMap<>();
    }

    public FetcherData(String id, String name, String type) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.data = new HashMap<>();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public void addData(String key, Object value) {
        this.data.put(key, value);
    }

    public Object getData(String key) {
        return this.data.get(key);
    }

    public T getModel() {
        return model;
    }

    public void setModel(T model) {
        this.model = model;
    }

    public boolean isMakeSql() {
        return makeSql;
    }

    public void setMakeSql(boolean makeSql) {
        this.makeSql = makeSql;
    }

    @Override
    public String toString() {
        return "FetcherData{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", data=" + data +
                '}';
    }
}
