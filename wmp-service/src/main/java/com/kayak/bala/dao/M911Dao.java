package com.kayak.bala.dao;

import com.kayak.bala.model.M911;
import com.kayak.bala.model.M911DaliyVol;
import com.kayak.bala.model.M911List;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.graphql.annotation.GraphQLField;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public class M911Dao extends ComnDao {
    /**
     * 查询分支机构划拨
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M911> findM911s(SqlParam<M911> params) throws Exception {
        return super.findRows("SELECT * FROM bala_transfer_org", SubDatabase.DATABASE_BALA_CENTER, params);
    }

    public SqlResult<M911> findTano(SqlParam<M911> params) throws Exception {
        return super.findRows("SELECT distinct tano FROM bala_org_daliy_vol", SubDatabase.DATABASE_BALA_CENTER, params);
    }

    public SqlResult<M911> findProdCode(SqlParam<M911> params) throws Exception {
        return super.findRows("SELECT distinct prod_code FROM bala_org_daliy_vol", SubDatabase.DATABASE_BALA_CENTER, params);
    }

    /**
     * 查询分支机构划拨分配
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M911List> findM911Lists(SqlParam<M911> params) throws Exception {
        String sql = "SELECT tano,prod_code,prod_name,orgname,bank_code,BRANCH_CODE,SUB_BRANCH_CODE,TRANS_ORGNO,start_date," +
                "end_date,transfer_status,transfer_date,earning_amt,daliy_vol," +
                "transfer_serno,system_no,create_time,update_time FROM bala_transfer_org_list \n"+
                "where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode} and start_date = $S{startDate} and end_date = $S{endDate}";
        List<M911List> volList =super.findRows(M911List.class,sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel());
        SqlResult<M911List> sqlRowSqlResult = new SqlResult<>();
        sqlRowSqlResult.setResults(volList.size());
        sqlRowSqlResult.setRows(volList);
        sqlRowSqlResult.setDesensitized(false);
        return sqlRowSqlResult;
    }


    /**
     * 查询分支机构日存量
     * @param params
     * @return
     * @throws Exception
     */
    public List<M911DaliyVol> findM911DaliyVols(SqlParam<M911> params) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT distinct system_no,tano,prod_code,bank_code,BRANCH_CODE,SUB_BRANCH_CODE,TRANS_ORGNO,\n" +
                "  sum(daliy_vol) as daliy_vol\n"+
                "  from bala_org_daliy_vol \n" +
                " where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode}\n"+
                " and stat_date >= $S{startDate} and stat_date <= $S{endDate}\n"+
                " group by system_no,tano,prod_code,bank_code,BRANCH_CODE,SUB_BRANCH_CODE,TRANS_ORGNO order by daliy_vol desc,TRANS_ORGNO");
        return super.findRows(M911DaliyVol.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params.getModel());
    }

    public M911List sumDaliyVol(SqlParam<M911> params) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT sum(daliy_vol) as daliy_vol\n"+
                "  from bala_org_daliy_vol \n" +
                " where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode}\n"+
                " and stat_date >= $S{startDate} and stat_date <= $S{endDate}\n");
        return super.findRow(M911List.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params.getModel());
    }


    public int addM911(SqlParam<M911> params) throws Exception {
        String sql = "INSERT INTO bala_transfer_org" +
                " (system_no, tano, prod_code, prod_name, start_date,end_date,transfer_status,total_earning,transfer_serno,create_time,update_time) "+
                "VALUES ($S{systemNo},$S{tano},$S{prodCode},$S{prodName},$S{startDate},$S{endDate},$S{transferStatus},$S{totalEarning},$S{transferSerno},sysdate,sysdate)";
        return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel()).getEffect();
    }

    public int addM911List(M911List params) throws Exception {
        String sql = "INSERT INTO bala_transfer_org_list" +
                " (system_no, tano, prod_code, prod_name,orgname,start_date,end_date,transfer_status,earning_amt,daliy_vol,transfer_serno,create_time,update_time,TRANS_ORGNO,bank_code,BRANCH_CODE,SUB_BRANCH_CODE) "+
                "VALUES ($S{systemNo},$S{tano},$S{prodCode},$S{prodName},$S{orgname},$S{startDate},$S{endDate},$S{transferStatus},$S{earningAmt},$S{daliyVol},$S{transferSerno},sysdate,sysdate,$S{transOrgno},$S{bankCode},$S{branchCode},$S{subBranchCode})";
        return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params).getEffect();
    }

    public int updateM911(SqlParam<M911> params) throws Exception {
        StringBuffer sql = new StringBuffer(" UPDATE bala_transfer_org SET ");
        if(Tools.isNotBlank(params.getModel().getTransferStatus())){
            sql.append(" transfer_status = $S{transferStatus},");
        }
        if(params.getModel().getTotalEarning() != null){
            sql.append("  total_earning = $S{totalEarning},");
        }
        if(Tools.isNotBlank(params.getModel().getTransferDate())){
            sql.append(" transfer_date = $S{transferDate},");
        }
        String updateSql = sql.toString().substring(0,sql.toString().length()-1)+"  where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode} and start_date = $S{startDate} and end_date = $S{endDate} " ;
        return super.update(updateSql, SubDatabase.DATABASE_BALA_CENTER, params.getModel()).getEffect();
    }

    public int updateM911List(M911List params) throws Exception {
        String sql = "UPDATE bala_transfer_org_list SET " +
                " transfer_status = $S{transferStatus}, transfer_date = $S{transferDate}"+
                " where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode} and start_date = $S{startDate} and end_date = $S{endDate} ";
        return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params).getEffect();
    }

    public void deleteM911(SqlParam<M911> params) throws Exception {
        doTrans(() -> {
            String sql = "DELETE FROM bala_transfer_org " +
                    " where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode} and start_date = $S{startDate} and end_date = $S{endDate} ";
            super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel()).getEffect();
            String sql1 = "DELETE FROM bala_transfer_org " +
                    " where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode} and start_date = $S{startDate} and end_date = $S{endDate} ";
            super.update(sql1, SubDatabase.DATABASE_BALA_CENTER, params.getModel()).getEffect();
        });

    }

    public int deleteM911List(SqlParam<M911List> params) throws Exception {
        String sql = "DELETE FROM bala_transfer_org " +
                " where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode} and start_date = $S{startDate} and end_date = $S{endDate} ";
        return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel()).getEffect();
    }

    public M911 getM911(SqlParam<M911> params) throws Exception {
        return super.findRow(M911.class,"SELECT * FROM bala_transfer_org where system_no = $S{systemNo} and tano = $S{tano} and prod_code = $S{prodCode} and start_date = $S{startDate} and end_date = $S{endDate} ", SubDatabase.DATABASE_BALA_CENTER, params.getModel());
    }

}
