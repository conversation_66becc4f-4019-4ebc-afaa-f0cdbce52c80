package com.kayak.bala.dao;

import com.kayak.bala.model.M901;
import com.kayak.bala.model.M906;
import com.kayak.bala.model.M945;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 封闭期交易控制标志
 * qpc
 * 2022/10/28
 */
@Repository
public class M945Dao extends ComnDao {

	/**
	 * 查询封闭期交易控制标志
	 * qpc
	 * 2022/10/31
	 */
	public SqlResult<M945> selectBalaCloseTranControl(SqlParam<M945> params) throws Exception {
		params.setMakeSql(false);
		return super.findRows(
				"SELECT paraid , dict, paravalue FROM sys_param WHERE paraid ='a0060001'  or paraid = 'a0060002'"
				, SubDatabase.DATABASE_BALA_CENTER,params);
	}
	/**
	 * 更新封闭期交易控制标志
	 * qpc
	 * 2022/11/04
	 */
	public UpdateResult updateBalaCloseTranControl(String var) throws Exception {
		return super.update(
				"update fdsbala.sys_param set paravalue = '"+var+"' WHERE paraid ='a0060001'"
				, SubDatabase.DATABASE_BALA_CENTER);
	}

	/**
	 * 查询封闭期交易控制标志
	 * qpc
	 * 2022/10/31
	 */
	public SqlResult<M945> selectTranControlOrRecoFlag(SqlParam<M945> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows(
				"SELECT paraid , dict, paravalue FROM sys_param WHERE paraid = $S{paraid} "
				, SubDatabase.DATABASE_BALA_CENTER,params);
	}

	/**
	 * 更新认购期标志标志
	 * qpc
	 * 2022/12/01
	 */
	public UpdateResult updateTranControlOrRecoFlag(String var,String vart) throws Exception {
		return super.update(
				"update fdsbala.sys_param set paravalue = '"+var+"' WHERE paraid ='"+vart+"'"
				, SubDatabase.DATABASE_BALA_CENTER);
	}

}