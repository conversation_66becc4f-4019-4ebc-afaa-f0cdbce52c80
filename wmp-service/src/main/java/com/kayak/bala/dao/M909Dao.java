package com.kayak.bala.dao;
import com.kayak.bala.model.M909;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public class M909Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M909> findM909s(SqlParam<M909> params) throws Exception{

        String sql ="select t.cust_no,\n" +
                "               t.cust_name,\n" +
                "               t.id_type,\n" +
                "               t.id_code,\n" +
                "               t.ACCT_NO,\n" +
                "               t.ACCT_NO as query_acct_no,\n" +
                "               t.branch_code,\n" +
                "               t.sub_branch_code,\n" +
                "               t.ACK_DATE,\n" +
                "               sum(t.TOTAL_AMT) as TOTAL_AMT,\n" +
                "               sum(t.TOTAL_AUTO_AMT) as TOTAL_AUTO_AMT,\n" +
                "               sum(t.TOTAL_AMT + t.TOTAL_AUTO_AMT) as tot_in_amt,\n" +
                "               sum(t.TOTAL_OUT_AMT) as TOTAL_OUT_AMT,\n" +
                "               sum(t.TOTAL_REDEEM_AMT) as TOTAL_REDEEM_AMT,\n" +
                "               sum(t.TOTAL_OUT_AMT + t.TOTAL_REDEEM_AMT) as tot_out_amt\n" +
                "          from bala_cust_trans_stat t where 1=1 ";
        //sql = sql+reportformUtil.getOrgIdForOrgLevel("t.");
        if (StringUtils.isNotBlank(params.getModel().getSubBranchCode())){
            String [] orgnos = params.getModel().getSubBranchCode().split(",");
            String inStr = " and t.sub_branch_code in (";
            for (int i = 0; i < orgnos.length; i++){
                if (i == orgnos.length - 1){
                    inStr += "'"+orgnos[i]+"')";
                }else {
                    inStr += "'"+orgnos[i]+"',";
                }
            }
            sql += inStr;
        }

        params.getModel().setSubBranchCode(null);
        params.setMakeSql(true);
        sql +=  "        group by t.cust_no,\n" +
                "               t.cust_name,\n" +
                "               t.id_type,\n" +
                "               t.id_code,\n" +
                "               t.ACCT_NO,\n" +
                "               t.branch_code,\n" +
                "               t.sub_branch_code,\n" +
                "               t.ACK_DATE order by t.ack_date desc ";
        return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
    }

    public String getReferrer(String custNo) throws Exception{
        String sql = "select referrer from bala_cust_sign where cust_no = '" + custNo + "'";
        return super.findRow(String.class,sql,SubDatabase.DATABASE_BALA_CENTER,null);
    }
}
