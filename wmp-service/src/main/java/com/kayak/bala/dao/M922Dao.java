// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M922;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.sql.UpdateResult;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// import java.util.Map;
//
// /**
//  * 源泉宝签约关系
//  */
// @Repository
// public class M922Dao extends ComnDao {
// 	@Autowired
// 	private ReportformUtil reportformUtil;
// 	/**
// 	 * 查询源泉宝签约关系
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M922> findBalaCustSign(SqlParam<M922> params) throws Exception {
// 		params.setMakeSql(true);
// 		return super.findRows("\n" +
// 				"select t.cust_no,\n" +
// 				" t.acct_no,\n" +
// 				" t.trans_acct_no,\n" +
// 				" t.cust_name,\n" +
// 				" t.id_type,\n" +
// 				" t.id_code,\n" +
// 				" t.sign_status,\n" +
// 				" t.sign_date,\n" +
// 				" t.cancel_date,\n" +
// 				" t.branch_code,\n" +
// 				" t.trans_orgno,\n" +
// 				" t.referrer,\n" +
// 				" t.auto_status,\n" +
// 				" t.auto_type,\n" +
// 				" t.auto_amt,\n" +
// 				" t.auto_sign_date,\n" +
// 				" t.auto_cancel_date,\n" +
// 				" t.create_time,\n" +
// 				" t.update_time\n" +
// 				"from bala_cust_sign t where 1=1 order by t.sign_date desc,t.cancel_date desc ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
// 	/*public SqlResult<M922> findBalaCustSign(SqlParam<M922> params) throws Exception {
// 		params.setMakeSql(true);
// 		return super.findRows("\n" +
// 				"select t.cust_no,\n" +
// 				" t.acct_no,\n" +
// 				" t.trans_acct_no,\n" +
// 				" t.cust_name,\n" +
// 				" t.id_type,\n" +
// 				" t.id_code,\n" +
// 				" t.sign_status,\n" +
// 				" t.sign_date,\n" +
// 				" t.cancel_date,\n" +
// 				" t.branch_code,\n" +
// 				" t.trans_orgno,\n" +
// 				" t.referrer,\n" +
// 				" t.auto_status,\n" +
// 				" t.auto_type,\n" +
// 				" t.auto_amt,\n" +
// 				" t.auto_sign_date,\n" +
// 				" t.auto_cancel_date,\n" +
// 				" t.create_time,\n" +
// 				" t.update_time\n" +
// 				"from bala_cust_sign t where 1=1 "+ reportformUtil.getOrgIdForOrgLevel("t.")+" order by t.sign_date desc,t.cancel_date desc ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}*/
//
// 	/**
// 	 * <AUTHOR>
// 	 * @Description 客户源泉宝签约关系表，所属机构改为合并机构
// 	 * @Date 2022/3/24
// 	 * @Param [params]
// 	 * @return com.kayak.core.sql.UpdateResult
// 	 **/
// 	public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
// 		return super.update("UPDATE bala_cust_sign SET TRANS_ORGNO = $S{mergeOrgno} WHERE TRANS_ORGNO = $S{removeOrgno} ",
// 				SubDatabase.DATABASE_BALA_CENTER,params);
// 	}
//
// }