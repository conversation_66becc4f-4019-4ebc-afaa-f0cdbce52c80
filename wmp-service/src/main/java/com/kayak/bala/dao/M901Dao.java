// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M901;
// import com.kayak.bala.model.M902;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import org.springframework.stereotype.Repository;
//
// /**
//  * 源泉宝产品基本信息
//  */
// @Repository
// public class M901Dao extends ComnDao {
//
// 	/**
// 	 * 查询源泉宝基本信息
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M901> findBalaProdInfo(SqlParam<M901> params) throws Exception {
// 		params.setMakeSql(true);
// 		return super.findRows("SELECT " +
// 				"bala_code, " +
// 				"bala_name, " +
// 				"trans_channel, " +
// 				"trans_org, " +
// 				"prod_risk_level, " +
// 				"workday_pgm , " +
// 				"pay_acct," +
// 				" ( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = pay_acct) pay_acct_name," +
// 				" collect_acct ," +
// 				" ( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = collect_acct) collect_acct_name," +
// 				" income_acct," +
// 				" ( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = income_acct) income_acct_name," +
// 				" advance_acct," +
// 				" ( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = advance_acct) advance_acct_name " +
// 				"FROM BALA_PROD_INFO where 1=1 ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
// 	/**
// 	 * 更新源泉宝基本信息
// 	 * @param params
// 	 * @throws Exception
// 	 */
// 	public void update(SqlParam<M901> params) throws Exception {
// 		M901 newM901 = params.getModel();
// 		doTrans(() -> {
// 			super.update("UPDATE BALA_PROD_INFO SET bala_name = $S{balaName},trans_channel = $S{transChannel}, trans_org = $S{transOrg},prod_risk_level = $S{prodRiskLevel}, pay_acct = $S{payAcct},workday_pgm = $S{workdayPgm}," +
// 					"collect_acct = $S{collectAcct},income_acct = $S{incomeAcct} , advance_acct = $S{advanceAcct} " +
// 					"WHERE bala_code = $S{balaCode}", SubDatabase.DATABASE_BALA_CENTER, newM901);
// 		});
//
// 	}
//
// 	public SqlResult<M901> findBankInfo(SqlParam<M901> params) throws Exception {
// 		return super.findRows("SELECT ACCT_NAME,ACCT_SERNO FROM BALA_ACCT_INFO WHERE ACCT_TYPE = $S{acctType}", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
// 	public SqlResult<M901> findBalaProdInfoList(SqlParam<M901> params) throws Exception {
// 		return super.findRows("select distinct bala_code,bala_name from bala_prod_info ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
//
// 	/**
// 	 * <AUTHOR>
// 	 * @Description 根据balaCode更改风险等级
// 	 * @Date 2022/2/9
// 	 * @Param [params]
// 	 * @return void
// 	 **/
// 	public void updateProdRiskLevel(SqlParam<M901> params) throws Exception {
// 		M901 newM901 = params.getModel();
// 		doTrans(() -> {
// 			super.update("UPDATE BALA_PROD_INFO SET prod_risk_level = $S{prodRiskLevel} " +
// 					"WHERE bala_code = $S{balaCode}", SubDatabase.DATABASE_BALA_CENTER, newM901);
// 		});
//
// 	}
//
// 	/**
// 	 * <AUTHOR>
// 	 * @Description 查询余额产品代码信息
// 	 * @Date 2022/2/22
// 	 * @Param [params]
// 	 * @return com.kayak.core.sql.SqlResult<com.kayak.bala.model.M902>
// 	 **/
// 	public SqlResult<M901> findBaProdCode(SqlParam<M901> params) throws Exception {
//
// 		StringBuffer sb=new StringBuffer("SELECT DISTINCT BALA_CODE,BALA_NAME FROM BALA_PROD_INFO  where 1=1 ");
// 		if(Tools.isNotBlank(params.getModel().getBalaCode())){
// 			sb.append(" and ( BALA_CODE like '"+params.getModel().getBalaCode()+"%' or  BALA_NAME like '%"+params.getModel().getBalaCode()+"%' ) ");
// 		}
// 		params.getModel().setBalaCode(null);
// 		SqlResult<M901> sqlResult=super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
//
// 		return sqlResult;
// 	}
//
// }