// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M925;
// import com.kayak.bala.model.M933;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import com.kayak.until.MakeSqlUntil;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// /**
//  * 底层交易确认流水
//  */
// @Repository
// public class M933Dao extends ComnDao {
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
// 	/**
// 	 * 底层交易确认流水查询
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M933> findBalaCustTransCfm(SqlParam<M933> params) throws Exception {
// 		//params.setMakeSql(true);
// 		String custNo = params.getModel().getCustNo();
// 		String transOrgno = params.getModel().getTransOrgno();
// 		params.getModel().setCustNo(null);
// 		params.getModel().setTransOrgno(null);
// 		String sql1 ="select * from (select t1.app_serno,\n" +
// 				"               t1.cust_name,\n" +
// 				"               t1.cust_no,\n" +
// 				"               t1.acct_no,\n" +
// 				"               t1.trans_acct_no,\n" +
// 				"               t1.ta_acct_no,\n" +
// 				"               t1.app_amt,\n" +
// 				"               t1.ack_vol,\n" +
// 				"               t1.trans_status,\n" +
// 				"               t1.prod_code,\n" +
// 				"               t1.prod_name,\n" +
// 				"               t1.busi_code,\n" +
// 				"               'CH0901015' channel_flag,\n" +
// 				"               t1.cust_manager,\n" +
// 				"               t2.branch_code,\n" +
// 				"               t1.busi_date,\n" +
// 				"               t2.trans_orgno,\n" +
// 				"               date_format(t1.mactime,'%T') as mactime,\n" +
// 				"               t1.ta_rtn_code,\n" +
// 				"               t1.ta_rtn_desc,\n" +
// 				"               t1.advance_income,\n" +
// 				"               t2.id_code,\n" +
// 				"               t2.id_type\n" +
// 				"          from bala_cust_trans_cfm t1\n" +
// 				"          left join bala_cust_sign t2 on t1.cust_no = t2.cust_no\n" +
// 				"         where 1 = 1 AND (t1.busi_code = '122' OR t1.busi_code = '124' or t1.busi_code =  '198') \n" +
// 				"           and t1.app_serno is not null  ";
// 		if(Tools.isNotBlank(custNo)){
// 			sql1 = sql1 +" and t1.cust_no in ('"+custNo+"')";
// 		}
// 		if(Tools.isNotBlank(transOrgno)){
// 			String inStr = " and t1.TRANS_ORGNO in (" ;
// 			String [] str = transOrgno.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sql1 += inStr;
// 		}
// 		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M933.class);
// 		//sql1 += reportformUtil.getOrgIdForOrgLevel("t1.");
// 		String sql2 = " select * from ( select t1.app_serno,\n" +
// 				"               t1.cust_name,\n" +
// 				"               t1.cust_no,\n" +
// 				"               t1.acct_no,\n" +
// 				"               t1.trans_acct_no,\n" +
// 				"               t1.ta_acct_no,\n" +
// 				"               t1.app_amt,\n" +
// 				"               t1.ack_vol,\n" +
// 				"               t1.trans_status,\n" +
// 				"               t1.prod_code,\n" +
// 				"               t1.prod_name,\n" +
// 				"               t1.busi_code,\n" +
// 				"               'CH0901015' channel_flag,\n" +
// 				"               t1.cust_manager,\n" +
// 				"               t2.branch_code,\n" +
// 				"               t1.busi_date,\n" +
// 				"               t2.trans_orgno,\n" +
// 				"               date_format(t1.mactime,'%T') as mactime,\n" +
// 				"               t1.ta_rtn_code,\n" +
// 				"               t1.ta_rtn_desc,\n" +
// 				"               t1.advance_income,\n" +
// 				"               t2.id_code,\n" +
// 				"               t2.id_type\n" +
// 				"          from bala_cust_trans_cfm_h t1\n" +
// 				"          left join bala_cust_sign t2 on t1.cust_no = t2.cust_no\n" +
// 				"         where 1 = 1 AND (t1.busi_code = '122' OR t1.busi_code = '124' or t1.busi_code =  '198')   \n" +
// 				"           and t1.app_serno is not null ";
// 		if(Tools.isNotBlank(custNo)){
// 			sql2 = sql2 +" and  t1.cust_no in ('"+custNo+"')";
// 		}
// 		if(Tools.isNotBlank(transOrgno)){
// 			String inStr = " and t1.TRANS_ORGNO in (" ;
// 			String [] str = transOrgno.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sql2 += inStr;
// 		}
// 		//sql2 += reportformUtil.getOrgIdForOrgLevel("t1.");
// 		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M933.class);
// 		String sql = " select * from ( " + sql1 +
// 				") sql_ta union all \n" +
// 				sql2 + ") sql_tb  ) sql_tc order by busi_date desc  ";
// 		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER,params);
// 	}
//
// /**
//  * "select t1.app_serno,\n" +
//  * 				"               t1.cust_name,\n" +
//  * 				"               t1.cust_no,\n" +
//  * 				"               t1.acct_no,\n" +
//  * 				"               t1.trans_acct_no,\n" +
//  * 				"               t1.ta_acct_no,\n" +
//  * 				"               t1.app_amt,\n" +
//  * 				"               t1.trans_status,\n" +
//  * 				"               t1.prod_code,\n" +
//  * 				"               t1.prod_name,\n" +
//  * 				"               t1.busi_code,\n" +
//  * 				"               (select t2.channel_flag\n" +
//  * 				"                  from bala_cust_trans_req t2\n" +
//  * 				"                 where t1.app_serno = t2.app_serno) as channel_flag,\n" +
//  * 				"               t1.cust_manager,\n" +
//  * 				"               (select t2.branch_code\n" +
//  * 				"                  from bala_cust_trans_req t2\n" +
//  * 				"                 where t1.app_serno = t2.app_serno) as branch_code,\n" +
//  * 				"               t1.busi_date,\n" +
//  * 				"               (select t2.trans_orgno\n" +
//  * 				"                  from bala_cust_trans_req t2\n" +
//  * 				"                 where t1.app_serno = t2.app_serno) as trans_orgno,\n" +
//  * 				"               to_char(t1.mactime, 'hh24:mi:ss') as mactime,\n" +
//  * 				"               (select t2.BANK_CODE\n" +
//  * 				"                  from bala_cust_trans_req_h t2\n" +
//  * 				"                 where t1.app_serno = t2.app_serno) as BANK_CODE,\n" +
//  * 				"               (select t2.SUB_BRANCH_CODE\n" +
//  * 				"                  from bala_cust_trans_req_h t2\n" +
//  * 				"                 where t1.app_serno = t2.app_serno) as SUB_BRANCH_CODE,\n" +
//  * 				"               t1.ta_rtn_code,\n" +
//  * 				"               t1.ta_rtn_desc,\n" +
//  * 				"               (select t2.advance_income\n" +
//  * 				"                  from bala_cust_trans_req t2\n" +
//  * 				"                 where t1.app_serno = t2.app_serno) as advance_income \n" +
//  * 				"          from bala_cust_trans_cfm t1\n" +
//  * 				"         where 1 = 1\n" +
//  * 				"           and t1.app_serno is not null ";
//  */
//
// }