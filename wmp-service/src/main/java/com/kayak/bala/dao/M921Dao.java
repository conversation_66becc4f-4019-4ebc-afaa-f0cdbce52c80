package com.kayak.bala.dao;

import com.kayak.bala.model.M921;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 账户信息查询
 */
@Repository
public class M921Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;

	/**
	 * 账户信息查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	/*public SqlResult<M921> findBalaCustTaAcct(SqlParam<M921> params) throws Exception {
		params.setMakeSql(true);
		String sql = " select t.tano,\n" +
				"       t.ta_acct_no,\n" +
				"       t.trans_acct_no,\n" +
				"       t2.acct_no,\n" +
				"       t.cust_no,\n" +
				"       t2.id_type,\n" +
				"       t2.id_code,\n" +
				"       t2.cust_name\n" +
				"  from bala_cust_ta_acct t\n" +
				"  left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				" where 1 = 1 "+ reportformUtil.getOrgIdForOrgLevel("t2.");
		if(Tools.isNotBlank(params.getModel().getCustNo())){
			sql = sql +" and t.cust_no in ('"+params.getModel().getCustNo()+"')";
		}
		params.getModel().setCustNo(null);
		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
	}*/



	/**
	 * 账户信息查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M921> findBalaCustTaAcct(SqlParam<M921> params) throws Exception {
		params.setMakeSql(true);
		String sql = " select t.tano,\n" +
				"       t.ta_acct_no,\n" +
				"       t.trans_acct_no,\n" +
				"       t2.acct_no,\n" +
				"       t.cust_no,\n" +
				"       t2.id_type,\n" +
				"       t2.id_code,\n" +
				"       t2.cust_name\n" +
				"  from bala_cust_ta_acct t\n" +
				"  left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				" where 1 = 1 ";
		if(Tools.isNotBlank(params.getModel().getCustNo())){
			sql = sql +" and t.cust_no in ('"+params.getModel().getCustNo()+"')";
		}
		params.getModel().setCustNo(null);
		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
	}


}