package com.kayak.bala.dao;
import com.kayak.bala.model.M930;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public class M930Dao extends ComnDao {
    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M930> findM930s(SqlParam<M930> params) throws Exception{

        StringBuffer sql = new StringBuffer();
         sql.append("select "+
                "               t.branch_code,\n" +
                "               t.ACK_DATE,\n" +
                "               sum(t.TOTAL_AMT) as TOTAL_AMT,\n" +
                "               sum(t.TOTAL_AUTO_AMT) as TOTAL_AUTO_AMT,\n" +
                "               sum(t.TOTAL_AMT + t.TOTAL_AUTO_AMT) as tot_in_amt,\n" +
                "               sum(t.TOTAL_OUT_AMT) as TOTAL_OUT_AMT,\n" +
                "               sum(t.TOTAL_REDEEM_AMT) as TOTAL_REDEEM_AMT,\n" +
                "               sum(t.TOTAL_OUT_AMT + t.TOTAL_REDEEM_AMT) as tot_out_amt\n" +
                "          from bala_cust_trans_stat t where 1=1 \n");
        //sql.append(reportformUtil.getOrgIdForOrgLevel("t."));
        if(Tools.isNotBlank(params.getModel().getBranchCode())){
            sql.append(" and t.branch_code in ( ");
            String [] str = params.getModel().getBranchCode().split(",");
            if(str != null && str.length > 0){
                for(int i=0;i<str.length;i++){
                    if(i != str.length -1){
                        sql.append("'"+str[i].toString()+"',");
                    }else{
                        sql.append("'"+str[i].toString()+"'");
                    }

                }
            }
            sql.append(" ) ");
        }
        if(Tools.isNotBlank(params.getModel().getAckDate())){
            sql.append(" and t.ack_date >= '"+params.getModel().getAckDate()+"'");

        }
        if(Tools.isNotBlank(params.getModel().getAckEndDate())){
            sql.append(" and t.ack_date <= '"+params.getModel().getAckEndDate()+"'");

        }
        sql.append("        group by "+
                "               t.branch_code,\n" +
                "               t.ACK_DATE order by t.ACK_DATE desc ");

        return super.findRows(sql.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
    }
}
