package com.kayak.bala.dao;

import com.kayak.bala.model.M923;
import com.kayak.bala.model.M927;
import com.kayak.bala.model.M929;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.ProdAcctInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 还款流水
 */
@Repository
public class M929Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;

	/**
	 * 还款流水查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M929> findBalaCustTransReq(SqlParam<M929> params) throws Exception {
		//params.setMakeSql(true);
		StringBuffer sb1 = new StringBuffer();
		sb1.append("select * from ( select t.busi_code,\n" +
				"               t.busi_date,\n" +
				"               t.busi_date as query_busi_date,\n" +
				"               t.trans_acct_no,\n" +
				"               t.acct_no,\n" +
				"               '3' as trans_status,\n" +
				"               t.cust_no,\n" +
				"               sum(t.js_app) as js_app_amt,\n" +
				"               sum(t.jg_app) as jg_app_amt,\n" +
				"               t2.cust_name,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code\n" +
				"          from (select\n" +
				"					busi_code,\n" +
				"   				busi_date ,\n" +
				"   				busi_date as query_busi_date ,\n" +
				"   				trans_acct_no ,\n" +
				"   				acct_no,\n" +
				"					'3' as trans_status ,\n" +
				"					cust_no,\n" +
				"					case \n" +
				"						busi_code when '122' then ack_amt else 0 \n" +
				"					end as js_app,\n" +
				"					case \n" +
				"						busi_code when '198' then ack_vol-ADVANCE_INCOME else 0\n" +
				"					end as jg_app\n" +
				"   				from bala_cust_trans_cfm ) t" +
				"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				"         where (t.busi_code = '122' or t.busi_code = '198')\n" +
				"           and t.trans_status in ('3', '4') ");
		if(StringUtils.isNotBlank(params.getModel().getTransAcctNo())){
			sb1.append(" and t.trans_acct_no in ("+params.getModel().getTransAcctNo().substring(0,params.getModel().getTransAcctNo().length()-1)+")");
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
			sb1.append(" and t.busi_date >= '"+params.getModel().getBusiDate()+"'" );
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb1.append(" and t.busi_date <= '"+params.getModel().getStartEndDate()+"'" );
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb1.append(" and t.acct_no = '"+params.getModel().getAcctNo()+"'" );
		}
		if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
			sb1.append(" and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustNo())){
			sb1.append(" and t.cust_no in  ('"+params.getModel().getCustNo()+"') ");
		}
		//sb1.append(reportformUtil.getOrgIdForOrgLevel("t."));
		sb1.append(" group by t.busi_code,\n" +
				"                  t.busi_date,\n" +
				"                  t.trans_acct_no,\n" +
				"                  t.trans_status,\n" +
				"                  t.acct_no,\n" +
				"                  t.cust_no,\n" +
				"                  t2.cust_name,\n" +
				"                  t2.id_type,\n" +
				"                  t2.id_code");

		StringBuffer sb2 = new StringBuffer();
		sb2.append(" select * from ( select t.busi_code,\n" +
				"               t.busi_date,\n" +
				"               t.busi_date as query_busi_date,\n" +
				"               t.trans_acct_no,\n" +
				"               t.acct_no,\n" +
				"               '3' as trans_status,\n" +
				"               t.cust_no,\n" +
				"               sum(js_app) as js_app_amt,\n" +
				"               sum(jg_app) as jg_app_amt,\n" +
				"               t2.cust_name,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code\n" +
				"          from (select\n" +
				"          busi_code,\n" +
				"   busi_date ,\n" +
				"   busi_date as query_busi_date ,\n" +
				"   trans_acct_no ,\n" +
				"   acct_no,\n" +
				"	'3' as trans_status ,\n" +
				"	cust_no,\n" +
				"	case \n" +
				"		busi_code when '122' then ack_amt else 0 \n" +
				"	end as js_app,\n" +
				"	case \n" +
				"		busi_code when '198' then ack_vol-ADVANCE_INCOME else 0\n" +
				"	end as jg_app\n" +
				" 		from bala_cust_trans_cfm_h ) t\n" +
				"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				"         where (t.busi_code = '122' or t.busi_code = '198')\n" +
				"           and t.trans_status in ('3', '4')");
		if(StringUtils.isNotBlank(params.getModel().getTransAcctNo())){
			sb2.append(" and t.trans_acct_no in ("+params.getModel().getTransAcctNo().substring(0,params.getModel().getTransAcctNo().length()-1)+")");
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
			sb2.append(" and t.busi_date >= '"+params.getModel().getBusiDate()+"'" );
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb2.append(" and t.busi_date <= '"+params.getModel().getStartEndDate()+"'" );
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb2.append( "and t.acct_no = '"+params.getModel().getAcctNo()+"'" );
		}
		if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
			sb2.append(" and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustNo())){
			sb2.append(" and t.cust_no in  ('"+params.getModel().getCustNo()+"') ");
		}
		//sb2.append(reportformUtil.getOrgIdForOrgLevel("t."));
		sb2.append("group by t.busi_code,\n" +
				"                  t.busi_date,\n" +
				"                  t.trans_acct_no,\n" +
				"                  t.trans_status,\n" +
				"                  t.acct_no,\n" +
				"                  t.cust_no,\n" +
				"                  t2.cust_name,\n" +
				"                  t2.id_type,\n" +
				"                  t2.id_code");
		String sb = " /*+QUERY_TIMEOUT(111111111)*/ select * from ( " +sb1.toString() +
				"\n ) xql_tc union all " + sb2.toString() + " ) xql_tb ) xql_ta order by busi_date desc  ";
		return super.findRows(sb, SubDatabase.DATABASE_BALA_CENTER, params);
	}

	//public SqlResult<M929> findBalaCustTransReq(SqlParam<M929> params) throws Exception {
	//	//params.setMakeSql(true);
	//	StringBuffer sb1 = new StringBuffer();
	//	sb1.append("select * from ( select t.busi_code,\n" +
	//			"               t.busi_date,\n" +
	//			"               t.busi_date as query_busi_date,\n" +
	//			"               t.trans_acct_no,\n" +
	//			"               t.acct_no,\n" +
	//			"               '3' as trans_status,\n" +
	//			"               t.cust_no,\n" +
	//			"               sum(decode(t.busi_code, '122', t.ack_amt, 0)) as js_app_amt,\n" +
	//			"               sum(decode(t.busi_code, '198', t.ack_vol-t.ADVANCE_INCOME, 0)) as jg_app_amt,\n" +
	//			"               t2.cust_name,\n" +
	//			"               t2.id_type,\n" +
	//			"               t2.id_code\n" +
	//			"          from bala_cust_trans_cfm t\n" +
	//			"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
	//			"         where (t.busi_code = '122' or t.busi_code = '198')\n" +
	//			"           and t.trans_status in ('3', '4') ");
	//	if(StringUtils.isNotBlank(params.getModel().getTransAcctNo())){
	//		sb1.append(" and t.trans_acct_no in ("+params.getModel().getTransAcctNo().substring(0,params.getModel().getTransAcctNo().length()-1)+")");
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
	//		sb1.append(" and t.busi_date >= '"+params.getModel().getBusiDate()+"'" );
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
	//		sb1.append(" and t.busi_date <= '"+params.getModel().getStartEndDate()+"'" );
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
	//		sb1.append(" and t.acct_no = '"+params.getModel().getAcctNo()+"'" );
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
	//		sb1.append(" and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getCustNo())){
	//		sb1.append(" and t.cust_no in  ('"+params.getModel().getCustNo()+"') ");
	//	}
	//	//sb1.append(reportformUtil.getOrgIdForOrgLevel("t."));
	//	sb1.append(" group by t.busi_code,\n" +
	//			"                  t.busi_date,\n" +
	//			"                  t.trans_acct_no,\n" +
	//			"                  t.trans_status,\n" +
	//			"                  t.acct_no,\n" +
	//			"                  t.cust_no,\n" +
	//			"                  t2.cust_name,\n" +
	//			"                  t2.id_type,\n" +
	//			"                  t2.id_code");
	//
	//	StringBuffer sb2 = new StringBuffer();
	//	sb2.append(" select * from ( select t.busi_code,\n" +
	//			"               t.busi_date,\n" +
	//			"               t.busi_date as query_busi_date,\n" +
	//			"               t.trans_acct_no,\n" +
	//			"               t.acct_no,\n" +
	//			"               '3' as trans_status,\n" +
	//			"               t.cust_no,\n" +
	//			"               sum(decode(t.busi_code, '122', t.ack_amt, 0)) as js_app_amt,\n" +
	//			"               sum(decode(t.busi_code, '198', t.ack_vol-t.ADVANCE_INCOME, 0)) as jg_app_amt,\n" +
	//			"               t2.cust_name,\n" +
	//			"               t2.id_type,\n" +
	//			"               t2.id_code\n" +
	//			"          from bala_cust_trans_cfm_h t\n" +
	//			"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
	//			"         where (t.busi_code = '122' or t.busi_code = '198')\n" +
	//			"           and t.trans_status in ('3', '4')");
	//	if(StringUtils.isNotBlank(params.getModel().getTransAcctNo())){
	//		sb2.append(" and t.trans_acct_no in ("+params.getModel().getTransAcctNo().substring(0,params.getModel().getTransAcctNo().length()-1)+")");
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
	//		sb2.append(" and t.busi_date >= '"+params.getModel().getBusiDate()+"'" );
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
	//		sb2.append(" and t.busi_date <= '"+params.getModel().getStartEndDate()+"'" );
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
	//		sb2.append( "and t.acct_no = '"+params.getModel().getAcctNo()+"'" );
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
	//		sb2.append(" and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
	//	}
	//	if(StringUtils.isNotBlank(params.getModel().getCustNo())){
	//		sb2.append(" and t.cust_no in  ('"+params.getModel().getCustNo()+"') ");
	//	}
	//	//sb2.append(reportformUtil.getOrgIdForOrgLevel("t."));
	//	sb2.append("group by t.busi_code,\n" +
	//			"                  t.busi_date,\n" +
	//			"                  t.trans_acct_no,\n" +
	//			"                  t.trans_status,\n" +
	//			"                  t.acct_no,\n" +
	//			"                  t.cust_no,\n" +
	//			"                  t2.cust_name,\n" +
	//			"                  t2.id_type,\n" +
	//			"                  t2.id_code");
	//	String sb = " /*+QUERY_TIMEOUT(111111111)*/ select * from ( " +sb1.toString() +
	//			"\n ) union all " + sb2.toString() + " ) ) order by busi_date desc  ";
	//	return super.findRows(sb, SubDatabase.DATABASE_BALA_CENTER, params);
	//}


}