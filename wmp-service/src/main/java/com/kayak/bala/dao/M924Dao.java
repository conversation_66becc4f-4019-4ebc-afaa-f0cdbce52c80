package com.kayak.bala.dao;

import com.kayak.bala.model.M924;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 客户底层份额
 */
@Repository
public class M924Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;

	/**
	 * 客户底层份额查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M924> findBalaCustBaseVol(SqlParam<M924> params) throws Exception {
		params.setMakeSql(true);
		String sql = "select t1.ta_acct_no,\n" +
				"       t1.tano,\n" +
				"       t1.prod_code,\n" +
				"       t2.prod_name,\n" +
				"       t1.vol,\n" +
				"       t1.redeem_frozen_vol,\n" +
				"       t1.justice_frozen_vol,\n" +
				"       t1.last_day_income,\n" +
				"       t1.last_week_income,\n" +
				"       t1.last_month_income,\n" +
				"       t1.total_income,\n" +
				"       t1.bank_code,\n" +
				"       t1.trans_orgno,\n" +
				"       t1.BRANCH_CODE,\n" +
				"       t1.cust_no,\n" +
				"       t3.cust_name,\n" +
				"       t3.id_type,\n" +
				"       t3.id_code,\n" +
				"       t3.acct_no\n" +
				"  from bala_cust_base_vol t1\n" +
				"  left join bala_prod_base_info t2 on t1.prod_code = t2.prod_code\n" +
				"                                  and t1.tano = t2.tano\n" +
				"  left join BALA_CUST_SIGN t3 on t1.cust_no = t3.cust_no\n" +
				" where 1 = 1\n " ;
		//+ reportformUtil.getOrgIdForOrgLevel("t1.")
		if(Tools.isNotBlank(params.getModel().getCustNo())){
			sql = sql +" and t1.cust_no in ('"+params.getModel().getCustNo()+"')";
		}
		params.getModel().setCustNo(null);

		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
	}



	/**
	 * 查询TANO
	 * @throws Exception
	 */
	public SqlResult<M924> findTano(SqlParam<M924> params) throws Exception {
		return super.findRows("SELECT DISTINCT(TANO) FROM bala_cust_sign bat ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

}