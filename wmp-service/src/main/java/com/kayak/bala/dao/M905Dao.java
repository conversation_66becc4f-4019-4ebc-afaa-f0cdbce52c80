// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M902;
// import com.kayak.bala.model.M905;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.core.exception.PromptException;
// import com.kayak.core.sql.Sql;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import com.kayak.system.model.M001;
// import org.apache.commons.lang3.StringUtils;
// import org.springframework.stereotype.Repository;
//
// import java.util.List;
//
// /**
//  * TA划拨账户维护
//  */
// @Repository
// public class M905Dao extends ComnDao {
//
// 	/**
// 	 * 查询TA划拨账户信息
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M905> findAccTaInfo(SqlParam<M905> params) throws Exception {
// 		String flag="";
// 		if(Tools.isNotBlank(params.getModel().getProdCode())){
// 			StringBuffer sql = new StringBuffer();
// 			sql.append("SELECT distinct(BALA_CODE) FROM BALA_PROD_BASE_INFO ");
// 			sql.append(" where 1=1 ");
// 			if(Tools.isNotBlank(params.getModel().getTano())){
// 				sql.append(" and tano = $S{tano} ");
// 			}
// 			if(Tools.isNotBlank(params.getModel().getProdCode())){
// 				sql.append(" and PROD_CODE = $S{prodCode} ");
// 			}
// 			if(Tools.isNotBlank(params.getModel().getSystemNo())){
// 				sql.append(" and SYSTEM_NO = $S{systemNo} ");
// 			}
// 			SqlResult<M905> sqlBalaResult=super.findRows(sql.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
//
// 			String balaCode="";
// 			if(null!=sqlBalaResult){
// 				if (sqlBalaResult.getRows() != null && sqlBalaResult.getRows().size() > 0 ){
// 					List<M905> balList = sqlBalaResult.getRows();
// 					for (M905 m905 : balList){
// 						balaCode = balaCode.trim()+"'" + m905.getBalaCode() + "',";
// 					}
// 				}
// 			}
// 			if(StringUtils.isNotBlank(balaCode)){
// 				balaCode=balaCode.substring(0,balaCode.length()-1);
// 			}else{
// 				balaCode="''";
// 			}
// 			params.getModel().setBalaCode(balaCode);
// 			flag="1";
// 		}
//
// 		StringBuffer sql2 = new StringBuffer();
// 		sql2.append("SELECT SYSTEM_NO, TANO, TA_FEE_ACCT,bala_code,contacts,contact_number, prod_code, " +
// 				"( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = bat.TA_FEE_ACCT) TA_FEE_ACCT_NAME, " +
// 				"TA_COLLECT_ACCT, ( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = bat.TA_COLLECT_ACCT) TA_COLLECT_ACCT_NAME," +
// 				" TA_COLLECT_RECV_ACCT, ( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = bat.TA_COLLECT_RECV_ACCT) TA_COLLECT_RECV_ACCT_NAME, " +
// 				"TA_REDEEM_ACCT, ( SELECT bai.ACCT_NAME FROM BALA_ACCT_INFO bai WHERE bai.ACCT_SERNO = bat.TA_REDEEM_ACCT) TA_REDEEM_ACCT_NAME, " +
// 				" REMARK FROM BALA_ACCT_TA bat where 1=1 ");
//
// 		if(Tools.isNotBlank(params.getModel().getTano())){
// 			sql2.append(" and tano = "+params.getModel().getTano() );
// 		}
// 		if(StringUtils.isNotBlank(flag)){
// 			sql2.append(" and bala_code in ("+params.getModel().getBalaCode()+")");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getSystemNo())){
// 			sql2.append(" and SYSTEM_NO = $S{systemNo} ");
// 		}
// 		return super.findRows(sql2.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
//
// 	/**
// 	 * 查询TA划拨账户信息
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M905> findProdInfo(SqlParam<M905> params) throws Exception {
// 			StringBuffer sql = new StringBuffer();
// 			sql.append("SELECT distinct(PROD_CODE) FROM BALA_PROD_BASE_INFO ");
// 			sql.append(" where 1=1 ");
// 			if(Tools.isNotBlank(params.getModel().getTano())){
// 				sql.append(" and tano = $S{tano} ");
// 			}
// 			if(Tools.isNotBlank(params.getModel().getProdCode())){
// 				sql.append(" and PROD_CODE = $S{prodCode} ");
// 			}
// 			if(Tools.isNotBlank(params.getModel().getBalaCode())){
// 				sql.append(" and bala_CODE = $S{balaCode} ");
// 			}
// 			if(Tools.isNotBlank(params.getModel().getSystemNo())){
// 				sql.append(" and SYSTEM_NO = $S{systemNo} ");
// 			}
// 			if(Tools.isNotBlank(params.getModel().getSystemNo())){
// 				sql.append(" and SYSTEM_NO = $S{systemNo} ");
// 			}
// 			return super.findRows(sql.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
// 	/**
//      * 修改TA划拨账户
// 	 * @param params
//      * @throws Exception
// 	 */
// 	public void update(SqlParam<M905> params) throws Exception {
// 		M905 newM905 = params.getModel();
// 		Sql sql = Sql.build()
// 				.mysqlSql("UPDATE BALA_ACCT_TA " +
// 						"SET TA_COLLECT_ACCT=$S{taCollectAcct}, TA_COLLECT_RECV_ACCT=$S{taCollectRecvAcct}," +
// 						" TA_REDEEM_ACCT=$S{taRedeemAcct}," +
// 						" REMARK=$S{remark},TA_FEE_ACCT = $S{taFeeAcct},UPDATE_TIME=now(),contacts=$S{contacts},contact_number=$S{contactNumber} " +
// 						"WHERE SYSTEM_NO=$S{systemNo} AND TANO=$S{tano} and prod_code=$S{prodCode}  and bala_code=$S{balaCode}");
// 		doTrans(() -> {
// 			super.update(sql,SubDatabase.DATABASE_BALA_CENTER, newM905);
// 		});
// 	}
//
// 	/**
// 	 * 删除TA划拨账户
// 	 * @param params
// 	 * @throws Exception
// 	 */
// 	public void delAccTaInfo(SqlParam<M905> params) throws Exception {
// 		M905 newM905 = params.getModel();
// 		doTrans(() -> {
// 			super.update("DELETE FROM BALA_ACCT_TA " +
// 					"WHERE SYSTEM_NO=$S{systemNo} AND TANO=$S{tano} and  PROD_CODE=$S{prodCode}",SubDatabase.DATABASE_BALA_CENTER, newM905);
// 		});
// 	}
//
// 	/**
// 	 * 校验划拨账户产品
// 	 * @param params
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M905>  checkProdCode(SqlParam<M905> params) throws Exception {
// 		StringBuffer sql = new StringBuffer();
// 		sql.append("SELECT PROD_CODE FROM BALA_ACCT_TA ");
// 		sql.append(" where 1=1 ");
// 		if(Tools.isNotBlank(params.getModel().getTano())){
// 			sql.append(" and tano = $S{tano} ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getProdCode())){
// 			sql.append(" and PROD_CODE = $S{prodCode} ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getBalaCode())){
// 			sql.append(" and bala_CODE = $S{balaCode} ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getSystemNo())){
// 			sql.append(" and SYSTEM_NO = $S{systemNo} ");
// 		}
// 		return super.findRows(sql.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
//
// 	/**
// 	 * 新增TA划拨账户
// 	 * @param params
// 	 * @throws Exception
// 	 */
// 	public void addAccTaInfo(SqlParam<M905> params) throws Exception {
// 		M905 newM905 = params.getModel();
// 		Sql sql = Sql.build()
// 				.mysqlSql("INSERT INTO BALA_ACCT_TA " +
// 					"  (SYSTEM_NO, TANO,TA_FEE_ACCT, TA_COLLECT_ACCT, TA_COLLECT_RECV_ACCT, TA_REDEEM_ACCT, " +
// 					"  REMARK, CREATE_TIME, UPDATE_TIME, BALA_CODE,contacts,contact_number,prod_code )  " +
// 					"  VALUES ($S{systemNo},$S{tano} ,$S{taFeeAcct},$S{taCollectAcct},$S{taCollectRecvAcct}, " +
// 					"  $S{taRedeemAcct},$S{remark},now() , now(),$S{balaCode},$S{contacts},$S{contactNumber},$S{prodCode}) ");
// 		doTrans(() -> {
// 			super.update(sql ,SubDatabase.DATABASE_BALA_CENTER, newM905);
// 		});
// 	}
//
// 	/**
// 	 * 查询TANO
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M905> findTano(SqlParam<M905> params) throws Exception {
// 		return super.findRows("SELECT DISTINCT(TANO) FROM BALA_ACCT_TA bat ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
// }