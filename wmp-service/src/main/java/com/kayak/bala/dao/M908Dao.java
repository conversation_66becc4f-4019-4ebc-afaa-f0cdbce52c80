package com.kayak.bala.dao;

import com.kayak.bala.model.M908;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.springframework.stereotype.Repository;

/**
 * 份额核对处理
 */
@Repository
public class M908Dao extends ComnDao {

	/**
	 * 查询份额核对信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M908> findBalaCheckVol(SqlParam<M908> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("\n" +
				"select check_serno,cust_name,\n" +
				"       id_type,\n" +
				"       id_code,\n" +
				"       trans_acct_no,\n" +
				"       acct_no,\n" +
				"       check_date,\n" +
				"       host_balance,\n" +
				"       bala_balance,\n" +
				"       deal_status \n" +
				"  from bala_check_vol t where 1=1 order by check_date desc ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
     * 修改TA划拨账户
	 * @param params
     * @throws Exception
	 */
	public void update(SqlParam<M908> params) throws Exception {
		M908 newM908 = params.getModel();
		doTrans(() -> {
			super.update("UPDATE bala_check_vol " +
					"SET deal_status='2',UPDATE_TIME=SYSDATE " +
					"WHERE check_serno=$S{checkSerno} ",SubDatabase.DATABASE_BALA_CENTER, newM908);
		});
	}

	/**
	 * 查询TANO
	 * @throws Exception
	 */
	public SqlResult<M908> findTano(SqlParam<M908> params) throws Exception {
		return super.findRows("SELECT DISTINCT(TANO) FROM BALA_ACCT_TA bat ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

}