package com.kayak.bala.dao;

import com.kayak.bala.model.M923;
import com.kayak.bala.model.M927;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 还款流水
 */
@Repository
public class M927Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;
	/**
	 * 还款流水查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M927> findBalaCustPaymentLog(SqlParam<M927> params) throws Exception {

		String custNo = params.getModel().getCustNo();
		params.getModel().setCustNo(null);
		StringBuffer sb = new StringBuffer();
		sb.append("select * from ( ");
		sb.append("  select t.trans_serno,\n" +
				"               t.acct_no,\n" +
				"               t.payment_amt,\n" +
				"               t.trans_status,\n" +
				"               t.host_rtn_code,\n" +
				"               t.host_rtn_desc,\n" +
				"                'LQB' prod_code,\n" +
				"               t.trans_date,\n" +
				"               t.BUSI_TYPE,\n" +
				"               t2.cust_name,\n" +
				"               t2.trans_acct_no,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code,\n" +
				"                t3.prod_name\n" +
				"          from bala_cust_payment_log t\n" +
				"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				"          left join bala_prod_base_info t3 on prod_code = t3.prod_code\n" +
				"         where 1 = 1\n" +
				"           and t.payment_amt != '0' ");
		//+ reportformUtil.getOrgIdForOrgLevel("t.")
		if(StringUtils.isNotBlank(params.getModel().getTransDate())){
			sb.append(" and t.trans_date >="+params.getModel().getTransDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb.append(" and t.trans_date <="+params.getModel().getStartEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb.append(" and t.acct_no in("+params.getModel().getAcctNo()+")");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransSerno())){
			sb.append(" and t.trans_serno = '"+params.getModel().getTransSerno()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiType())){
			sb.append(" and t.busi_type = '"+params.getModel().getBusiType()+"'");
		}
		if(Tools.isNotBlank(custNo)){
			sb.append(" and t.cust_no in ('"+custNo+"')" );
		}
		sb.append("  UNION ALL  ");
		sb.append(" select t.trans_serno,\n" +
				"               t.acct_no,\n" +
				"               t.payment_amt,\n" +
				"               t.trans_status,\n" +
				"               t.host_rtn_code,\n" +
				"               t.host_rtn_desc,\n" +
				"                'LQB' prod_code,\n" +
				"               t.trans_date,\n" +
				"               t.BUSI_TYPE,\n" +
				"               t2.cust_name,\n" +
				"               t2.trans_acct_no,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code,\n" +
				"               t3.prod_name\n" +
				"          from bala_cust_payment_log_h t\n" +
				"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				"          left join bala_prod_base_info t3 on prod_code = t3.prod_code\n" +
				"         where 1 = 1\n" +
				"           and t.payment_amt != '0' ");
		//+ reportformUtil.getOrgIdForOrgLevel("t.")
		if(StringUtils.isNotBlank(params.getModel().getTransDate())){
			sb.append(" and t.trans_date >="+params.getModel().getTransDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb.append(" and t.trans_date <="+params.getModel().getStartEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb.append(" and t.acct_no in("+params.getModel().getAcctNo()+")");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransSerno())){
			sb.append(" and t.trans_serno = '"+params.getModel().getTransSerno()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiType())){
			sb.append(" and t.busi_type = '"+params.getModel().getBusiType()+"'");
		}
		if(Tools.isNotBlank(custNo)){
			sb.append(" and t.cust_no in ('"+custNo+"')" );
		}
		sb.append(" ) sql_ta  order by  trans_date desc,TRANS_SERNO desc ");
		return super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
	}




}