package com.kayak.bala.dao;

import com.kayak.bala.model.M943;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;

import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;

import org.springframework.stereotype.Repository;

@Repository
public class M943Dao extends ComnDao {

    public SqlResult<M943> findM943(SqlParam<M943> params) throws Exception {
        //params.setMakeSql(true);
        String sql = " select busi_type, sum(unfrz_bala) as unfrz_bala, sum(subtract_bala) as subtract_bala, sum(payment_amt) as payment_amt, file_no, busi_date, check_flag, CHECK_OPRATER, check_date, check_time " +
                " from bala_cust_payment_log where busi_type = '1' group by busi_type, file_no, busi_date, check_flag, check_oprater, check_date, check_time  ORDER BY BUSI_DATE desc";
        return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
    }

    public int updateCheckFlag(SqlParam<M943> params) throws Exception {
        Sql sql = new Sql().mysqlSql("update bala_cust_payment_log t set t.check_flag='1',t.CHECK_OPRATER =$S{checkOprater},t.CHECK_DATE=$S{checkDate},t.CHECK_TIME=DATE_FORMAT(now(), '%H%i%s') where busi_type = '1' and file_no = $S{fileNo} ");
        return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel()).getEffect();
    }

}
