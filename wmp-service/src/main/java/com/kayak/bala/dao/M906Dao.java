package com.kayak.bala.dao;

import com.alibaba.fastjson.JSONObject;
import com.kayak.bala.model.M906;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import org.springframework.stereotype.Repository;

/**
 * 批量差错处理
 */
@Repository
public class M906Dao extends ComnDao {

	/**
	 * 查询批量差错信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M906> findBalaBatchError(SqlParam<M906> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("\n" +
				"select trans_Serno,\n" +
				"       acct_No,\n" +
				"       refund_Date,\n" +
				"       refund_Time,\n" +
				"       error_Amt,\n" +
				"       error_Vol,\n" +
				"       error_Type,\n" +
				"       host_Trans_Serno,\n" +
				"       host_Capital_Serno,\n" +
				"       trans_Status,\n" +
				"       host_Rtn_Code,\n" +
				"       host_Rtn_Desc,\n" +
				"       error_serno,\n" +
				"       error_Status\n" +
				"  from bala_batch_error t where 1=1 order by refund_Date desc ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
     * 修改TA划拨账户
	 * @param params
     * @throws Exception
	 */
	public void update(SqlParam<M906> params) throws Exception {
		M906 newM906 = params.getModel();
		doTrans(() -> {
			super.update("UPDATE BALA_ACCT_TA " +
					"SET TA_COLLECT_ACCT=$S{taCollectAcct}, TA_COLLECT_RECV_ACCT=$S{taCollectRecvAcct}," +
					" TA_REDEEM_ACCT=$S{taRedeemAcct}," +
					" REMARK=$S{remark},TA_FEE_ACCT = $S{taFeeAcct},UPDATE_TIME=SYSDATE " +
					"WHERE SYSTEM_NO=$S{systemNo} AND TANO=$S{tano} ",SubDatabase.DATABASE_BALA_CENTER, newM906);
		});
	}

	/**
	 * 删除TA划拨账户
	 * @param params
	 * @throws Exception
	 */
	public void delAccTaInfo(SqlParam<M906> params) throws Exception {
		M906 newM906 = params.getModel();
		doTrans(() -> {
			super.update("DELETE FROM BALA_ACCT_TA " +
					"WHERE SYSTEM_NO=$S{systemNo} AND TANO=$S{tano} ",SubDatabase.DATABASE_BALA_CENTER, newM906);
		});
	}

	/**
	 * 新增TA划拨账户
	 * @param params
	 * @throws Exception
	 */
	public void addAccTaInfo(SqlParam<M906> params) throws Exception {
		M906 newM906 = params.getModel();
		doTrans(() -> {
			super.update("INSERT INTO BALA_ACCT_TA" +
					"(SYSTEM_NO, TANO,TA_FEE_ACCT, TA_COLLECT_ACCT, TA_COLLECT_RECV_ACCT, TA_REDEEM_ACCT, " +
					"REMARK, CRT_TIME, UPD_TIME) " +
					"VALUES($S{systemNo},$S{tano} ,$S{taFeeAcct},$S{taCollectAcct},$S{taCollectRecvAcct}," +
					" $S{taRedeemAcct},$S{remark},SYSDATE , SYSDATE ) " ,SubDatabase.DATABASE_BALA_CENTER, newM906);
		});
	}

	/**
	 * 查询TANO
	 * @throws Exception
	 */
	public SqlResult<M906> findTano(SqlParam<M906> params) throws Exception {
		return super.findRows("SELECT DISTINCT(TANO) FROM BALA_ACCT_TA bat ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
	 * <AUTHOR>
	 * @Description 修改TA划拨信息表数据
	 * @Date 2021/12/24
	 * @Param [params]
	 * @return void
	 **/
	public void updateAcctTa(SqlParam<M906> params) throws Exception {
		M906 newM906 = params.getModel();
			super.update("UPDATE BALA_ACCT_TA " +
					"SET SYSTEM_NO=$S{systemNo} , TANO=$S{tano}  , TA_FEE_ACCT = $S{taFeeAcct} ," +
					" TA_COLLECT_ACCT=$S{taCollectAcct}, TA_COLLECT_RECV_ACCT=$S{taCollectRecvAcct}," +
					" TA_REDEEM_ACCT=$S{taRedeemAcct}," +
					" REMARK=$S{remark},UPDATE_TIME=SYSDATE " +
					"WHERE SYSTEM_NO=$S{systemNo} AND TANO=$S{tano} ",SubDatabase.DATABASE_BALA_CENTER, newM906);
	}

	/**
	 * <AUTHOR>
	 * @Description 查询TA账户划拨信息
	 * @Date 2021/12/24
	 * @Param [params]
	 * @return com.kayak.core.sql.SqlResult<com.kayak.bala.model.M906>
	 **/
	public SqlResult<M906> findBalaAcctTa(SqlParam<M906> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("\n" +
				"select system_No,\n" +
				"       tano,\n" +
				"       ta_Fee_Acct,\n" +
				"       ta_Collect_Acct,\n" +
				"       ta_Collect_Recv_Acct,\n" +
				"       ta_Redeem_Acct,\n" +
				"       remark \n" +
				"  from BALA_ACCT_TA t where 1=1 ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

	public UpdateResult updateErrorStatus(JSONObject params) throws Exception {
		String sql="UPDATE bala_batch_error SET error_status= $S{errorStatus}  WHERE  ERROR_SERNO=$S{errorSerno} ";
		return super.update(sql,SubDatabase.DATABASE_BALA_CENTER, params);
	}
}