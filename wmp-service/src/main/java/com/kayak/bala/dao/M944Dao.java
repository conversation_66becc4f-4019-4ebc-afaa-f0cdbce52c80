// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M944;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.core.sql.Sql;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.sql.UpdateResult;
// import com.kayak.core.util.Tools;
// import org.springframework.stereotype.Repository;
//
// @Repository
// public class M944Dao extends ComnDao {
//
//     public SqlResult<M944> findProdDocumentInfo(SqlParam<M944> params) throws Exception {
//         StringBuffer mysqlSql = new StringBuffer();
//         mysqlSql.append("SELECT a.bala_code, a.system_no, a.tano, a.prod_code, a.doc_type, a.online_flag, a.doc_version, a.doc_path, a.doc_status, a.doc_name, DATE_FORMAT(a.update_time, '%Y-%m-%d %H:%i:%s') as update_time, a.FILE_DATE, b.prod_name ")
//                 .append("  FROM bala_prod_document_info a ")
//                 .append(" INNER JOIN bala_prod_base_info b on a.bala_code = b.bala_code and a.system_no = b.system_no and a.tano = b.tano and a.prod_code = b.prod_code  ")
//                 .append(" WHERE 1 = 1");
//         if(Tools.isBlank(params.getModel().getDocStatus())){
//             mysqlSql.append(" and  a.doc_status != '2' ");
//         }
//         mysqlSql.append(" order by a.update_time desc");
//
//         Sql sql = Sql.build().mysqlSql(mysqlSql.toString());
//         return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
//     }
//
//     public UpdateResult addProdDocumentInfo(SqlParam<M944> params) throws Exception {
//         String mysqlSql = "INSERT INTO bala_prod_document_info " +
//                 "	  (bala_code, system_no, tano, prod_code, doc_type,online_flag,doc_version,doc_path,doc_status,doc_name,update_time,FILE_DATE) " +
//                 "VALUES" +
//                 "     ($S{balaCode}, $S{systemNo},$S{tano},$S{prodCode},$S{docType},$S{onlineFlag},$S{docVersion},$S{docPath},$S{docStatus},$S{docName},now(),$S{fileDate})";
//         Sql sql = Sql.build().mysqlSql(mysqlSql);
//         return super.update(sql,SubDatabase.DATABASE_BALA_CENTER, params.getModel());
//     }
//
//     public UpdateResult updateProdDocumentInfo(SqlParam<M944> params) throws Exception {
//         Sql sql = Sql.build().mysqlSql("UPDATE bala_prod_document_info SET doc_path=$S{docPath}, doc_name=$S{docName}, update_time=now(),file_date = $S{fileDate} WHERE system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND bala_code = $S{balaCode} AND doc_type=$S{docType} AND doc_version=$S{docVersion} AND online_flag=$S{onlineFlag}");
//         return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel());
//     }
//
//     public UpdateResult updateStatus(M944 params) throws Exception {
//         Sql sql = Sql.build().mysqlSql("UPDATE bala_prod_document_info SET doc_status=$S{docStatus}, update_time=now() WHERE system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND bala_code = $S{balaCode} AND doc_type=$S{docType} AND doc_version=$S{docVersion} AND online_flag=$S{onlineFlag} ");
//         return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params);
//     }
//
//     public UpdateResult deleteProdDocumentInfo(SqlParam<M944> params) throws Exception {
//         return super.update("DELETE FROM bala_prod_document_info WHERE  system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND bala_code = $S{balaCode}  AND doc_type=$S{docType} AND doc_version=$S{docVersion} AND online_flag=$S{onlineFlag} ",
//                 SubDatabase.DATABASE_BALA_CENTER, params.getModel());
//     }
//
// }
