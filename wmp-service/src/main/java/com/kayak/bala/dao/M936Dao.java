package com.kayak.bala.dao;

import com.alibaba.fastjson.JSONObject;
import com.kayak.bala.model.M936;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;

import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;


import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class M936Dao extends ComnDao {

    public SqlResult<M936> findM936s(SqlParam<M936> params) throws Exception {
        Sql sql = Sql.build().mysqlSql("select t.app_serno," +
                        "       t.busi_date," +
                        "       t.busi_code," +
                        "       t.tano," +
                        "       t.prod_code," +
                        "       t.trans_status," +
                        "       t.capital_status," +
                        "       t.cust_no," +
                        "       t.cust_name," +
                        "       t.id_type," +
                        "       t.id_code," +
                        "       DATE_FORMAT(t.mactime,'%H:%i:%s') as mactime," +
                        "       DATE_FORMAT(t.mactime,'%Y-%m-%d') as macdate," +
                        "       t.host_rtn_code," +
                        "       t.host_rtn_desc," +
                        "       t.host_trans_serno," +
                        "       t.channel_serno," +
                        "       t.acct_no" +
                        "  from BALA_CUST_TRANS_REQ t where 1=1 and t.capital_status in ('B','C','D','E','F','I')");
        return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
       // return null;
    }

    public UpdateResult updateErrorStatus(JSONObject params) throws Exception {
        String sql="UPDATE paym_capital_check_error SET error_status= $S{errorStatus}  WHERE  ERROR_SERNO=$S{errorSerno} ";
        return super.update(sql,SubDatabase.DATABASE_BALA_CENTER, params);
    }

    //根据调用T921接口，返回的结果更新交易状态和资金状态
    public UpdateResult updateStatus(SqlParam<M936> params) throws Exception {
        StringBuffer sb=new StringBuffer(" UPDATE BALA_CUST_TRANS_REQ SET TRANS_STATUS= $S{transStatus},CAPITAL_STATUS=$S{capitalStatus} ");
        if(StringUtils.isNotBlank(params.getModel().getHostRtnCode())){
            sb.append(" , HOST_RTN_CODE = $S{hostRtnCode} ");
        }
        if(StringUtils.isNotBlank(params.getModel().getHostRtnDesc())){
            sb.append(" , HOST_RTN_DESC = $S{hostRtnDesc} ");
        }
        sb.append(" WHERE  APP_SERNO=$S{appSerno} ");
        return super.update(sb.toString(),SubDatabase.DATABASE_BALA_CENTER,params.getModel());
    }

}
