// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M926;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import com.kayak.until.MakeSqlUntil;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// /**
//  * 底层交易申请流水
//  */
// @Repository
// public class M926Dao extends ComnDao {
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	/**
// 	 * 底层交易申请流水查询
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M926> findBalaCustTransReq(SqlParam<M926> params) throws Exception {
// 		String custNo = params.getModel().getCustNo();
// 		params.getModel().setCustNo(null);
// 		String transOrgno = params.getModel().getTransOrgno();//所属机构
// 		params.getModel().setTransOrgno(null);
// 		String sql1 = "select t.app_serno,\n" +
// 				"       t.cust_name,\n" +
// 				"       t.cust_no,\n" +
// 				"       t.acct_no,\n" +
// 				"       t.trans_acct_no,\n" +
// 				"       t.ta_acct_no,\n" +
// 				"       t.app_amt,\n" +
// 				"       t.app_vol,\n" +
// 				"       t.trans_status,\n" +
// 				"       t.prod_code,\n" +
// 				"       t.prod_name,\n" +
// 				"       t.channel_flag,\n" +
// 				"       t.cust_manager,\n" +
// 				"       t.branch_code,\n" +
// 				"       t.busi_date,\n" +
// 				"       t.busi_code,\n" +
// 				"       date_format(t.mactime,'%Y%m%d') as macdate,\n" +
// 				"       date_format(t.mactime,'%H%i%s') as mactime,\n" +
// 				"       t.trans_orgno,\n" +
// 				"       t.rtn_code,\n" +
// 				"       t.advance_income,\n" +
// 				"       t.rtn_desc,t.id_code,t.id_type\n" +
// 				"  from bala_cust_trans_req t where 1 = 1 and t.busi_code in ('022','024','098') ";
// 		if(Tools.isNotBlank(custNo)){
// 			sql1 = sql1 +" and cust_no in ('"+custNo+"')";
// 		}
// 		//所属机构
// 		if(Tools.isNotBlank(transOrgno)){
// 			String inStr = " and t.trans_orgno in (" ;
// 			String [] str = transOrgno.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sql1 = sql1 + inStr;
// 		}
// 		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(),M926.class);
// 		//sql1 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		String sql2 = " select t.app_serno,\n" +
// 				"       t.cust_name,\n" +
// 				"       t.cust_no,\n" +
// 				"       t.acct_no,\n" +
// 				"       t.trans_acct_no,\n" +
// 				"       t.ta_acct_no,\n" +
// 				"       t.app_amt,\n" +
// 				"       t.app_vol,\n" +
// 				"       t.trans_status,\n" +
// 				"       t.prod_code,\n" +
// 				"       t.prod_name,\n" +
// 				"       t.channel_flag,\n" +
// 				"       t.cust_manager,\n" +
// 				"       t.branch_code,\n" +
// 				"       t.busi_date,\n" +
// 				"       t.busi_code,\n" +
// 				"       date_format(t.mactime,'%Y%m%d') as macdate,\n" +
// 				"       date_format(t.mactime,'%H%i%s') as mactime,\n" +
// 				"       t.trans_orgno,\n" +
// 				"       t.rtn_code,\n" +
// 				"       t.advance_income,\n" +
// 				"       t.rtn_desc,t.id_code,t.id_type\n" +
// 				"  from bala_cust_trans_req_h t where 1 = 1 and t.busi_code in ('022','024','098') ";
// 		//所属机构
// 		if(Tools.isNotBlank(transOrgno)){
// 			String inStr = " and t.trans_orgno in (" ;
// 			String [] str = transOrgno.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sql2 = sql1 + inStr;
// 		}
// 		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M926.class);
// 		if(Tools.isNotBlank(custNo)){
// 			sql2 = sql2 +" and cust_no in ('"+custNo+"')";
// 		}
//
// 		//sql2 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		String sql =
// 				"select * from ( " + sql1 +
// 						"  union all \n" +
// 						sql2 + "  ) sql_ta ORDER BY BUSI_DATE desc ";
// 		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
// 	/*public SqlResult<M926> findBalaCustTransReq(SqlParam<M926> params) throws Exception {
// 		String custNo = params.getModel().getCustNo();
// 		params.getModel().setCustNo(null);
// 		String transOrgno = params.getModel().getTransOrgno();//所属机构
// 		params.getModel().setTransOrgno(null);
// 		String sql1 = "select t.app_serno,\n" +
// 				"       t.cust_name,\n" +
// 				"       t.cust_no,\n" +
// 				"       t.acct_no,\n" +
// 				"       t.trans_acct_no,\n" +
// 				"       t.ta_acct_no,\n" +
// 				"       t.app_amt,\n" +
// 				"       t.app_vol,\n" +
// 				"       t.trans_status,\n" +
// 				"       t.prod_code,\n" +
// 				"       t.prod_name,\n" +
// 				"       t.channel_flag,\n" +
// 				"       t.cust_manager,\n" +
// 				"       t.branch_code,\n" +
// 				"       t.busi_date,\n" +
// 				"       t.busi_code,\n" +
// 				"       to_char(t.mactime,'yyyymmdd') as macdate,\n" +
// 				"       to_char(t.mactime,'hh24miss') as mactime,\n" +
// 				"       t.trans_orgno,\n" +
// 				"       t.rtn_code,\n" +
// 				"       t.advance_income,\n" +
// 				"       t.rtn_desc,t.id_code,t.id_type\n" +
// 				"  from bala_cust_trans_req t where 1 = 1 and t.busi_code in ('022','024','098') ";
// 		if(Tools.isNotBlank(custNo)){
// 			sql1 = sql1 +" and cust_no in ('"+custNo+"')";
// 		}
// 		//所属机构
// 		if(Tools.isNotBlank(transOrgno)){
// 			String inStr = " and t.trans_orgno in (" ;
// 			String [] str = transOrgno.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sql1 = sql1 + inStr;
// 		}
// 		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(),M926.class);
// 		//sql1 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		String sql2 = " select t.app_serno,\n" +
// 				"       t.cust_name,\n" +
// 				"       t.cust_no,\n" +
// 				"       t.acct_no,\n" +
// 				"       t.trans_acct_no,\n" +
// 				"       t.ta_acct_no,\n" +
// 				"       t.app_amt,\n" +
// 				"       t.app_vol,\n" +
// 				"       t.trans_status,\n" +
// 				"       t.prod_code,\n" +
// 				"       t.prod_name,\n" +
// 				"       t.channel_flag,\n" +
// 				"       t.cust_manager,\n" +
// 				"       t.branch_code,\n" +
// 				"       t.busi_date,\n" +
// 				"       t.busi_code,\n" +
// 				"       to_char(t.mactime,'yyyymmdd') as macdate,\n" +
// 				"       to_char(t.mactime,'hh24miss') as mactime,\n" +
// 				"       t.trans_orgno,\n" +
// 				"       t.rtn_code,\n" +
// 				"       t.advance_income,\n" +
// 				"       t.rtn_desc,t.id_code,t.id_type\n" +
// 				"  from bala_cust_trans_req_h t where 1 = 1 and t.busi_code in ('022','024','098') ";
// 		//所属机构
// 		if(Tools.isNotBlank(transOrgno)){
// 			String inStr = " and t.trans_orgno in (" ;
// 			String [] str = transOrgno.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sql2 = sql1 + inStr;
// 		}
// 		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M926.class);
// 		if(Tools.isNotBlank(custNo)){
// 			sql2 = sql2 +" and cust_no in ('"+custNo+"')";
// 		}
//
// 		//sql2 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		String sql =
// 				"select * from ( " + sql1 +
// 						"  union all \n" +
// 						sql2 + "  ) sql_ta ORDER BY BUSI_DATE desc ";
// 		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
// 	}*/
//
//
//
// 	/**
// 	 * 查询TANO
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M926> findTano(SqlParam<M926> params) throws Exception {
// 		return super.findRows("SELECT DISTINCT(TANO) FROM bala_cust_sign bat ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
// }