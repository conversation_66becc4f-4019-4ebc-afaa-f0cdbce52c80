package com.kayak.bala.dao;
import com.kayak.bala.model.M918;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public class M918Dao extends ComnDao {
    public SqlResult<M918> findM918s(SqlParam<M918> params) throws Exception{
        StringBuffer sb = new StringBuffer();
        sb.append("select tano,\n" +
                " prod_code,\n" +
                " prod_name ,\n" +
                " ack_date,\n" +
                " sum(case when trans_order = '0' then total_amt else 0 end) as sub_amt,\n" +
                " sum(case when trans_order = '1' then total_amt else 0 end) as red_amt\n" +
                "from bala_transfer_ta \n" +
                "where transfer_status = '1' and payment_status = '1' ");
        if(StringUtils.isNotBlank(params.getModel().getAckDate())){
            sb.append(" and ack_date = "+params.getModel().getAckDate() + "\n");
        }
        if(StringUtils.isNotBlank(params.getModel().getTano())){
            sb.append(" and tano = "+params.getModel().getTano() + "\n");
        }
        sb.append("group by tano,prod_code,prod_name,ack_date order by ack_date desc ");
        List<M918> volList = super.findRows(M918.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        SqlResult<M918> sqlRowSqlResult = new SqlResult<>();
        sqlRowSqlResult.setResults(volList.size());
        sqlRowSqlResult.setRows(volList);
        sqlRowSqlResult.setDesensitized(false);
        return sqlRowSqlResult;
    }

    /**
     * 原sql语句，因为新版数据库不支持decode语句需要改造，改在下面  pc
     */
    /*public SqlResult<M918> sumM918(SqlParam<M918> params) throws Exception{
        StringBuffer sb = new StringBuffer();
        sb.append("  select '汇总' as ack_date, \n" +
                 "    '-' as    tano,\n" +
                "  '-' as  prod_code,\n" +
                "  '-' as  prod_name ,\n" +
                "  sum(decode(t.trans_order, '0', t.total_amt, 0)) as sub_amt,\n" +
                "  sum(decode(t.trans_order, '1', t.total_amt, 0)) as red_amt\n" +
                "   from bala_transfer_ta t\n" +
                "  where t.transfer_status = '1'\n" +
                "    and t.payment_status = '1' ");
        if(StringUtils.isNotBlank(params.getModel().getAckDate())){
            sb.append(" and ack_date = "+params.getModel().getAckDate() + "\n");
        }
        if(StringUtils.isNotBlank(params.getModel().getTano())){
            sb.append(" and tano = "+params.getModel().getTano() + "\n");
        }
        List<M918> volList = super.findRows(M918.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        SqlResult<M918> sqlRowSqlResult = new SqlResult<>();
        sqlRowSqlResult.setResults(volList.size());
        sqlRowSqlResult.setRows(volList);
        sqlRowSqlResult.setDesensitized(false);
        return sqlRowSqlResult;
    }*/

    public SqlResult<M918> sumM918(SqlParam<M918> params) throws Exception{
        StringBuffer sb = new StringBuffer();

        sb.append("  select '汇总' as ack_date,  \n" +
                        "    '-' as    tano,\n" +
                        "  '-' as  prod_code,\n" +
                        "  '-' as  prod_name ,\n" +
                        " sum(t.sub_amt ) as sub_amt, \n" +
                        " sum(t.red_amt) as red_amt\n" +
                "  from (select \n" +
                " transfer_status ,\n" +
                " payment_status ,\n" +
                " ack_date,\n" +
                " tano,\n" +
        "  case\n" +
                "  trans_order when '0' then total_amt else 0\n" +
       "   end as sub_amt,\n" +
        "  case\n" +
                "  trans_order when '1' then total_amt else 0\n" +
        "  end as red_amt\n" +
       "   from bala_transfer_ta) t\n" +
        "  where t.transfer_status = '1'\n" +
        "  and t.payment_status = '1' " );
        if(StringUtils.isNotBlank(params.getModel().getAckDate())){
            sb.append(" and ack_date = "+params.getModel().getAckDate() + "\n");
        }
        if(StringUtils.isNotBlank(params.getModel().getTano())){
            sb.append(" and tano = "+params.getModel().getTano() + "\n");
        }
        List<M918> volList = super.findRows(M918.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        SqlResult<M918> sqlRowSqlResult = new SqlResult<>();
        sqlRowSqlResult.setResults(volList.size());
        sqlRowSqlResult.setRows(volList);
        sqlRowSqlResult.setDesensitized(false);
        return sqlRowSqlResult;
    }

}
