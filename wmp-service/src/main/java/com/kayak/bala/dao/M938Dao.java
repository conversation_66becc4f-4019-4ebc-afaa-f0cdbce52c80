package com.kayak.bala.dao;
import com.kayak.bala.model.M938;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public class M938Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M938> findM938s(SqlParam<M938> params) throws Exception{
        params.setMakeSql(true);
        String sql = " select t.prod_code,\n" +
                "       t.ack_date,\n" +
                "       sum(t.TOTAL_AMT) as TOTAL_AMT,\n" +
                "       sum(t.TOTAL_OUT_AMT) as TOTAL_OUT_AMT,\n" +
                "       sum(t.TOTAL_REDEEM_AMT) as TOTAL_REDEEM_AMT,\n" +
                "       sum(t.TOTAL_OUT_AMT + t.TOTAL_REDEEM_AMT) as tot_out_amt,\n" +
                "       t2.prod_name\n" +
                "  from BALA_CUST_TRANS_STAT_BASE t\n" +
                "  left join bala_prod_base_info t2 on t.prod_code = t2.prod_code\n" +
                "                                  and t.tano = t2.tano where 1=1 ";
        sql = sql+
                "      group by t.prod_code, t.ack_date, t2.prod_name\n" +
                " order by t.ack_date desc nulls last ";
        /*sql = sql+reportformUtil.getOrgIdForOrgLevel("t.")+
                "      group by t.prod_code, t.ack_date, t2.prod_name\n" +
                " order by t.ack_date desc nulls last ";*/
        return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
    }
}
