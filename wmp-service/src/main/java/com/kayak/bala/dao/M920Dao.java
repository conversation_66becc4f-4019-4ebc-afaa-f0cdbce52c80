package com.kayak.bala.dao;

import com.kayak.bala.model.M920;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M518;
import com.kayak.system.dao.M006Dao;
import com.kayak.until.MakeSqlUntil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 底层账户流水
 */
@Repository
public class M920Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;
	/**
	 * 查询底层账户流水信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M920> findbalaCustAcctReq(SqlParam<M920> params) throws Exception {
		//params.setMakeSql(true);
		String sql1 ="select * from (select t.app_serno,\n" +
				"       t.cust_name,\n" +
				"       t.id_type,\n" +
				"       t.id_code,\n" +
				"       t.trans_acct_no,\n" +
				"       t.acct_no,\n" +
				"       t.ori_acct_no,\n" +
				"       t.busi_code,\n" +
				"       t.trans_status,\n" +
				"       t.channel_date,\n" +
				"       t.channel_time,\n" +
				"       t.rtn_code,\n" +
				"       t.tano,\n" +
				"       t.rtn_desc\n" +
				"  from bala_cust_acct_req t where 1=1 and t.busi_code in ('001','002','003','004','005','009','008') ";
		//sql1 = sql1 + reportformUtil.getOrgIdForOrgLevel("t.");
		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M920.class);
		String sql2 = "select * from (select t.app_serno,\n" +
				"       t.cust_name,\n" +
				"       t.id_type,\n" +
				"       t.id_code,\n" +
				"       t.trans_acct_no,\n" +
				"       t.acct_no,\n" +
				"       t.ori_acct_no,\n" +
				"       t.busi_code,\n" +
				"       t.trans_status,\n" +
				"       t.channel_date,\n" +
				"       t.channel_time,\n" +
				"       t.rtn_code,\n" +
				"       t.tano,\n" +
				"       t.rtn_desc\n" +
				"  from bala_cust_acct_req_h t where 1=1  and t.busi_code in ('001','002','003','004','005','009','008')";
		//sql2 = sql2 + reportformUtil.getOrgIdForOrgLevel("t.");
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M920.class);
		String sql =
				"select * from ( " + sql1 +
						" )xsql_tc union all \n" +
						sql2 + " ) xsql_ta ) xsql_tb order by channel_date desc";
		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER,params);
	}
}