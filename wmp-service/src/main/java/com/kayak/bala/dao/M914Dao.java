// package com.kayak.bala.dao;
// import com.kayak.bala.model.M914;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import org.apache.commons.lang.StringUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// import java.util.List;
// @Repository
// public class M914Dao extends ComnDao {
//     @Autowired
//     private ReportformUtil reportformUtil;
//
//     public SqlResult<M914> findM914s(SqlParam<M914> params) throws Exception{
//         params.setMakeSql(true);
//         StringBuffer sql = new StringBuffer();
//         sql.append( "select "+
//                 "               t.branch_code,\n" +
//                 "               t.sub_branch_code,\n" +
//                 "               t.ACK_DATE,\n" +
//                 "               sum(t.TOTAL_AMT) as TOTAL_AMT,\n" +
//                 "               sum(t.TOTAL_AUTO_AMT) as TOTAL_AUTO_AMT,\n" +
//                 "               sum(t.TOTAL_AMT + t.TOTAL_AUTO_AMT) as tot_in_amt,\n" +
//                 "               sum(t.TOTAL_OUT_AMT) as TOTAL_OUT_AMT,\n" +
//                 "               sum(t.TOTAL_REDEEM_AMT) as TOTAL_REDEEM_AMT,\n" +
//                 "               sum(t.TOTAL_OUT_AMT + t.TOTAL_REDEEM_AMT) as tot_out_amt\n" +
//                 "          from bala_cust_trans_stat t where 1=1 ");
//         //sql.append(reportformUtil.getOrgIdForOrgLevel("t."));
//         if(Tools.isNotBlank(params.getModel().getSubBranchCode())){
//             sql.append(" and t.sub_branch_code in ( ");
//             String [] str = params.getModel().getSubBranchCode().split(",");
//             if(str != null && str.length > 0){
//                 for(int i=0;i<str.length;i++){
//                     if(i != str.length -1){
//                         sql.append("'"+str[i].toString()+"',");
//                     }else{
//                         sql.append("'"+str[i].toString()+"'");
//                     }
//
//                 }
//             }
//             sql.append(" ) ");
//         }
//         sql.append( "        group by "+
//                 "               t.branch_code,\n" +
//                 "               t.sub_branch_code,\n" +
//                 "               t.ACK_DATE" +
//                 "               ORDER BY t.BRANCH_CODE,t.ACK_DATE desc ");
//         params.getModel().setSubBranchCode(null);
//         return super.findRows(sql.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
//     }
// }
