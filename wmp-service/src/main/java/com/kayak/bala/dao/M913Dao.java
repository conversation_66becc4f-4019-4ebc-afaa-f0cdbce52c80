package com.kayak.bala.dao;

import com.kayak.bala.model.M913;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class M913Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M913> findM913s(SqlParam<M913> params) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("select t.tano,t.prod_code,t.prod_name,t.stat_date,\n" +
                "  sum(t.prod_balance) as prod_balance,\n"+
                "  sum(t.cust_count) as cust_count\n"+
                "  from bala_report_base_prod_storage t where 1=1 ");
        //sb.append(reportformUtil.getOrgIdForOrgLevel("t."));
        if(StringUtils.isNotBlank(params.getModel().getStatDate())){
            sb.append(" and t.stat_date ="+params.getModel().getStatDate());
        }
        if(StringUtils.isNotBlank(params.getModel().getTano())){
            sb.append(" and t.tano ="+params.getModel().getTano());
        }
        if(StringUtils.isNotBlank(params.getModel().getProdCode())){
            sb.append(" and t.prod_code ="+params.getModel().getProdCode());
        }
        if(StringUtils.isNotBlank(params.getModel().getProdName())){
            sb.append(" and t.prod_name like '%"+params.getModel().getProdName()+"%'");
        }
        sb.append(" group by t.tano,t.prod_code,t.prod_name,t.stat_date order by t.stat_date desc \n");
        List<M913> volList = super.findRows(M913.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        SqlResult<M913> sqlResult = new SqlResult<>();
        sqlResult.setResults(volList.size());
        sqlResult.setRows(volList);
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}