package com.kayak.bala.dao;

import com.kayak.bala.model.M934;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.until.MakeSqlUntil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M934Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M934> findM934(SqlParam<M934> params) throws Exception {
        String sql1 = "select * from ( select prod_code,prod_name,busi_date, " +
                "SUM(CASE WHEN busi_code='198' THEN APP_VOL-ADVANCE_INCOME ELSE 0 END) AS total_app_amt \n" +
                "from bala_cust_trans_cfm where 1=1 AND busi_code='198' ";
        sql1 = sql1 + reportformUtil.getOrgIdForOrgLevel("") + " group by prod_code,prod_name,busi_date ";
        sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(),M934.class);
        String sql2 = "select * from ( select prod_code,prod_name,busi_date, " +
                "SUM(CASE WHEN busi_code='198' THEN APP_VOL-ADVANCE_INCOME ELSE 0 END) AS total_app_amt \n" +
                "from bala_cust_trans_cfm_h where 1=1 AND busi_code='198' ";
        sql2 = sql2 + reportformUtil.getOrgIdForOrgLevel("") + " group by prod_code,prod_name,busi_date ";
        sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M934.class);
        String sql =
                "select * from ( " + sql1 +
                        "\n ) union all \n" +
                        sql2 + ") ) ORDER BY busi_date desc nulls last ";
        return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
    }

    public SqlResult<M934> findProdCode(SqlParam<M934> params) throws Exception {
        String sql = "select distinct prod_code from bala_cust_trans_req";
        return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
    }
}
