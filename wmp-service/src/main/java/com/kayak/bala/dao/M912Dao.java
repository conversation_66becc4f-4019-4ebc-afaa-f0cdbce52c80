// package com.kayak.bala.dao;
// import com.kayak.bala.model.M912;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import org.apache.commons.lang.StringUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// import java.util.List;
// @Repository
// public class M912Dao extends ComnDao {
//
//     @Autowired
//     private ReportformUtil reportformUtil;
//
//     public SqlResult<M912> findM912s(SqlParam<M912> params) throws Exception{
//         params.setMakeSql(true);
//         String sql = "  select t.bala_code,\n" +
//                 "       t.ack_date,\n" +
//                 "       sum(t.TOTAL_AMT) as TOTAL_AMT,\n" +
//                 "       sum(t.TOTAL_AUTO_AMT) as TOTAL_AUTO_AMT,\n" +
//                 "       sum(t.TOTAL_AMT + t.TOTAL_AUTO_AMT) as tot_in_amt,\n" +
//                 "       sum(t.TOTAL_OUT_AMT) as TOTAL_OUT_AMT,\n" +
//                 "       sum(t.TOTAL_REDEEM_AMT) as TOTAL_REDEEM_AMT,\n" +
//                 "       sum(t.TOTAL_OUT_AMT + t.TOTAL_REDEEM_AMT) as tot_out_amt,\n" +
//                 "       t2.bala_name\n" +
//                 "  from bala_cust_trans_stat t\n" +
//                 "  left join bala_prod_info t2 on t.bala_code = t2.bala_code\n" +
//                 " where 1 = 1 ";
//         sql = sql+
//                 "    group by t.bala_code, t.ack_date,t2.bala_name order by t.ack_date desc ";
//
//         return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
//     }
// }
