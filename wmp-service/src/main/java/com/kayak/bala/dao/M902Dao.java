package com.kayak.bala.dao;

import com.kayak.bala.model.M902;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 余额理财底层产品维护
 */
@Repository
public class M902Dao extends ComnDao {

	/**
	 * 查询底层产品信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M902> findAccTaInfo(SqlParam<M902> params) throws Exception {
		params.setMakeSql(true);
		Sql sql = Sql.build()
				.mysqlSql("SELECT BALA_CODE, SYSTEM_NO, TANO, tano as old_tano, PROD_CODE, PROD_NAME, PROD_RISK_LEVEL, PROD_TYPE, PROD_LIMIT, PROD_STATUS, DATE_FORMAT(CREATE_TIME,'%Y-%m-%d %H:%i:%s') CREATE_TIME FROM BALA_PROD_BASE_INFO ");
		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
	 * 修改底层产品信息
	 * @param params
	 * @throws Exception
	 */
	public void update(SqlParam<M902> params) throws Exception {
		M902 newM902 = params.getModel();
		Sql sql = Sql.build()
				.mysqlSql("UPDATE BALA_PROD_BASE_INFO  SET prod_limit=$S{prodLimit}, PROD_RISK_LEVEL=$S{prodRiskLevel}, PROD_TYPE=$S{prodType}, UPDATE_TIME=now(), tano = $S{tano} WHERE SYSTEM_NO=$S{systemNo} AND TANO=$S{oldTano} AND PROD_CODE=$S{prodCode} ");
		doTrans(() -> {
			super.update(sql, SubDatabase.DATABASE_BALA_CENTER, newM902);
		});
	}

	/**
	 * 修改底层产品状态
	 */
	public void updateProdStatus(M902 newM902) throws Exception {
		doTrans(() -> {
			super.update("UPDATE BALA_PROD_BASE_INFO SET PROD_STATUS = $S{prodStatus} WHERE bala_code=$S{balaCode}  AND SYSTEM_NO=$S{systemNo} AND TANO=$S{tano} AND PROD_CODE=$S{prodCode} ", SubDatabase.DATABASE_BALA_CENTER, newM902);
		});
	}

	public void add(M902 newM902) throws Exception {
		Sql sql = Sql.build()
				.mysqlSql("INSERT INTO BALA_PROD_BASE_INFO (SYSTEM_NO, TANO, PROD_CODE, BALA_CODE, PROD_NAME, PROD_RISK_LEVEL, PROD_TYPE, PROD_LIMIT, PROD_STATUS, CREATE_TIME, UPDATE_TIME) " +
						" VALUES ($S{prodType}, $S{tano}, $S{prodCode}, $S{balaCode}, $S{prodName}, $S{prodRiskLevel}, $S{prodType}, $S{prodLimit}, $S{prodStatus}, now() , now() ) ");
		doTrans(() -> {
			super.update(sql, SubDatabase.DATABASE_BALA_CENTER, newM902);
		});
	}

	public UpdateResult delete(M902 newM902) throws Exception {
		Sql sql = Sql.build()
				.mysqlSql("delete FROM BALA_PROD_BASE_INFO WHERE prod_code = $S{prodCode} ");

			return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, newM902);
	}

	/**
	 * 查询TANO
	 * @throws Exception
	 */
	public SqlResult<M902> findTano(SqlParam<M902> params) throws Exception {
		return super.findRows("SELECT DISTINCT(TANO) FROM BALA_PROD_BASE_INFO  ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
	 * 查询TANO
	 * @throws Exception
	 */
	public SqlResult<M902> findProdCode(SqlParam<M902> params) throws Exception {
		return super.findRows("SELECT DISTINCT(PROD_CODE) FROM BALA_PROD_BASE_INFO  ", SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
	 * 查询底层产品净值信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M902> findNav(SqlParam<M902> params) throws Exception {
		return super.findRows("SELECT BPN.SYSTEM_NO, BPN.TANO,bpbi.PROD_NAME ,bpbi.PROD_TYPE ," +
				"BPN.PROD_CODE, BPN.NAV_DATE, BPN.NAV, BPN.SEVEN_DAY_ANNUALIZED_YIELD, " +
				"BPN.REVENUE_PER_TEN_KILO_UNITS, BPN.TOTAL_INCOME " +
				"FROM BALA_PROD_NAV bpn LEFT JOIN BALA_PROD_BASE_INFO bpbi ON BPN.PROD_CODE = BPBI.PROD_CODE " +
				" AND BPN.TANO = BPBI.TANO AND BPN.SYSTEM_NO = BPBI.SYSTEM_NO " +
				"WHERE BPN.PROD_CODE = $S{prodCode} AND BPN.TANO = $S{tano} AND BPN.SYSTEM_NO = $S{systemNo} ORDER BY BPN.NAV_DATE DESC", SubDatabase.DATABASE_BALA_CENTER, params);
	}


	/**
	 * <AUTHOR>
	 * @Description 根据balaCode查询底层产品最高风险等级
	 * @Date 2022/2/9
	 * @Param [params]
	 * @return com.kayak.core.sql.SqlResult<com.kayak.bala.model.M902>
	 **/
	public SqlResult<M902> findRiskLevelByBalaCode(SqlParam<M902> params) throws Exception {
		StringBuffer sb=new StringBuffer();
		sb.append(" SELECT ");
		sb.append(" ( SELECT max(PROD_RISK_LEVEL) FROM BALA_PROD_BASE_INFO where 1=1 and BALA_CODE=( " +
				" SELECT bala_code " +
				" FROM BALA_PROD_BASE_INFO where 1=1 ");
		if(Tools.isNotBlank(params.getModel().getSystemNo())){
			sb.append(" and SYSTEM_NO = $S{systemNo} ");
		}
		if(Tools.isNotBlank(params.getModel().getTano())){
			sb.append(" and TANO = $S{tano} ");
		}
		if(Tools.isNotBlank(params.getModel().getProdCode())){
			sb.append(" and PROD_CODE = $S{prodCode} ");
		}
		sb.append(" )) as PROD_RISK_LEVEL ,BALA_CODE FROM BALA_PROD_BASE_INFO WHERE 1=1 ");
		if(Tools.isNotBlank(params.getModel().getSystemNo())){
			sb.append(" and SYSTEM_NO = $S{systemNo} ");
		}
		if(Tools.isNotBlank(params.getModel().getTano())){
			sb.append(" and TANO = $S{tano} ");
		}
		if(Tools.isNotBlank(params.getModel().getProdCode())){
			sb.append(" and PROD_CODE = $S{prodCode} ");
		}
		return super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
	 * <AUTHOR>
	 * @Description 获取余额TA List
	 * @Date 2022/2/22
	 * @Param [params]
	 * @return java.util.List<java.util.Map>
	 **/
	public List<Map> findTanoList(SqlParam<M902> params) throws Exception {
		return super.findRows(Map.class,"SELECT DISTINCT(TANO) as no  FROM BALA_PROD_BASE_INFO  ",SubDatabase.DATABASE_BALA_CENTER,null);
	}

	/**
	 * <AUTHOR>
	 * @Description 获取余额产品代码 List
	 * @Date 2022/2/22
	 * @Param [params]
	 * @return com.kayak.core.sql.SqlResult<com.kayak.bala.model.M902>
	 **/
	public SqlResult<M902> findBalaProdCode(SqlParam<M902> params) throws Exception {

		StringBuffer sb=new StringBuffer("SELECT DISTINCT PROD_CODE,PROD_NAME FROM BALA_PROD_BASE_INFO  where 1=1 ");
		if(Tools.isNotBlank(params.getModel().getProdCode())){
			sb.append(" and ( PROD_CODE like '"+params.getModel().getProdCode()+"%' or  PROD_NAME like '%"+params.getModel().getProdCode()+"%' ) ");
		}
		if(Tools.isNotBlank(params.getModel().getTano())){
			sb.append(" and  tano = '"+params.getModel().getTano()+ "' ");
		}
		params.getModel().setProdCode(null);
		SqlResult<M902> sqlResult=super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);

		return sqlResult;
	}

	/**
	 * <AUTHOR>
	 * @Description 获取余额产品名称
	 * @Date 2022/2/22
	 * @Param [prodCode]
	 * @return java.lang.String
	 **/
	public String getBalaProdName(String prodCode) throws  Exception{
		String sql = "SELECT PROD_NAME   FROM BALA_PROD_BASE_INFO where 1=1 " ;
		if(Tools.isNotBlank(prodCode)){
			sql += " and prod_code = '"+prodCode+"' ";
		}
		SqlRow row = super.findRow(sql, SubDatabase.DATABASE_BALA_CENTER,null);
		String prodName="";//产品名称
		if (row != null && row.size() >= 1 && StringUtils.isNotBlank(row.getString("prod_name"))){
			prodName=row.getString("prod_name");
		}
		return prodName;
	}


	public String getSystemNoByProdCode(String balaCode, String prodCode) throws Exception {
		String sql = "SELECT system_no FROM BALA_PROD_BASE_INFO WHERE 1=1 " ;
		if(Tools.isNotBlank(balaCode)){
			sql += " and bala_code = '"+balaCode+"' ";
		}
		if(Tools.isNotBlank(prodCode)){
			sql += " and prod_code = '"+prodCode+"' ";
		}
		SqlRow row = super.findRow(sql, SubDatabase.DATABASE_BALA_CENTER,null);
		String systemNo="";   //系统编号
		if (row != null && row.size() >= 1 && StringUtils.isNotBlank(row.getString("system_no"))){
			systemNo = row.getString("system_no");
		}
		return systemNo;
	}

}