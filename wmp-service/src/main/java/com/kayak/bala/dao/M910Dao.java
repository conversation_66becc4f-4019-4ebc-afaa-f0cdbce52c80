package com.kayak.bala.dao;

import com.kayak.bala.model.M910;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import org.springframework.stereotype.Repository;

@Repository
public class M910Dao extends ComnDao {
    public SqlResult<M910> findM910s(SqlParam<M910> params) throws Exception {
        return super.findRows("SELECT " +
                " system_no,tano,bala_code,ack_date,transfer_status," +
                " transfer_date,trans_order,out_acct_no,in_acct_no," +
                " total_amt,fee_amt,actual_amt,payment_status,payment_serno, remark " +
                " FROM bala_transfer_ta order by ack_date desc", SubDatabase.DATABASE_BALA_CENTER, params);
    }

    public UpdateResult updateM910s(SqlParam<M910> params) throws Exception {
        String sql = "update bala_transfer_ta set payment_status = $S{paymentStatus} where system_no = $S{systemNo} " +
                " and tano = $S{tano} and ack_date = $S{ackDate} and trans_order = $S{transOrder}";
        return super.update(sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel());
    }
}
