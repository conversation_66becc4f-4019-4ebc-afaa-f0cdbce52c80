package com.kayak.bala.dao;

import com.kayak.bala.model.M904;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 银行账户维护
 */
@Repository
public class M904Dao extends ComnDao {

	/**
	 * 查询银行账户信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M904> findAccInfo(SqlParam<M904> params) throws Exception {

		String orgno = params.getModel().getOrgno();
		params.getModel().setOrgno(null);
		StringBuffer sb=new StringBuffer();
		sb.append("SELECT ACCT_SERNO, ACCT_NAME, ACCT_TYPE, ACCT_NO, BANK_ACCT_NAME,\n" +
				"BANK_NAME, IS_INTERNAL, ORGNO, CONTACT_NAME, TELNO,settlement_type,CUR,\n" +
				"HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,INTER_CODE \n" +
				"FROM BALA_ACCT_INFO where 1=1 ");
		if(Tools.isNotBlank(orgno)){
			String inStr = " and ORGNO in (" ;
			String [] str = orgno.split(",");
			if(str != null && str.length > 0){
				for(int i=0;i<str.length;i++){
					if (i == str.length - 1){
						inStr += "'"+str[i]+"')";
					}else {
						inStr += "'"+str[i]+"',";
					}
				}
			}
			sb.append(inStr);
		}
		params.setMakeSql(true);
		return super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
     * 修改银行账户信息
	 * @param params
     * @throws Exception
	 */
	public void update(SqlParam<M904> params) throws Exception {
		M904 newM904 = params.getModel();
		Sql sql = Sql.build()
				.mysqlSql("UPDATE BALA_ACCT_INFO  SET ACCT_NAME=$S{acctName}, ACCT_TYPE=$S{acctType}, ACCT_NO=$S{acctNo}, BANK_ACCT_NAME=$S{bankAcctName}," +
					" BANK_NAME=$S{bankName}, IS_INTERNAL=$S{isInternal}, ORGNO=$S{orgno}, CONTACT_NAME=$S{contactName}," +
					" TELNO=$S{telno},  UPDATE_TIME=now(),settlement_type = $S{settlementType},CUR=$S{cur},HOST_PROD_TYPE=$S{hostProdType} ,HOST_ACCT_SEQ_NO=$S{hostAcctSeqNo} ,HOST_ACCT_TYPE=$S{hostAcctType} ,HOST_ORGNO=$S{hostOrgno} ,INTER_CODE=$S{interCode} " +
					"WHERE ACCT_SERNO=$S{acctSerno} ");
		doTrans(() -> {
			super.update(sql, SubDatabase.DATABASE_BALA_CENTER, newM904);
		});
	}

	/**
	 * 删除银行账户信息
	 * @param params
	 * @throws Exception
	 */
	public void delAccInfo(SqlParam<M904> params) throws Exception {
		M904 newM904 = params.getModel();
		doTrans(() -> {
			super.update("DELETE FROM BALA_ACCT_INFO " +
					"WHERE ACCT_SERNO=$S{acctSerno} ",SubDatabase.DATABASE_BALA_CENTER, newM904);
		});
	}

	/**
	 * 新增银行账户信息
	 * @param params
	 * @throws Exception
	 */
	public void addAccInfo(SqlParam<M904> params) throws Exception {
		M904 newM904 = params.getModel();
		Sql sql = Sql.build()
				.mysqlSql("INSERT INTO BALA_ACCT_INFO  (ACCT_SERNO, ACCT_NAME, ACCT_TYPE, ACCT_NO, BANK_ACCT_NAME, BANK_NAME, IS_INTERNAL, ORGNO, CONTACT_NAME, TELNO, CREATE_TIME, UPDATE_TIME,settlement_type,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,INTER_CODE) " +
						" VALUES ($S{acctSerno}, $S{acctName}, $S{acctType}, $S{acctNo}, $S{bankAcctName}, $S{bankName}, $S{isInternal}, $S{orgno}, $S{contactName}, $S{telno}, now() , now(),$S{settlementType},$S{cur},$S{hostProdType},$S{hostAcctSeqNo},$S{hostAcctType},$S{hostOrgno},$S{interCode} ) ");
		doTrans(() -> {
			super.update( sql ,SubDatabase.DATABASE_BALA_CENTER, newM904);
		});
	}

	/**
	 * <AUTHOR>
	 * @Description 根据账户类别和银行账号查询银行账户信息
	 * @Date 2022/2/8
	 * @Param [params]
	 * @return com.kayak.core.sql.SqlResult<com.kayak.bala.model.M904>
	 **/
	public SqlResult<M904> findAccInfoByAcct(SqlParam<M904> params) throws Exception {

		StringBuffer sb=new StringBuffer();
		sb.append("SELECT ACCT_SERNO, ACCT_NAME, ACCT_TYPE, ACCT_NO, BANK_ACCT_NAME, " +
				"BANK_NAME, IS_INTERNAL, ORGNO, CONTACT_NAME, TELNO,settlement_type,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,INTER_CODE " +
				"FROM BALA_ACCT_INFO where 1=1 ");
		if(Tools.isNotBlank(params.getModel().getAcctType())){
			sb.append(" and ACCT_TYPE = $S{acctType} ");
		}
		if(Tools.isNotBlank(params.getModel().getAcctNo())){
			sb.append(" and ACCT_NO = $S{acctNo} ");
		}
		return super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
	}

	/**
	 * <AUTHOR>
	 * @Description 银行账户信息表，所属机构改为合并机构
	 * @Date 2022/3/24
	 * @Param [params]
	 * @return com.kayak.core.sql.UpdateResult
	 **/
	public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
		return super.update("UPDATE BALA_ACCT_INFO SET ORGNO = $S{mergeOrgno} WHERE ORGNO = $S{removeOrgno} ",
				SubDatabase.DATABASE_BALA_CENTER,params);
	}

}