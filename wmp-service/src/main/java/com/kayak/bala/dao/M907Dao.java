// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M907;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.core.sql.*;
// import org.springframework.stereotype.Repository;
// import org.springframework.util.CollectionUtils;
//
// import java.util.List;
//
// @Repository
// public class M907Dao extends ComnDao {
//
//     public SqlResult<M907> find(SqlParam<M907> params) throws Exception {
//         return super.findRows("SELECT * FROM sys_param ", SubDatabase.DATABASE_BALA_CENTER,params);
//     }
//
//     public void update(List<M907> params) throws Exception {
//         if (CollectionUtils.isEmpty(params)) {
//             return;
//         }
//         doTrans(() -> {
//             for (M907 p : params) {
//                 super.update("UPDATE sys_param SET paravalue = $S{paravalue} WHERE paraid = $S{paraid}", SubDatabase.DATABASE_BALA_CENTER,p);
//             }
//         });
//     }
//
//     //修改
//     public UpdateResult updateSysParam(SqlParam<M907> params) throws Exception {
//         return super.update("UPDATE SYS_PARAM SET paravalue=$S{paravalue} ,paraname=$S{paraname}   WHERE  moduleid=$S{moduleid} and paraid=$S{paraid}",
//                 SubDatabase.DATABASE_BALA_CENTER,params.getModel());
//     }
//
//     /**
//      * 查询某个系统参数
//      *
//      * @param params
//      * @return
//      * @throws Exception
//      */
//     public M907 findOne(SqlParam<M907> params) throws Exception {
//         // oracle 不支持limit 1
//         String sql = "SELECT paravalue ,paraname ,paraid ,moduleid ,isdisplay ,groupparaid ,graphql ,functype ,fieldtype ,execaction ,dict ,confoption FROM sys_param";
//         SqlResult<M907> row = super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
//         if (row != null && row.getRows().size() >= 1) {
//             return row.getRows().get(0);
//         }
//         return null;
//     }
// }
//
