// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M917;
// import com.kayak.bala.model.M932;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import com.kayak.graphql.model.FetcherData;
// import com.kayak.system.dao.M001Dao;
// import com.kayak.system.model.M001;
// import org.apache.commons.collections4.CollectionUtils;
// import org.apache.commons.lang.StringUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
//
// @Repository
// public class M932Dao extends ComnDao {
//
// 	@Autowired
// 	private M001Dao m001Dao;
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	public SqlResult<M932> findBalaCustSign(SqlParam<M932> params) throws Exception {
// 		params.setMakeSql(false);
// 		StringBuffer sb = new StringBuffer();
// 		sb.append("select t.branch_code,\n" );
// 				//----在时间段内的签约----
// 				sb.append("       SUM(CASE WHEN t.sign_status = '1' \n" );
// 				if(StringUtils.isNotBlank(params.getModel().getStartDate())){
// 					sb.append(" and t.sign_date >= "+params.getModel().getStartDate());
// 				}
// 				if(StringUtils.isNotBlank(params.getModel().getEndDate())){
// 					sb.append(" and t.sign_date <= "+params.getModel().getEndDate());
// 				}
// 				sb.append(" THEN 1 ELSE 0 END) AS qy_sign_status, ");
// 				//----在时间段内的解约----
// 				sb.append("       SUM(CASE WHEN t.sign_status = '0' \n" );
// 				if(StringUtils.isNotBlank(params.getModel().getStartDate())){
// 					sb.append(" and t.CANCEL_DATE >= "+params.getModel().getStartDate());
// 				}
// 				if(StringUtils.isNotBlank(params.getModel().getEndDate())){
// 					sb.append(" and t.CANCEL_DATE <= "+params.getModel().getEndDate());
// 				}
// 				sb.append(" THEN 1 ELSE 0 END) AS jy_sign_status, ");
// 				//----在时间段内的自动转入签约----
// 				sb.append("       SUM(CASE WHEN t.auto_status = '1' \n" );
// 				if(StringUtils.isNotBlank(params.getModel().getStartDate())){
// 					sb.append(" and t.AUTO_SIGN_DATE >= "+params.getModel().getStartDate());
// 				}
// 				if(StringUtils.isNotBlank(params.getModel().getEndDate())){
// 					sb.append(" and t.AUTO_SIGN_DATE <= "+params.getModel().getEndDate());
// 				}
// 				sb.append(" THEN 1 ELSE 0 END) AS qy_auto_status, ");
// 				//----在时间段内的自动转入解约----
// 				sb.append("       SUM(CASE WHEN t.auto_status = '0' \n" );
// 				if(StringUtils.isNotBlank(params.getModel().getStartDate())){
// 					sb.append(" and t.AUTO_CANCEL_DATE >= "+params.getModel().getStartDate());
// 				}
// 				if(StringUtils.isNotBlank(params.getModel().getEndDate())){
// 					sb.append(" and t.AUTO_CANCEL_DATE <= "+params.getModel().getEndDate());
// 				}
// 				sb.append(" THEN 1 ELSE 0 END) AS jy_auto_status \n");
// 				sb.append("  from bala_cust_sign t\n" );
// 				sb.append("  where 1 = 1 ");
// 		//sb.append(reportformUtil.getOrgIdForOrgLevel("t."));
//
// 		if(Tools.isNotBlank(params.getModel().getBelongArea())){
// 			sb.append(" and t.BRANCH_CODE in ( ");
// 			String [] str = params.getModel().getBelongArea().split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if(i != str.length -1){
// 						sb.append("'"+str[i].toString()+"',");
// 					}else{
// 						sb.append("'"+str[i].toString()+"'");
// 					}
//
// 				}
// 			}
// 			sb.append(" ) ");
// 		}
//
// 		sb.append(" group by t.branch_code ");
// 		List<M932> volList = super.findRows(M932.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
//
// 		//获取四项客户数都为0的数据
// 		List<M932> zeroList=new ArrayList<>();
// 		if(CollectionUtils.isNotEmpty(volList)){
// 			volList.stream().map(item->{
//
// 				//移除合计0的数据
// 				if (Double.valueOf(item.getQySignStatus()) == 0 && Double.valueOf(item.getJySignStatus()) ==0 &&
// 						Double.valueOf(item.getQyAutoStatus()) == 0 && Double.valueOf(item.getJyAutoStatus()) == 0) {
// 					zeroList.add(item);
// 				}else{
// 					try {
// 						//翻译签约卡开户区域
// 						if(org.apache.commons.lang.StringUtils.isNotBlank(item.getBranchCode())){
// 							Map<String,Object> map = new HashMap<>();
// 							map.put("orgno",item.getBranchCode());
// 							SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
// 							SqlResult<M001> m001Info = m001Dao.find(dateParams);
// 							if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
// 								item.setBranchName(m001Info.getRows().get(0).getOrgname());
// 							}
// 						}
// 					} catch (Exception e) {
// 						log.error(e.getMessage());
// 					}
// 				}
// 				return item;
// 			}).collect(Collectors.toList());
// 		}
//
// 		//移除四项客户数都为0的数据
// 		volList.removeAll(zeroList);
//
// 		SqlResult<M932> sqlRowSqlResult = new SqlResult<>();
// 		sqlRowSqlResult.setResults(volList.size());
// 		sqlRowSqlResult.setRows(volList);
// 		// 手动分页
// 		int count = volList.size();
// 		int start = params.getStart();
// 		int limit = params.getLimit();
// 		if (limit == 0){
// 			sqlRowSqlResult.setRows(volList);
// 		}else {
// 			sqlRowSqlResult.setRows(volList.subList(start, Math.min(start + limit, count)));
// 		}
// 		sqlRowSqlResult.setResults(count);
// 		sqlRowSqlResult.setDesensitized(false);
//
// 		return sqlRowSqlResult;
//
// 	}
// }