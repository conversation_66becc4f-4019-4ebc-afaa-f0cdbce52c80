// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M926;
// import com.kayak.bala.model.M941;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import com.kayak.until.MakeSqlUntil;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// /**
//  * 源泉宝交易流水
//  */
// @Repository
// public class M941Dao extends ComnDao {
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	/**
// 	 * 源泉宝交易流水查询
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M941> findBalaCustHostTransLog(SqlParam<M941> params) throws Exception {
// 		params.setMakeSql(false);
// 		String custNo = params.getModel().getCustNo();
// 		params.getModel().setCustNo(null);
// 		String branchCode = params.getModel().getBranchCode();//所属区域
// 		params.getModel().setBranchCode(null);
// 		String subBranchCode = params.getModel().getSubBranchCode();//所属机构
// 		params.getModel().setSubBranchCode(null);
// 		StringBuffer sb3 =new StringBuffer();
// 		String hostTransSerno=params.getModel().getHostTransSerno();//交易流水号
// 		String transAmtMin=params.getModel().getTransAmtMin();//起始金额
// 		String transAmtMax=params.getModel().getTransAmtMax();//最高金额
// 		String transDate=params.getModel().getTransDate();//交易日期
// 		String transEndDate=params.getModel().getTransEndDate();//结束日期
// 		sb3.append( " select * from ( SELECT t.APP_SERNO AS HOST_TRANS_SERNO,\n" +
// 				"               t.CUST_NO,\n" +
// 				"               t.ACCT_NO,\n" +
// 				"               t.TRANS_ACCT_NO,\n" +
// 				"               t.BUSI_CODE AS TRANS_TYPE,\n" +
// 				"               t.APP_AMT AS TRANS_AMT,\n" +
// 				"               t.TRANS_STATUS,\n" +
// 				"               t.PROD_CODE AS BALA_CODE,\n" +
// 				"               t.CHANNEL_FLAG,\n" +
// 				"               t.CUST_MANAGER,\n" +
// 				"               t.BRANCH_CODE,\n" +
// 				"               t.SUB_BRANCH_CODE,\n" +
// 				"               t.BUSI_DATE as TRANS_DATE,\n" +
// 				"               TO_CHAR(t.mactime, 'hh24miss') AS TRANS_TIME,\n" +
// 				"               t.TRANS_ORGNO,\n" +
// 				"               t.HOST_RTN_CODE,\n" +
// 				"               t.HOST_RTN_DESC,\n" +
// 				"               t.TRANS_STATUS AS ACK_STATUS,\n" +
// 				"               t.REMARK,\n" +
// 				"               t2.cust_name,\n" +
// 				"               t2.id_type,\n" +
// 				"               t2.id_code,\n" +
// 				"               t.prod_name as bala_name\n" +
// 				"          FROM bala_cust_trans_req t\n" +
// 				"          left join BALA_CUST_SIGN t2 on t.cust_no = t2.cust_no\n" +
// 				"         WHERE t.BUSI_CODE = '909' ");
// 		if(Tools.isNotBlank(hostTransSerno)){
// 			sb3.append(" and t.APP_SERNO = '"+hostTransSerno+"'");
// 			params.getModel().setHostTransSerno(null);
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransType())){
// 			sb3.append(" and t.busi_code = '"+params.getModel().getTransType()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getSubBranchCode())){
// 			sb3.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
//
// 		}
// 		if(Tools.isNotBlank(transAmtMin)){
// 			sb3.append(" and t.app_amt >= '"+transAmtMin+"'");
// 			params.getModel().setTransAmtMin(null);
// 		}
// 		if(Tools.isNotBlank(transAmtMax)){
// 			sb3.append(" and t.app_amt <= '"+transAmtMax+"'");
// 			params.getModel().setTransAmtMax(null);
// 		}
// 		if(Tools.isNotBlank(transDate)){
// 			sb3.append(" and t.BUSI_DATE >="+transDate);
// 			params.getModel().setTransDate(null);
// 		}
// 		if(Tools.isNotBlank(transEndDate)){
// 			sb3.append(" and t.BUSI_DATE <="+transEndDate);
// 			params.getModel().setTransEndDate(null);
// 		}
// 		//所属区域
// 		if(Tools.isNotBlank(branchCode)){
// 			String inStr = " and t.BRANCH_CODE in (" ;
// 			String [] str = branchCode.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sb3.append(inStr);
// 		}
// 		//所属机构
// 		if(Tools.isNotBlank(subBranchCode)){
// 			String inStr = " and t.SUB_BRANCH_CODE in (" ;
// 			String [] str = subBranchCode.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sb3.append(inStr);
// 		}
// /*		if(Tools.isNotBlank(params.getModel().getBranchCode())){
// 			sb3.append(" and t.branch_code = '"+params.getModel().getBranchCode()+"'");
// 		}*/
// 		if(Tools.isNotBlank(params.getModel().getAckStatus())){
// 			sb3.append(" and t.TRANS_STATUS = '"+params.getModel().getAckStatus()+"'");
// 		}
// 		String sql3 = MakeSqlUntil.makeSql(sb3.toString(),params.getParams(), M941.class);
// 		//sql3 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		if(Tools.isNotBlank(custNo)){
// 			sql3 = sql3 + " and t.cust_no in ('"+custNo+"')" ;
// 		}
// 		StringBuffer sb4 = new StringBuffer();
// 		sb4.append(" select * from (  SELECT t.APP_SERNO AS HOST_TRANS_SERNO,\n" +
// 				"               t.CUST_NO,\n" +
// 				"               t.ACCT_NO,\n" +
// 				"               t.TRANS_ACCT_NO,\n" +
// 				"               t.BUSI_CODE AS TRANS_TYPE,\n" +
// 				"               t.APP_AMT AS TRANS_AMT,\n" +
// 				"               t.TRANS_STATUS,\n" +
// 				"               t.PROD_CODE AS BALA_CODE,\n" +
// 				"               t.CHANNEL_FLAG,\n" +
// 				"               t.CUST_MANAGER,\n" +
// 				"               t.BRANCH_CODE,\n" +
// 				"               t.SUB_BRANCH_CODE,\n" +
// 				"               t.BUSI_DATE as TRANS_DATE,\n" +
// 				"               TO_CHAR(t.mactime, 'hh24:mi:ss') AS TRANS_TIME,\n" +
// 				"               t.TRANS_ORGNO,\n" +
// 				"               t.HOST_RTN_CODE,\n" +
// 				"               t.HOST_RTN_DESC,\n" +
// 				"               t.TRANS_STATUS AS ACK_STATUS,\n" +
// 				"               t.REMARK,\n" +
// 				"               t2.cust_name,\n" +
// 				"               t2.id_type,\n" +
// 				"               t2.id_code,\n" +
// 				"               t.prod_name as bala_name\n" +
// 				"          FROM bala_cust_trans_req_h t\n" +
// 				"          left join BALA_CUST_SIGN t2 on t.cust_no = t2.cust_no\n" +
// 				"         WHERE t.BUSI_CODE = '909'");
// 		if(Tools.isNotBlank(hostTransSerno)){
// 			sb4.append(" and t.APP_SERNO = '"+hostTransSerno+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransType())){
// 			sb4.append(" and t.busi_code = '"+params.getModel().getTransType()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getSubBranchCode())){
// 			sb4.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
// 		}
// 		if(Tools.isNotBlank(transAmtMin)){
// 			sb4.append(" and t.app_amt >= '"+transAmtMin+"'");
// 		}
// 		if(Tools.isNotBlank(transAmtMax)){
// 			sb4.append(" and t.app_amt <= '"+transAmtMax+"'");
// 		}
// 		if(Tools.isNotBlank(transDate)){
// 			sb4.append(" and t.BUSI_DATE >="+transDate);
// 		}
// 		if(Tools.isNotBlank(transEndDate)){
// 			sb4.append(" and t.BUSI_DATE <="+transEndDate);
// 		}
// 		//所属区域
// 		if(Tools.isNotBlank(branchCode)){
// 			String inStr = " and t.BRANCH_CODE in (" ;
// 			String [] str = branchCode.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sb4.append(inStr);
// 		}
// 		//所属机构
// 		if(Tools.isNotBlank(subBranchCode)){
// 			String inStr = " and t.SUB_BRANCH_CODE in (" ;
// 			String [] str = subBranchCode.split(",");
// 			if(str != null && str.length > 0){
// 				for(int i=0;i<str.length;i++){
// 					if (i == str.length - 1){
// 						inStr += "'"+str[i]+"')";
// 					}else {
// 						inStr += "'"+str[i]+"',";
// 					}
// 				}
// 			}
// 			sb4.append(inStr);
// 		}
// 		if(Tools.isNotBlank(params.getModel().getAckStatus())){
// 			sb4.append(" and t.TRANS_STATUS = '"+params.getModel().getAckStatus()+"'");
// 		}
// 		String sql4 = MakeSqlUntil.makeSql(sb4.toString(),params.getParams(),M941.class);
// 		//sql4 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		if(Tools.isNotBlank(custNo)){
// 			sql4 = sql4 + " and t.cust_no in ('"+custNo+"')" ;
// 		}
// 		/**StringBuffer sb5 = new StringBuffer();
// 		sb5.append("  SELECT t.TRANS_SERNO,\n" +
// 				"        t.CUST_NO,\n" +
// 				"        t.ACCT_NO,\n" +
// 				"        t.TRANS_ACCT_NO,\n" +
// 				"        t.TRANS_TYPE,\n" +
// 				"        t.AUTO_AMT AS TRANS_AMT,\n" +
// 				"        t.TRANS_STATUS,\n" +
// 				"        t.bala_code,\n" +
// 				"        t.CHANNEL_FLAG,\n" +
// 				"        t.REFERRER as CUST_MANAGER,\n" +
// 				"        t.BRANCH_CODE,\n" +
// 				"        t.SUB_BRANCH_CODE,\n" +
// 				//"		 to_date(t.TRANS_DATE,'yyyyMMdd') as TRANS_DATE,\n" +
// 				"               t.TRANS_DATE as TRANS_DATE,\n" +
// 				"        t.TRANS_TIME,\n" +
// 				"        t.TRANS_ORGNO,\n" +
// 				"        t.HOST_RTN_CODE,\n" +
// 				"        t.HOST_RTN_DESC,\n" +
// 				"        t.TRANS_STATUS AS ACK_STATUS,\n" +
// 				"        '' as REMARK\n" +
// 				"   FROM bala_cust_sign_trans_log t where (t.trans_type='9' or t.trans_type='B') ");
// 		if(Tools.isNotBlank(params.getModel().getHostTransSerno())){
// 			sb5.append(" and t.TRANS_SERNO = '"+params.getModel().getHostTransSerno()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransType())){
// 			sb5.append(" and t.TRANS_TYPE = '"+params.getModel().getTransType()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getSubBranchCode())){
// 			sb5.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransAmtMin())){
// 			sb5.append(" and t.AUTO_AMT >= '"+params.getModel().getTransAmtMin()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransAmtMax())){
// 			sb5.append(" and t.AUTO_AMT <= '"+params.getModel().getTransAmtMax()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransDate())){
// 			sb5.append(" and t.TRANS_DATE >="+params.getModel().getTransDate());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransEndDate())){
// 			sb5.append(" and t.TRANS_DATE <="+params.getModel().getTransEndDate());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getBranchCode())){
// 			sb3.append(" and t.branch_code = '"+params.getModel().getTransEndDate()+"'");
// 		}
// 		String sql5 = MakeSqlUntil.makeSql(sb5.toString(),params.getParams(),M926.class);
// 		sql5 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		if(Tools.isNotBlank(custNo)){
// 			sql5 = sql5 + " and t.cust_no in ('"+custNo+"')" ;
// 		}
//
// 		StringBuffer sb6 = new StringBuffer();
// 		sb6.append("  SELECT t.TRANS_SERNO,\n" +
// 				"        t.CUST_NO,\n" +
// 				"        t.ACCT_NO,\n" +
// 				"        t.TRANS_ACCT_NO,\n" +
// 				"        t.TRANS_TYPE,\n" +
// 				"        t.AUTO_AMT AS TRANS_AMT,\n" +
// 				"        t.TRANS_STATUS,\n" +
// 				"        t.bala_code,\n" +
// 				"        t.CHANNEL_FLAG,\n" +
// 				"        t.REFERRER as CUST_MANAGER,\n" +
// 				"        t.BRANCH_CODE,\n" +
// 				"        t.SUB_BRANCH_CODE,\n" +
// 				//"		 to_date(t.TRANS_DATE,'yyyyMMdd') as TRANS_DATE,\n" +
// 				"        t.TRANS_DATE as TRANS_DATE,\n" +
// 				"        t.TRANS_TIME,\n" +
// 				"        t.TRANS_ORGNO,\n" +
// 				"        t.HOST_RTN_CODE,\n" +
// 				"        t.HOST_RTN_DESC,\n" +
// 				"        t.TRANS_STATUS AS ACK_STATUS,\n" +
// 				"        '' as REMARK\n" +
// 				"   FROM bala_cust_sign_trans_log_h t where (t.trans_type='9' or t.trans_type='B') ");
// 		if(Tools.isNotBlank(params.getModel().getHostTransSerno())){
// 			sb6.append(" and t.TRANS_SERNO = '"+params.getModel().getHostTransSerno()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransType())){
// 			sb6.append(" and t.TRANS_TYPE = '"+params.getModel().getTransType()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getSubBranchCode())){
// 			sb6.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransAmtMin())){
// 			sb6.append(" and t.AUTO_AMT >= '"+params.getModel().getTransAmtMin()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransAmtMax())){
// 			sb6.append(" and t.AUTO_AMT <= '"+params.getModel().getTransAmtMax()+"'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransDate())){
// 			sb6.append(" and t.TRANS_DATE >="+params.getModel().getTransDate());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransEndDate())){
// 			sb6.append(" and t.TRANS_DATE <="+params.getModel().getTransEndDate());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getBranchCode())){
// 			sb3.append(" and branch_code = '"+params.getModel().getTransEndDate()+"'");
// 		}
// 		String sql6 = MakeSqlUntil.makeSql(sb6.toString(),params.getParams(),M926.class);
// 		sql6 += reportformUtil.getOrgIdForOrgLevel("t.");
// 		if(Tools.isNotBlank(custNo)){
// 			sql6 = sql6 + " and t.cust_no in ('"+custNo+"')" ;
// 		}
// */
//
// 		String sql =
// 				"select * from ( " + sql3 +
// 						" ) union all \n" +
// 						sql4 +
// 						/**"union all \n" +
// 						sql3 +
// 						"union all \n" +
// 						sql4 +
// 						"union all \n" +
// 						sql5 +
// 						"union all \n" +
// 						sql6 +*/
// 						") ) order by TRANS_DATE desc ";
// 		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER,params);
//
// 	}
//
//
//
// 	/**
// 	 * 查询TANO
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M941> findTano(SqlParam<M941> params) throws Exception {
// 		return super.findRows("SELECT DISTINCT(TANO) FROM bala_cust_sign bat ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
//
// 	/**
// 	 * 查询TANO
// 	 * @throws Exception
// 	 */
// 	public String findBalaName(String balaCode) throws Exception {
// 		String sql = "select t.bala_name from bala_prod_info t where t.bala_code='"+balaCode+"'";
// 		return super.findRow(String.class,sql, SubDatabase.DATABASE_BALA_CENTER,null);
//
// 	}
// }