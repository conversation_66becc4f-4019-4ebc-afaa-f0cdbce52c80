package com.kayak.bala.dao;

import com.kayak.bala.model.M916;
import com.kayak.bala.model.M917;
import com.kayak.bala.model.M931;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M916Dao extends ComnDao {
    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M916> findM916s(SqlParam<M916> params) throws Exception {
        //params.setMakeSql(true);
        StringBuffer sb = new StringBuffer();
        sb.append("select t.branch_code,t.sub_branch_code,t.tano,t.prod_code,t.prod_name,t.stat_date,\n" +
                "  sum(t.prod_balance) as prod_balance,sum(t.cust_count) as cust_count\n"+
                "  from bala_report_base_prod_storage t\n" +
                " where 1 = 1 and t.sub_branch_code is not null ");
        //sb.append(reportformUtil.getOrgIdForOrgLevel("t."));

        if(Tools.isNotBlank(params.getModel().getSubBranchCode())){
            sb.append(" and t.sub_branch_code in ( ");
            sb.append(params.getModel().getSubBranchCode());
            sb.append(" ) ");
        }

        if(StringUtils.isNotBlank(params.getModel().getStatDate())){
            sb.append(" and t.stat_date = "+params.getModel().getStatDate());
          //  params.getModel().setStatDate(null);
        }
        sb.append(" group by t.branch_code,t.sub_branch_code,tano,t.prod_code,t.prod_name,t.stat_date\n");
        sb.append(" order by t.branch_code,t.sub_branch_code,t.stat_date desc  \n");
        List<M916> volList = super.findRows(M916.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        SqlResult<M916> sqlRowSqlResult = new SqlResult<>();
        sqlRowSqlResult.setResults(volList.size());
        sqlRowSqlResult.setRows(volList);
        sqlRowSqlResult.setDesensitized(false);
        return sqlRowSqlResult;

    }

    public List<M916> findM916Count(SqlParam<M916> params) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("select t.branch_code,t.sub_branch_code,\n" +
                "  sum(t.prod_balance) as prod_balance,sum(t.cust_count) as cust_count\n"+
                "  from bala_report_base_prod_storage t\n" +
                " where 1 = 1 and t.sub_branch_code is not null ");

        if(Tools.isNotBlank(params.getModel().getSubBranchCode())){
            sb.append(" and t.sub_branch_code in ( ");
            sb.append(params.getModel().getSubBranchCode());
            sb.append(" ) ");
        }

        if(StringUtils.isNotBlank(params.getModel().getStatDate())){
            sb.append(" and t.stat_date = "+params.getModel().getStatDate());
        }
        sb.append(" group by t.branch_code,t.sub_branch_code\n");
        sb.append(" order by t.branch_code,t.sub_branch_code \n");
        List<M916> volList = super.findRows(M916.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        return volList;
    }

    public List<M916> findCustCount(String transOrgno) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT count(DISTINCT CUST_NO) as cust_count FROM bala_cust_base_vol t WHERE t.TRANS_ORGNO='"+transOrgno+"' AND t.vol > 0");
        List<M916> volList = super.findRows(M916.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,null);
        return volList;

    }
}