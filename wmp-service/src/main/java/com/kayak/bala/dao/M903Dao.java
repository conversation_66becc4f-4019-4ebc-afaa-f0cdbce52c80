// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M903;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// //import com.mysql.cj.x.protobuf.MysqlxDatatypes;
// import org.springframework.stereotype.Repository;
//
// /**
//  * 余额理财底层产品维护
//  */
// @Repository
// public class M903Dao extends ComnDao {
//
// 	/**
// 	 * 查询底层产品净值信息
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M903> findProdInfo(SqlParam<M903> params) throws Exception {
// 		StringBuffer sql = new StringBuffer();
// 		sql.append("SELECT BPN.SYSTEM_NO, BPN.TANO,bpbi.PROD_NAME ,bpbi.PROD_TYPE ," +
// 				"BPN.PROD_CODE, BPN.NAV_DATE, BPN.NAV, BPN.SEVEN_DAY_ANNUALIZED_YIELD, " +
// 				"BPN.REVENUE_PER_TEN_KILO_UNITS, BPN.TOTAL_INCOME " +
// 				"FROM BALA_PROD_NAV bpn LEFT JOIN BALA_PROD_BASE_INFO bpbi ON BPN.PROD_CODE = BPBI.PROD_CODE " +
// 				" AND BPN.TANO = BPBI.TANO AND BPN.SYSTEM_NO = BPBI.SYSTEM_NO ");
// 		sql.append(" where 1=1 ");
// 		if(Tools.isNotBlank(params.getModel().getTano())){
// 			sql.append(" and bpn.tano = $S{tano} ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getProdCode())){
// 			sql.append(" and bpn.PROD_CODE = $S{prodCode} ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getSystemNo())){
// 			sql.append(" and bpn.SYSTEM_NO = $S{systemNo} ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getNavDate())){
// 			sql.append(" and bpn.nav_date = $S{navDate} ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getProdName())){
// 			sql.append(" and bpbi.PROD_NAME  like '%$U{prodName}%'");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getProdType())){
// 			sql.append(" and bpbi.PROD_TYPE  = $S{prodType} ");
// 		}
//
// 		sql.append(" ORDER BY BPN.NAV_DATE DESC ");
// 		return super.findRows(sql.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
// }