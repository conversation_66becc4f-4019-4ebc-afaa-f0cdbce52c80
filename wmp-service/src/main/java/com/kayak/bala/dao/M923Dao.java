// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M923;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.sql.UpdateResult;
// import com.kayak.core.util.Tools;
// import org.apache.commons.lang.StringUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// import java.util.List;
// import java.util.Map;
//
// /**
//  * 客户源泉宝份额
//  */
// @Repository
// public class M923Dao extends ComnDao {
// 	@Autowired
// 	private ReportformUtil reportformUtil;
// 	public List<M923> findCusts(SqlParam<M923> param)  throws Exception {
// 		String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
// 				" where cust_name = $S{custName} and id_type = $S{idType} and id_code = $S{idCode}";
// 		return super.findRows(M923.class,sql, SubDatabase.DATABASE_CUST_CENTER,param.getModel());
// 	}
//
// 	public M923 getCusts(String no)  throws Exception {
// 		String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
// 				" where cust_no = '"+no+"'";
// 		return super.findRow(M923.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
// 	}
//
// 	public String getCustNoByAcctNo(String no)  throws Exception {
// 		String sql = "select cust_no from cust_trans_acct\n" +
// 				" where acct_no = '"+no+"'";
// 		return super.findRow(String.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
// 	}
//
// 	public String getAcctNo(String no)  throws Exception {
// 		String sql = "select acct_no from cust_trans_acct\n" +
// 				" where cust_no = '"+no+"'";
// 		return super.findRow(String.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
// 	}
//
// 	/**
// 	 * 查源泉宝份额
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M923> findBalaCustBaseVol(SqlParam<M923> params) throws Exception {
// 		//params.setMakeSql(true);
// 		StringBuffer sb = new StringBuffer();
// 		sb.append(" select * from ( select t.trans_acct_no,\n" +
// 				"         t.cust_no,\n" +
// 				"         sum(t.vol) as vol,\n" +
// 				"         sum(t.redeem_frozen_vol) as redeem_frozen_vol,\n" +
// 				"         sum(t.justice_frozen_vol) as justice_frozen_vol,\n" +
// 				"         sum(t.last_day_income) as last_day_income,\n" +
// 				"         sum(t.last_week_income) as last_week_income,\n" +
// 				"         sum(t.last_month_income) as last_month_income,\n" +
// 				"         sum(t.total_income) as total_income,\n" +
// 				"         t.BRANCH_CODE,\n" +
// 				"         t.trans_orgno,\n" +
// 				"         t2.cust_name,\n" +
// 				"         t2.id_type,\n" +
// 				"         t2.id_code,\n" +
// 				"         t2.acct_no\n" +
// 				"    from bala_cust_base_vol t\n" +
// 				"    left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
// 				"   where 1=1 " );
// 		//+ reportformUtil.getOrgIdForOrgLevel("t.")
// 		if(Tools.isNotBlank(params.getModel().getCustNo())){
// 			sb.append(" and t.cust_no in ('"+params.getModel().getCustNo()+"')");
// 		}
// 		params.getModel().setCustNo(null);
// 		if(StringUtils.isNotBlank(params.getModel().getCrtTime())){
// 			sb.append(" and to_char(t.create_time,'yyyymmdd') >="+params.getModel().getCrtTime());
// 		}
// 		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
// 			sb.append(" and to_char(t.create_time,'yyyymmdd') <="+params.getModel().getStartEndDate());
// 		}
// 		sb.append(" group by t.BRANCH_CODE,\n" +
// 				"            t.trans_orgno,\n" +
// 				"            t.cust_no,\n" +
// 				"            t.trans_acct_no,\n" +
// 				"            t2.cust_name,\n" +
// 				"            t2.id_type,\n" +
// 				"            t2.id_code,\n" +
// 				"            t2.acct_no ) t3 where 1=1 ");
// 		if(Tools.isNotBlank(params.getModel().getMaxVol())){
// 			sb.append(" and t3.vol <= '"+params.getModel().getMaxVol()+"' ");
// 		}
// 		if(Tools.isNotBlank(params.getModel().getMinVol())){
// 			sb.append(" and t3.vol >= '"+params.getModel().getMinVol()+"' ");
// 		}
// 		return super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
//
//
// 	/**
// 	 * 查询TANO
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M923> findTano(SqlParam<M923> params) throws Exception {
// 		return super.findRows("SELECT DISTINCT(TANO) FROM bala_cust_sign bat ", SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
// 	public String getAcctNoByTrans(String custNo,String transAcctNo)  throws Exception {
// 		String sql = "select acct_no from cust_trans_acct\n" +
// 				" where cust_no = '"+custNo+"' and TRANS_ACCT_NO = '"+transAcctNo+"' ";
// 		return super.findRow(String.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
// 	}
//
// 	/**
// 	 * <AUTHOR>
// 	 * @Description 客户交易账号表,交易机构和发卡交易机构改为合并机构
// 	 * @Date 2022/3/24
// 	 * @Param [params]
// 	 * @return com.kayak.core.sql.UpdateResult
// 	 **/
// 	public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
// 		return super.update("UPDATE CUST_TRANS_ACCT SET TRANS_ORGNO = $S{mergeOrgno},ISS_TRANS_ORGNO = $S{mergeOrgno} WHERE TRANS_ORGNO = $S{removeOrgno} OR ISS_TRANS_ORGNO = $S{removeOrgno}",
// 				SubDatabase.DATABASE_CUST_CENTER,params);
// 	}
//
// }