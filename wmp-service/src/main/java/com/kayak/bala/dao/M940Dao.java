package com.kayak.bala.dao;

import com.kayak.bala.model.M940;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 还款流水
 */
@Repository
public class M940Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;
	/**
	 * 源泉宝收益查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M940> findBalaCustPaymentLog(SqlParam<M940> params) throws Exception {
		String custNo = params.getModel().getCustsNo();
		params.getModel().setCustsNo(null);
		StringBuffer sb = new StringBuffer();
		sb.append(" select t.cust_no as custs_no,\n" +
				"       t.bala_code,\n" +
				"       t.income_date,\n" +
				"       t.trans_acct_no,\n" +
				"       sum(t.income_amt) as income_amt,\n" +
				"       sum(t.income_amt) as calc_vol,\n" +
				"       (select t3.bala_name\n" +
				"          from bala_prod_info t3\n" +
				"         where t.bala_code = t3.bala_code) as bala_name,\n" +
				"       (select t2.cust_name\n" +
				"          from BALA_CUST_SIGN t2\n" +
				"         where t.cust_no = t2.cust_no) as cust_name\n" +
				"  from bala_cust_income_list t\n" +
				" where 1 = 1 ");
		if(Tools.isNotBlank(custNo)){
			sb.append(" and t.cust_no in ('"+custNo+"')" );
		}
		sb.append( " group by  t.bala_code, t.income_date,t.cust_no,t.trans_acct_no order by t.income_date desc  nulls last");
		params.setMakeSql(true);
		return super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
	}




}