// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M939;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import org.apache.commons.lang.StringUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// /**
//  * 还款流水
//  */
// @Repository
// public class M939Dao extends ComnDao {
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
// 	/**
// 	 * 源泉宝收益查询
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M939> findBalaCustPaymentLog(SqlParam<M939> params) throws Exception {
//
// 		String custNo = params.getModel().getCustNo();
// 		params.getModel().setCustNo(null);
// 		StringBuffer sb = new StringBuffer();
// 		sb.append("  select t.TANO,\n" +
// 				"        t.cust_no,\n" +
// 				"        t.prod_code,\n" +
// 				"        t.income_date,\n" +
// 				"        t.trans_acct_no,\n" +
// 				"        sum(t.income_amt) as income_amt,\n" +
// 				"        sum(t.CALC_VOL) as calc_vol,\n" +
// 				"        (select t3.prod_name\n" +
// 				"           from bala_prod_base_info t3\n" +
// 				"          where t.prod_code = t3.prod_code) as prod_name,\n" +
// 				"        (select t2.cust_name\n" +
// 				"           from BALA_CUST_SIGN t2\n" +
// 				"          where t.cust_no = t2.cust_no) as cust_name\n" +
// 				"   from bala_cust_income_list t\n" +
// 				"  where 1 = 1\n" );
// 		if(Tools.isNotBlank(custNo)){
// 			sb.append(" and t.cust_no in ('"+custNo+"')" );
// 		}
// 		sb.append( " group by t.tano, t.prod_code, t.income_date, t.cust_no, t.trans_acct_no order by income_date desc nulls last");
// 		params.setMakeSql(true);
// 		return super.findRows(sb.toString(), SubDatabase.DATABASE_BALA_CENTER, params);
// 	}
//
//
//
//
// }