// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M919;
// import com.kayak.bala.model.M920;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.until.MakeSqlUntil;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// /**
//  * 源泉宝签约变更解约流水
//  */
// @Repository
// public class M919Dao extends ComnDao {
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	/**
// 	 * 查询源泉宝签约变更解约流水
// 	 * @param params
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public SqlResult<M919> findBalaCustSignTransLog(SqlParam<M919> params) throws Exception {
// 		//params.setMakeSql(true);
// 		//params.setMakeSql(true);
// 		String sql1 ="select t.trans_serno,\n" +
// 				"       t.cust_name,\n" +
// 				"       t.id_type,\n" +
// 				"       t.id_code,\n" +
// 				"       t.trans_acct_no,\n" +
// 				"       t.ori_acct_no,\n" +
// 				"       t.acct_no,\n" +
// 				"       t.trans_type,\n" +
// 				"       t.trans_status,\n" +
// 				"       t.trans_date,\n" +
// 				"       t.trans_time,\n" +
// 				"       t.AUTO_TYPE,\n" +
// 				"       t.AUTO_AMT,\n" +
// 				"       t.HOST_RTN_CODE,\n" +
// 				"       t.HOST_RTN_DESC,\n" +
// 				"       t.ORI_AUTO_TYPE,\n" +
// 				"       t.ORI_AUTO_AMT\n" +
// 				"  from bala_cust_sign_trans_log t " +
// 				" where 1 = 1 ";
// 		//sql1 = sql1 + reportformUtil.getOrgIdForOrgLevel("t.");
// 		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M919.class);
// 		String sql2 ="select t.trans_serno,\n" +
// 				"       t.cust_name,\n" +
// 				"       t.id_type,\n" +
// 				"       t.id_code,\n" +
// 				"       t.trans_acct_no,\n" +
// 				"       t.ori_acct_no,\n" +
// 				"       t.acct_no,\n" +
// 				"       t.trans_type,\n" +
// 				"       t.trans_status,\n" +
// 				"       t.trans_date,\n" +
// 				"       t.trans_time,\n" +
// 				"       t.AUTO_TYPE,\n" +
// 				"       t.AUTO_AMT,\n" +
// 				"       t.HOST_RTN_CODE,\n" +
// 				"       t.HOST_RTN_DESC,\n" +
// 				"       t.ORI_AUTO_TYPE,\n" +
// 				"       t.ORI_AUTO_AMT\n" +
// 				"  from bala_cust_sign_trans_log_h t " +
// 				" where 1 = 1 ";
// 		//sql2 = sql2 + reportformUtil.getOrgIdForOrgLevel("t.");
// 		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M919.class);
// 		String sql =
// 				"select * from ( " + sql1 +
// 						"union all \n" +
// 						sql2 + ")xsql_ta order by trans_date desc ";
// 		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER,params);
//
// 	}
//
// }