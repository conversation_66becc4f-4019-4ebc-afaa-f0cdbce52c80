package com.kayak.bala.dao;

import com.kayak.bala.model.M931;
import com.kayak.bala.model.M931;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class M931Dao extends ComnDao {
    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M931> findM931s(SqlParam<M931> params) throws Exception {
        //params.setMakeSql(true);
        StringBuffer sb = new StringBuffer();
        sb.append("select t.branch_code,t.tano,t.prod_code,t.prod_name,t.stat_date,\n" +
                "  sum(t.prod_balance) as prod_balance,sum(t.cust_count) as cust_count\n"+
                "  from bala_report_base_prod_storage t\n" +
                " where 1 = 1 ");
        //sb.append(reportformUtil.getOrgIdForOrgLevel("t."));
        if(Tools.isNotBlank(params.getModel().getBranchCode())){
            sb.append(" and t.branch_code in ( ");
            String [] str = params.getModel().getBranchCode().split(",");
            if(str != null && str.length > 0){
                for(int i=0;i<str.length;i++){
                    if(i != str.length -1){
                        sb.append("'"+str[i].toString()+"',");
                    }else{
                        sb.append("'"+str[i].toString()+"'");
                    }

                }
            }
            sb.append(" ) ");
        }

        if(StringUtils.isNotBlank(params.getModel().getStatDate())){
            sb.append(" and t.stat_date = "+params.getModel().getStatDate());
        }
        sb.append(" group by t.branch_code,tano,t.prod_code,t.prod_name,t.stat_date \n");
        sb.append(" order by t.branch_code,t.stat_date desc  \n");
        List<M931> volList = super.findRows(M931.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        SqlResult<M931> sqlRowSqlResult = new SqlResult<>();
        sqlRowSqlResult.setResults(volList.size());
        sqlRowSqlResult.setRows(volList);
        sqlRowSqlResult.setDesensitized(false);
        return sqlRowSqlResult;
    }

    public List<M931> findM931Count(SqlParam<M931> params) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("select t.branch_code,\n" +
                "  sum(t.prod_balance) as prod_balance,sum(t.cust_count) as cust_count\n"+
                "  from bala_report_base_prod_storage t\n" +
                " where 1 = 1");
        if(Tools.isNotBlank(params.getModel().getBranchCode())){
            sb.append(" and t.branch_code in ( ");
            String [] str = params.getModel().getBranchCode().split(",");
            if(str != null && str.length > 0){
                for(int i=0;i<str.length;i++){
                    if(i != str.length -1){
                        sb.append("'"+str[i].toString()+"',");
                    }else{
                        sb.append("'"+str[i].toString()+"'");
                    }

                }
            }
            sb.append(" ) ");
        }
        
        if(StringUtils.isNotBlank(params.getModel().getStatDate())){
            sb.append(" and t.stat_date = "+params.getModel().getStatDate());
        }
        sb.append(" group by t.branch_code\n");
        sb.append(" order by t.branch_code\n");
        List<M931> volList = super.findRows(M931.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,params);
        return volList;
    }

    public List<M931> findCustCount(String branchCode) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT count(DISTINCT CUST_NO) as cust_count FROM bala_cust_base_vol t WHERE t.BRANCH_CODE='"+branchCode+"' AND t.vol > 0");
        List<M931> volList = super.findRows(M931.class,sb.toString(), SubDatabase.DATABASE_BALA_CENTER,null);
        return volList;

    }
}