package com.kayak.bala.dao;

import com.kayak.bala.model.M920;
import com.kayak.bala.model.M925;
import com.kayak.bala.model.M926;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.until.MakeSqlUntil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 源泉宝交易流水
 */
@Repository
public class M925Dao extends ComnDao {
	@Autowired
	private ReportformUtil reportformUtil;

	/**
	 * 源泉宝交易流水查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M925> findBalaCustHostTransLog(SqlParam<M925> params) throws Exception {
		//params.setMakeSql(true);
		String custNo = params.getModel().getCustNo();
		params.getModel().setCustNo(null);
		String branchCode = params.getModel().getBranchCode();//所属区域
		params.getModel().setBranchCode(null);
		String subBranchCode = params.getModel().getSubBranchCode();//所属机构
		params.getModel().setSubBranchCode(null);
		String sql1 =" select * from ( select t1.HOST_TRANS_SERNO,\n" +
				"               t1.CUST_NO,\n" +
				"               t1.ACCT_NO,\n" +
				"               t1.TRANS_ACCT_NO,\n" +
				"               t1.TRANS_TYPE,\n" +
				"               t1.TRANS_AMT,\n" +
				"               t1.TRANS_STATUS,\n" +
				"               t1.BALA_CODE,\n" +
				"               t1.CHANNEL_FLAG,\n" +
				"               t1.CUST_MANAGER,\n" +
				"               t1.BRANCH_CODE,\n" +
				"               t1.SUB_BRANCH_CODE,\n" +
				"               t1.TRANS_DATE,\n" +
				"               t1.TRANS_TIME,\n" +
				"               t1.TRANS_ORGNO,\n" +
				"               t1.HOST_RTN_CODE,\n" +
				"               t1.HOST_RTN_DESC,\n" +
				"               t1.ACK_STATUS,\n" +
				"               t1.REMARK,\n" +
				"               t2.cust_name,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code,\n" +
				"               t3.bala_name\n" +
				"          from BALA_CUST_HOST_TRANS_LOG t1\n" +
				"          left join BALA_CUST_SIGN t2 on t1.cust_no = t2.cust_no\n" +
				"          left join bala_prod_info t3 on t1.bala_code = t3.bala_code\n" +
				"         where 1 = 1\n" +
				"           and t1.TRANS_TYPE in\n" +
				"               ('T020', 'T010', 'T030', 'T080', 'T050', 'T060', 'T070') ";
		//所属区域
		if(Tools.isNotBlank(branchCode)){
			String inStr = " and t1.BRANCH_CODE in (" ;
			String [] str = branchCode.split(",");
			if(str != null && str.length > 0){
				for(int i=0;i<str.length;i++){
					if (i == str.length - 1){
						inStr += "'"+str[i]+"')";
					}else {
						inStr += "'"+str[i]+"',";
					}
				}
			}
			sql1 += inStr;
		}
		//所属机构
		if(Tools.isNotBlank(subBranchCode)){
			String inStr = " and t1.SUB_BRANCH_CODE in (" ;
			String [] str = subBranchCode.split(",");
			if(str != null && str.length > 0){
				for(int i=0;i<str.length;i++){
					if (i == str.length - 1){
						inStr += "'"+str[i]+"')";
					}else {
						inStr += "'"+str[i]+"',";
					}
				}
			}
			sql1 += inStr;
		}
		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M925.class);
		//sql1 += reportformUtil.getOrgIdForOrgLevel("t1.");
		if(Tools.isNotBlank(custNo)){
			sql1 = sql1 + " and t1.cust_no in ('"+custNo+"')" ;
		}
		//sql1 = sql1+" order by t1.TRANS_DATE desc ";
		String sql2 =" select * from ( select t1.HOST_TRANS_SERNO,\n" +
				"               t1.CUST_NO,\n" +
				"               t1.ACCT_NO,\n" +
				"               t1.TRANS_ACCT_NO,\n" +
				"               t1.TRANS_TYPE,\n" +
				"               t1.TRANS_AMT,\n" +
				"               t1.TRANS_STATUS,\n" +
				"               t1.BALA_CODE,\n" +
				"               t1.CHANNEL_FLAG,\n" +
				"               t1.CUST_MANAGER,\n" +
				"               t1.BRANCH_CODE,\n" +
				"               t1.SUB_BRANCH_CODE,\n" +
				"               t1.TRANS_DATE,\n" +
				"               t1.TRANS_TIME,\n" +
				"               t1.TRANS_ORGNO,\n" +
				"               t1.HOST_RTN_CODE,\n" +
				"               t1.HOST_RTN_DESC,\n" +
				"               t1.ACK_STATUS,\n" +
				"               t1.REMARK,\n" +
				"               t2.cust_name,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code,\n" +
				"               t3.bala_name\n" +
				"          from BALA_CUST_HOST_TRANS_LOG_H t1\n" +
				"          left join BALA_CUST_SIGN t2 on t1.cust_no = t2.cust_no\n" +
				"          left join bala_prod_info t3 on t1.bala_code = t3.bala_code\n" +
				"         where 1 = 1\n" +
				"           and t1.TRANS_TYPE in\n" +
				"               ('T020', 'T010', 'T030', 'T080', 'T050', 'T060', 'T070') ";
		if(Tools.isNotBlank(branchCode)){
			String inStr = " and t1.BRANCH_CODE in (" ;
			String [] str = branchCode.split(",");
			if(str != null && str.length > 0){
				for(int i=0;i<str.length;i++){
					if (i == str.length - 1){
						inStr += "'"+str[i]+"')";
					}else {
						inStr += "'"+str[i]+"',";
					}
				}
			}
			sql2 += inStr;
		}
		if(Tools.isNotBlank(subBranchCode)){
			String inStr = " and t1.SUB_BRANCH_CODE in (" ;
			String [] str = subBranchCode.split(",");
			if(str != null && str.length > 0){
				for(int i=0;i<str.length;i++){
					if (i == str.length - 1){
						inStr += "'"+str[i]+"')";
					}else {
						inStr += "'"+str[i]+"',";
					}
				}
			}
			sql2 += inStr;
		}
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M925.class);
		//sql2 += reportformUtil.getOrgIdForOrgLevel("t1.");
		if(Tools.isNotBlank(custNo)){
			sql2 = sql2 + " and t1.cust_no in ('"+custNo+"')" ;
		}
		//sql2 = sql2+" order by t1.TRANS_DATE desc ";
		/**StringBuffer sb3 =new StringBuffer();
		sb3.append( " SELECT \n" +
				"t.APP_SERNO AS HOST_TRANS_SERNO,\n" +
				"t.CUST_NO,\n" +
				"t.ACCT_NO,\n" +
				"t.TRANS_ACCT_NO,\n" +
				"t.BUSI_CODE AS TRANS_TYPE,\n" +
				"t.APP_AMT AS TRANS_AMT,\n" +
				"t.TRANS_STATUS,\n" +
				"t.PROD_CODE AS BALA_CODE,\n" +
				"t.CHANNEL_FLAG,\n" +
				"t.CUST_MANAGER,\n" +
				"t.BRANCH_CODE,\n" +
				"t.SUB_BRANCH_CODE,\n" +
				//"to_date(t.BUSI_DATE,'yyyyMMdd') as TRANS_DATE,\n" +
				"               t.BUSI_DATE as TRANS_DATE,\n" +
				"TO_CHAR(t.mactime,'hh24miss') AS TRANS_TIME,\n" +
				"t.TRANS_ORGNO,\n" +
				"t.HOST_RTN_CODE,\n" +
				"t.HOST_RTN_DESC,\n" +
				"t.TRANS_STATUS AS ACK_STATUS,\n" +
				"t.REMARK\n" +
				"FROM \n" +
				"bala_cust_trans_req t WHERE (t.BUSI_CODE='120' or t.BUSI_CODE='121' or t.BUSI_CODE='909')");
		if(StringUtils.isNotBlank(params.getModel().getHostTransSerno())){
			sb3.append(" and t.APP_SERNO = '"+params.getModel().getHostTransSerno()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransType())){
			sb3.append(" and t.busi_code = '"+params.getModel().getTransType()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getSubBranchCode())){
			sb3.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMin())){
			sb3.append(" and t.app_amt >= '"+params.getModel().getTransAmtMin()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMax())){
			sb3.append(" and t.app_amt <= '"+params.getModel().getTransAmtMax()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransDate())){
			sb3.append(" and t.BUSI_DATE >="+params.getModel().getTransDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getTransEndDate())){
			sb3.append(" and t.BUSI_DATE <="+params.getModel().getTransEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBranchCode())){
			sb3.append(" and t.branch_code = '"+params.getModel().getTransEndDate()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getAckStatus())){
			sb3.append(" and t.ack_status = '"+params.getModel().getAckStatus()+"'");
		}
		String sql3 = MakeSqlUntil.makeSql(sb3.toString(),params.getParams(),M926.class);
		sql3 += reportformUtil.getOrgIdForOrgLevel("t.");
		if(Tools.isNotBlank(custNo)){
			sql3 = sql3 + " and t.cust_no in ('"+custNo+"')" ;
		}
		StringBuffer sb4 = new StringBuffer();
		sb4.append(" SELECT \n" +
				"t.APP_SERNO AS HOST_TRANS_SERNO,\n" +
				"t.CUST_NO,\n" +
				"t.ACCT_NO,\n" +
				"t.TRANS_ACCT_NO,\n" +
				"t.BUSI_CODE AS TRANS_TYPE,\n" +
				"t.APP_AMT AS TRANS_AMT,\n" +
				"t.TRANS_STATUS,\n" +
				"t.PROD_CODE AS BALA_CODE,\n" +
				"t.CHANNEL_FLAG,\n" +
				"t.CUST_MANAGER,\n" +
				"t.BRANCH_CODE,\n" +
				"t.SUB_BRANCH_CODE,\n" +
				//"to_date(t.BUSI_DATE,'yyyyMMdd') as TRANS_DATE,\n" +
				"               t.BUSI_DATE as TRANS_DATE,\n" +
				"TO_CHAR(t.mactime,'hh24:mi:ss') AS TRANS_TIME,\n" +
				"t.TRANS_ORGNO,\n" +
				"t.HOST_RTN_CODE,\n" +
				"t.HOST_RTN_DESC,\n" +
				"t.TRANS_STATUS AS ACK_STATUS,\n" +
				"t.REMARK\n" +
				"FROM \n" +
				"bala_cust_trans_req_h t WHERE (t.BUSI_CODE='120' or t.BUSI_CODE='121' or t.BUSI_CODE='909')");
		if(StringUtils.isNotBlank(params.getModel().getHostTransSerno())){
			sb4.append(" and t.APP_SERNO = '"+params.getModel().getHostTransSerno()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransType())){
			sb4.append(" and t.busi_code = '"+params.getModel().getTransType()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getSubBranchCode())){
			sb4.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMin())){
			sb4.append(" and t.app_amt >= '"+params.getModel().getTransAmtMin()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMax())){
			sb4.append(" and t.app_amt <= '"+params.getModel().getTransAmtMax()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransDate())){
			sb4.append(" and t.BUSI_DATE >="+params.getModel().getTransDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getTransEndDate())){
			sb4.append(" and t.BUSI_DATE <="+params.getModel().getTransEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBranchCode())){
			sb3.append(" and t.branch_code = '"+params.getModel().getTransEndDate()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getAckStatus())){
			sb3.append(" and t.ack_status = '"+params.getModel().getAckStatus()+"'");
		}
		String sql4 = MakeSqlUntil.makeSql(sb4.toString(),params.getParams(),M926.class);
		sql4 += reportformUtil.getOrgIdForOrgLevel("t.");
		if(Tools.isNotBlank(custNo)){
			sql4 = sql4 + " and t.cust_no in ('"+custNo+"')" ;
		}
		StringBuffer sb5 = new StringBuffer();
		sb5.append("  SELECT t.TRANS_SERNO,\n" +
				"        t.CUST_NO,\n" +
				"        t.ACCT_NO,\n" +
				"        t.TRANS_ACCT_NO,\n" +
				"        t.TRANS_TYPE,\n" +
				"        t.AUTO_AMT AS TRANS_AMT,\n" +
				"        t.TRANS_STATUS,\n" +
				"        t.bala_code,\n" +
				"        t.CHANNEL_FLAG,\n" +
				"        t.REFERRER as CUST_MANAGER,\n" +
				"        t.BRANCH_CODE,\n" +
				"        t.SUB_BRANCH_CODE,\n" +
				//"		 to_date(t.TRANS_DATE,'yyyyMMdd') as TRANS_DATE,\n" +
				"               t.TRANS_DATE as TRANS_DATE,\n" +
				"        t.TRANS_TIME,\n" +
				"        t.TRANS_ORGNO,\n" +
				"        t.HOST_RTN_CODE,\n" +
				"        t.HOST_RTN_DESC,\n" +
				"        t.TRANS_STATUS AS ACK_STATUS,\n" +
				"        '' as REMARK\n" +
				"   FROM bala_cust_sign_trans_log t where (t.trans_type='9' or t.trans_type='B') ");
		if(StringUtils.isNotBlank(params.getModel().getHostTransSerno())){
			sb5.append(" and t.TRANS_SERNO = '"+params.getModel().getHostTransSerno()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransType())){
			sb5.append(" and t.TRANS_TYPE = '"+params.getModel().getTransType()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getSubBranchCode())){
			sb5.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMin())){
			sb5.append(" and t.AUTO_AMT >= '"+params.getModel().getTransAmtMin()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMax())){
			sb5.append(" and t.AUTO_AMT <= '"+params.getModel().getTransAmtMax()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransDate())){
			sb5.append(" and t.TRANS_DATE >="+params.getModel().getTransDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getTransEndDate())){
			sb5.append(" and t.TRANS_DATE <="+params.getModel().getTransEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBranchCode())){
			sb3.append(" and t.branch_code = '"+params.getModel().getTransEndDate()+"'");
		}
		String sql5 = MakeSqlUntil.makeSql(sb5.toString(),params.getParams(),M926.class);
		sql5 += reportformUtil.getOrgIdForOrgLevel("t.");
		if(Tools.isNotBlank(custNo)){
			sql5 = sql5 + " and t.cust_no in ('"+custNo+"')" ;
		}

		StringBuffer sb6 = new StringBuffer();
		sb6.append("  SELECT t.TRANS_SERNO,\n" +
				"        t.CUST_NO,\n" +
				"        t.ACCT_NO,\n" +
				"        t.TRANS_ACCT_NO,\n" +
				"        t.TRANS_TYPE,\n" +
				"        t.AUTO_AMT AS TRANS_AMT,\n" +
				"        t.TRANS_STATUS,\n" +
				"        t.bala_code,\n" +
				"        t.CHANNEL_FLAG,\n" +
				"        t.REFERRER as CUST_MANAGER,\n" +
				"        t.BRANCH_CODE,\n" +
				"        t.SUB_BRANCH_CODE,\n" +
				//"		 to_date(t.TRANS_DATE,'yyyyMMdd') as TRANS_DATE,\n" +
				"        t.TRANS_DATE as TRANS_DATE,\n" +
				"        t.TRANS_TIME,\n" +
				"        t.TRANS_ORGNO,\n" +
				"        t.HOST_RTN_CODE,\n" +
				"        t.HOST_RTN_DESC,\n" +
				"        t.TRANS_STATUS AS ACK_STATUS,\n" +
				"        '' as REMARK\n" +
				"   FROM bala_cust_sign_trans_log_h t where (t.trans_type='9' or t.trans_type='B') ");
		if(StringUtils.isNotBlank(params.getModel().getHostTransSerno())){
			sb6.append(" and t.TRANS_SERNO = '"+params.getModel().getHostTransSerno()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransType())){
			sb6.append(" and t.TRANS_TYPE = '"+params.getModel().getTransType()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getSubBranchCode())){
			sb6.append(" and t.trans_orgno = '"+params.getModel().getSubBranchCode()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMin())){
			sb6.append(" and t.AUTO_AMT >= '"+params.getModel().getTransAmtMin()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransAmtMax())){
			sb6.append(" and t.AUTO_AMT <= '"+params.getModel().getTransAmtMax()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransDate())){
			sb6.append(" and t.TRANS_DATE >="+params.getModel().getTransDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getTransEndDate())){
			sb6.append(" and t.TRANS_DATE <="+params.getModel().getTransEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBranchCode())){
			sb3.append(" and branch_code = '"+params.getModel().getTransEndDate()+"'");
		}
		String sql6 = MakeSqlUntil.makeSql(sb6.toString(),params.getParams(),M926.class);
		sql6 += reportformUtil.getOrgIdForOrgLevel("t.");
		if(Tools.isNotBlank(custNo)){
			sql6 = sql6 + " and t.cust_no in ('"+custNo+"')" ;
		}
*/

		String sql = "select * from ("+ sql1 +
						" ) sql_ta  union all \n" +
						sql2+") sql_tc  ) sql_tb  order by TRANS_DATE desc ";
		return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER,params);

	}



	/**
	 * 查询TANO
	 * @throws Exception
	 */
	public SqlResult<M925> findTano(SqlParam<M925> params) throws Exception {
		return super.findRows("SELECT DISTINCT(TANO) FROM bala_cust_sign bat ", SubDatabase.DATABASE_BALA_CENTER, params);
	}


	/**
	 * 查询TANO
	 * @throws Exception
	 */
	public String findBalaName(String balaCode) throws Exception {
		String sql = "select t.bala_name from bala_prod_info t where t.bala_code='"+balaCode+"'";
		return super.findRow(String.class,sql, SubDatabase.DATABASE_BALA_CENTER,null);

	}
}