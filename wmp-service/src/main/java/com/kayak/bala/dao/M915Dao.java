// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M915;
// import com.kayak.bala.model.M933;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.until.MakeSqlUntil;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Repository;
//
// import java.util.List;
//
// @Repository
// public class M915Dao extends ComnDao {
//     @Autowired
//     private ReportformUtil reportformUtil;
//
//     public List<M915> findM915s(SqlParam<M915> params) throws Exception {
//         String sql = "SELECT tano,prod_code,prod_name,trans_orgno,orgname,branch_code,start_date,end_date,transfer_status," +
//                 "transfer_date,earning_amt,daliy_vol,transfer_serno,system_no,create_time,update_time FROM bala_transfer_org_list where 1=1 ";
//         //sql = sql +reportformUtil.getOrgIdForOrgLevel("");
//         sql = MakeSqlUntil.makeSql(sql,params.getParams(), M915.class);
//         sql = sql + " order by  transfer_date desc ";
//         return super.findRows(M915.class,sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel());
//     }
//
//     public List<M915> findM915Count(SqlParam<M915> params) throws Exception {
//         String sql="SELECT trans_orgno,sum(earning_amt) as earning_amt, "+
//                 "sum(daliy_vol) as daliy_vol FROM bala_transfer_org_list group by branch_code,trans_orgno";
//         sql = MakeSqlUntil.makeSql(sql.toString(),params.getParams(), M915.class);
//         return super.findRows(M915.class,sql, SubDatabase.DATABASE_BALA_CENTER, params.getModel());
//     }
// }
