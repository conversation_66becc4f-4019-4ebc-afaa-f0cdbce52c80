// package com.kayak.bala.dao;
//
// import com.kayak.bala.model.M935;
// import com.kayak.base.dao.ComnDao;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.until.MakeSqlUntil;
// import org.springframework.stereotype.Repository;
//
// @Repository
// public class M935Dao extends ComnDao {
//     public SqlResult<M935> findM935(SqlParam<M935> params) throws Exception {
//         params.setMakeSql(true);
//         String sql = " SELECT TRANS_SERNO,APP_SERNO,TRANS_CODE,TRANS_NAME,STATUS FROM AGGREGATION_TRANS_LOG WHERE STATUS IN('3','5','6','7') ORDER BY CRT_DATE,CRT_TIME ";
//         return super.findRows(sql, SubDatabase.DATABASE_BALA_CENTER, params);
//     }
//
//
// }
