package com.kayak.bala.dao;

import com.kayak.bala.model.M937;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 还款流水
 */
@Repository
public class M937Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;

	/**
	 * 还款流水查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M937> findBalaCustTransReq(SqlParam<M937> params) throws Exception {
		params.setMakeSql(false);
		StringBuffer sb1 = new StringBuffer();
		sb1.append("select * from ( select t.busi_code,\n" +
				"       t.busi_date,\n" +
				"       t.busi_date as query_busi_date,\n" +
				"       t.trans_acct_no,\n" +
				"       t.acct_no,\n" +
				"       t.trans_status,\n" +
				"       t.cust_no,\n" +
				"       t.host_date,\n" +
				"       sum(t.app_amt) as app_amt,\n" +
				"       sum(t.APP_VOL) as app_vol,\n" +
				"       sum(t.ack_amt) as ack_amt,\n" +
				"       sum(t.ack_vol) as ack_vol,\n" +
				"       sum(nvl(t.APP_VOL,0) - nvl(t.ADVANCE_INCOME,0)) as advance_income,t.ID_TYPE,t.id_code,t.cust_name\n" +
				"  from bala_cust_trans_req t\n" +
				" where t.busi_code in ('098','024', '022')\n");

		if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
			sb1.append(" and t.busi_date >= "+params.getModel().getBusiDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiCode())){
			sb1.append(" and t.busi_code = "+params.getModel().getBusiCode());
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb1.append(" and t.busi_date <= "+params.getModel().getStartEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb1.append(" and t.acct_no = '"+params.getModel().getAcctNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
			sb1.append(" and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustNo())){
			sb1.append(" and t.cust_no = '"+params.getModel().getCustNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getIdCode())){
			sb1.append(" and t.id_code = '"+params.getModel().getIdCode()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getIdType())){
			sb1.append(" and t.id_type = '"+params.getModel().getIdType()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustName())){
			sb1.append(" and t.cust_name = '"+params.getModel().getCustName()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb1.append(" and t.acct_no = '"+params.getModel().getAcctNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransStatus())){
			sb1.append(" and t.trans_status = '"+params.getModel().getTransStatus()+"' ");
		}
		//sb1.append(reportformUtil.getOrgIdForOrgLevel("t."));
		sb1.append(" group by t.busi_code,\n" +
				"          t.busi_date,\n" +
				"          t.trans_acct_no,\n" +
				"          t.trans_status,\n" +
				"          t.acct_no,\n" +
				"          t.cust_no,\n" +
				"          t.host_date,t.ID_TYPE,t.id_code,t.cust_name\n");
		StringBuffer sb2 = new StringBuffer();
		sb2.append(" select * from (select t.busi_code,\n" +
				"       t.busi_date,\n" +
				"       t.busi_date as query_busi_date,\n" +
				"       t.trans_acct_no,\n" +
				"       t.acct_no,\n" +
				"       t.trans_status,\n" +
				"       t.cust_no,\n" +
				"       t.host_date,\n" +
				"       sum(t.app_amt) as app_amt,\n" +
				"       sum(t.APP_VOL) as app_vol,\n" +
				"       sum(t.ack_amt) as ack_amt,\n" +
				"       sum(t.ack_vol) as ack_vol,\n" +
				"      sum(nvl(t.APP_VOL,0) - nvl(t.ADVANCE_INCOME,0)) as advance_income,t.ID_TYPE,t.id_code,t.cust_name\n" +
				"  from bala_cust_trans_req_h t\n" +
				" where t.busi_code in ('098','024', '022')\n");
		if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
			sb2.append(" and t.busi_date >= "+params.getModel().getBusiDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiCode())){
			sb2.append(" and t.busi_code = "+params.getModel().getBusiCode());
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb2.append(" and t.busi_date <= "+params.getModel().getStartEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb2.append(" and t.acct_no = '"+params.getModel().getAcctNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
			sb2.append(" and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustNo())){
			sb2.append(" and t.cust_no = '"+params.getModel().getCustNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getIdCode())){
			sb2.append(" and t.id_code = '"+params.getModel().getIdCode()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getIdType())){
			sb2.append(" and t.id_type = '"+params.getModel().getIdType()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb2.append(" and t.acct_no = '"+params.getModel().getAcctNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustName())){
			sb2.append(" and t.cust_name = '"+params.getModel().getCustName()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransStatus())){
			sb2.append(" and t.trans_status = '"+params.getModel().getTransStatus()+"' ");
		}
		//sb2.append(reportformUtil.getOrgIdForOrgLevel("t."));
		sb2.append(" group by t.busi_code,\n" +
				"          t.busi_date,\n" +
				"          t.trans_acct_no,\n" +
				"          t.trans_status,\n" +
				"          t.acct_no,\n" +
				"          t.cust_no,\n" +
				"          t.host_date,t.ID_TYPE,t.id_code,t.cust_name");
		/**StringBuffer sb3 = new StringBuffer();
		sb3.append(" select t.busi_code,\n" +
				"               t.busi_date,\n" +
				"               t.busi_date as query_busi_date,\n" +
				"               t.trans_acct_no,\n" +
				"               t.acct_no,\n" +
				"               t.trans_status,\n" +
				"               t.cust_no,\n" +
				"               t.BUSI_DATE as host_date,\n" +
				"               sum(t.app_amt) as app_amt,\n" +
				"               sum(t.APP_VOL) as app_vol,\n" +
				"               sum(t.ack_amt) as ack_amt,\n" +
				"               sum(t.ack_vol) as ack_vol,\n" +
				"               sum(nvl(t.APP_VOL, 0) - nvl(t.ADVANCE_INCOME, 0)) as advance_income,\n" +
				"               t2.ID_TYPE,\n" +
				"               t2.id_code,\n" +
				"               t2.cust_name\n" +
				"          from bala_cust_trans_cfm t\n" +
				"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				"         where t.busi_code in ('022', '024', '098', '198', '124', '122') ");
		if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
			sb3.append("and t.busi_date >= "+params.getModel().getBusiDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiCode())){
			sb3.append("and t.busi_code = "+params.getModel().getBusiCode());
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb3.append("and t.busi_date <= "+params.getModel().getStartEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb3.append("and t.acct_no = '"+params.getModel().getAcctNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
			sb3.append("and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustNo())){
			sb3.append("and t.cust_no = '"+params.getModel().getCustNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransStatus())){
			sb3.append("and t.trans_status = '"+params.getModel().getTransStatus()+"' ");
		}
		sb3.append(reportformUtil.getOrgIdForOrgLevel("t."));
		sb3.append(" group by t.busi_code,\n" +
				"                  t.busi_date,\n" +
				"                  t.trans_acct_no,\n" +
				"                  t.trans_status,\n" +
				"                  t.acct_no,\n" +
				"                  t.cust_no,\n" +
				"                  t.BUSI_DATE,\n" +
				"                  t2.ID_TYPE,\n" +
				"                  t2.id_code,\n" +
				"                  t2.cust_name ");
		StringBuffer sb4 = new StringBuffer();
		sb4.append(" select t.busi_code,\n" +
				"               t.busi_date,\n" +
				"               t.busi_date as query_busi_date,\n" +
				"               t.trans_acct_no,\n" +
				"               t.acct_no,\n" +
				"               t.trans_status,\n" +
				"               t.cust_no,\n" +
				"               t.BUSI_DATE as host_date,\n" +
				"               sum(t.app_amt) as app_amt,\n" +
				"               sum(t.APP_VOL) as app_vol,\n" +
				"               sum(t.ack_amt) as ack_amt,\n" +
				"               sum(t.ack_vol) as ack_vol,\n" +
				"               sum(nvl(t.APP_VOL, 0) - nvl(t.ADVANCE_INCOME, 0)) as advance_income,\n" +
				"               t2.ID_TYPE,\n" +
				"               t2.id_code,\n" +
				"               t2.cust_name\n" +
				"          from bala_cust_trans_cfm_h t\n" +
				"          left join bala_cust_sign t2 on t.cust_no = t2.cust_no\n" +
				"         where t.busi_code in ('022', '024', '098', '198', '124', '122') ");
		if(StringUtils.isNotBlank(params.getModel().getBusiDate())){
			sb4.append("and t.busi_date >= "+params.getModel().getBusiDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getBusiCode())){
			sb4.append("and t.busi_code = "+params.getModel().getBusiCode());
		}
		if(StringUtils.isNotBlank(params.getModel().getStartEndDate())){
			sb4.append("and t.busi_date <= "+params.getModel().getStartEndDate());
		}
		if(StringUtils.isNotBlank(params.getModel().getAcctNo())){
			sb4.append("and t.acct_no = '"+params.getModel().getAcctNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getQueryBusiDate())){
			sb4.append("and t.busi_date = '"+params.getModel().getQueryBusiDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustNo())){
			sb4.append("and t.cust_no = '"+params.getModel().getCustNo()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransStatus())){
			sb4.append("and t.trans_status = '"+params.getModel().getTransStatus()+"' ");
		}
		sb4.append(reportformUtil.getOrgIdForOrgLevel("t."));
		sb4.append(" group by t.busi_code,\n" +
				"                  t.busi_date,\n" +
				"                  t.trans_acct_no,\n" +
				"                  t.trans_status,\n" +
				"                  t.acct_no,\n" +
				"                  t.cust_no,\n" +
				"                  t.BUSI_DATE,\n" +
				"                  t2.ID_TYPE,\n" +
				"                  t2.id_code,\n" +
				"                  t2.cust_name ");*/
		String sb = "select * from (" +sb1.toString() +
				"\n ) union all " + sb2.toString() +")  )  order by busi_date desc nulls last ";
				//"\n union all " + sb3.toString() +
				//"\n union all " + sb4.toString() + ")";
		return super.findRows(sb, SubDatabase.DATABASE_BALA_CENTER, params);
	}
}