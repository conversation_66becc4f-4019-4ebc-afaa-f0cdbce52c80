package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M917Dao;
import com.kayak.bala.model.M917;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@APIDefine(desc = "分机构源泉宝产品签约情况", model = M917.class)
public class M917Service {

	@Autowired
	private M917Dao m917Dao;


	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "分机构源泉宝产品签约情况统计", auth = APIAuth.YES)
	public SqlResult<M917> findBalaCustSign(SqlParam<M917> params) throws Exception {
		SqlResult<M917> sqlResult = m917Dao.findBalaCustSign(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		return sqlResult;
	}

}
