package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M938Dao;
import com.kayak.bala.model.M938;
import com.kayak.batch.model.M635;
import com.kayak.common.constants.SystemNo;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M229;
import com.kayak.fund.model.M231;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "底层产品交易统计表", model = M938.class)
public class M938Service {
    @Autowired
    private M938Dao m938Dao;
    @Autowired
    private M215Service m215Service;


    @API(desc = "查询底层产品交易统计信息", auth = APIAuth.YES)
    public SqlResult<M938> findM938s(SqlParam<M938> params) throws Exception {
        SqlResult<M938> sqlResult = m938Dao.findM938s(params);
       /** List<M938> volList = sqlResult.getRows();
        if (volList != null && volList.size() > 0){
            for (M938 m938 : volList){
                Map<String, Object> prodParam = new HashMap<>();
                prodParam.put("prodCode", m938.getProdCode());
                List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
                for(Map<String, String> prodPara : prodParaList) {
                    m938.setProdName(prodPara.get("prod_name"));	//产品状态
                }
            }
        }*/
        return sqlResult;
    }
}
