package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M920Dao;
import com.kayak.bala.model.M920;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.model.M102;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "底层账户流水查询服务", model = M920.class)
public class M920Service {

	@Autowired
	private M920Dao m921Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "底层账户流水查询", auth = APIAuth.YES)
	public SqlResult<M920> findbalaCustAcctReq(SqlParam<M920> params) throws Exception {
		SqlResult<M920> sqlResult = m921Dao.findbalaCustAcctReq(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			if (StringUtils.isNotBlank(item.getTano())){
				try{
						item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
				}catch (Exception ex){
					throw new RuntimeException("M920"+ex.getMessage());
				}

			}

			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(true);
		return sqlResult;

	}
}
