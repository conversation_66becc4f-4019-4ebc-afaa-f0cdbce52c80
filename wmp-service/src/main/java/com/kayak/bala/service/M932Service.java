package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M932Dao;
import com.kayak.bala.model.M932;
import com.kayak.bala.model.M932;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.dao.M102Dao;
import com.kayak.cust.model.M101;
import com.kayak.cust.model.M102;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "分区域行源泉宝产品签约情况", model = M932.class)
public class M932Service {

	@Autowired
	private M932Dao m932Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "分区域行源泉宝产品签约情况统计", auth = APIAuth.YES)
	public SqlResult<M932> findBalaCustSign(SqlParam<M932> params) throws Exception {
		SqlResult<M932> sqlResult = m932Dao.findBalaCustSign(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		return sqlResult;
	}

}
