package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M934Dao;
import com.kayak.bala.model.M934;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "源泉宝过户数据统计", model = M934.class)
public class M934Service {
    @Autowired
    private M934Dao m934Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "源泉宝过户数据统计查询", auth = APIAuth.YES)
    public SqlResult<M934> findM934(SqlParam<M934> params) throws Exception {
        SqlResult<M934> sqlResult = m934Dao.findM934(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        sqlResult.setDesensitized(false);
        return sqlResult;
    }

    @API(desc = "查询产品代码")
    public SqlResult<M934> findProdCode(SqlParam<M934> params) throws Exception {
        SqlResult<M934> sqlResult = m934Dao.findProdCode(params);
        return sqlResult;
    }
}
