package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M914Dao;
import com.kayak.bala.model.M914;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@APIDefine(desc = "源泉宝客户交易统计表", model = M914.class)
public class M914Service {
    @Autowired
    private M914Dao m914Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询源泉宝客户交易统计信息", auth = APIAuth.YES)
    public SqlResult<M914> findM914s(SqlParam<M914> params) throws Exception {
        SqlResult<M914> sqlResult = m914Dao.findM914s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M914> volList = sqlResult.getRows();
        for (M914 m914 : volList){
            M001 m001 = m001Dao.get(m914.getSubBranchCode());
            if (m001 != null){
                m914.setSubBranchCodeName(m001.getOrgname());
            }

            M001 m0011 = m001Dao.get(m914.getBranchCode());
            if (m0011 != null){
                m914.setBranchCodeName(m0011.getOrgname());
            }
        }
        sqlResult.setRows(volList);
        return sqlResult;
    }
}
