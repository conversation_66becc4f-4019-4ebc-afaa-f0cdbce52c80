package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M908Dao;
import com.kayak.bala.dao.M908Dao;
import com.kayak.bala.model.M908;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "份额核对处理服务", model = M908.class)
public class M908Service {

	@Autowired
	private M908Dao m908Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询T份额核对信息", auth = APIAuth.YES)
	public SqlResult<M908> findBalaCheckVol(SqlParam<M908> params) throws Exception {
		SqlResult<M908> sqlResult = m908Dao.findBalaCheckVol(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

	@API(desc = "修改份额核对状态--已阅", params = "M908")
	public String update(SqlParam<M908> params) throws Exception {
		M908 model = params.getModel();
		m908Dao.update(params);
		return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
	}
}
