// package com.kayak.bala.service;
//
// import com.alibaba.fastjson.JSON;
// import com.hundsun.jrescloud.rpc.annotation.CloudReference;
// import com.kayak.aspect.annotations.API;
// import com.kayak.aspect.annotations.APIAuth;
// import com.kayak.aspect.annotations.APIDefine;
// import com.kayak.bala.dao.M936Dao;
// import com.kayak.bala.model.M936;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.exception.PromptException;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.system.RequestSupport;
// import com.kayak.core.util.Tools;
// // import com.kayakwise.bala.api.T921DubboDecorator;
// // import com.kayakwise.bala.req.T921ServiceRequest;
// // import com.kayakwise.bala.resp.T921ServiceResponse;
// import org.apache.commons.lang.StringUtils;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import java.util.Map;
// import java.util.stream.Collectors;
//
// @Service
// @APIDefine(desc = "璧勯噾娴佹按瀵硅处宸敊琛ㄥ疄浣撶被鏈嶅姟", model = M936.class)
// public class M936Service {
// 	protected static final Logger log = LoggerFactory.getLogger(M936Service.class);
//
// 	@Autowired
// 	private M936Dao m936Dao;
//
// 	@CloudReference
// // 	private T921DubboDecorator t921DubboDecorator;
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	@API(desc = "鏌ヨ璧勯噾娴佹按瀵硅处宸敊琛ㄥ疄浣撶被淇℃伅", auth = APIAuth.YES)
// 	public SqlResult<M936> findM936s(SqlParam<M936> params) throws Exception {
// 		params.setMakeSql(true);
// 		SqlResult<M936> sqlResult = m936Dao.findM936s(params);
// 		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
// 			if(Tools.isNotBlank(item.getTano())){
// 				try {
// 					item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
// 				} catch (Exception e) {
// 					log.error(e.getMessage());
// 				}
// 			}
// 			if(Tools.isNotBlank(item.getProdCode()) && Tools.isBlank(item.getProdName())){
// 				item.setProdName(reportformUtil.getBalaProdName(item.getProdCode()));
// 			}
// 			return item;
// 		}).collect(Collectors.toList()));
// 		return sqlResult;
// 	}
//
// 	@API(desc = "宸敊澶勭悊", auth = APIAuth.NO)
// 	public String dealError(SqlParam<M936> params) throws Exception {
// 		//鑾峰彇绠＄悊鍙伴€変腑鐨勬暟鎹?
// 		String channelSerno = params.getModel().getChannelSerno();
// 		T921ServiceRequest t921ServiceRequest = new T921ServiceRequest();
// 		t921ServiceRequest.setChannelSerno(channelSerno);
// // 		T921ServiceResponse t921ServiceResponse = t921DubboDecorator.excute(t921ServiceRequest);
//
// 		if (!"000000".equals(t921ServiceResponse.getRtnCode())) {
// 			String rtnDesc = t921ServiceResponse.getRtnDesc();
// 			if (rtnDesc != null) {
// 				throw new PromptException("鎿嶄綔璋冭处澶辫触锛?+t921ServiceResponse.getRtnDesc());
// 			}
// 		}else{
// 			if(StringUtils.isNotBlank(t921ServiceResponse.getTransStatus())){
// 				String transStatus=t921ServiceResponse.getTransStatus();
// 				//S-杩斿洖鎴愬姛锛岀敵璇疯〃涓璽rans_status 鏇存柊涓猴細0   CAPITAL_STATUS鏇存柊涓猴細3
// 				if("S".equals(transStatus)){
// 					params.getModel().setTransStatus("0");
// 					params.getModel().setCapitalStatus("3");
// 					m936Dao.updateStatus(params);
// 				}else if("F".equals(transStatus) || "E".equals(transStatus)){
// 					//F-杩斿洖澶辫触 || E-鍘熶氦鏄撲笉瀛樺湪,  鐢宠琛ㄤ腑trans_status 鏇存柊涓猴細1   CAPITAL_STATUS鏇存柊涓猴細4,涓旀洿鏂颁竴涓媓ost_rtn_code host_rtn_desc
// 					params.getModel().setTransStatus("1");
// 					params.getModel().setCapitalStatus("4");
// 					params.getModel().setHostRtnCode(t921ServiceResponse.getRtnCode());
// 					params.getModel().setHostRtnDesc(t921ServiceResponse.getRtnDesc());
// 					m936Dao.updateStatus(params);
// 				}else if("N".equals(transStatus)){
// 					//N-澶勭悊涓紝 涓嶆洿鏂扮姸鎬侊紝鏇存柊涓€涓媓ost_rtn_code ,host_rtn_desc
// 					params.getModel().setHostRtnCode(t921ServiceResponse.getRtnCode());
// 					params.getModel().setHostRtnDesc(t921ServiceResponse.getRtnDesc());
// 					m936Dao.updateStatus(params);
// 				}
// 			}
// 		}
// 		return RequestSupport.updateReturnJson(true, "鎿嶄綔璋冭处瀹屾垚", null).toString();
// 	}
//
// 	private byte[] changeMapToByte(Map<String,Object> map) throws Exception {
//
// 		byte[] bytes = null;
// 		try {
// 			bytes = JSON.toJSONString(map, true).getBytes();
// 		} catch (Exception e) {
// 			log.error("map鍒癰yte[]杞崲寮傚父");
// 			throw new Exception("map鍒癰yte[]杞崲寮傚父");
// 		}
//
// 		return bytes;
// 	}
//
// }
