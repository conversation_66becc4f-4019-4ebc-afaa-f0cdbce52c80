package com.kayak.bala.service;
import com.alibaba.fastjson.JSON;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.bala.dao.M907Dao;
import com.kayak.bala.model.M907;
import com.kayak.common.component.RedisUtil;
import com.kayak.common.constants.SystemNo;
import com.kayak.config.ConfigUitl;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.core.system.constants.SystemParamConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@APIDefine(desc = "余额理财参数服务", model = M907.class)
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class M907Service {

    private final M907Dao m907Dao;

    @Autowired
    RedisUtil redisUtil;

    @API(desc = "保存余额理财参数列表")
    public String update(SqlParam<M907> param) throws Exception {
        M907 model = param.getModel();
        String paraid = model.getParaid();
        String paravalue = model.getParavalue();

        if (Tools.isBlank(paraid)) {
            return RequestSupport.updateReturnJson(true, "保存成功", null).toString();
        }
        // 等号分隔转换为list
        List<M907> params = new ArrayList<>();
        String[] paraidArr = paraid.split("=");
        String[] paravalueArr = paravalue.split("=");
        for (int i = 0; i < paraidArr.length; i++) {
            M907 p = new M907();
            p.setParaid(paraidArr[i]);
            p.setParavalue(paravalueArr[i]);
            params.add(p);
        }

        m907Dao.update(params);
        refreshCache();
        return RequestSupport.updateReturnJson(true, "保存成功", null).toString();
    }

    @API(desc = "查询余额理财参数列表", auth = APIAuth.YES)
    public SqlResult<M907> find(SqlParam<M907> params) throws Exception {
        M907 model = params.getModel();
        model.setIsdisplay(SystemParamConstants.SHOW);
        params.setMakeSql(true);
        return m907Dao.find(params);
    }


    @API(desc = "修改余额理财参数表", params = "system_no,moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay",
            auth = APIAuth.NO, operation = APIOperation.UPDATE)
    public int updateSysParam(SqlParam<M907> params) throws Exception {
        int effect = m907Dao.updateSysParam(params).getEffect();
        refreshCache();
        // 存入redis
        if(null!=params.getModel()){
            // key
            String key = "wmp:cache:param:" + params.getModel().getModuleid()+":"+params.getModel().getParaid()+":getParavalByParaid";
            // 存储
            redisUtil.set(key, params.getModel().getParavalue());
        }
        return effect;
    }


    private void refreshCache() throws Exception {
        String content = Tools.getStringFromDate("yyyy-MM-dd hh:mm:ss", new Date());
        ConfigUitl.publicNacosConfig("kcloud_system_params", content);
    }

    @API(desc = "查询某个参数", auth = APIAuth.NO)
    public SqlResult<M907> getSysParam(SqlParam<M907> params) throws Exception {
        params.setMakeSql(true);
        M907 one = m907Dao.findOne(params);
        List<M907> list = new ArrayList<>();
        list.add(one);
        return SqlResult.build(list);
    }
}

