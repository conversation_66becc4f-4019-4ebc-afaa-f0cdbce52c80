// package com.kayak.bala.service;
//
// import com.alibaba.fastjson.JSONArray;
// import com.alibaba.fastjson.JSONObject;
// import com.hundsun.jrescloud.rpc.annotation.CloudReference;
// import com.kayak.aspect.annotations.API;
// import com.kayak.aspect.annotations.APIAuth;
// import com.kayak.aspect.annotations.APIDefine;
// import com.kayak.bala.dao.M906Dao;
// import com.kayak.bala.model.M906;
// import com.kayak.base.dao.util.DaoUtil;
// import com.kayak.common.constants.ErrorDealType;
// import com.kayak.common.constants.SubDatabase;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.exception.PromptException;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.system.RequestSupport;
// import com.kayak.core.util.Tools;
// // import com.kayakwise.bala.api.T918DubboDecorator;
// // import com.kayakwise.bala.req.T918ServiceRequest;
// // import com.kayakwise.bala.resp.T918ServiceResponse;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
//
// @Service
// @APIDefine(desc = "鎵归噺宸敊澶勭悊鏈嶅姟", model = M906.class)
// public class M906Service {
//
// 	protected static final Logger log = LoggerFactory.getLogger(M906Service.class);
//
// 	static boolean result=true;
//
// 	@Autowired
// 	private M906Dao m906Dao;
//
// 	@CloudReference
// // 	private T918DubboDecorator t918DubboDecorator;
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	@API(desc = "鎵归噺宸敊澶勭悊鏁版嵁鏌ヨ", auth = APIAuth.YES)
// 	public SqlResult<M906> findBalaBatchError(SqlParam<M906> params) throws Exception {
// 		SqlResult<M906> sqlResult= m906Dao.findBalaBatchError(params);
// 		return sqlResult;
// 	}
//
// 	@API(desc = "淇敼TA鍒掓嫧璐︽埛淇℃伅", params = "m906")
// 	public String update(SqlParam<M906> params) throws Exception {
// 		m906Dao.updateAcctTa(params);
// 		return RequestSupport.updateReturnJson(true, "淇敼鎴愬姛", null).toString();
// 	}
//
// 	@API(desc = "鏌ヨTA鍒掓嫧璐︽埛淇℃伅", auth = APIAuth.NO)
// 	public SqlResult<M906> findBalaAcctTa(SqlParam<M906> params) throws Exception {
// 		return m906Dao.findBalaAcctTa(params);
// 	}
//
// 	@API(desc = "鍒嗙孩宸敊澶勭悊", auth = APIAuth.NO)
// 	public String dealError(SqlParam<M906> params) throws Exception {
// 		//鑾峰彇绠＄悊鍙伴€変腑鐨勬暟鎹?
// 		Object object = params.getParams().get("chooseData");
// 		String chooseData = (String) object;
// 		JSONArray jsonArray = JSONArray.parseArray(chooseData);
// 		if (jsonArray.size() <= 0) {
// 			log.error("鎵€閫夋暟缁勯暱搴︿负0");
// 			throw new Exception("鎵€閫夋暟缁勯暱搴︿负0");
// 		}
// 		//宸敊澶勭悊绫诲瀷
// 		String errorDealType = "";
// 		if (params.getModel().getErrorDealType().equals("dealError")){
// 			errorDealType = ErrorDealType.DEALERROR.getType();
// 			List<String> errorSernoList = new ArrayList<>();
// 			try {
// 				jsonArray.stream().forEach(item->{
// 					JSONObject updateDate = (JSONObject)item;
// 					errorSernoList.add(updateDate.get("errorSerno").toString());
// 				});
// 				T918ServiceRequest t918ServiceRequest = new T918ServiceRequest();
// 				t918ServiceRequest.setErrorSernoList(errorSernoList);
// // 				T918ServiceResponse t918ServiceResponse = t918DubboDecorator.excute(t918ServiceRequest);
// 				if (Tools.isNotBlank(t918ServiceResponse.getRtnCode()) && !"000000".equals(t918ServiceResponse.getRtnCode())) {
// 					String rtnDesc = t918ServiceResponse.getRtnDesc();
// 					if (rtnDesc != null) {
// 						throw new PromptException("鍒掓嫧澶辫触锛?+t918ServiceResponse.getRtnDesc());
// 					}
// 				}
// 			} catch (Exception e) {
// 				log.error("鍒掓嫧澶辫触"+e.getMessage());
// 				throw new PromptException("鍒掓嫧澶辫触锛?+e.getMessage());
// 			}
// 		}else{
// 			errorDealType = ErrorDealType.OFFLINE.getType();
// 			DaoUtil.doTrans(()->{
// 				jsonArray.stream().forEach(item->{
// 					try {
// 						JSONObject updateDate = (JSONObject)item;
// 						if(Tools.isNotBlank(updateDate.get("errorStatus").toString())){
// 							if(updateDate.get("errorStatus").toString().equals("2")||updateDate.get("errorStatus").toString().equals("3")||
// 									updateDate.get("errorStatus").toString().equals("4")){
// 								result=false;
// 							}
// 						}
// 						if(Tools.isNotBlank(updateDate.get("errorSerno").toString()) && result){
// 							updateDate.put("errorStatus","4");
// 							m906Dao.updateErrorStatus(updateDate).getEffect();
// 						}else if(result){
// 							throw new RuntimeException("绾夸笅澶勭悊澶辫触:宸敊娴佹按鍙蜂笉鑳戒负绌?);
// 						}
// 					} catch (Exception e) {
// 						log.error("绾夸笅澶勭悊澶辫触"+e.getMessage());
//
// 					//	throw new RuntimeException("绾夸笅澶勭悊澶辫触"+e.getMessage());
// 					}
// 				});
// 			}, SubDatabase.DATABASE_BALA_CENTER);
// 		}
//
// 		//缁勮鎵归噺澶辫触璁板綍娴佹按鍙?
// 		List<String> errorSernoList = new ArrayList<>();
//
// 		for (int i=0;i<jsonArray.size();i++){
// 			JSONObject _json = (JSONObject) jsonArray.get(i);
// 			errorSernoList.add((String) _json.get("errorSerno"));
// 		}
//
// 		//杩斿洖椤甸潰鍙傛暟
// 		Map<String,Object> map1 = new HashMap<>();
// 		if(!result){
// 			return RequestSupport.updateReturnJson(false,"绾夸笅澶勭悊澶辫触:澶勭悊鐘舵€佸凡澶勭悊瀹屾垚,涓嶈兘杩涜绾夸笅澶勭悊",map1).toString();
// 		}
// 		map1.put("length",jsonArray.size());
// 		return RequestSupport.updateReturnJson(true, "鍚庡彴"+(errorDealType.equals(ErrorDealType.DEALERROR.getType())?"璋冭处":"绾夸笅澶勭悊")+"瀹屾垚锛屽叡閫夋嫨"+jsonArray.size()+"鏉″樊閿?, map1).toString();
// 	}
// }
