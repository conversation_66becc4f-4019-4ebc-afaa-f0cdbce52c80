// package com.kayak.bala.service;
//
// import com.hundsun.jrescloud.rpc.annotation.CloudReference;
// import com.kayak.aspect.annotations.API;
// import com.kayak.aspect.annotations.APIAuth;
// import com.kayak.aspect.annotations.APIDefine;
// import com.kayak.bala.dao.M910Dao;
// import com.kayak.bala.model.M910;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.exception.PromptException;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.system.RequestSupport;
// // import com.kayakwise.bala.api.T916DubboDecorator;
// // import com.kayakwise.bala.req.T916ServiceRequest;
// // import com.kayakwise.bala.resp.T916ServiceResponse;
// import com.kayakwise.wmp.base.util.Tools;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import java.util.stream.Collectors;
//
// @Service
// @APIDefine(desc = "婧愭硥瀹漈A娓呯畻鍒掓嫧", model = M910.class)
// public class M910Service {
//
//     private final Logger log = LoggerFactory.getLogger(this.getClass());
//     @Autowired
//     private M910Dao m910Dao;
//     @CloudReference
// //     private T916DubboDecorator t916DubboDecorator;
//
//     @Autowired
//     private ReportformUtil reportformUtil;
//
//     @API(desc = "鏌ヨ婧愭硥瀹漈A娓呯畻鍒掓嫧淇℃伅", auth = APIAuth.YES)
//     public SqlResult<M910> findM910s(SqlParam<M910> params) throws Exception {
//         params.setMakeSql(true);
//         SqlResult<M910> sqlResult = m910Dao.findM910s(params);
//         reportformUtil.checkMaxExcel(sqlResult.getRows().size());
//         sqlResult.setRows(sqlResult.getRows().stream().map(item->{
//             if(com.kayak.core.util.Tools.isNotBlank(item.getTano())){
//                 try {
//                     item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
//                 } catch (Exception e) {
//                     log.error(e.getMessage());
//                 }
//             }
//             return item;
//         }).collect(Collectors.toList()));
//         return sqlResult;
//     }
//
//     @API(desc = "纭鍒掓嫧", auth = APIAuth.YES)
//     public String confirm(SqlParam<M910> params) throws Exception {
//         M910 m910 = params.getModel();
//         T916ServiceRequest t916ServiceRequest = new T916ServiceRequest();
//         t916ServiceRequest.setAckDate(m910.getAckDate());
//         t916ServiceRequest.setTano(m910.getTano());
//         t916ServiceRequest.setBalaCode(m910.getBalaCode());
//         t916ServiceRequest.setSystemNo(m910.getSystemNo());
//         t916ServiceRequest.setTransOrder(m910.getTransOrder());
// //         T916ServiceResponse response = t916DubboDecorator.excute(t916ServiceRequest);
//         if (Tools.isObjNotBlank(response) && !"000000".equals(response.getRtnCode())) {
//             String rtnDesc = response.getRtnDesc();
//             if (rtnDesc != null) {
//                 throw new PromptException("鍒掓嫧澶辫触锛?+response.getRtnDesc());
//             }else {
//                 throw new PromptException("鍒掓嫧澶辫触!");
//             }
//         }
//         return RequestSupport.updateReturnJson(true, "宸茬‘璁ゅ垝鎷?, null).toString();
//     }
// }
