package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M919Dao;
import com.kayak.bala.model.M919;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "源泉宝签约变更解约流水服务", model = M919.class)
public class M919Service {

	@Autowired
	private M919Dao m919Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "源泉宝签约变更解约流水查询", auth = APIAuth.YES)
	public SqlResult<M919> findBalaCustSignTransLog(SqlParam<M919> params) throws Exception {
		SqlResult<M919> sqlResult = m919Dao.findBalaCustSignTransLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
