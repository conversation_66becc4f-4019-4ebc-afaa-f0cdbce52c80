// ackage com.kayak.bala.service;
//
// import com.hundsun.jrescloud.rpc.annotation.CloudReference;
// import com.kayak.aspect.annotations.API;
// import com.kayak.aspect.annotations.APIAuth;
// import com.kayak.aspect.annotations.APIDefine;
// import com.kayak.bala.dao.M901Dao;
// import com.kayak.bala.dao.M902Dao;
// import com.kayak.bala.model.M901;
// import com.kayak.bala.model.M902;
// import com.kayak.common.constants.Yes1No0;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.exception.PromptException;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.sql.UpdateResult;
// import com.kayak.core.system.RequestSupport;
// import com.kayak.core.util.Tools;
// import com.kayak.graphql.model.FetcherData;
// import com.kayak.prod.service.M215Service;
// // import com.kayakwise.bala.api.T915DubboDecorator;
// // import com.kayakwise.bala.req.T915ServiceRequest;
// // import com.kayakwise.bala.resp.T915ServiceResponse;
// import org.apache.commons.collections4.CollectionUtils;
// import org.apache.commons.lang3.StringUtils;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
// @Service
// @APIDefine(desc = "底层产品信息表服务", model = M902.class)
// public class M902Service {
//
// 	private final Logger log = LoggerFactory.getLogger(this.getClass());
// 	@CloudReference
// // 	private T915DubboDecorator t915DubboDecorator;
//
// 	@Autowired
// 	private M902Dao m902Dao;
//
// 	@Autowired
// 	private M901Dao m901Dao;
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	@Autowired
// 	private M215Service m215Service;
//
// 	@API(desc = "查询底层产品信息", auth = APIAuth.YES)
// 	public SqlResult<M902> findAccTaInfo(SqlParam<M902> params) throws Exception {
// 		SqlResult<M902> sqlResult=m902Dao.findAccTaInfo(params);
// 		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
// 			if(Tools.isNotBlank(item.getTano())){
// 				try {
// 					item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
// 				} catch (Exception e) {
// 					log.error(e.getMessage());
// 				}
// 			}
// 			return item;
// 		}).collect(Collectors.toList()));
// 		return sqlResult;
// 	}
//
// 	@API(desc = "修改底层产品信息", params = "m902")
// 	public String update(SqlParam<M902> params) throws Exception {
// 		M902 model = params.getModel();
// 		m902Dao.update(params);
// 		//修改成功后，将源泉宝产品基本信息的产品风险等级改为，同一产品下底层产品中产品风险等级最高值。
// 		String prodRiskLevelMax="";
// 		String balaCode="";
// 		SqlResult<M902> sqlResult=m902Dao.findRiskLevelByBalaCode(params);
// 		if(sqlResult != null && sqlResult.getRows().size() > 0){
// 			List<M902> accList=sqlResult.getRows();
// 			if(null!=accList && accList.size()>0){
// 				prodRiskLevelMax=accList.get(0).getProdRiskLevel();
// 				balaCode=accList.get(0).getBalaCode();
// 			}
// 		}
// 		Map<String, Object> sqlParam = new HashMap<>();
// 		sqlParam.put("balaCode", balaCode);
// 		sqlParam.put("prodRiskLevel", prodRiskLevelMax);
// 		m901Dao.updateProdRiskLevel(new FetcherData<>(sqlParam, M901.class));
// 		return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
// 	}
//
// 	@API(desc = "新增底层产品信息", params = "m902")
// 	public String addProd(SqlParam<M902> params) throws Exception {
// 		M902 model = params.getModel();
// 		//下面是原来的代码，root是写死的法人代码，跟前端传入的法人代码不一致，所以查不出来数据。 qiaopc
// 		//Map<String, String> prodInfoMap = m215Service.getProdInfo("ROOT", model.getProdType(), model.getTano(), model.getProdCode());
// 		Map<String, String> prodInfoMap = m215Service.getProdInfo((String) params.getAuthInfo().get("legalCode"), model.getProdType(), model.getTano(), model.getProdCode());
// 		model.setProdName(prodInfoMap.get("prod_name"));
// 		m902Dao.add(model);
// 		//修改成功后，将源泉宝产品基本信息的产品风险等级改为，同一产品下底层产品中产品风险等级最高值。
// 		String prodRiskLevelMax="";
// 		String balaCode="";
// 		SqlResult<M902> sqlResult=m902Dao.findRiskLevelByBalaCode(params);
// 		if(sqlResult != null && sqlResult.getRows().size() > 0){
// 			List<M902> accList=sqlResult.getRows();
// 			if(null!=accList && accList.size()>0){
// 				prodRiskLevelMax=accList.get(0).getProdRiskLevel();
// 				balaCode=accList.get(0).getBalaCode();
// 			}
// 		}
// 		Map<String, Object> sqlParam = new HashMap<>();
// 		sqlParam.put("balaCode", balaCode);
// 		sqlParam.put("prodRiskLevel", prodRiskLevelMax);
// 		m901Dao.updateProdRiskLevel(new FetcherData<>(sqlParam, M901.class));
// 		return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
// 	}
//
// 	@API(desc = "删除底层产品信息", params = "m902")
// 	public String deleteProd(SqlParam<M902> params) throws Exception {
// 		M902 model = params.getModel();
// 		//删除
// 		UpdateResult delete = m902Dao.delete(model);
// 		if (delete.getEffect()==1){
// 			return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
// 		}else {
// 			return RequestSupport.updateReturnJson(false, "删除失败", null).toString();
// 		}
//
// 	}
//
//
//
// 	@API(desc = "上架", auth = APIAuth.YES)
// 	public String saleOnProd(SqlParam<M902> params) throws Exception {
// 		M902 m902 = params.getModel();
// 		m902.setProdStatus("1");
// 		m902Dao.updateProdStatus(m902);
// 		return RequestSupport.updateReturnJson(true, "产品已上架", null).toString();
// 	}
//
// 	@API(desc = "下架", auth = APIAuth.YES)
// 	public String saleOffProd(SqlParam<M902> params) throws Exception {
// 		M902 m902 = params.getModel();
// 		m902.setProdStatus("0");
// 		m902Dao.updateProdStatus(m902);
// 		return RequestSupport.updateReturnJson(true, "产品已下架", null).toString();
// 	}
//
// 	@API(desc = "查询底层产品净值信息", auth = APIAuth.NO)
// 	public SqlResult<M902> findNav(SqlParam<M902> params) throws Exception {
// 		return m902Dao.findNav(params);
// 	}
//
// 	@API(desc = "查询TANO", auth = APIAuth.NO)
// 	public SqlResult<M902> findTano(SqlParam<M902> params) throws Exception {
// 		SqlResult<M902> sqlResult = m902Dao.findTano(params);
// 		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
// 			if(Tools.isNotBlank(item.getTano())){
// 				try {
// 					item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
// 				} catch (Exception e) {
// 					log.error(e.getMessage());
// 				}
// 			}
// 			return item;
// 		}).collect(Collectors.toList()));
// 		return sqlResult;
// 	}
//
// 	@API(desc = "查询产品代码", auth = APIAuth.NO)
// 	public SqlResult<M902> findProdCode(SqlParam<M902> params) throws Exception {
// 		SqlResult<M902> sqlResult=m902Dao.findProdCode(params);
// 		return sqlResult;
// 	}
//
// 	@API(desc = "同步产品信息", auth = APIAuth.YES)
// 	public String synBalaProdInfo(SqlParam<M902> params) throws Exception {
// 		try{
// 			T915ServiceRequest t915ServiceRequest = new T915ServiceRequest();
// // 			T915ServiceResponse response = t915DubboDecorator.excute(t915ServiceRequest);
// 			// 若modifySucessFlag为0，表示新增/修改失败
// 			if (Yes1No0.NO.equals(response.getRtnCode())) {
// 				String rtnDesc = response.getRtnDesc();
// 				if (rtnDesc != null) {
// 					throw new PromptException("同步信息失败："+response.getRtnDesc());
// 				}
// 			}
// 		}catch (Exception ex){
// 			throw new PromptException("同步信息失败："+ex.getMessage());
// 		}
//
// 		return  RequestSupport.updateReturnJson(true, "同步成功", null).toString();
// 	}
//
// 	@API(desc = "查询余额tano列表", auth = APIAuth.NO)
// 	public SqlResult<Map> findTanoList(SqlParam<M902> params) throws Exception {
//
// 		SqlResult<Map> result = new SqlResult<>();
// 		List<Map> tanoList = m902Dao.findTanoList(params);
// 		if(CollectionUtils.isNotEmpty(tanoList)){
// 			tanoList.forEach(item->{
// 				if(StringUtils.isNotBlank(item.get("no").toString())){
// 					try {
// 						if(StringUtils.isBlank(reportformUtil.getBalaTaName(item.get("no").toString()))){
// 							item.put("name","");
// 						}else{
// 							item.put("name",reportformUtil.getBalaTaName(item.get("no").toString()));
// 						}
// 					} catch (Exception e) {
// 						log.error(e.getMessage());
// 					}
// 				}
// 			});
// 		}
// 		result.setRows(tanoList);
// 		return result;
// 	}
//
// 	/**
// 	 * <AUTHOR>
// 	 * @Description 查询余额底层产品代码信息
// 	 * @Date 2022/2/22
// 	 * @Param [params]
// 	 * @return com.kayak.core.sql.SqlResult<com.kayak.bala.model.M902>
// 	 **/
// 	@API(desc = "查询余额底层产品代码List", auth = APIAuth.NO)
// 	public SqlResult<M902> findBalaProdCode(SqlParam<M902> params) throws Exception {
// 		SqlResult<M902> sqlResult=m902Dao.findBalaProdCode(params);
// 		return sqlResult;
// 	}
//
// }
