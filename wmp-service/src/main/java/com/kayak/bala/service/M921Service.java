package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M921Dao;
import com.kayak.bala.model.M921;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.cust.model.M102;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "账户信息查询服务", model = M921.class)
public class M921Service {

	@Autowired
	private M921Dao m921Dao;

	@Autowired
	private M101Dao m101Dao;

	@Autowired
	private ReportformUtil reportformUtil;


	@API(desc = "账户信息查询", auth = APIAuth.YES)
	public SqlResult<M921> findBalaCustTaAcct(SqlParam<M921> params) throws Exception {
		//如果是查询条件进来，必须预先判断是否存在客户信息
		//TODO 注掉SystemNo 待验证
		params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()/*, SystemNo.BALA*/));
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		params.getModel().setAcctNo(null);
		SqlResult<M921> sqlResult = m921Dao.findBalaCustTaAcct(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			Map<String,Object> map = new HashMap<>();
			map.put("custNo",item.getCustNo());
			try {
				/**SqlParam<M101> custParams = new FetcherData<>(map, M101.class);
				custParams.setMakeSql(true);
				SqlResult<M101> cusInfo = m101Dao.findCustInfos(custParams,"");
				if(cusInfo.getRows() != null && cusInfo.getRows().size() > 0){
					item.setCustName(cusInfo.getRows().get(0).getCustName());
					item.setIdType(cusInfo.getRows().get(0).getIdType());
					item.setIdCode(cusInfo.getRows().get(0).getIdCode());
				}*/
				//获取TA名称
				//item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
				item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
			} catch (Exception e) {
				throw new RuntimeException("M921错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(true);
		return sqlResult;
	}
}
