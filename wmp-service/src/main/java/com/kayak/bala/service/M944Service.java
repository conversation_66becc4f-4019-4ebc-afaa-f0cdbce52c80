package com.kayak.bala.service;

import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M902Dao;
import com.kayak.bala.dao.M944Dao;
import com.kayak.bala.model.M944;
import com.kayak.common.constants.RtnCodeStatus;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.FileTransferAction;
import com.kayak.common.util.ReportformUtil;
import com.kayak.common.util.SequenceIncreaseUtil;
import com.kayak.core.dao.DaoService;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.service.M215Service;
import com.kayak.prod.utils.FileUtils;
import com.kayak.prod.utils.QZFileTransCode;
import com.kayak.prod.utils.QZFileUtils;
import com.kayakwise.prod.in.T235DubboDecorator;
import com.kayakwise.prod.req.T235ServiceRequest;
import com.kayakwise.prod.resp.T235ServiceResponse;
import com.kayakwise.wmp.base.pojo.host.resp.CFResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@APIDefine(desc = "产品文档服务", model = M944.class)
public class M944Service {

    private static Logger log = LoggerFactory.getLogger(M944Service.class);

    @Value("${file.tem.path:null}")
    private String temPath;

    @Autowired
    private M944Dao m944Dao;
    @Autowired
    private M902Dao m902Dao;
    @Autowired
    private SequenceIncreaseUtil sequenceIncreaseUtil;
    @Autowired
    private DaoService daoService;
    @CloudReference
    private T235DubboDecorator t235DubboDecorator;
    @Autowired
    private M215Service m215Service;
    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询产品文档信息", auth = APIAuth.NO)
    public SqlResult<M944> findM944(SqlParam<M944> params) throws Exception {
        params.setMakeSql(true);

        SqlResult<M944> sqlResult = m944Dao.findProdDocumentInfo(params);
        //获取TA名称
        sqlResult.setRows(sqlResult.getRows().stream().map(item -> {
            if (Tools.isNotBlank(item.getTano())) {
                try {
                    item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            return item;
        }).collect(Collectors.toList()));
        return sqlResult;
    }

    @API(desc = "查询产品文档信息", auth = APIAuth.YES)
    public SqlResult<M944> findM944New(SqlParam<M944> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M944> sqlResult = m944Dao.findProdDocumentInfo(params);
        //获取TA名称
        sqlResult.setRows(sqlResult.getRows().stream().map(item -> {
            if (Tools.isNotBlank(item.getTano())) {
                try {
                    item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            return item;
        }).collect(Collectors.toList()));
        return sqlResult;
    }

    @API(desc = "获取版本号", auth = APIAuth.NO)
    public String getVersion(SqlParam<M944> params) throws Exception {
        String systemNo = m902Dao.getSystemNoByProdCode(params.getModel().getBalaCode(), params.getModel().getProdCode());
        params.getModel().setSystemNo(systemNo);
        params.getModel().setDocStatus("0");//设为未启用

        Map<String, Object> map = new HashMap<>();
        map.put("systemNo", params.getModel().getSystemNo());
        map.put("tano", params.getModel().getTano());
        map.put("prodCode", params.getModel().getProdCode());
        map.put("docStatus", params.getModel().getDocStatus());

        FetcherData<M944> fetcherData = new FetcherData<>(map, M944.class);
        SqlResult<M944> prodDocumentInfoSqlResult = this.findM944(fetcherData);

        List<M944> prodDocumentInfoList = prodDocumentInfoSqlResult.getRows();
        String version;
        if (prodDocumentInfoList != null && prodDocumentInfoList.size() > 0) {
            version = prodDocumentInfoList.get(0).getDocVersion();
        } else {
            version = sequenceIncreaseUtil.increaseNum("prod_document_info", SubDatabase.DATABASE_SYS_CENTER);
        }
        return version;
    }

    @API(desc = "添加产品文档", params = "system_no,supply_code,prod_code,legal_code,doc_type,doc_path,doc_name,upload_date", auth = APIAuth.YES)
    public int addM944(SqlParam<M944> params) throws Exception {
        params.getModel().setDocStatus("0");//设为未启用

        String systemNo = m902Dao.getSystemNoByProdCode(params.getModel().getBalaCode(), params.getModel().getProdCode());
        params.getModel().setSystemNo(systemNo);

        Map<String, Object> map = new HashMap<>();
        map.put("balaCode", params.getModel().getBalaCode());
        map.put("systemNo", systemNo);
        map.put("tano", params.getModel().getTano());
        map.put("prodCode", params.getModel().getProdCode());
        map.put("docStatus", params.getModel().getDocStatus());
        map.put("docType", params.getModel().getDocType());
        map.put("onlineFlag", params.getModel().getOnlineFlag());
        map.put("docVersion", params.getModel().getDocVersion());

        FetcherData<M944> fetcherData = new FetcherData<>(map, M944.class);
        SqlResult<M944> prodDocumentInfoSqlResult = this.findM944(fetcherData);

        List<M944> prodDocumentInfoList = prodDocumentInfoSqlResult.getRows();
        if (prodDocumentInfoList != null && prodDocumentInfoList.size() > 0) {
            throw new PromptException("M94400", "系统中已经有该产品文档信息，请不要重复录入");
        }

        int effect = m944Dao.addProdDocumentInfo(params).getEffect();
        if (effect == 0) {
            throw new PromptException("M94401", "新增产品文档信息失败，请稍后再试");
        }
        return effect;
    }

    @API(desc = "修改产品文档", params = "system_no,supply_code,prod_code,legal_code,doc_type,doc_path,doc_name,upload_date", auth = APIAuth.YES)
    public int updateM944(SqlParam<M944> params) throws Exception {
        String systemNo = m902Dao.getSystemNoByProdCode(params.getModel().getBalaCode(), params.getModel().getProdCode());
        params.getModel().setSystemNo(systemNo);
        int effect = 0;
        effect = m944Dao.updateProdDocumentInfo(params).getEffect();
        if (effect == 0) {
            throw new PromptException("M94402", "修改产品文档信息失败，请刷新列表再试");
        }
        return effect;
    }

    @API(desc = "删除产品文档", params = "system_no,supply_code,prod_code,legal_code,doc_type,doc_path,doc_name,upload_date", auth = APIAuth.YES)
    public int deleteM944(SqlParam<M944> params) throws Exception {
        String systemNo = m902Dao.getSystemNoByProdCode(params.getModel().getBalaCode(), params.getModel().getProdCode());
        params.getModel().setSystemNo(systemNo);
        try {
            M944 prodDocumentInfo = params.getModel();
            File path = new File(prodDocumentInfo.getDocPath() + File.separator + prodDocumentInfo.getDocName());
            FileTransferAction.deleteFile(path);
        } catch (Throwable t) {
            log.error(t.getMessage());
        }

        int effect = 0;
        effect = m944Dao.deleteProdDocumentInfo(params).getEffect();
        if (effect == 0) {
            throw new PromptException("M94403", "删除产品文档信息失败，请刷新列表再试");
        }
        return effect;
    }

    @API(desc = "启用产品文档", auth = APIAuth.YES)
    public int useM944(SqlParam<M944> params) throws Exception {
        String systemNo = m902Dao.getSystemNoByProdCode(params.getModel().getBalaCode(), params.getModel().getProdCode());
        params.getModel().setSystemNo(systemNo);
        M944 model = params.getModel();
        if (!"0".equals(model.getDocStatus())) {
            throw new PromptException("该产品文档不是未启用状态，无法启用");
        }

        daoService.doTrans(() -> {
            //把该产品当前最新版设为过时
            Map<String, Object> map = new HashMap<>();
            map.put("balaCode", model.getBalaCode());
            map.put("systemNo", model.getSystemNo());
            map.put("tano", model.getTano());
            map.put("prodCode", model.getProdCode());
            map.put("docStatus", "1");
            FetcherData<M944> fetcherData = new FetcherData<>(map, M944.class);
            SqlResult<M944> prodDocumentInfoSqlResult = this.findM944(fetcherData);
            List<M944> prodDocumentInfoList = prodDocumentInfoSqlResult.getRows();
            if (prodDocumentInfoList != null && prodDocumentInfoList.size() > 0) {
                for (M944 m944 : prodDocumentInfoList) {
                    m944.setDocStatus("2");
                    m944Dao.updateStatus(m944);
                }
            }

            map.put("docStatus", "0");
            String osName = System.getProperty("os.name");
            String zipFilePath = (osName.toLowerCase().startsWith("win") ? "O:/wmp/" : temPath);
            FileUtils.createDir(zipFilePath + "tem");
            map.put("onlineFlag", "1");
            fetcherData = new FetcherData<>(map, M944.class);
            prodDocumentInfoSqlResult = this.findM944(fetcherData);
            prodDocumentInfoList = prodDocumentInfoSqlResult.getRows();

            //把要启用的文件设为最新版，把文件复制到暂存文件夹下
            if (prodDocumentInfoList != null && prodDocumentInfoList.size() > 0) {
                for (M944 m944 : prodDocumentInfoList) {
                    m944.setDocStatus("1");
                    m944Dao.updateStatus(m944);
                }
                String name = model.getSystemNo() + "_" +
                        model.getTano() + "_" +
                        model.getProdCode() + "_1_" +
                        model.getDocVersion() + ".zip";
                //如果不为压缩包，把暂存文件夹压缩成压缩包，并删除暂存文件夹
                if (!"A".equals(model.getDocType())) {
                    String sourceFolder = zipFilePath + "tem";
                    FileUtils.zip(sourceFolder, zipFilePath + name);
                    FileUtils.removeDir(sourceFolder);
                } else {//如果为压缩包，判断名字是否满足命名规则，不满足则复制出一个满足的
                    if (!FileUtils.isFileExists(zipFilePath, name)) {
                        FileUtils.copyFile(model.getDocPath() + model.getDocName(), zipFilePath + name);
                    }
                }
                //上传文件
                String file = QZFileUtils.uploadFile(zipFilePath + name, name, QZFileTransCode.DOC_DOWNLOAD, 0);
                //TODO: 广播文件
                T235ServiceRequest manRadioServiceRequest = new T235ServiceRequest();
                BeanUtils.copyProperties(model, manRadioServiceRequest);
                manRadioServiceRequest.setOnlineFlag("1");
                manRadioServiceRequest.setFilePath(file);
                manRadioServiceRequest.setBroadcastReceSys("100003");//网银
                CFResp<T235ServiceResponse> cfResp = t235DubboDecorator.execute(manRadioServiceRequest);
                if (!cfResp.getBody().getRtnCode().equals(RtnCodeStatus.RTNCODE)) {
                    throw new PromptException("广播手机银行失败：" + cfResp.getBody().getRtnDesc());
                }
            }
            map.put("onlineFlag", "2");
            fetcherData = new FetcherData<>(map, M944.class);
            prodDocumentInfoSqlResult = this.findM944(fetcherData);
            prodDocumentInfoList = prodDocumentInfoSqlResult.getRows();
            if (prodDocumentInfoList != null && prodDocumentInfoList.size() > 0) {
                for (M944 m944 : prodDocumentInfoList) {
                    m944.setDocStatus("1");
                    m944Dao.updateStatus(m944);
                    FileUtils.copyFile(m944.getDocPath() + m944.getDocName(), zipFilePath + "tem" + File.separator + m944.getDocName());
                }
                String name = model.getSystemNo() + "_" +
                        model.getTano() + "_" +
                        model.getProdCode() + "_2_" +
                        model.getDocVersion() + ".zip";
                if (!"A".equals(model.getDocType())) {
                    String sourceFolder = zipFilePath + "tem";
                    FileUtils.zip(sourceFolder, zipFilePath + name);
                    FileUtils.removeDir(sourceFolder);
                } else {
                    if (!FileUtils.isFileExists(zipFilePath, name)) {
                        FileUtils.copyFile(model.getDocPath() + model.getDocName(), zipFilePath + name);
                    }
                }
                String file = QZFileUtils.uploadFile(zipFilePath + name, name, QZFileTransCode.DOC_DOWNLOAD, 0);
                T235ServiceRequest manRadioServiceRequest = new T235ServiceRequest();
                BeanUtils.copyProperties(model, manRadioServiceRequest);
                manRadioServiceRequest.setOnlineFlag("2");
                manRadioServiceRequest.setFilePath(file);
                manRadioServiceRequest.setBroadcastReceSys("100003");//网银
                CFResp<T235ServiceResponse> cfResp = t235DubboDecorator.execute(manRadioServiceRequest);
                if (!cfResp.getBody().getRtnCode().equals(RtnCodeStatus.RTNCODE)) {
                    throw new PromptException("广播手机银行失败：" + cfResp.getBody().getRtnDesc());
                }
            }
        }, SubDatabase.DATABASE_BALA_CENTER);
        return 1;
    }


}
