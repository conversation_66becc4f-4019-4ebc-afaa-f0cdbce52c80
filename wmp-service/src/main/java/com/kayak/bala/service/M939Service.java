package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M939Dao;
import com.kayak.bala.model.M939;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.dao.M102Dao;
import com.kayak.cust.model.M101;
import com.kayak.cust.model.M102;
import com.kayak.fund.dao.M230Dao;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "源泉宝收益查询", model = M939.class)
public class M939Service {

	@Autowired
	private M939Dao m939Dao;

	@Autowired
	private M102Dao m102Dao;

	@Autowired
	private M230Dao m230Dao;

	@Autowired
	private M101Dao m101Dao;


	@Autowired
	private ReportformUtil reportformUtil;


	@API(desc = "源泉宝收益查询", auth = APIAuth.YES)
	public SqlResult<M939> findBalaProfitDetailed(SqlParam<M939> params) throws Exception {

		//如果是查询条件进来，必须预先判断是否存在客户信息
		StringBuffer custInfoSb = new StringBuffer();
		if(Tools.isBlank(params.getModel().getCustNo())){
			//TODO 注掉SystemNo 待验证
			params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()/*, SystemNo.BALA*/));
		}
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		params.getModel().setAcctNo(null);
		SqlResult<M939> sqlResult = m939Dao.findBalaCustPaymentLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				/**if (StringUtils.isNotBlank(item.getProdCode())){
					item.setProdName(reportformUtil.getProdName("",item.getTano(),item.getProdCode()));
				}*/
				if (StringUtils.isNotBlank(item.getTano())){
					String taName = reportformUtil.getFundTaName(item.getTano());
					if(Tools.isNotBlank(taName)){
						item.setTaName(taName);
					}else{
						item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
					}
				}
				/**if(StringUtils.isNotBlank(item.getCustNo())){
					Map<String,Object> map = new HashMap<>();
					map.put("custNo",item.getCustNo());
					SqlParam<M101> custParams = new FetcherData<>(map, M101.class);
					custParams.setMakeSql(true);
					SqlResult<M101> cusInfo = m101Dao.findCustInfos(custParams,"");
					if(cusInfo.getRows() != null && cusInfo.getRows().size() > 0) {
						item.setCustName(cusInfo.getRows().get(0).getCustName());
					}
				}*/
			}catch (Exception ex){
				throw new RuntimeException("查询失败："+ex.getMessage());
			}

			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(true);
		return sqlResult;
	}

}
