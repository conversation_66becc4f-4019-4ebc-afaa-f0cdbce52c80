package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M923Dao;
import com.kayak.bala.model.M923;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M102Dao;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.dao.M009Dao;
import com.kayak.system.model.M001;
import com.kayak.system.model.M009;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "源泉宝份额查询服务", model = M923.class)
public class M923Service {

	@Autowired
	private M923Dao m923Dao;

	@Autowired
	private M009Dao m009Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M001Dao m001Dao;



	@API(desc = "客户源泉宝份额查询", auth = APIAuth.YES)
	public SqlResult<M923> findBalaCustBaseVol(SqlParam<M923> params) throws Exception {
		//如果是查询条件进来，必须预先判断是否存在客户信息
		if(Tools.isBlank(params.getModel().getCustNo())){
			params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()));
		}
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		SqlResult<M923> sqlResult = m923Dao.findBalaCustBaseVol(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try {
				/**if(StringUtils.isNotBlank(item.getCustNo())){
					M923 cust = m923Dao.getCusts(item.getCustNo());
					if (cust != null){
						item.setCustName(cust.getCustName());
						item.setIdType(cust.getIdType());
						item.setIdCode(cust.getIdCode());
					}
					String acctNo = m923Dao.getAcctNoByTrans(item.getCustNo(),item.getTransAcctNo());
					item.setAcctNo(acctNo);
				}*/

				if(StringUtils.isNotBlank(item.getTransOrgno())){
					Map<String,Object> map1 = new HashMap<>();
					map1.put("orgno",item.getTransOrgno());
					SqlParam<M001> dateParams = new FetcherData<>(map1, M001.class);
					dateParams.setMakeSql(true);
					SqlResult<M001> m001Info = m001Dao.find(dateParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setOrgName(m001Info.getRows().get(0).getOrgname());
					}
				}

				if(StringUtils.isNotBlank(item.getBranchCode())){
					Map<String,Object> map2 = new HashMap<>();
					map2.put("orgno",item.getBranchCode());
					SqlParam<M001> dateParams = new FetcherData<>(map2, M001.class);
					dateParams.setMakeSql(true);
					SqlResult<M001> m001Info2 = m001Dao.find(dateParams);
					if(m001Info2.getRows() != null && m001Info2.getRows().size() > 0){
						item.setBranchName(m001Info2.getRows().get(0).getOrgname());
					}
				}


			} catch (Exception e) {
				throw new RuntimeException("M923错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(false);
		return sqlResult;
	}
}
