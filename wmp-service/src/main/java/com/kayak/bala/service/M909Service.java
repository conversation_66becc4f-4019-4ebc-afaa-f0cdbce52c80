package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M909Dao;
import com.kayak.bala.model.M909;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@APIDefine(desc = "源泉宝客户交易统计表", model = M909.class)
public class M909Service {
    @Autowired
    private M909Dao m909Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询源泉宝客户交易统计信息", auth = APIAuth.YES)
    public SqlResult<M909> findM909s(SqlParam<M909> params) throws Exception {
        if (StringUtils.isNotBlank(params.getModel().getAckDate()) && StringUtils.isNotBlank(params.getModel().getAckEndDate())){
            Integer min = Integer.parseInt(params.getModel().getAckDate());
            Integer max = Integer.parseInt(params.getModel().getAckEndDate());
            if (max < min) {
                throw new PromptException("开始日期不能大于结束日期");
            }
        }
        SqlResult<M909> sqlResult = m909Dao.findM909s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M909> volList = sqlResult.getRows();
        for (M909 m909 : volList){
            M001 m001 = m001Dao.get(m909.getBranchCode());
            if (m001 != null){
                m909.setBranchCodeName(m001.getOrgname());
            }

            M001 m0011 = m001Dao.get(m909.getSubBranchCode());
            if (m0011 != null){
                m909.setSubBranchCodeName(m0011.getOrgname());
            }

            String referrer = m909Dao.getReferrer(m909.getCustNo());
            if (StringUtils.isNotBlank(referrer)){
                m909.setReferrer(referrer);
            }
        }
        sqlResult.setRows(volList);
        sqlResult.setDesensitized(false);
        return sqlResult;
    }
}
