package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M901Dao;
import com.kayak.bala.dao.M912Dao;
import com.kayak.bala.model.M901;
import com.kayak.bala.model.M912;
import com.kayak.batch.model.M635;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M231;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "底层产品交易统计表", model = M912.class)
public class M912Service {
    @Autowired
    private M912Dao m912Dao;
    @Autowired
    private M215Service m215Service;
    @Autowired
    private M901Dao m901Dao;

    @API(desc = "查询底层产品交易统计信息", auth = APIAuth.YES)
    public SqlResult<M912> findM912s(SqlParam<M912> params) throws Exception {
        SqlResult<M912> sqlResult = m912Dao.findM912s(params);

       /** sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            if(Tools.isNotBlank(item.getBalaCode())){
                try {
                    Map<String,Object> map = new HashMap<>();
                    map.put("balaCode",item.getBalaCode());
                    SqlParam<M901> balaParams = new FetcherData<>(map, M901.class);
                    balaParams.setMakeSql(true);
                    SqlResult<M901> balaInfo = m901Dao.findBalaProdInfoList(balaParams);
                    if(balaInfo.getRows() != null && balaInfo.getRows().size() > 0) {
                        item.setBalaName(balaInfo.getRows().get(0).getBalaName());
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            return item;
        }).collect(Collectors.toList()));*/

        return sqlResult;
    }
}
