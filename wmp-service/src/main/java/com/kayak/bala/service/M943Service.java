package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M943Dao;
import com.kayak.bala.model.M943;
import com.kayak.common.constants.RtnCodeStatus;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.SysUtil;
import com.kayak.core.util.Tools;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@APIDefine(desc = "基金批量还款资金核对", model = M943.class)
public class M943Service {

    @Autowired
    private M943Dao m943Dao;
    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "基金批量还款资金核对", auth = APIAuth.YES)
    public SqlResult<M943> findM943s(SqlParam<M943> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M943> sqlResult = m943Dao.findM943(params);
        sqlResult.setDesensitized(true);
        return sqlResult;
    }

    @API(desc = "更新对账信息", auth = APIAuth.YES)
    public String updateCheck(SqlParam<M943> params) throws Exception {
        if(Tools.isBlank(params.getModel().getFileNo())){
            throw new PromptException("产品编号、系统编号、文件编号数据缺失，无法更新数据");
        }else{
            try{
                params.getModel().setCheckOprater(SysUtil.getSysUserParamValue("sys_user_userid").toString());
                String updateDate = reportformUtil.getWorkDate();
                params.getModel().setCheckDate(updateDate);
                m943Dao.updateCheckFlag(params);
            }catch (Exception ex){
                return RequestSupport.updateReturnJson(false, "核對失敗："+ex.getMessage(), null).toString();
            }
        }
        return RequestSupport.updateReturnJson(true, "核对保存成功，状态已更新", null).toString();
    }
}

