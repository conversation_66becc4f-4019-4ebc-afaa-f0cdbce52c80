// package com.kayak.bala.service;
//
//
//
//
//
// import com.alibaba.fastjson.JSON;
// import com.hundsun.jrescloud.rpc.annotation.CloudReference;
// import com.kayak.aspect.annotations.API;
// import com.kayak.aspect.annotations.APIAuth;
// import com.kayak.aspect.annotations.APIDefine;
// import com.kayak.aspect.annotations.APIOperation;
// import com.kayak.bala.dao.M945Dao;
// import com.kayak.bala.model.M906;
// import com.kayak.bala.model.M945;
// import com.kayak.common.util.BalaProdParam;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.sql.SqlRow;
// import com.kayak.core.sql.UpdateResult;
// import com.kayak.core.system.RequestSupport;
//
// import com.kayak.prod.utils.TransException;
//
// // import com.kayakwise.bala.api.T926DubboDecorator;
//
// // import com.kayakwise.bala.req.T926ServiceRequest;
// // import com.kayakwise.bala.resp.T926ServiceResponse;
// import com.kayakwise.wmp.pub.pojo.GlobalContents;
// import com.mysql.cj.log.Log;
// import jdk.nashorn.internal.ir.IfNode;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
//
// @Service
// @APIDefine(desc = "灏侀棴鏈熶氦鏄撴帶鍒舵爣蹇楁湇鍔?, model = M945.class)
// public class M945Service {
//
// 	private static Logger log = LoggerFactory.getLogger(M945Service.class);
// 	@Autowired
// 	private M945Dao m945Dao;
//
// 	/*@Autowired
// 	private BalaCenterServerDubbo balaCenterServerDubbo;*/
//
// 	@CloudReference
// // 	private T926DubboDecorator t926DubboDecorator;
//
//
// 	/**
// 	 * 浜ゆ槗鎺у埗鏍囧織鏌ヨ
// 	 * @param
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	@API(desc = "鏌ヨ灏侀棴鏈熶氦鏄撴帶鍒舵爣蹇?, params = "m945",  auth = APIAuth.YES)
// 	public SqlResult<M945> findCloseTranControlFlag(SqlParam<M945> params) throws Exception {
//
// 		try {
// 			SqlResult<M945> sqlResult= m945Dao.selectBalaCloseTranControl(params);
// 			if (!sqlResult.getRows().isEmpty()) {
//
// 				return sqlResult;
// 			} else {
// 				throw new TransException("M945","灏侀棴鏈熶氦鏄撴帶鍒舵爣蹇楁煡璇㈠紓甯?);
// 			}
//
// 		}catch (Exception e){
// 			System.out.println(e.getMessage()+e.toString());
// 			throw new TransException("M945","灏侀棴鏈熶氦鏄撴帶鍒舵爣蹇楁煡璇㈠紓甯?);
// 		}
// 	}
// 	/**
// 	 * 鏇存柊浜ゆ槗鎺у埗鏍囧織
// 	 * @param
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	/*@API(desc = "浜ゆ槗鎺у埗", params = "m945", operation = APIOperation.UPDATE)
// 	public String update(SqlParam<M945> params) throws Exception {
//
// 		//鏌ヨ鏁版嵁搴撲腑鐨勬帶鍒舵爣蹇?濡傛灉娌℃湁鏌ヨ鍒版垨鑰呮煡璇㈠埌鐨勫€煎拰鐢ㄦ埛淇敼鐨勭洰鏍囧€间竴鏍凤紝灏辨姏鍑哄紓甯镐俊鎭嫆缁濅慨鏀?
// 		SqlResult<M945> sqlResult= m945Dao.selectBalaCloseTranControl(params);
// 		if (sqlResult.getRows().isEmpty() || sqlResult.getRows().get(0).getParavalue().equals(params.getModel().getParavalue())) {
// 			throw new TransException("M945","灏侀棴鏈熶氦鏄撴帶鍒舵爣蹇楁煡璇㈠紓甯告垨鏃犳晥淇敼");
// 		}else if (!(params.getModel().getParavalue().equals("0") || params.getModel().getParavalue().equals("1") || params.getModel().getParavalue().equals("2"))){
// 			return RequestSupport.updateReturnJson(false, "浜ゆ槗鎺у埗鏍囧織鏃犺淇敼閫夐」", null).toString();
// 		}
//
// 		//閫氳繃dubbo璋冪敤浣欓鐞嗚储鏈嶅姟T926璋冪敤鏍稿績鐨勬棩鍒囨垨鑰呬氦鏄撴帶鍒舵湇鍔?
// // 		T926ServiceResponse t926ServiceResponse = t926DubboDecorator.webExecute(new T926ServiceRequest(){{
// 			setOperFlag("2");
// 			setUserId("81102");
// 			setCloseTranControlFlag(params.getModel().getParavalue());
// 		}});
//
//
// 		//璋冪敤dubbo鍙戦€佹洿鏂版帶鍒舵爣蹇楃殑璇锋眰锛岃姹傚弬鏁版槸json瀛楃涓?
// // 		//t926DubboDecorator.execute(JSONObject.toJSONString(params.getModel()));
// 		*//*Map header = new HashMap();
// 		header = JSON.parseObject(JSON.toJSONString(params),Map.class);*//*
// 		//濡傛灉鏍稿績鏇存柊浜ゆ槗鎺у埗鏍囧織鎴愬姛锛屾洿鏂版暟鎹簱涓氦鏄撴帶鍒舵爣蹇楃殑鍙傛暟
// 		if (t926ServiceResponse.getRtnCode().equals(GlobalContents.RTN_CODE_SUCCESS)) {
// 			UpdateResult updateResult = m945Dao.updateBalaCloseTranControl(params.getModel().getParavalue());
// 			if (updateResult.getEffect()==1) {
// 				sqlResult.getRows().get(0).setParavalue(sqlResult.getRows().get(0).getParavalue().equals("0") ? "1" : "0");
// 				return RequestSupport.updateReturnJson(true, "淇敼鎺у埗鏍囧織鎴愬姛", null).toString();
// 			}else {
// 				return RequestSupport.updateReturnJson(false, "鍚庡彴淇敼鎺у埗鏍囧噯澶辫触", null).toString();
// 			}
// 		}else {
// 			return RequestSupport.updateReturnJson(false, "淇敼鎺у埗鏍囧噯澶辫触", null).toString();
// 		}
// 	}*/
//
// 	/**
// 	 * 鏇存柊浜ゆ槗鎺у埗鏍囧織
// 	 * @param
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	@API(desc = "鏇存柊浜ゆ槗鎺у埗鏍囧織,鍘绘牳蹇冩洿鏂版帶鍒舵爣蹇?, params = "m945", operation = APIOperation.UPDATE)
// 	public SqlResult<M945> recoverOrg(SqlParam<M945> params) throws Exception {
//
// 		//鏌ヨ鏁版嵁搴撲腑鐨勬帶鍒舵爣蹇楋紝鎷挎潵鍜屽弬鏁颁腑鐨勫姣斾互闃叉暟鎹淇敼涓嶄竴鑷翠骇鐢熼敊璇?
// 		SqlResult<M945> sqlResult= m945Dao.selectTranControlOrRecoFlag(params);
// 		if (sqlResult.getRows().isEmpty() ||  !sqlResult.getRows().get(0).getParavalue().equals(params.getModel().getParavalue())) {
// 			throw new TransException("M945","鏍囧織鏌ヨ寮傚父");
// 		}
//
//
// 		//鍘绘牳蹇冧慨鏀规帶鍒舵爣蹇楋紝鏀跺埌杩斿洖鐨勭粨鏋?
// // 		T926ServiceResponse t926ServiceResponse = t926DubboDecorator.webExecute(new T926ServiceRequest(){{
// 			if (params.getModel().getParaid().equals("a0060001")){
// 				setOperFlag("2");
// 				setCloseTranControlFlag(params.getModel().getParavalue().equals("1")?"0":"1");
// 			}else if (params.getModel().getParaid().equals("a0060002")){
// 				setOperFlag("3");
// 				setRecoFlag(params.getModel().getParavalue().equals("1")?"0":"1");
// 			}else {
// 				throw new TransException("M945","鍙傛暟杈撳叆寮傚父寮傚父");
// 			}
// 			setUserId("81102");
// 		}});
//
// 		//鍒ゆ柇缁撴灉锛屽鏋滄湁閿欒鍒欐姏鍑哄紓甯?
// 		if (t926ServiceResponse.getRtnCode().equals(GlobalContents.HEAD_RTN_CODE_SUCCESS)){
// 			UpdateResult updateResult = m945Dao.updateTranControlOrRecoFlag(params.getModel().getParavalue().equals("1")?"0":"1",params.getModel().getParaid());
// 			if (updateResult.getEffect()==1){
// 			sqlResult.getRows().get(0).setParavalue(params.getModel().getParavalue());
// 			return sqlResult;
// 			}else {
// 				throw new TransException("M945","鏍囧織鏇存柊澶辫触");
// 			}
// 		}else {
// 			throw new TransException("M945","鏍稿績鏍囧織鏇存柊澶辫触");
// 		}
//
// 	}
//
//
// }
