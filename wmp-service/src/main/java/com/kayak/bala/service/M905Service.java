package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M905Dao;
import com.kayak.bala.model.M905;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.graphql.model.FetcherData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "TA划拨账户维护表服务", model = M905.class)
public class M905Service {

	@Autowired
	private M905Dao m905Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询TA划拨账户信息", auth = APIAuth.NO)
	public SqlResult<M905> findAccTaInfo(SqlParam<M905> params) throws Exception {
		SqlResult<M905> sqlResult= m905Dao.findAccTaInfo(params);
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			if(Tools.isNotBlank(item.getTano())){
				try {
					item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
					item.setProdName(reportformUtil.getProdName("",item.getTano(),item.getProdCode()));
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			return item;
		}).collect(Collectors.toList()));
		return sqlResult;
	}

	@API(desc = "修改TA划拨账户", params = "m905", auth = APIAuth.NO)
	public String update(SqlParam<M905> params) throws Exception {
		M905 model = params.getModel();
		/**Map<String,Object> map = new HashMap<>();
		map.put("systemNo",model.getSystemNo());
		map.put("tano",model.getTano());
		SqlParam<M905> sqlParam = new FetcherData<>(map,M905.class);
		SqlResult<M905> sqlResult = m905Dao.findProdInfo(sqlParam);
		if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0 ){
			throw new  PromptException("产品已存在，不能重复设置");
		}*/
		m905Dao.update(params);
		return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
	}

	@API(desc = "删除TA划拨账户", auth = APIAuth.NO)
	public String delAccTaInfo(SqlParam<M905> params) throws Exception {
		M905 model = params.getModel();

		m905Dao.delAccTaInfo(params);
		return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
	}

	@API(desc = "新增TA划拨账户", auth = APIAuth.NO)
	public String addAccTaInfo(SqlParam<M905> params) throws Exception {
		M905 model = params.getModel();
		Map<String,Object> map = new HashMap<>();
		map.put("systemNo",model.getSystemNo());
		map.put("tano",model.getTano());
		map.put("prodCode",model.getProdCode());
		SqlParam<M905> sqlParam = new FetcherData<>(map,M905.class);
		SqlResult<M905> sqlResult = m905Dao.checkProdCode(sqlParam);
		if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0 ){
			throw new  PromptException("产品已存在，不能重复设置");
		}
		m905Dao.addAccTaInfo(params);
		return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
	}

	@API(desc = "查询TANO", auth = APIAuth.NO)
	public SqlResult<M905> findTano(SqlParam<M905> params) throws Exception {
		return m905Dao.findTano(params);
	}
}
