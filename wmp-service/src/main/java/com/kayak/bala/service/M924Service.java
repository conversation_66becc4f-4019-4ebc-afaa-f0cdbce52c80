package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M924Dao;
import com.kayak.bala.model.M924;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M102Dao;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "客户底层份额查询服务", model = M924.class)
public class M924Service {

	@Autowired
	private M924Dao m924Dao;

	@Autowired
	private M102Dao m102Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "客户底层份额查询", auth = APIAuth.YES)
	public SqlResult<M924> findBalaCustBaseVol(SqlParam<M924> params) throws Exception {

		//TODO 注掉SystemNo 待验证
		params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()/*, SystemNo.BALA*/));
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		params.getModel().setAcctNo(null);
		SqlResult<M924> sqlResult = m924Dao.findBalaCustBaseVol(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{

				try {
					/**if(StringUtils.isNotBlank(item.getCustNo())){
						Map<String, Object> map = new HashMap<>();
						map.put("custNo", item.getCustNo());
						SqlParam<M102> custParams = new FetcherData<>(map, M102.class);
						custParams.setMakeSql(true);
						SqlResult<M102> cusInfo = m102Dao.findCustTransAccts(custParams);
						if (cusInfo.getRows() != null && cusInfo.getRows().size() > 0) {
							item.setCustName(cusInfo.getRows().get(0).getCustName());
							item.setIdType(cusInfo.getRows().get(0).getIdType());
							item.setIdCode(cusInfo.getRows().get(0).getIdCode());
							item.setAcctNo(cusInfo.getRows().get(0).getAcctNo());
						}
					}*/

					if(StringUtils.isNotBlank(item.getTransOrgno())){
						Map<String,Object> map = new HashMap<>();
						map.put("orgno",item.getTransOrgno());
						SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
						SqlResult<M001> m001Info = m001Dao.find(dateParams);
						if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
							item.setOrgName(m001Info.getRows().get(0).getOrgname());
						}
					}

					if(StringUtils.isNotBlank(item.getBranchCode())){
						Map<String,Object> map2 = new HashMap<>();
						map2.put("orgno",item.getBranchCode());
						SqlParam<M001> param = new FetcherData<>(map2, M001.class);
						SqlResult<M001> m001Info1 = m001Dao.find(param);
						if(m001Info1.getRows() != null && m001Info1.getRows().size() > 0){
							item.setBranchName(m001Info1.getRows().get(0).getOrgname());
						}
					}
					item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
				} catch (Exception e) {
					throw new RuntimeException("M924错误："+e.getMessage());
				}

			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(true);
		return sqlResult;
	}
}
