package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M918Dao;
import com.kayak.bala.model.M918;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "底层产品划拨金额", model = M918.class)
public class M918Service {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private M918Dao m918Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询底层产品划拨金额信息", auth = APIAuth.YES)
    public SqlResult<M918> findM918s(SqlParam<M918> params) throws Exception {
        SqlResult<M918> sqlResult = m918Dao.findM918s(params);
        SqlResult<M918> sqlResult2 = m918Dao.sumM918(params);
        if(sqlResult2 != null && sqlResult2.getRows() != null && sqlResult2.getRows().size() > 0){
            List<M918> list = sqlResult2.getRows();
            sqlResult.getRows().addAll(list);
        }
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            if(Tools.isNotBlank(item.getTano())){
                try {
                    item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            return item;
        }).collect(Collectors.toList()));
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        return sqlResult;
    }
}
