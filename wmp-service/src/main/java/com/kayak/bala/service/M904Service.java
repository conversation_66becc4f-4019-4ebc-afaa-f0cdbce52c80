package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M904Dao;
import com.kayak.bala.model.M904;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@APIDefine(desc = "银行账户维护表服务", model = M904.class)
public class M904Service {

	@Autowired
	private M904Dao m904Dao;

	@Autowired
	private M001Dao m001Dao;


	@API(desc = "查询银行账户信息", auth = APIAuth.YES)
	public SqlResult<M904> findAccInfo(SqlParam<M904> params) throws Exception {
		SqlResult<M904> sqlResult = m904Dao.findAccInfo(params);
		List<M904> rows = sqlResult.getRows();
		if (rows != null){
			for (M904 m904 : rows){
				M001 m001 = m001Dao.get(m904.getOrgno());
				if (m001 != null){
					m904.setOrgName(m001.getOrgname());
				}
			}
		}
		return sqlResult;
	}

	@API(desc = "修改银行账户信息", params = "m904")
	public String update(SqlParam<M904> params) throws Exception {
		M904 model = params.getModel();
		try{
			m904Dao.update(params);
		}catch (Exception ex){
			throw new PromptException("保存出错："+ex.getMessage());
		}
		return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
	}

	@API(desc = "删除银行账户信息", auth = APIAuth.YES)
	public String delAccInfo(SqlParam<M904> params) throws Exception {
		M904 model = params.getModel();
		m904Dao.delAccInfo(params);
		return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
	}

	@API(desc = "新增银行账户信息", auth = APIAuth.YES)
	public String addAccInfo(SqlParam<M904> params) throws Exception {
		M904 model = params.getModel();
		model.setAcctSerno(UUID.randomUUID().toString().replaceAll("-",""));
		SqlResult<M904> sqlResult = m904Dao.findAccInfoByAcct(params);
		if (sqlResult.getRows() != null && sqlResult.getRows().size() > 0 ){
			throw new PromptException("相同银行账户类别下不能有相同的银行账号，请修改");
		}
		try{
			m904Dao.addAccInfo(params);
		}catch (Exception ex){
			throw new PromptException("保存出错："+ex.getMessage());
		}

		return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
	}

}
