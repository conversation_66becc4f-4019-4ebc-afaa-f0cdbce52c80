package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M930Dao;
import com.kayak.bala.model.M930;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@APIDefine(desc = "分区域行源泉宝客户交易统计表", model = M930.class)
public class M930Service {
    @Autowired
    private M930Dao m930Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询分区域行源泉宝客户交易统计信息", auth = APIAuth.YES)
    public SqlResult<M930> findM930s(SqlParam<M930> params) throws Exception {
        SqlResult<M930> sqlResult = m930Dao.findM930s(params);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M930> volList = sqlResult.getRows();
        for (M930 m930 : volList){
            M001 m0011 = m001Dao.get(m930.getBranchCode());
            if (m0011 != null){
                m930.setOrgName(m0011.getOrgname());
            }
        }
        sqlResult.setRows(volList);
        return sqlResult;
    }
}
