package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M937Dao;
import com.kayak.bala.model.M937;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.dao.M102Dao;
import com.kayak.cust.model.M101;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "余额理财净申购赎回服务", model = M937.class)
public class M937Service {

	@Autowired
	private M937Dao m937Dao;

	@Autowired
	private M102Dao m102Dao;

	@Autowired
	private M101Dao m101Dao;

	@Autowired
	private ReportformUtil reportformUtil;


	@API(desc = "余额理财净申购赎回查询", auth = APIAuth.YES)
	public SqlResult<M937> findBalaCustTransReq(SqlParam<M937> params) throws Exception {
		//如果是查询条件进来，必须预先判断是否存在客户信息
		//if(Tools.isNotBlank(params.getModel().getCustName()) || Tools.isNotBlank(params.getModel().getIdCode())
		//	||Tools.isNotBlank(params.getModel().getIdType())||Tools.isNotBlank(params.getModel().getAcctNo())){
		//	params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()));
		//}
		///params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()));
		//params.getModel().setCustName(null);
		//params.getModel().setIdCode(null);
		//params.getModel().setIdType(null);



		SqlResult<M937> sqlResult = m937Dao.findBalaCustTransReq(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		/**sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			if (StringUtils.isNotBlank(item.getCustNo())){
				Map<String,Object> map = new HashMap<>();
				map.put("custNo",item.getCustNo());
				try {
					SqlParam<M101> custParams = new FetcherData<>(map, M101.class);
					custParams.getModel().setCustNo(item.getCustNo());
					custParams.setMakeSql(true);
					SqlResult<M101> cusInfo = m101Dao.findCustInfos(custParams,"");
					if(cusInfo.getRows() != null && cusInfo.getRows().size() > 0){
						item.setIdType(cusInfo.getRows().get(0).getIdType());
						item.setIdCode(cusInfo.getRows().get(0).getIdCode());
						item.setCustName(cusInfo.getRows().get(0).getCustName());
					}
				} catch (Exception e) {
					throw new RuntimeException("M937错误："+e.getMessage());
				}
			}
			return item;
		}).collect(Collectors.toList()));*/
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
