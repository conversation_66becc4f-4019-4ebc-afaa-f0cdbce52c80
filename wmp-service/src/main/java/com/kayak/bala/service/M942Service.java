// package com.kayak.bala.service;
//
// import com.hundsun.jrescloud.rpc.annotation.CloudReference;
// import com.kayak.aspect.annotations.API;
// import com.kayak.aspect.annotations.APIAuth;
// import com.kayak.aspect.annotations.APIDefine;
//
// import com.kayak.bala.model.M942;
// import com.kayak.common.constants.RtnCodeStatus;
// import com.kayak.common.constants.SystemNo;
// import com.kayak.common.util.ReportformUtil;
// import com.kayak.core.exception.PromptException;
// import com.kayak.core.sql.SqlParam;
// import com.kayak.core.sql.SqlResult;
// import com.kayak.core.util.Tools;
// import com.kayak.fund.dao.M230Dao;
// import com.kayak.fund.model.M230;
// import com.kayak.graphql.model.FetcherData;
// // import com.kayakwise.bala.api.T919DubboDecorator;
// // import com.kayakwise.bala.model.BalaLog;
// // import com.kayakwise.bala.req.T919ServiceRequest;
// // import com.kayakwise.bala.resp.T919ServiceResponse;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
//
// @Service
// @APIDefine(desc = "婧愭硥瀹濆鎴峰疄鏃朵氦鏄撴祦姘存煡璇?, model = M942.class)
// public class M942Service {
//
//
// 	@Autowired
// 	private ReportformUtil reportformUtil;
//
// 	@CloudReference
// // 	private T919DubboDecorator t919DubboDecorator;
//
// 	@Autowired
// 	private M230Dao m230Dao;
//
// 	@API(desc = "鏌ヨ婧愭硥瀹濅氦鏄撴祦姘存煡璇?, auth = APIAuth.YES)
// 	public SqlResult<M942> findBalaCustHostTransLog(SqlParam<M942> params) throws Exception {
// 		T919ServiceRequest t919ServiceRequest = new T919ServiceRequest();
// 		if(Tools.isNotBlank(params.getModel().getCustName())||Tools.isNotBlank(params.getModel().getIdCode())
// 				||Tools.isNotBlank(params.getModel().getIdType())||Tools.isNotBlank(params.getModel().getAcctNo())){
// 			String idCode = params.getModel().getIdCode();
// 			if(Tools.isBlank(params.getModel().getCustNo())){
// 				//TODO 娉ㄦ帀SystemNo 寰呴獙璇?
// 				String custNo = reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()/*, SystemNo.BALA*/);
// 				t919ServiceRequest.setCustNo(custNo);
// 			}
// 		}
// 		if(Tools.isNotBlank(params.getModel().getCustNo())){
// 			t919ServiceRequest.setCustNo(params.getModel().getCustNo());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransDate())){
// 			t919ServiceRequest.setBeginDate(params.getModel().getTransDate());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getTransEndDate())){
// 			t919ServiceRequest.setEndDate(params.getModel().getTransEndDate());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getReference())){
// 			t919ServiceRequest.setReference(params.getModel().getReference());
// 		}
// 		if(Tools.isNotBlank(params.getModel().getChannelSeqNo())){
// 			t919ServiceRequest.setChannelSeqNo(params.getModel().getChannelSeqNo());
// 		}
// 		t919ServiceRequest.setCardPbInd("C");
// 		List<M942> m942List = new ArrayList<>();
//
// 		if(params.getStart() == 0 && params.getLimit() == 10){
// 			t919ServiceRequest.setPageNum(1);
// 			t919ServiceRequest.setTotalNum(10);
// 		}else if(params.getStart() == 0 && params.getLimit() == 0){
// 			t919ServiceRequest.setPageNum(1);
// 			t919ServiceRequest.setTotalNum(100000);
// 		}else{
// 			int startNum = params.getStart()/params.getLimit();
// 			t919ServiceRequest.setTotalNum(params.getLimit());
// 			t919ServiceRequest.setPageNum(startNum+1);
// 		}
//
// 		//t919ServiceRequest.setTotalNum(params.getLimit());
// // 		T919ServiceResponse t919ServiceResponse = t919DubboDecorator.excute(t919ServiceRequest);
// 		if(t919ServiceResponse != null && t919ServiceResponse.getRtnCode().equals(RtnCodeStatus.RTNCODE)){
// 			List<BalaLog> balaLogList = t919ServiceResponse.getTranHistArray();
// 			if(balaLogList != null && balaLogList.size() > 0){
// 				for(BalaLog balaLog:balaLogList){
// 					M942 m942 = new M942();
// 					if(Tools.isNotBlank(balaLog.getCustNo())){
// 						Map<String,Object> map = new HashMap<>();
// 						map.put("custNo",balaLog.getCustNo());
// 						SqlParam<M230> custParams = new FetcherData<>(map, M230.class);
// 						List<M230> list = m230Dao.findCusts(custParams);
// 						if(list != null && list.size() > 0){
// 							m942.setCustName(list.get(0).getCustName());
// 							m942.setIdCode(list.get(0).getIdCode());
// 							m942.setIdType(list.get(0).getIdType());
//
// 						}
// 					}
// 					m942.setCustNo(balaLog.getCustNo());
// 					m942.setReference(balaLog.getReference());
// 					m942.setChannelSeqNo(balaLog.getChannelSeqNo());
// 					m942.setAcctNo(balaLog.getBaseAcctNo());
// 					m942.setTransDate(balaLog.getTranDate());
// 					m942.setTransType(balaLog.getTranType());
// 					m942.setCfmSuccessAmt(balaLog.getConfirmAmt() == null?"":balaLog.getConfirmAmt().toString());
// 					m942.setCfmFailAmt(balaLog.getUnConfirmAmt() == null?"":balaLog.getUnConfirmAmt().toString());
// 					m942.setAppAmt(balaLog.getTranAmt() == null?"":balaLog.getTranAmt().toString());
// 					m942.setTransStatus(balaLog.getStatus());
// 					m942.setRemarks(balaLog.getRetMsg());
//
// 					m942List.add(m942);
// 				}
// 			}
// 		}else{
// 			throw new PromptException("璋冪敤鎺ュ彛T919澶辫触,杩斿洖鎺ュ彛CODE锛?+t919ServiceResponse.getRtnCode()+",杩斿洖閿欒淇℃伅锛?+t919ServiceResponse.getRtnDesc());
// 		}
//
//
//
// 		SqlResult<M942> sqlResult = new SqlResult<>();
// 		int count = (int)t919ServiceResponse.getTotalNum();
// 		int start = params.getStart();
// 		int limit = params.getLimit();
// 		sqlResult.setRows(m942List);
// 		//if (limit != 0){
// 		//	sqlResult.setRows(m942List.subList(start, Math.min(start + limit, count)));
// 		//}else {
// 		//	sqlResult.setRows(m942List);
// 		//}
// 		sqlResult.setResults(t919ServiceResponse.getTotalNum());
// 		sqlResult.setDesensitized(true);
// 		return sqlResult;
// 	}
// }
