package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M922Dao;
import com.kayak.bala.model.M922;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "源泉宝签约关系服务", model = M922.class)
public class M922Service {

	@Autowired
	private M922Dao m922Dao;
	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "源泉宝签约关系查询", auth = APIAuth.YES)
	public SqlResult<M922> findBalaCustSign(SqlParam<M922> params) throws Exception {
		if(StringUtils.isNotBlank(params.getModel().getIdCode())){
			params.getModel().setIdCode(params.getModel().getIdCode().trim());
		}
		SqlResult<M922> sqlResult = m922Dao.findBalaCustSign(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try {
				//翻译签约卡开户区域
				if(org.apache.commons.lang.StringUtils.isNotBlank(item.getBranchCode())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getBranchCode());
					SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(dateParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setBranchName(m001Info.getRows().get(0).getOrgname());
					}
				}
				//翻译签约卡开户机构
				if(org.apache.commons.lang.StringUtils.isNotBlank(item.getTransOrgno())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getTransOrgno());
					map.put("userid",params.getAuthInfo().get("userid"));
					SqlParam<M001> param = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(param);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setTransName(m001Info.getRows().get(0).getOrgname());
					}
				}
			} catch (Exception e) {
				throw new RuntimeException("M922错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
