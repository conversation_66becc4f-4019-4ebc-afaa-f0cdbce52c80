package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M935Dao;
import com.kayak.bala.model.M935;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "余额理财聚合交易流水", model = M935.class)
public class M935Service {
    @Autowired
    private M935Dao m935Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "余额理财聚合交易流水", auth = APIAuth.YES)
    public SqlResult<M935> findM935(SqlParam<M935> params) throws Exception {
        SqlResult<M935> sqlResult = m935Dao.findM935(params);
        //sqlResult.setDesensitized(false);
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        return sqlResult;
    }

}
