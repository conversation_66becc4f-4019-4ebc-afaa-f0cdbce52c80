package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M915Dao;
import com.kayak.bala.model.M915;
import com.kayak.bala.model.M915;
import com.kayak.bala.model.M915;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "分支机构费用划拨分配", model = M915.class)
public class M915Service {
    @Autowired
    private M915Dao m915Dao;

    @Autowired
    private M001Dao m001Dao;
    @API(desc = "查询分支机构费用划拨分配数据", auth = APIAuth.YES)
    public SqlResult<M915> findM915s(SqlParam<M915> params) throws Exception {
        params.setMakeSql(true);
        List<M915> volList = m915Dao.findM915s(params);
        SqlResult<M915> sqlResult = new SqlResult<>();
        sqlResult.setResults(volList.size());
        sqlResult.setDesensitized(false);
        Map<String, List<M915>> volMap = groupList(volList);
        List<M915> countList = m915Dao.findM915Count(params);
        Map<String, List<M915>> countMap = groupList(countList);
        List<M915> list = new ArrayList<>();
        for(String key : volMap.keySet()){
            list.addAll(volMap.get(key));
            M915 m915 = countMap.get(key).get(0);
            m915.setOrgname("机构合计");
            list.add(m915);
        }
        for (M915 m915 : list){
            if (m915.getBranchCode() != null && !"".equals(m915.getBranchCode())){
                M001 m001 = m001Dao.get(m915.getBranchCode());
                if (m001 != null){
                    m915.setBranchCode(m001.getOrgname());
                }
            }
        }
        sqlResult.setRows(list);
        return sqlResult;
    }

    public Map<String, List<M915>> groupList(List<M915> list) {
        Map<String, List<M915>> map = list.stream().collect(Collectors.groupingBy(M915::getTransOrgno));
        return map;
    }
}
