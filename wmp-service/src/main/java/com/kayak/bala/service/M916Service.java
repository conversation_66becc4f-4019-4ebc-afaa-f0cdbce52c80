package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M916Dao;
import com.kayak.bala.model.M916;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "分机构源泉宝存量规模及客户数统计表", model = M916.class)
public class M916Service {

    @Autowired
    private M916Dao m916Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "分机构源泉宝存量规模及客户数统计表", auth = APIAuth.YES)
    public SqlResult<M916> findM916s(SqlParam<M916> params) throws Exception {
        if(StringUtils.isNotBlank(params.getModel().getSubBranchCode())){
            StringBuffer subBranchCodeStr = new StringBuffer();
            String [] str = params.getModel().getSubBranchCode().split(",");
            if(str != null && str.length > 0){
                for(int i=0;i<str.length;i++){
                    if(i != str.length -1){
                        subBranchCodeStr.append("'"+str[i].toString()+"',");
                    }else{
                        subBranchCodeStr.append("'"+str[i].toString()+"'");
                    }

                }
            }
            params.getModel().setSubBranchCode(null);
            params.getModel().setSubBranchCode(subBranchCodeStr.toString());
        }
        SqlResult<M916> sqlResult = m916Dao.findM916s(params);
        List<M916> volList = sqlResult.getRows();
        Map<String, List<M916>> volMap = groupList(volList);
        List<M916> countList = m916Dao.findM916Count(params);
        Map<String, List<M916>> countMap = groupList(countList);
        List<M916> list = new ArrayList<>();
        for(String key : volMap.keySet()){
            list.addAll(volMap.get(key));
            M916 m916 = countMap.get(key).get(0);
            List<M916> m916list = m916Dao.findCustCount(key);
            if(m916list != null && m916list.size() > 0){
                m916.setCustCount(m916list.get(0).getCustCount());
            }else{
                m916.setCustCount(Long.parseLong("0"));
            }

            m916list.get(0).setBranchCodeName("");
            m916list.get(0).setSubBranchCodeName("机构合计");
            list.add(m916list.get(0));
        }
        for (M916 m916 : list){
            if(!"机构合计".equals(m916.getSubBranchCodeName())){
                M001 m001 = m001Dao.get(m916.getSubBranchCode());
                if (m001 != null) {
                    m916.setSubBranchCodeName(m001.getOrgname());
                }

                M001 m0011 = m001Dao.get(m916.getBranchCode());
                if (m0011 != null){
                    m916.setBranchCodeName(m0011.getOrgname());
                }
            }
            //获取TA名称
            m916.setTaName(reportformUtil.getBalaTaName(m916.getTano()));
        }
        sqlResult.setRows(list);
        return sqlResult;
    }

    public Map<String, List<M916>> groupList(List<M916> list) {
        Map<String, List<M916>> map = list.stream().collect(Collectors.groupingBy(M916::getSubBranchCode));
        return map;
    }

}
