package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M913Dao;
import com.kayak.bala.model.M909;
import com.kayak.bala.model.M913;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "源泉宝存量规模及客户数统计表", model = M913.class)
public class M913Service {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private M913Dao m913Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "源泉宝存量规模及客户数统计表", auth = APIAuth.YES)
    public SqlResult<M913> findM913s(SqlParam<M913> params) throws Exception {
        //是否自动追加参数
        //params.setMakeSql(true);
        SqlResult<M913> sqlResult = m913Dao.findM913s(params);
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            if(Tools.isNotBlank(item.getTano())){
                try {
                    item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            return item;
        }).collect(Collectors.toList()));
        reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        return sqlResult;
    }
}
