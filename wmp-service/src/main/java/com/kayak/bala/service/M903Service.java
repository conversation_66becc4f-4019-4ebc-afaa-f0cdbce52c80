package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M902Dao;
import com.kayak.bala.dao.M903Dao;
import com.kayak.bala.model.M902;
import com.kayak.bala.model.M903;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

@Service
@APIDefine(desc = "底层产品信息表服务", model = M903.class)
public class M903Service {

	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Autowired
	private M903Dao m903Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询产品代码", auth = APIAuth.YES)
	public SqlResult<M903> findProdInfo(SqlParam<M903> params) throws Exception {
		SqlResult<M903> sqlResult= m903Dao.findProdInfo(params);
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			if(Tools.isNotBlank(item.getTano())){
				try {
					item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
				} catch (Exception e) {
					log.error(e.getMessage());
				}
			}
			return item;
		}).collect(Collectors.toList()));
		return sqlResult;
	}
}
