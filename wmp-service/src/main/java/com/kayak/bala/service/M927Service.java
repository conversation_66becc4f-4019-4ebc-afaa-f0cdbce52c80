package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M927Dao;
import com.kayak.bala.model.M927;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.dao.M102Dao;
import com.kayak.cust.model.M101;
import com.kayak.cust.model.M102;
import com.kayak.fund.dao.M230Dao;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "还款流水服务", model = M927.class)
public class M927Service {

	@Autowired
	private M927Dao m927Dao;

	@Autowired
	private M102Dao m102Dao;

	@Autowired
	private M230Dao m230Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@Autowired
	private M215Service m215Service;



	@API(desc = "还款流水查询", auth = APIAuth.YES)
	public SqlResult<M927> findBalaCustPaymentLog(SqlParam<M927> params) throws Exception {

		//如果是查询条件进来，必须预先判断是否存在客户信息
		StringBuffer custInfoSb = new StringBuffer();
		if(Tools.isBlank(params.getModel().getCustNo())){
			params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()));
		}
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);

		SqlResult<M927> sqlResult = m927Dao.findBalaCustPaymentLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		/**sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			if (StringUtils.isNotBlank(item.getAcctNo())){
				Map<String,Object> map = new HashMap<>();
				map.put("acctNo",item.getAcctNo());
				try {
					SqlParam<M102> custParams = new FetcherData<>(map, M102.class);
					custParams.setMakeSql(true);
					SqlResult<M102> cusInfo = m102Dao.findCustTransAccts(custParams);
					if(cusInfo.getRows() != null && cusInfo.getRows().size() > 0){
						item.setIdType(cusInfo.getRows().get(0).getIdType());
						item.setIdCode(cusInfo.getRows().get(0).getIdCode());
						item.setCustName(cusInfo.getRows().get(0).getCustName());
						item.setTransAcctNo(cusInfo.getRows().get(0).getTransAcctNo());
					}
				} catch (Exception e) {
					throw new RuntimeException("M927错误："+e.getMessage());
				}
			}
			if (Tools.isNotBlank(item.getProdCode())){
				try{
					Map<String,Object> m215Map = new HashMap<>();
					m215Map.put("prodCode",item.getProdCode());
					SqlParam<M215> dateParams = new FetcherData<>(m215Map, M215.class);
					dateParams.setMakeSql(true);
					SqlResult<Map<String, String>> m215Info = m215Service.queryProdInfoList(dateParams);
					if(m215Info.getRows() != null && m215Info.getRows().size() > 0){
						item.setProdName(m215Info.getRows().get(0).get("prod_name"));
					}
				}catch (Exception ex){
					throw new RuntimeException("M927错误："+ex.getMessage());
				}
			}
			return item;
		}).collect(Collectors.toList()));*/
		sqlResult.setDesensitized(false);
		return sqlResult;
	}

}
