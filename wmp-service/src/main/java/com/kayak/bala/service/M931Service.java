package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M931Dao;
import com.kayak.bala.model.M931;
import com.kayak.bala.model.M931;
import com.kayak.bala.model.M931;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.dao.M102Dao;
import com.kayak.cust.model.M101;
import com.kayak.cust.model.M102;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "分区域行源泉宝存量规模及客户数统计表", model = M931.class)
public class M931Service {

    @Autowired
    private M931Dao m931Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "分区域行源泉宝存量规模及客户数统计表", auth = APIAuth.YES)
    public SqlResult<M931> findM931s(SqlParam<M931> params) throws Exception {
        SqlResult<M931> sqlResult = m931Dao.findM931s(params);
        //reportformUtil.checkMaxExcel(sqlResult.getRows().size());
        List<M931> volList = sqlResult.getRows();
        Map<String, List<M931>> volMap = groupList(volList);
        List<M931> countList = m931Dao.findM931Count(params);
        Map<String, List<M931>> countMap = groupList(countList);
        List<M931> list = new ArrayList<>();
        for(String key : volMap.keySet()){
            list.addAll(volMap.get(key));
            M931 m931 = countMap.get(key).get(0);
            List<M931>  m931List = m931Dao.findCustCount(key);
            if(m931 != null && m931List.size() > 0){
                m931.setCustCount(m931List.get(0).getCustCount());
            }else{
                m931.setCustCount(Long.parseLong("0"));
            }

            m931.setOrgName("区域合计");
            list.add(m931);
        }
        for (M931 m931 : list){
            M001 m001 = m001Dao.get(m931.getBranchCode());
            if (m001 != null){
                m931.setOrgName(m001.getOrgname());
            }
            //获取TA名称
            m931.setTaName(reportformUtil.getBalaTaName(m931.getTano()));
        }
        sqlResult.setRows(list);
        return sqlResult;
    }

    public Map<String, List<M931>> groupList(List<M931> list) {
        Map<String, List<M931>> map = list.stream().collect(Collectors.groupingBy(M931::getBranchCode));
        return map;
    }
}
