package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M901Dao;
import com.kayak.bala.dao.M940Dao;
import com.kayak.bala.model.M901;
import com.kayak.bala.model.M940;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.dao.M102Dao;
import com.kayak.cust.dao.M113Dao;
import com.kayak.cust.model.M101;
import com.kayak.cust.model.M113;
import com.kayak.fund.dao.M230Dao;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "源泉宝收益汇总", model = M940.class)
public class M940Service {

	@Autowired
	private M940Dao m940Dao;

	@Autowired
	private M102Dao m102Dao;

	@Autowired
	private M230Dao m230Dao;

	@Autowired
	private M113Dao m113Dao;


	@Autowired
	private M901Dao m901Dao;


	@Autowired
	private ReportformUtil reportformUtil;


	@API(desc = "源泉宝收益汇总", auth = APIAuth.YES)
	public SqlResult<M940> findBalaProfitSum(SqlParam<M940> params) throws Exception {

		//如果是查询条件进来，必须预先判断是否存在客户信息
		StringBuffer custInfoSb = new StringBuffer();
		if(Tools.isBlank(params.getModel().getCustsNo())){
			//TODO 注掉SystemNo 待验证
			params.getModel().setCustsNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()/*, SystemNo.BALA*/));
		}
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		params.getModel().setAcctNo(null);
		SqlResult<M940> sqlResult = m940Dao.findBalaCustPaymentLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				/**if (Tools.isNotBlank(item.getBalaCode())){
					Map<String,Object> map = new HashMap<>();
					map.put("balaCode",item.getBalaCode());
					SqlParam<M901> balaParams = new FetcherData<>(map, M901.class);
					balaParams.setMakeSql(true);
					SqlResult<M901> balaInfo = m901Dao.findBalaProdInfoList(balaParams);
					if(balaInfo.getRows() != null && balaInfo.getRows().size() > 0) {
						item.setBalaName(balaInfo.getRows().get(0).getBalaName());
					}

				}**/
				if (Tools.isNotBlank(item.getTano())){
					String taName = reportformUtil.getFundTaName(item.getTano());
					if(Tools.isNotBlank(taName)){
						item.setTaName(taName);
					}else{
						item.setTaName(reportformUtil.getFinaTaName(item.getTano()));
					}
				}
				/**if(Tools.isNotBlank(item.getCustsNo())){
					M113 cusInfo = m113Dao.getCusts(item.getCustsNo());
					if(cusInfo != null) {
						item.setCustName(cusInfo.getCustName());
					}
				}**/
			}catch (Exception ex){
				throw new RuntimeException("查询失败："+ex.getMessage());
			}

			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(true);
		return sqlResult;
	}

}
