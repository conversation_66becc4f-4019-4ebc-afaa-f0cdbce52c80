package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.bala.dao.M902Dao;
import com.kayak.bala.dao.M911Dao;
import com.kayak.bala.model.M902;
import com.kayak.bala.model.M911;
import com.kayak.bala.model.M911DaliyVol;
import com.kayak.bala.model.M911List;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.dao.DaoService;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.cust.model.M101;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.kayak.base.dao.util.DaoUtil.doTrans;

@Service
@APIDefine(desc = "分支机构费用划拨", model = M911.class)
public class M911Service {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private M911Dao m911Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private DaoService daoService;

    @Autowired
    private M902Dao m902Dao;

    @Autowired
    private ReportformUtil reportformUtil;

    @API(desc = "查询分支机构费用划拨列表", auth = APIAuth.YES)
    public SqlResult<M911> find(SqlParam<M911> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M911> sqlResult=m911Dao.findM911s(params);
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            if(Tools.isNotBlank(item.getTano())){
                try {
                    item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            /**if(Tools.isNotBlank(item.getProdCode()) && Tools.isBlank(item.getProdName())){
                item.setProdName(reportformUtil.getBalaProdName(item.getProdCode()));
            }*/
            return item;
        }).collect(Collectors.toList()));
        return sqlResult;
    }

    @API(desc = "查询分支机构费用划拨列表数据", auth = APIAuth.NO)
    public SqlResult<M911List> findList(SqlParam<M911> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<M911List> sqlResult=m911Dao.findM911Lists(params);
        sqlResult.setRows(sqlResult.getRows().stream().map(item->{
            if(Tools.isNotBlank(item.getTano())){
                try {
                    item.setTaName(reportformUtil.getBalaTaName(item.getTano()));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            if(Tools.isNotBlank(item.getProdCode()) && Tools.isBlank(item.getProdName())){
                item.setProdName(reportformUtil.getBalaProdName(item.getProdCode()));
            }
            return item;
        }).collect(Collectors.toList()));
        return sqlResult;
    }

    @API(desc = "生成分支机构费用划拨列表数据")
    public String creatList(SqlParam<M911> params) throws Exception {
        params.setMakeSql(true);
        M911 m911 = m911Dao.getM911(params);
        if (m911 == null){
            throw new PromptException("找不到划拨记录");
        }
        if (!m911.getTransferStatus().equals("0")){
            throw new PromptException("只有未处理的划拨可以生成");
        }
        daoService.doTrans(() -> {
            params.getModel().setTransferStatus("1");
            int updateres = m911Dao.updateM911(params);
            if (updateres < 1){
                throw new PromptException("划拨状态不正确，生成失败");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Date start = sdf.parse(params.getModel().getStartDate());
            Date end = sdf.parse(params.getModel().getEndDate());
            int days = getDayDiffer(start,end) + 1;
            List<M911DaliyVol> daliyVolList = m911Dao.findM911DaliyVols(params);
            if (daliyVolList == null || daliyVolList.size() < 1){
                throw new PromptException("没有分支机构可分配");
            }
            BigDecimal sumDaliyVol = m911Dao.sumDaliyVol(params).getDaliyVol();
            BigDecimal totalEarningAmt = BigDecimal.ZERO;
            List<M911List> m911Lists = new ArrayList<>();
            for(M911DaliyVol daliyVol : daliyVolList){
                M911List m911List = new M911List();
                BeanUtils.copyProperties(m911,m911List);
                m911List.setBankCode(daliyVol.getBankCode());
                m911List.setSubBranchCode(daliyVol.getSubBranchCode());
                m911List.setBranchCode(daliyVol.getBranchCode());
                m911List.setOrgno(daliyVol.getTransOrgno());
                m911List.setTransOrgno(daliyVol.getTransOrgno());
                m911List.setArea(daliyVol.getBankCode());
                m911List.setTransferStatus("0");
                //机构中收金额 = （分支机构每日存量表）日存量除于（结束日期-开始日期366），保存两位小数，
                BigDecimal orgDaliyVol = daliyVol.getDaliyVol().divide(BigDecimal.valueOf(days),2, RoundingMode.DOWN);
                m911List.setDaliyVol(orgDaliyVol);
                //机构中收金额 = （分支机构费用划拨表）总中收金额乘于（日存量除于该ta下对应产品的日存量），保存两位小数，
                BigDecimal earningAmt = m911.getTotalEarning().multiply(daliyVol.getDaliyVol()).divide(sumDaliyVol, 2,RoundingMode.DOWN);
                totalEarningAmt = totalEarningAmt.add(earningAmt);
                m911List.setEarningAmt(earningAmt);
                m911Lists.add(m911List);
            }
            BigDecimal dValue = m911.getTotalEarning().subtract(totalEarningAmt);
            m911Lists.get(0).setEarningAmt(m911Lists.get(0).getEarningAmt().add(dValue));
            int i=0;
            for (M911List m911List : m911Lists){
                i++;
                System.out.println("第"+i+"次");
                M001 m001 = m001Dao.get(m911List.getOrgno());
                if (m001 != null){
                    m911List.setOrgname(m001.getOrgname());
                }
                m911Dao.addM911List(m911List);
            }
        }, SubDatabase.DATABASE_BALA_CENTER);
        return RequestSupport.updateReturnJson(true, "生成成功", null).toString();
    }

    @API(desc = "划拨")
    public String doTransfer(SqlParam<M911> params) throws Exception {
        params.setMakeSql(true);
        M911 m911 = m911Dao.getM911(params);
        if (m911 == null){
            throw new PromptException("找不到划拨记录");
        }
        if (!(m911.getTransferStatus().equals("1") || m911.getTransferStatus().equals("2"))){
            throw new PromptException("只有已生成划拨数据和部分划拨成功的划拨可以划拨");
        }
        /**
         * todo
         * ⑥　将生成的文件上送核心进行批量资金处理
         * ⑦　接收核心结果文件并根据核心结果更新“分支机构产品费用分配表”的【划拨状态】，成功的更新为“划拨成功”，失败的更新为“划拨失败”
         * ⑧　根据核心结果，更新“分支机构费用划拨”表【划拨状态】，全部成功更新为“全部划拨成功”，只要有失败的数据，就更新为“部分划拨成功”
         */
        List<M911List> m911Lists = m911Dao.findM911Lists(params).getRows();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date now = new Date();
        String nowStr = sdf.format(now);
        for(M911List m911List : m911Lists){
            m911List.setTransferDate(nowStr);
            m911List.setTransferStatus("3");
            m911Dao.updateM911List(m911List);
        }
        params.getModel().setTransferDate(nowStr);
        params.getModel().setTransferStatus("2");
        m911Dao.updateM911(params);
        return RequestSupport.updateReturnJson(true, "划拨成功", null).toString();
    }

    @API(desc = "修改分支机构费用划拨", operation = APIOperation.UPDATE)
    public String edit(SqlParam<M911> params) throws Exception {
        if (m911Dao.updateM911(params) < 1) {
            throw new PromptException("修改失败");
        }
        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    @API(desc = "添加分支机构费用划拨", operation = APIOperation.INSTER)
    public String add(SqlParam<M911> params) throws Exception {
        params.getModel().setSystemNo("FUND");
        params.getModel().setTransferStatus("0");
        params.getModel().setTransferSerno("1");
        if(Tools.isNotBlank(params.getModel().getProdCode())){
            Map<String,Object> map = new HashMap<>();
            map.put("prodCode",params.getModel().getProdCode());
            SqlParam<M902> prodParams = new FetcherData<>(map, M902.class);
            SqlResult<M902> balaProdnfo = m902Dao.findAccTaInfo(prodParams);
            if(balaProdnfo.getRows() != null && balaProdnfo.getRows().size() > 0){
                params.getModel().setProdName(balaProdnfo.getRows().get(0).getProdName());
            }
        }
        if (m911Dao.addM911(params) < 1) {
            throw new PromptException("添加失败");
        }
        return RequestSupport.updateReturnJson(true, "添加成功", null).toString();
    }

    @API(desc = "删除分支机构费用划拨", operation = APIOperation.DELETE)
    public String delete(SqlParam<M911> params) throws Exception {
        M911 m911 = m911Dao.getM911(params);
        if (m911 == null){
            throw new PromptException("找不到划拨记录");
        }
        if (!(m911.getTransferStatus().equals("0") || m911.getTransferStatus().equals("1"))){
            throw new PromptException("只有未处理和已生成的划拨可以删除");
        }
        m911Dao.deleteM911(params);
        return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
    }

    public static int getDayDiffer(Date startDate, Date endDate) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        long startDateTime = dateFormat.parse(dateFormat.format(startDate)).getTime();
        long endDateTime = dateFormat.parse(dateFormat.format(endDate)).getTime();
        return (int) ((endDateTime - startDateTime) / (1000 * 3600 * 24));
    }

}
