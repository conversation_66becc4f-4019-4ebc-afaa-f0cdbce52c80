package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M941Dao;
import com.kayak.bala.model.M941;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "源泉宝交易流水查询", model = M941.class)
public class M941Service {

	@Autowired
	private M941Dao m941Dao;

	@Autowired
	private M101Dao m101Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询源泉宝交易流水查询", auth = APIAuth.YES)
	public SqlResult<M941> findBalaCustHostTransLog(SqlParam<M941> params) throws Exception {

		//TODO 注掉SystemNo 待验证
		params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()/*, SystemNo.BALA*/));
		params.getModel().setCustName(null);
		params.getModel().setIdCode(null);
		params.getModel().setIdType(null);
		params.getModel().setAcctNo(null);
		SqlResult<M941> sqlResult = m941Dao.findBalaCustHostTransLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try {
				/**if(StringUtils.isNotBlank(item.getCustNo())){
					Map<String, Object> custmap = new HashMap<>();
					custmap.put("custNo", item.getCustNo());
					SqlParam<M101> custParams = new FetcherData<>(custmap, M101.class);
					custParams.setMakeSql(true);
					SqlResult<M101> cusInfo = m101Dao.findCustInfos(custParams,"");
					if (cusInfo.getRows() != null && cusInfo.getRows().size() > 0) {
						item.setIdType(cusInfo.getRows().get(0).getIdType());
						item.setIdCode(cusInfo.getRows().get(0).getIdCode());
						item.setCustName(cusInfo.getRows().get(0).getCustName());
					}
				}*/

				if(StringUtils.isNotBlank(item.getBranchCode())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getBranchCode());
					SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(dateParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setBranchName(m001Info.getRows().get(0).getOrgname());
					}
				}

				if(StringUtils.isNotBlank(item.getTransOrgno())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getTransOrgno());
					SqlParam<M001> param = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(param);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setOrgName(m001Info.getRows().get(0).getOrgname());
					}
				}

				/**if(StringUtils.isNotBlank(item.getBalaCode())){
					Map<String,Object> map = new HashMap<>();
					String balaName = m941Dao.findBalaName(item.getBalaCode());
					item.setBalaName(balaName);
				}*/

			} catch (Exception e) {
				throw new RuntimeException("M941错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(true);
		return sqlResult;
	}
}
