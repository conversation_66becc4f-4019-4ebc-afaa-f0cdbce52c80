package com.kayak.bala.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.bala.dao.M925Dao;
import com.kayak.bala.model.M925;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.dao.M009Dao;
import com.kayak.system.model.M001;
import com.kayak.system.model.M009;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "源泉宝交易流水查询", model = M925.class)
public class M925Service {

	@Autowired
	private M925Dao m925Dao;

	@Autowired
	private M101Dao m101Dao;

	@Autowired
	private M001Dao m001Dao;

	@Autowired
	private ReportformUtil reportformUtil;

	@API(desc = "查询源泉宝交易流水查询", auth = APIAuth.YES)
	public SqlResult<M925> findBalaCustHostTransLog(SqlParam<M925> params) throws Exception {
		if(Tools.isNotBlank(params.getModel().getCustName())||Tools.isNotBlank(params.getModel().getIdCode())||Tools.isNotBlank(params.getModel().getIdType())||Tools.isNotBlank(params.getModel().getAcctNo())){
			params.getModel().setCustNo(reportformUtil.getCustNoForCustInfo(params.getModel().getCustName(),params.getModel().getIdCode(),params.getModel().getIdType(),params.getModel().getAcctNo()));
			params.getModel().setCustName(null);
			params.getModel().setIdCode(null);
			params.getModel().setIdType(null);
			params.getModel().setAcctNo(null);
		}
		SqlResult<M925> sqlResult = m925Dao.findBalaCustHostTransLog(params);
		reportformUtil.checkMaxExcel(sqlResult.getRows().size());
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try {
				if(StringUtils.isNotBlank(item.getBranchCode())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getBranchCode());
					SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(dateParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setBranchName(m001Info.getRows().get(0).getOrgname());
					}
				}

				if(StringUtils.isNotBlank(item.getTransOrgno())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",item.getTransOrgno());
					map.put("userid",params.getAuthInfo().get("userid"));
					SqlParam<M001> param = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(param);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						item.setOrgName(m001Info.getRows().get(0).getOrgname());
					}
				}

			} catch (Exception e) {
				throw new RuntimeException("M925错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		sqlResult.setDesensitized(false);
		return sqlResult;
	}
}
