package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m905Service", table = "bala_acct_ta")
@Data
public class M905 {
   @GraphQLField(label = "TA代码",  sql = " tano = $S{tano} ",field = "tano",kkhtmlDefault = true)
   private String tano;

   @GraphQLField(label = "TA名称" ,field = "taName")
   private String taName;

   @GraphQLField(label = "产品代码",  sql = " PROD_CODE = $S{prodCode} ",field = "prod_code",kkhtmlDefault = true)
   private String prodCode;

   @GraphQLField(label = "产品名称" ,field = "prodName")
   private String prodName;

   @GraphQLField(label = "系统编号" ,field = "system_no")
   private String systemNo;

   @GraphQLField(label = "产品编号" ,field = "bala_code")
   private String balaCode;

   @GraphQLField(label = "TA手续费户",field = "ta_fee_acct")
   private String taFeeAcct;

   @GraphQLField(label = "TA申认购账户",field = "ta_collect_acct")
   private String taCollectAcct;

   @GraphQLField(label = "TA申购收款账户",field = "ta_collect_recv_acct")
   private String taCollectRecvAcct;

   @GraphQLField(label = "TA赎回账户",field = "ta_redeem_acct")
   private String taRedeemAcct;

   @GraphQLField(label = "TA手续费户名",field = "ta_fee_acct_name")
   private String taFeeAcctName;

   @GraphQLField(label = "TA申认购账户名",field = "ta_collect_acct_name")
   private String taCollectAcctName;

   @GraphQLField(label = "TA申购收款账户名",field = "ta_collect_recv_acct_name")
   private String taCollectRecvAcctName;

   @GraphQLField(label = "TA赎回账户名",field = "ta_redeem_acct_name")
   private String taRedeemAcctName;

   @GraphQLField(label = "备注",field = "remark")
   private String remark;

   @GraphQLField(label = "联系人",field = "contacts")
   private String contacts;

   @GraphQLField(label = "联系电话",field = "contact_number")
   private String contactNumber;


}