package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m904Service", table = "bala_acct_info")
@Data
public class M904 {
   @GraphQLField(label = "TA代码", field = "tano")
   private String tano;

   @GraphQLField(label = "账户编号" ,field = "acct_serno")
   private String acctSerno;

   @GraphQLField(label = "账户名称",field = "acct_name")
   private String acctName;

   @GraphQLField(kkhtml = "KFieldSelect",kkhtmlExt="{\"data-dict\": \"bala_acct_type\"}", sql = "acct_type = $S{acctType}",
           label = "银行账户类别",field = "acct_type",kkhtmlDefault=true)
   private String acctType;

   @GraphQLField(label = "银行账号",field = "acct_no")
   private String acctNo;

   @GraphQLField(label = "银行账户名称",field = "bank_acct_name")
   private String bankAcctName;

   @GraphQLField(label = "开户银行",field = "bank_name")
   private String bankName;

   @GraphQLField(label = "是否行内账号",field = "is_internal")
   private String isInternal;

   @GraphQLField(label = "币种",field = "cur")
   private String cur;

   @GraphQLField(label = "所属机构", field = "orgno",kkhtmlDefault=true)
   private String orgno;

   @GraphQLField(label = "联系人",field = "contact_name")
   private String contactName;

   @GraphQLField(label = "所属机构名称",field = "org_name")
   private String orgName;

   @GraphQLField(label = "联系电话",field = "telno")
   private String telno;

   @GraphQLField(label = "交收方式",field = "settlement_type",sql = "settlement_type = $S{settlementType}")
   private String settlementType;

   @GraphQLField( label = "核心产品类型", sql = " HOST_PROD_TYPE= $S{hostProdType}" ,field = "HOST_PROD_TYPE")
   private String hostProdType;

   @GraphQLField( label = "账户序号", sql = " HOST_ACCT_SEQ_NO= $S{hostAcctSeqNo}" ,field = "HOST_ACCT_SEQ_NO")
   private String hostAcctSeqNo;

   @GraphQLField( label = "核心账户类型", sql = " HOST_ACCT_TYPE= $S{hostAcctType}" ,field = "HOST_ACCT_TYPE")
   private String hostAcctType;

   @GraphQLField( label = "核心账户机构", sql = "HOST_ORGNO= $S{hostOrgno}" ,field = "HOST_ORGNO")
   private String hostOrgno;

   @GraphQLField(label = "联行行号",field = "INTER_CODE")
   private String interCode;


   public String getAcctSerno() {
      return acctSerno;
   }

   public void setAcctSerno(String acctSerno) {
      this.acctSerno = acctSerno;
   }
}