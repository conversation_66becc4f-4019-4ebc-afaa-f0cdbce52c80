package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m925Service",firstLineNotDesensitized = true)
public class M925 implements Cloneable{

    /** @GraphQLField(label = "交易流水号",field = "app_serno",
            kkhtml = "KFieldText",  sql = " t1.app_serno = $S{appSerno}")
    private String appSerno;
    */
    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " t1.cust_name like '%$U{custName}%'",field = "cust_name",kkhtmlDefault = true)
    private String custName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = '$U{idType}'",field = "id_type",kkhtmlDefault = true)
    private String idType;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " t1.id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
    private String idCode;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " t1.acct_no = $S{acctNo}",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
    private String acctNo;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "交易账号",  sql = " t1.TRANS_ACCT_NO = $S{transAcctNo} ",field = "TRANS_ACCT_NO",kkhtmlDefault = true)
    private String transAcctNo;

    @GraphQLField(label = "客户号",field = "cust_no")
    private String custNo;

    /**@GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"balance_trade_turnover_inquiry\",\"data-dict-filter\":\"\"}",key = true ,label = "交易类型",  sql = " busi_code = '$U{busiCode}'",field = "busi_code",kkhtmlDefault = true)
    private String busiCode;
    */
    @GraphQLField(label = "交易金额",field = "APP_AMT")
    private String appAmt;


    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "起始金额",  sql = " t1.TRANS_AMT >= $S{transAmtMin}",field = "trans_amt_min",kkhtmlDefault = true)
    private String transAmtMin;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "最高金额",  sql = " t1.TRANS_AMT <= $S{transAmtMax}",field = "trans_amt_max",kkhtmlDefault = true)
    private String transAmtMax;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"trans_status\",\"data-dict-filter\":\"0,1\"}",key = true ,label = "交易状态",  sql = " t1.trans_status = '$U{transStatus}'",field = "trans_status",kkhtmlDefault = true)
    private String transStatus;

    @GraphQLField(label = "产品代码",field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称",field = "prod_name")
    private String prodName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"channel_flag\",\"data-dict-filter\":\"\"}",key = true ,label = "交易渠道",  sql = " t1.CHANNEL_FLAG = '$U{channelFlag}'",field = "channel_flag",kkhtmlDefault = true)
    private String channelFlag;

    @GraphQLField(label = "推荐人",field = "CUST_MANAGER")
    private String custManager;

    @GraphQLField(label = "所属区域",field = "BRANCH_CODE",kkhtmlDefault = true)
    private String branchCode;

    @GraphQLField(label = "所属机构",field = "SUB_BRANCH_CODE",kkhtmlDefault = true)
    private String subBranchCode;

    /*    *//**
     * 开始日期
     *//*
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "t1.busi_date >= $S{busiDate}", field = "busi_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'busiEndDate'}")
    private String busiDate;

    *//**
     * 开始日期(前端查询时间范围使用)
     *//*
    @GraphQLField(label = "结束日期", sql = "t1.busi_date <= $S{busiEndDate}", field = "busi_end_date")
    private String busiEndDate;*/

    @GraphQLField(label = "交易时间",field = "TRANS_TIME")
    private String transTime;

    @GraphQLField(label = "返回码",field = "HOST_RTN_CODE")
    private String hostRtnCode;

    @GraphQLField(label = "返回信息",field = "HOST_RTN_DESC")
    private String hostRtnDesc;


    @GraphQLField(label = "所属区域",field = "branch_name")
    private String branchName;

    @GraphQLField(label = "所属机构名称",field = "org_name")
    private String orgName;

    @GraphQLField(label = "交易机构机构",field = "TRANS_ORGNO")
    private String transOrgno;

    //@GraphQLField(label = "交易类型",field = "TRANS_TYPE")
    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"bala_host_trans_type\",\"data-dict-filter\":\"T020,T010,T030,T080,T050,T060,T070\"}",key = true ,label = "交易类型",  sql = " t1.trans_type = '$U{transType}'",field = "trans_type",kkhtmlDefault = true)
    private String transType;
    @GraphQLField(label = "确认金额",field = "TRANS_AMT")
    private String transAmt;
    @GraphQLField(label = "产品名称",field = "bala_name")
    private String balaName;


    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "t1.TRANS_DATE >= $S{transDate}", field = "TRANS_DATE",
            kkhtmlExt = "{'data-type':'daterange',endDateFeild:'transEndDate',\"data-allowblank\":false}")
    private String transDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "t1.TRANS_DATE <= $S{transEndDate}", field = "trans_end_date")
    private String transEndDate;

    @GraphQLField(label = "产品代码",field = "BALA_CODE")
    private String balaCode;
    @GraphQLField(label = "交易流水号",field = "HOST_TRANS_SERNO",
    kkhtml = "KFieldText",  sql = " t1.HOST_TRANS_SERNO = $S{hostTransSerno}")
    private String hostTransSerno;
    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"trans_status\",\"data-dict-filter\":\"2,3,5,4\"}",key = true ,label = "确认状态",  sql = " t1.ack_status = $S{ackStatus} ",field = "ack_status",kkhtmlDefault = true)
    private String ackStatus;
    @GraphQLField(label = "备注",field = "remark")
    private String remark;


    public M925 clone() throws CloneNotSupportedException{
      return (M925) super.clone();
    }



    public String getCustName() {
    return custName;
    }


    public void setCustName(String custName) {
    this.custName = custName;
    }


    public String getIdType() {
    return idType;
    }


    public void setIdType(String idType) {
    this.idType = idType;
    }


    public String getIdCode() {
    return idCode;
    }


    public void setIdCode(String idCode) {
    this.idCode = idCode;
    }


    public String getAcctNo() {
    return acctNo;
    }


    public void setAcctNo(String acctNo) {
    this.acctNo = acctNo;
    }


    public String getTransAcctNo() {
    return transAcctNo;
    }


    public void setTransAcctNo(String transAcctNo) {
    this.transAcctNo = transAcctNo;
    }


    public String getCustNo() {
    return custNo;
    }


    public void setCustNo(String custNo) {
    this.custNo = custNo;
    }





    public String getAppAmt() {
    return appAmt;
    }


    public void setAppAmt(String appAmt) {
    this.appAmt = appAmt;
    }


    public String getTransAmtMin() {
    return transAmtMin;
    }


    public void setTransAmtMin(String transAmtMin) {
    this.transAmtMin = transAmtMin;
    }


    public String getTransAmtMax() {
    return transAmtMax;
    }


    public void setTransAmtMax(String transAmtMax) {
    this.transAmtMax = transAmtMax;
    }


    public String getTransStatus() {
    return transStatus;
    }


    public void setTransStatus(String transStatus) {
    this.transStatus = transStatus;
    }


    public String getProdCode() {
    return prodCode;
    }


    public void setProdCode(String prodCode) {
    this.prodCode = prodCode;
    }


    public String getProdName() {
    return prodName;
    }


    public void setProdName(String prodName) {
    this.prodName = prodName;
    }


    public String getChannelFlag() {
    return channelFlag;
    }


    public void setChannelFlag(String channelFlag) {
    this.channelFlag = channelFlag;
    }


    public String getCustManager() {
    return custManager;
    }


    public void setCustManager(String custManager) {
    this.custManager = custManager;
    }


    public String getBranchCode() {
    return branchCode;
    }


    public void setBranchCode(String branchCode) {
    this.branchCode = branchCode;
    }


    public String getSubBranchCode() {
    return subBranchCode;
    }


    public void setSubBranchCode(String subBranchCode) {
    this.subBranchCode = subBranchCode;
    }


    /*public String getBusiDate() {
    return busiDate;
    }


    public void setBusiDate(String busiDate) {
    this.busiDate = busiDate;
    }


    public String getBusiEndDate() {
    return busiEndDate;
    }


    public void setBusiEndDate(String busiEndDate) {
    this.busiEndDate = busiEndDate;
    }*/


    public String getTransTime() {
    return transTime;
    }


    public void setTransTime(String transTime) {
    this.transTime = transTime;
    }


    public String getHostRtnCode() {
    return hostRtnCode;
    }


    public void setHostRtnCode(String hostRtnCode) {
    this.hostRtnCode = hostRtnCode;
    }


    public String getHostRtnDesc() {
    return hostRtnDesc;
    }


    public void setHostRtnDesc(String hostRtnDesc) {
    this.hostRtnDesc = hostRtnDesc;
    }


    public String getBranchName() {
    return branchName;
    }


    public void setBranchName(String branchName) {
    this.branchName = branchName;
    }


    public String getOrgName() {
    return orgName;
    }


    public void setOrgName(String orgName) {
    this.orgName = orgName;
    }


    public String getAckStatus() {
        return ackStatus;
    }

    public void setAckStatus(String ackStatus) {
        this.ackStatus = ackStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}