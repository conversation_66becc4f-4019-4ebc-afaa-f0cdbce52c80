package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;


@GraphQLModel(fetcher = "m914Service")
@Data
public class M914 {

    @GraphQLField(kkhtml = "KFieldDate",label = "开始日期", kkhtmlDefault = true, sql = " ack_date >= $S{ackDate}",
            field = "ack_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'ackEndDate',\"data-allowblank\":false}")
    private String ackDate;
    @GraphQLField(label = "结束日期", sql = " ack_date <= $S{ackEndDate}",field = "ack_end_date")
    private String ackEndDate;

	@GraphQLField(label = "所属区域",field = "branch_code")
	private String branchCode;

	@GraphQLField(label = "所属区域",field = "branch_code_name")
	private String branchCodeName;

	@GraphQLField(label = "所属机构",field = "sub_branch_code_name")
	private String subBranchCodeName;

	/**@GraphQLField(kkhtml = "KFieldCascader", kkhtmlExt="{\"data-diffcondition\":\"orgno,parentorgno\",\"data-graphql\":\"{queryM001(action:\\\"find\\\") {rows{orgid, orgname, parentorgno, orgno},results}}\",\"data-display-child\":\"children\",\"data-check-strictly\":true,\"data-show-num\":true,\"data-props\":\"{ expandTrigger: 'hover'}\",\"data-size\":\"medium\",\"data-clearable\":true,\"data-fileterable\":true,\"data-display-field\":\"orgname\",\"data-value-field\":\"orgno\",\"data-multiple\":\"true\"}",
			label = "所属机构", sql = "sub_branch_code = $S{subBranchCode}", field = "sub_branch_code",kkhtmlDefault = true)*/
    @GraphQLField(label = "所属机构代码",field = "sub_branch_code")
	private String subBranchCode;

    @GraphQLField(label = "转入总金额",field = "total_amt")
    private BigDecimal totalAmt;

    @GraphQLField(label = "自动转入总金额",field = "total_auto_amt")
    private BigDecimal totalAutoAmt;

    @GraphQLField(label = "转入合计",field = "tot_in_amt")
    private BigDecimal totInAmt;

    @GraphQLField(label = "转出总金额",field = "total_out_amt")
    private BigDecimal totalOutAmt;

    @GraphQLField(label = "普赎总金额",field = "total_redeem_amt")
    private BigDecimal totalRedeemAmt;

    @GraphQLField(label = "转出合计",field = "tot_out_amt")
    private BigDecimal totOutAmt;

    
    
}
