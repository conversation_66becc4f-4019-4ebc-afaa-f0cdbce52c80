package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m927Service",firstLineNotDesensitized = true)
public class M927 implements Cloneable{

    @GraphQLField(label = "理财交易流水号",field = "trans_serno",sql = " trans_serno = $S{transSerno} ")
    private String transSerno;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " cust_name like '%$U{custName}%'",field = "cust_name",kkhtmlDefault = true)
   private String custName;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = '%$U{idType}%'",field = "id_type",kkhtmlDefault = true)
   private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
   private String idCode;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"busl_type\",\"data-dict-filter\":\"\"}",key = true ,label = "还款类型",  sql = " busi_type = '%$U{busiType}%'",field = "busi_type",kkhtmlDefault = true)
    private String busiType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;

  @GraphQLField(label = "交易账号",field = "trans_acct_no",sql = " trans_acct_no = $S{transAcctNo} ")
   private String transAcctNo;

   @GraphQLField(label = "交易金额",field = "payment_amt")
   private String paymentAmt;

   @GraphQLField(label = "资金状态",field = "trans_status")
   private String transStatus;

    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "trans_date >= $S{transDate}", field = "trans_date",
            kkhtmlExt = "{'data-type':'daterange',endDateFeild:'startEndDate',\"data-allowblank\":false}")
    private String transDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "trans_date <= $S{startEndDate}", field = "trans_date",kkhtmlDefault = true)
    private String startEndDate;

   @GraphQLField(label = "核心返回码",field = "host_rtn_code")
   private String hostRtnCode;

   @GraphQLField(label = "核心返回信息",field = "host_rtn_desc")
   private String hostRtnDesc;


    @GraphQLField(label = "客户号",field = "cust_no")
    private String custNo;

    @GraphQLField(label = "产品代码",field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称",field = "prod_name")
    private String prodName;

   public M927 clone() throws CloneNotSupportedException{
      return (M927) super.clone();
   }

public String getTransSerno() {
	return transSerno;
}

public void setTransSerno(String transSerno) {
	this.transSerno = transSerno;
}

public String getCustName() {
	return custName;
}

public void setCustName(String custName) {
	this.custName = custName;
}

public String getIdType() {
	return idType;
}

public void setIdType(String idType) {
	this.idType = idType;
}

public String getIdCode() {
	return idCode;
}

public void setIdCode(String idCode) {
	this.idCode = idCode;
}

public String getBusiType() {
	return busiType;
}

public void setBusiType(String busiType) {
	this.busiType = busiType;
}

public String getAcctNo() {
	return acctNo;
}

public void setAcctNo(String acctNo) {
	this.acctNo = acctNo;
}

public String getTransAcctNo() {
	return transAcctNo;
}

public void setTransAcctNo(String transAcctNo) {
	this.transAcctNo = transAcctNo;
}

public String getPaymentAmt() {
	return paymentAmt;
}

public void setPaymentAmt(String paymentAmt) {
	this.paymentAmt = paymentAmt;
}

public String getTransStatus() {
	return transStatus;
}

public void setTransStatus(String transStatus) {
	this.transStatus = transStatus;
}

public String getTransDate() {
	return transDate;
}

public void setTransDate(String transDate) {
	this.transDate = transDate;
}

public String getStartEndDate() {
	return startEndDate;
}

public void setStartEndDate(String startEndDate) {
	this.startEndDate = startEndDate;
}

public String getHostRtnCode() {
	return hostRtnCode;
}

public void setHostRtnCode(String hostRtnCode) {
	this.hostRtnCode = hostRtnCode;
}

public String getHostRtnDesc() {
	return hostRtnDesc;
}

public void setHostRtnDesc(String hostRtnDesc) {
	this.hostRtnDesc = hostRtnDesc;
}

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }
}