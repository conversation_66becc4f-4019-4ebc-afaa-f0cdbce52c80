package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m916Service")
@Data
public class M916 {
    @GraphQLField(label = "所属区域",field = "branch_code")
    private String branchCode;

	@GraphQLField(label = "所属区域",field = "branch_code_name")
	private String branchCodeName;

	@GraphQLField(label = "所属机构",field = "sub_branch_code_name")
	private String subBranchCodeName;

	/**@GraphQLField(kkhtml = "KFieldCascader", kkhtmlExt="{\"data-diffcondition\":\"orgno,parentorgno\",\"data-graphql\":\"{queryM001(action:\\\"find\\\") {rows{orgid, orgname, parentorgno, orgno},results}}\",\"data-display-child\":\"children\",\"data-check-strictly\":true,\"data-show-num\":true,\"data-props\":\"{ expandTrigger: 'hover'}\",\"data-size\":\"medium\",\"data-clearable\":true,\"data-fileterable\":true,\"data-display-field\":\"orgname\",\"data-value-field\":\"orgno\",\"data-multiple\":\"true\"}",
			label = "所属机构", sql = "sub_branch_code = $S{subBranchCode}", field = "sub_branch_code")*/
    @GraphQLField(label = "所属机构代码",field = "sub_branch_code")
    private String subBranchCode;

   // @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
    //        label = "查询日期", sql = "stat_date = $S{statDate}" ,field = "stat_date")
    @GraphQLField(label = "查询日期", field = "stat_date")
    private String statDate;

    @GraphQLField(label = "TA代码", field = "tano")
    private String tano;

    @GraphQLField(label = "TA名称" ,field = "taName")
    private String taName;

    @GraphQLField(label = "产品代码", field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;

    @GraphQLField(label = "存量余额", field = "prod_balance")
    private BigDecimal prodBalance;

    @GraphQLField(label = "持有客户数", field = "cust_count")
    private Long custCount;

    
}
