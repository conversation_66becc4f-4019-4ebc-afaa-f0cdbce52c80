package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;


@GraphQLModel(fetcher = "m918Service", table = "bala_transfer_ta")
@Data
public class M918 {
    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
             label = "统计日期", sql = "ack_date = $S{ackDate}" ,field = "ack_date")
    private String ackDate;

    @GraphQLField(label = "产品代码", field = "bala_code")
    private String balaCode;

    @GraphQLField(label = "产品名称", field = "bala_name")
    private String balaName;

	@GraphQLField(label = "TA代码",  sql = " tano = $S{tano} ",field = "tano",kkhtmlDefault = true)
	private String tano;

	@GraphQLField(label = "TA名称" ,field = "taName")
	private String taName;

    @GraphQLField(label = "申购款总金额",field = "sub_amt")
    private BigDecimal subAmt;

    @GraphQLField(label = "赎回款总金额",field = "red_amt")
    private BigDecimal redAmt;

	@GraphQLField(label = "底层产品代码", field = "prod_code")
	private String prodCode;

	@GraphQLField(label = "产品名称", field = "prod_name")
	private String prodName;

	@GraphQLField(label = "申购款总金额合计", field = "sum_sub_amt")
	private String sumSubAmt;

	@GraphQLField(label = "赎回款总金额合计", field = "sum_red_amt")
	private String sumRedAmt;

	public String getAckDate() {
		return ackDate;
	}

	public void setAckDate(String ackDate) {
		this.ackDate = ackDate;
	}

	public String getBalaCode() {
		return balaCode;
	}

	public void setBalaCode(String balaCode) {
		this.balaCode = balaCode;
	}

	public String getBalaName() {
		return balaName;
	}

	public void setBalaName(String balaName) {
		this.balaName = balaName;
	}

	public String getTano() {
		return tano;
	}

	public void setTano(String tano) {
		this.tano = tano;
	}

	public BigDecimal getSubAmt() {
		return subAmt;
	}

	public void setSubAmt(BigDecimal subAmt) {
		this.subAmt = subAmt;
	}

	public BigDecimal getRedAmt() {
		return redAmt;
	}

	public void setRedAmt(BigDecimal redAmt) {
		this.redAmt = redAmt;
	}

	public String getProdCode() {
		return prodCode;
	}

	public void setProdCode(String prodCode) {
		this.prodCode = prodCode;
	}

	public String getProdName() {
		return prodName;
	}

	public void setProdName(String prodName) {
		this.prodName = prodName;
	}
}
