package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m911Service", table = "bala_org_daliy_vol")
@Data
public class M911DaliyVol {
    @GraphQLField (label = "TA代码", field = "tano")
    private String tano;

    @GraphQLField(label = "产品代码", field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "区域代码",field = "area")
    private String area;

    @GraphQLField(label = "总行", field = "bank_code")
    private String bankCode;

    @GraphQLField(label = "分行", field = "BRANCH_CODE")
    private String branchCode;

    @GraphQLField(label = "支行", field = "SUB_BRANCH_CODE")
    private String subBranchCode;

    @GraphQLField(label = "交易机构", field = "TRANS_ORGNO")
    private String transOrgno;

    @GraphQLField(label = "机构代码", field = "orgno")
    private String orgno;

    @GraphQLField(label = "统计日期", field = "start_date")
    private String statDate;

    @GraphQLField(label = "日存量",field = "daliy_vol")
    private BigDecimal daliyVol;

    @GraphQLField(label = "系统编号",field = "system_no")
    private String systemNo;
}
