package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m937Service",firstLineNotDesensitized = true)
public class M937 implements Cloneable{

    @GraphQLField(label = "理财交易流水号",field = "trans_serno",sql =" trans_serno = $S{transSerno}",kkhtmlDefault = true)
    private String transSerno;

    @GraphQLField(label = "脱敏date",field = "query_busi_date",sql =" busi_date = $S{queryBusiDate}",kkhtmlDefault = true)
    private String queryBusiDate;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称", field = "cust_name",kkhtmlDefault = true)
   private String custName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = '%$U{idType}%'",field = "id_type",kkhtmlDefault = true)
    private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码", field = "id_code",kkhtmlDefault = true,desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号", field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;


    /**
     * 开始日期
     */
    @GraphQLField(label = "创建时间", field = "crtTime")
    private String crtTime;
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "busi_date >= $S{busiDate}", field = "busi_date",
            kkhtmlExt = "{'data-type':'daterange',endDateFeild:'startEndDate',\"data-allowblank\":false}")
    private String busiDate;
    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "busi_date <= $S{startEndDate}", field = "busi_date",kkhtmlDefault = true)
    private String startEndDate;

    @GraphQLField(label = "申购金额",field = "app_amt")
    private String appAmt;

    @GraphQLField(label = "交易日期",field = "host_date")
    private String hostDate;

    @GraphQLField(label = "申购金额(含垫子收益)",field = "advance_income")
    private String advanceIncome;

    @GraphQLField(label = "申购份额",field = "app_vol")
    private String appVol;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"trans_status\",\"data-dict-filter\":\"0,1,3,4,5\"}",key = true ,label = "处理状态",  sql = " trans_status = '%$U{transStatus}%'",field = "trans_status",kkhtmlDefault = true)
    private String transStatus;

    @GraphQLField(label = "交易账户",field = "trans_acct_no")
    private String transAcctNo;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"busi_code\",\"data-dict-filter\":\"098,024,022\"}",key = true ,label = "业务代码",  sql = " busi_code = '%$U{busiCode}%'",field = "busi_code",kkhtmlDefault = true)
    private String busiCode;

    @GraphQLField(label = "客户号",field = "cust_no",sql = " cust_no = $S{custNo} ")
    private String custNo;

    @GraphQLField(label = "确认金额",field = "ack_amt")
    private String ackAmt;

    @GraphQLField(label = "确认份额",field = "ack_vol")
    private String ackVol;
}