package com.kayak.bala.model;

import com.kayak.common.base.BaseModel;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

/**
 * 余额理财底层产品维护
 */
@Data
@GraphQLModel(fetcher = "m944Service",table = "bala_prod_document_info")
public class M944 extends BaseModel {
    @GraphQLField(key = true , label = "余额理财产品代码", sql = "a.bala_code = $S{balaCode}" ,field = "balaCode")
    private String balaCode;
    @GraphQLField(label = "系统编号", sql = "a.system_no = $S{systemNo}" ,field = "system_no")
    private String systemNo;
    @GraphQLField(key = true , label = "TA代码", sql = "a.tano = $S{tano}" ,field = "tano")
    private String tano;
    @GraphQLField(key = true , label = "产品代码", sql = "a.prod_code = $S{prodCode}" ,field = "prod_code")
    private String prodCode;
    @GraphQLField(key = true , label = "文档类型", sql = "a.doc_type = $S{docType}" ,field = "doc_type")
    private String docType;
    @GraphQLField(key = true , label = "线上线下标志", sql = "a.online_flag = $S{onlineFlag}" ,field = "online_flag")
    private String onlineFlag;
    @GraphQLField(key = true , label = "文档版本号", sql = "a.doc_version = $S{docVersion}" ,field = "doc_version")
    private String docVersion;
    @GraphQLField(key = true , label = "文档状态", sql = "a.doc_status = $S{docStatus}" ,field = "doc_status")
    private String docStatus;
    @GraphQLField(label = "", sql = "doc_path = $S{docPath}" ,field = "doc_path")
    private String docPath;
    @GraphQLField(label = "文档名称", sql = "a.doc_name like '%$U{docName}%'" ,field = "doc_name")
    private String docName;
    @GraphQLField(label = "", sql = "upload_date = $S{uploadDate}" ,field = "upload_date")
    private String uploadDate;
    @GraphQLField(label = "", sql = "update_time = $S{updateTime}" ,field = "updateTime")
    private String updateTime;
    @GraphQLField(label = "", sql = "create_time = $S{createTime}" ,field = "create_time")
    private String createTime;
    @GraphQLField
    private String prodName;
    @GraphQLField
    private String taName;
    @GraphQLField
    private String fileDate;

    public String getBalaCode() {
        return balaCode;
    }

    public void setBalaCode(String balaCode) {
        this.balaCode = balaCode;
    }

    public String getSystemNo() {
        return systemNo;
    }

    public void setSystemNo(String systemNo) {
        this.systemNo = systemNo;
    }

    public String getTano() {
        return tano;
    }

    public void setTano(String tano) {
        this.tano = tano;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getOnlineFlag() {
        return onlineFlag;
    }

    public void setOnlineFlag(String onlineFlag) {
        this.onlineFlag = onlineFlag;
    }

    public String getDocVersion() {
        return docVersion;
    }

    public void setDocVersion(String docVersion) {
        this.docVersion = docVersion;
    }

    public String getDocStatus() {
        return docStatus;
    }

    public void setDocStatus(String docStatus) {
        this.docStatus = docStatus;
    }

    public String getDocPath() {
        return docPath;
    }

    public void setDocPath(String docPath) {
        this.docPath = docPath;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(String uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getFileDate() {
        return fileDate;
    }

    public void setFileDate(String fileDate) {
        this.fileDate = fileDate;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public String getTaName() {
        return taName;
    }

    public void setTaName(String taName) {
        this.taName = taName;
    }

}
