package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m911Service", table = "bala_transfer_org_list")
@Data
public class M911List {
    @GraphQLField(label = "TA代码",  sql = " tano like '%$U{tano}%'",field = "tano",kkhtmlDefault = true)
    private String tano;

    @GraphQLField(label = "TA名称" ,field = "taName")
    private String taName;

    @GraphQLField(kkhtmlDefault = true,
            label = "产品代码", sql = "prod_code = $S{prodCode}" ,field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称",field = "prod_name")
    private String prodName;

    @GraphQLField(label = "机构代码", field = "orgno")
    private String orgno;

    @GraphQLField(label = "机构名称", field = "orgname")
    private String orgname;

    @GraphQLField(label = "所属区域", field = "area")
    private String area;

    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate",
            label = "开始日期", field = "start_date", sql = "start_date = $S{startDate}")
    private String startDate;

    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
            label = "结束日期", field = "end_date", sql = "end_date = $S{endDate}")
    private String endDate;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "划款状态",field = "transfer_status",sql = "transfer_status = $S{transferStatus}" ,
            kkhtmlExt="{\"data-dict\": \"transfer_status\"}")
    private String transferStatus;

    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
            label = "划拨日期", sql = "transfer_date = $S{transferDate}" ,field = "transfer_date")
    private String transferDate;

    @GraphQLField(label = "中收金额",field = "earning_amt")
    private BigDecimal earningAmt;

    @GraphQLField(label = "日均存量",field = "daliy_vol")
    private BigDecimal daliyVol;

    @GraphQLField(label = "划拨序号",field = "transfer_serno")
    private String transferSerno;

    @GraphQLField(label = "系统编号",field = "system_no")
    private String systemNo;

    @GraphQLField(label = "创建时间", field = "create_time")
    private String createTime;

    @GraphQLField(label = "更新时间", field = "update_time")
    private String updateTime;

    @GraphQLField(label = "总行", field = "bank_code")
    private String bankCode;

    @GraphQLField(label = "分行", field = "BRANCH_CODE")
    private String branchCode;

    @GraphQLField(label = "支行", field = "SUB_BRANCH_CODE")
    private String subBranchCode;

    @GraphQLField(label = "交易机构", field = "TRANS_ORGNO")
    private String transOrgno;
}
