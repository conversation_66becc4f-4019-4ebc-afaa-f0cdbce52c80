package com.kayak.bala.model;

import com.alibaba.fastjson.JSONArray;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m931Service")
@Data
public class M931 {


    /** @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-action\":'M001.findArea',\"data-allowblank\":false,\"data-display-field\":\"orgname\",\"data-value-field\":\"orgno\",\"data-multiple\":\"true\"}",
			label = "所属区域", sql = "t.branch_code = $S{branchCode}", field = "branch_code")*/
	@GraphQLField( label = "所属区域" ,field = "branch_code")
	private String branchCode;

	private JSONArray branchCodeList;

    @GraphQLField( label = "查询日期" ,field = "stat_date")
    //@GraphQLField(label = "查询日期", field = "stat_date")
    private String statDate;

    @GraphQLField(label = "TA代码", field = "tano")
    private String tano;

	@GraphQLField(label = "TA名称" ,field = "taName")
	private String taName;

    @GraphQLField(label = "产品代码", field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称", field = "prod_name")
    private String prodName;
    @GraphQLField(label = "机构名称", field = "org_name")
    private String orgName;

    @GraphQLField(label = "存量余额", field = "prod_balance")
    private BigDecimal prodBalance;

    @GraphQLField(label = "持有客户数", field = "cust_count")
    private Long custCount;

	public JSONArray getBranchCodeList() {
		return branchCodeList;
	}

	public void setBranchCodeList(JSONArray branchCodeList) {
		this.branchCodeList = branchCodeList;
	}

	public String getTaName() {
		return taName;
	}

	public void setTaName(String taName) {
		this.taName = taName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getBranchCode() {
		return branchCode;
	}

	public void setBranchCode(String branchCode) {
		this.branchCode = branchCode;
	}

	public String getStatDate() {
		return statDate;
	}

	public void setStatDate(String statDate) {
		this.statDate = statDate;
	}

	public String getTano() {
		return tano;
	}

	public void setTano(String tano) {
		this.tano = tano;
	}

	public String getProdCode() {
		return prodCode;
	}

	public void setProdCode(String prodCode) {
		this.prodCode = prodCode;
	}

	public String getProdName() {
		return prodName;
	}

	public void setProdName(String prodName) {
		this.prodName = prodName;
	}

	public BigDecimal getProdBalance() {
		return prodBalance;
	}

	public void setProdBalance(BigDecimal prodBalance) {
		this.prodBalance = prodBalance;
	}

	public Long getCustCount() {
		return custCount;
	}

	public void setCustCount(Long custCount) {
		this.custCount = custCount;
	}
    
    
}
