package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m902Service", table = "bala_prod_base_info")
@Data
public class M902 {
   @GraphQLField(label = "TA代码",  sql = " tano like '%$U{tano}%'",field = "tano",kkhtmlDefault = true)
   private String tano;

   @GraphQLField(label = "TA名称" ,field = "taName")
   private String taName;

   @GraphQLField(label = "系统编号" ,field = "system_no")
   private String systemNo;

   @GraphQLField(label = "产品代码",  sql = " PROD_CODE like '%$U{prodCode}%'",field = "prod_code",kkhtmlDefault = true)
   private String prodCode;

   @GraphQLField(label = "余额理财产品代码",field = "bala_code")
   private String balaCode;

   @GraphQLField(label = "产品名称",  sql = " PROD_NAME like '%$U{prodName}%'",field = "prod_name",kkhtmlDefault = true)
   private String prodName;

   @GraphQLField(label = "产品风险等级",field = "prod_risk_level")
   private String prodRiskLevel;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"bala_prod_type\",\"data-dict-filter\":\"\"}",key = true ,label = "产品类型",  sql = " prod_type = $S{prodType} ",field = "prod_type",kkhtmlDefault = true)
   private String prodType;

   @GraphQLField(label = "产品限额", field = "prod_limit")
   private BigDecimal prodLimit;

   @GraphQLField(label = "产品状态",field = "prod_status")
   private String prodStatus;

   @GraphQLField(label = "创建时间",field = "create_time")
   private String createTime;

   @GraphQLField(label = "修改时间",field = "update_time")
   private String updateTime;

   @GraphQLField(label = "净值日期",field = "nav_date")
   private String navDate;

   @GraphQLField(label = "净值",field = "nav")
   private BigDecimal nav;

   @GraphQLField(label = "七日年化收益率",field = "seven_day_annualized_yield")
   private BigDecimal sevenDayAnnualizedYield;

   @GraphQLField(label = "每万份单位收益",field = "revenue_per_ten_kilo_units")
   private BigDecimal revenuePerTenKiloUnits;

   @GraphQLField(label = "总收益",field = "total_income")
   private BigDecimal totalIncome;

   @GraphQLField(label = "旧TA",field = "old_tano")
   private String oldTano;

}