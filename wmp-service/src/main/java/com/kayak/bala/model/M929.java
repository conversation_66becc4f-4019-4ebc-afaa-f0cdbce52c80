package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m929Service",firstLineNotDesensitized = true)
public class M929 implements Cloneable{

    @GraphQLField(label = "理财交易流水号",field = "trans_serno",sql =" trans_serno = $S{transSerno}",kkhtmlDefault = true)
    private String transSerno;

    @GraphQLField(label = "脱敏date",field = "query_busi_date",sql =" busi_date = $S{queryBusiDate}",kkhtmlDefault = true)
    private String queryBusiDate;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称", field = "cust_name",kkhtmlDefault = true)
   private String custName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = '%$U{idType}%'",field = "id_type",kkhtmlDefault = true)
    private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码", field = "id_code",kkhtmlDefault = true,desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号", field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;


    /**
     * 开始日期
     */
    @GraphQLField(label = "创建时间", field = "crtTime")
    private String crtTime;
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "busi_date >= $S{busiDate}", field = "busi_date",
            kkhtmlExt = "{'data-type':'daterange',endDateFeild:'startEndDate',\"data-allowblank\":false}")
    private String busiDate;
    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "busi_date <= $S{startEndDate}", field = "busi_date",kkhtmlDefault = true)
    private String startEndDate;

    @GraphQLField(label = "净申购金额",field = "js_app_amt")
    private String jsAppAmt;

    @GraphQLField(label = "净过户金额",field = "jg_app_amt")
    private String jgAppAmt;

    @GraphQLField(label = "处理状态",field = "trans_status")
    private String transStatus;

    @GraphQLField(label = "交易账户",field = "trans_acct_no")
    private String transAcctNo;

    @GraphQLField(label = "业务代码",field = "busi_code")
    private String busiCode;

    @GraphQLField(label = "业务代码",field = "cust_no",sql = " cust_no = $S{custNo} ",kkhtmlDefault = true)
    private String custNo;

   public M929 clone() throws CloneNotSupportedException{
      return (M929) super.clone();
   }

public String getTransSerno() {
	return transSerno;
}

public void setTransSerno(String transSerno) {
	this.transSerno = transSerno;
}

public String getQueryBusiDate() {
	return queryBusiDate;
}

public void setQueryBusiDate(String queryBusiDate) {
	this.queryBusiDate = queryBusiDate;
}

public String getCustName() {
	return custName;
}

public void setCustName(String custName) {
	this.custName = custName;
}

public String getIdType() {
	return idType;
}

public void setIdType(String idType) {
	this.idType = idType;
}

public String getIdCode() {
	return idCode;
}

public void setIdCode(String idCode) {
	this.idCode = idCode;
}

public String getAcctNo() {
	return acctNo;
}

public void setAcctNo(String acctNo) {
	this.acctNo = acctNo;
}

public String getCrtTime() {
	return crtTime;
}

public void setCrtTime(String crtTime) {
	this.crtTime = crtTime;
}

public String getBusiDate() {
	return busiDate;
}

public void setBusiDate(String busiDate) {
	this.busiDate = busiDate;
}

public String getStartEndDate() {
	return startEndDate;
}

public void setStartEndDate(String startEndDate) {
	this.startEndDate = startEndDate;
}

public String getJsAppAmt() {
	return jsAppAmt;
}

public void setJsAppAmt(String jsAppAmt) {
	this.jsAppAmt = jsAppAmt;
}

public String getJgAppAmt() {
	return jgAppAmt;
}

public void setJgAppAmt(String jgAppAmt) {
	this.jgAppAmt = jgAppAmt;
}

public String getTransStatus() {
	return transStatus;
}

public void setTransStatus(String transStatus) {
	this.transStatus = transStatus;
}

public String getTransAcctNo() {
	return transAcctNo;
}

public void setTransAcctNo(String transAcctNo) {
	this.transAcctNo = transAcctNo;
}

public String getBusiCode() {
	return busiCode;
}

public void setBusiCode(String busiCode) {
	this.busiCode = busiCode;
}

public String getCustNo() {
	return custNo;
}

public void setCustNo(String custNo) {
	this.custNo = custNo;
}
   
   
}