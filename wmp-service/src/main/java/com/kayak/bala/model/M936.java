package com.kayak.bala.model;

import com.kayak.common.base.BaseModel;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m936Service",firstLineNotDesensitized = true)
public class M936 extends BaseModel {
    @GraphQLField(label = "申请单编号", sql = " app_serno = $S{appSerno}" ,field = "APP_SERNO")
    private String appSerno;
/*    @GraphQLField(label = "业务日期",field = "BUSI_DATE")
    private String busiDate;*/
    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"busi_code\",\"data-dict-filter\":\"909\"}",key = true ,label = "业务代码",  sql = " busi_code = '$U{busiCode}'",field = "busi_code",kkhtmlDefault = true)
    private String busiCode;
    @GraphQLField( label = "TA代码", sql = " tano = $S{tano}" ,field = "TANO")
    private String tano;
    @GraphQLField(label = "产品代码", sql = "prod_code = $S{prodCode}" ,field = "PROD_CODE")
    private String prodCode;
    @GraphQLField(label = "TA名称" ,field = "taName")
    private String taName;
    @GraphQLField(label = "产品名称",field = "prod_name")
    private String prodName;
    @GraphQLField( label = "交易状态",field = "TRANS_STATUS" )
    private String transStatus;
    @GraphQLField(label = "资金状态", field = "CAPITAL_STATUS")
    private String capitalStatus;
    @GraphQLField(label = "客户号",field = "CUST_NO", sql = " cust_no = $S{custNo}" )
    private String custNo;
    @GraphQLField(kkhtml = "KFieldText",label = "卡号", sql = "acct_no = $S{acctNo}" ,field = "ACCT_NO",kkhtmlDefault = true)
    private String acctNo;
    @GraphQLField(kkhtml = "KFieldText",label = "客户名称", sql = "cust_name = $S{custName}" ,field = "CUST_NAME",kkhtmlDefault = true)
    private String custName;
    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = '$U{idType}'",field = "id_type",kkhtmlDefault = true)
    private String idType;
    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
    private String idCode;
    @GraphQLField(label = "机器时间" ,field = "MACTIME")
    private String mactime;
    @GraphQLField(label = "机器日期" ,field = "MACDATE")
    private String macdate;
    @GraphQLField(label = "核心返回码",field = "HOST_RTN_CODE")
    private String hostRtnCode;
    @GraphQLField(label = "核心返回描述" ,field = "HOST_RTN_DESC")
    private String hostRtnDesc;
    @GraphQLField(label = "核心返回流水号" ,field = "HOST_TRANS_SERNO")
    private String hostTransSerno;
    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "业务日期", sql = "busi_date >= $S{busiDate}", field = "BUSI_DATE", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'busiEndDate'}")
    private String busiDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = " busi_date <= $S{busiEndDate}", field = "BUSI_END_DATE")
    private String busiEndDate;

    @GraphQLField(label = "错误类型",field = "error_deal_type")
    private String errorDealType;

    @GraphQLField(label = "渠道流水号",field = "channel_serno")
    private String channelSerno;

    @GraphQLField(label = "调账数据",field = "choose_data")
    private String chooseData;

    public String getChooseData() {
        return chooseData;
    }

    public void setChooseData(String chooseData) {
        this.chooseData = chooseData;
    }
}