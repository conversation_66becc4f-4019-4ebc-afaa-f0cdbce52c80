package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@GraphQLModel(fetcher = "m934Service",firstLineNotDesensitized = true)
public class M934 implements Cloneable{

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-action\":\"M902.findProdCode\",\"data-value-field\":\"prodCode\",\"data-display-field\":\"prodCode\"}",
            label = "产品代码", field = "prod_code", sql = "prod_code = $S{prodCode}",kkhtmlDefault = true)
    private String prodCode;

    @GraphQLField(kkhtml = "KFieldText",sql = "prod_name like '%$U{prodName}%'",
            label = "产品名称", field = "prod_name",kkhtmlDefault = true)
    private String prodName;


    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "busi_date >= $S{busiDate}", field = "busi_date",
            kkhtmlExt = "{'data-type':'daterange',endDateFeild:'startEndDate',\"data-allowblank\":false}")
    private String busiDate;
    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "busi_date <= $S{startEndDate}", field = "busi_date",kkhtmlDefault = true)
    private String startEndDate;

    @GraphQLField(label = "总过户金额",field = "total_app_amt")
    private BigDecimal totalAppAmt;



    
}