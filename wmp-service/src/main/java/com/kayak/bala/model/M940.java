package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m940Service",firstLineNotDesensitized = true)
public class M940 implements Cloneable{

    @GraphQLField(label = "客户号",field = "custs_no")
    private String custsNo;

    @GraphQLField(label = "TA代码",field = "tano",sql = " tano = $S{tano} ")
    private String tano;

    @GraphQLField(label = "TA名称",field = "ta_name")
    private String taName;

    @GraphQLField(label = "产品名称",field = "bala_name")
    private String balaName;

    @GraphQLField(label = "产品代码",field = "bala_code")
    private String balaCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",field = "cust_name",kkhtmlDefault = true)
   private String custName;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型", field = "id_type",kkhtmlDefault = true)
   private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;

  @GraphQLField(kkhtml = "KFieldText",key = true ,kkhtmlDefault = true,label = "交易账号",field = "trans_acct_no",sql = " t.trans_acct_no = $S{transAcctNo} ")
   private String transAcctNo;

   @GraphQLField(label = "分红金额",field = "INCOME_AMT")
   private String incomeAmt;

   @GraphQLField(label = "分红份额",field = "CALC_VOL")
   private String calcVol;

    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "分红日期", sql = "INCOME_DATE >= $S{incomeDate}", field = "INCOME_DATE",
            kkhtmlExt = "{'data-type':'daterange',endDateFeild:'incomeEndDate',\"data-allowblank\":false}")
    private String incomeDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "INCOME_DATE <= $S{incomeEndDate}",kkhtmlDefault = true, field = "INCOME_END_DATE")
    private String incomeEndDate;




   
}