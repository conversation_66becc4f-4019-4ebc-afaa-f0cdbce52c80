package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;


@Data
@GraphQLModel(fetcher = "m926Service",firstLineNotDesensitized = true)
public class M926 implements Cloneable{

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "交易流水号",  sql = " t.app_serno = $S{appSerno} ",field = "app_serno",kkhtmlDefault = true)
    private String appSerno;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " t.cust_name like '%$U{custName}%'",field = "cust_name",kkhtmlDefault = true)
   private String custName;

    @GraphQLField(label = "客户编号",field = "cust_no", sql="t.cust_no = $S{custNo}")
    private String custNo;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " t.id_type = $S{idType}",field = "id_type",kkhtmlDefault = true)
   private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " t.id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " t.acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;

    @GraphQLField(label = "交易账号",field = "trans_acct_no" ,sql = " TRANS_ACCT_NO like '%$U{transAcctNo}%'")
    private String transAcctNo;

   @GraphQLField(label = "TA账号",field = "ta_acct_no")
   private String taAcctNo;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"balance_underlying_turnover\",\"data-dict-filter\":\"\"}",key = true ,label = "交易类型",  sql = " t.busi_code = $S{busiCode}",field = "busi_code",kkhtmlDefault = true)
   private String busiCode;

   @GraphQLField(label = "交易金额",field = "app_amt")
   private String appAmt;

   @GraphQLField(label = "垫资收益",field = "advance_income")
   private String advanceIncome;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"trans_status\",\"data-dict-filter\":\"0,1,3,5,4\"}",key = true ,label = "交易状态",  sql = " t.trans_status = $S{transStatus}",field = "trans_status",kkhtmlDefault = true)
   private String transStatus;

   @GraphQLField(label = "产品代码",field = "prod_code")
   private String prodCode;

   @GraphQLField(label = "产品名称",field = "prod_name")
   private String prodName;

  // @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"channel_flag\",\"data-dict-filter\":\"\"}",key = true ,label = "交易渠道",  sql = " t.channel_flag = $S{channelFlag}",field = "channel_flag",kkhtmlDefault = true)
  @GraphQLField(label = "交易渠道",field = "channel_flag")
   private String channelFlag;

   @GraphQLField(label = "推荐人",field = "cust_manager")
   private String custManager;

	@GraphQLField(label = "机器日期",field = "macdate")
	private String macdate;

    @GraphQLField(label = "机器时间",field = "mactime")
    private String mactime;

    @GraphQLField(label = "法人代码",field = "legal_code")
    private String legalCode;

    @GraphQLField(label = "所属区域",field = "branch_code")
    private String branchCode;

   @GraphQLField( label = "所属机构", field = "trans_orgno")
   private String transOrgno;


   @GraphQLField(label = "返回编码",field = "rtn_code")
   private String rtnCode;

   @GraphQLField(label = "返回信息",field = "rtn_desc")
   private String rtnDesc;

    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "业务日期", sql = "busi_date >= $S{busiDate}", field = "operation_date",
			kkhtmlExt = "{'data-type':'daterange',endDateFeild:'startEndDate',\"data-allowblank\":false}")
    private String busiDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "busi_date <= $S{startEndDate}", field = "busi_date")
    private String startEndDate;

    @GraphQLField(label = "所属区域",field = "branch_name")
    private String branchName;

    @GraphQLField(label = "所属机构名称",field = "org_name")
    private String orgName;

	@GraphQLField(label = "申请份额",field = "app_vol")
	private String appVol;


	public String getAppSerno() {
		return appSerno;
	}

	public void setAppSerno(String appSerno) {
		this.appSerno = appSerno;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getIdType() {
		return idType;
	}

	public void setIdType(String idType) {
		this.idType = idType;
	}

	public String getIdCode() {
		return idCode;
	}

	public void setIdCode(String idCode) {
		this.idCode = idCode;
	}

	public String getAcctNo() {
		return acctNo;
	}

	public void setAcctNo(String acctNo) {
		this.acctNo = acctNo;
	}

	public String getTransAcctNo() {
		return transAcctNo;
	}

	public void setTransAcctNo(String transAcctNo) {
		this.transAcctNo = transAcctNo;
	}

	public String getTaAcctNo() {
		return taAcctNo;
	}

	public void setTaAcctNo(String taAcctNo) {
		this.taAcctNo = taAcctNo;
	}

	public String getBusiCode() {
		return busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public String getAppAmt() {
		return appAmt;
	}

	public void setAppAmt(String appAmt) {
		this.appAmt = appAmt;
	}

	public String getAdvanceIncome() {
		return advanceIncome;
	}

	public void setAdvanceIncome(String advanceIncome) {
		this.advanceIncome = advanceIncome;
	}

	public String getTransStatus() {
		return transStatus;
	}

	public void setTransStatus(String transStatus) {
		this.transStatus = transStatus;
	}

	public String getProdCode() {
		return prodCode;
	}

	public void setProdCode(String prodCode) {
		this.prodCode = prodCode;
	}

	public String getProdName() {
		return prodName;
	}

	public void setProdName(String prodName) {
		this.prodName = prodName;
	}

	public String getChannelFlag() {
		return channelFlag;
	}

	public void setChannelFlag(String channelFlag) {
		this.channelFlag = channelFlag;
	}

	public String getCustManager() {
		return custManager;
	}

	public void setCustManager(String custManager) {
		this.custManager = custManager;
	}

	public String getMactime() {
		return mactime;
	}

	public void setMactime(String mactime) {
		this.mactime = mactime;
	}

	public String getLegalCode() {
		return legalCode;
	}

	public void setLegalCode(String legalCode) {
		this.legalCode = legalCode;
	}

	public String getBranchCode() {
		return branchCode;
	}

	public void setBranchCode(String branchCode) {
		this.branchCode = branchCode;
	}

	public String getTransOrgno() {
		return transOrgno;
	}

	public void setTransOrgno(String transOrgno) {
		this.transOrgno = transOrgno;
	}

	public String getRtnCode() {
		return rtnCode;
	}

	public void setRtnCode(String rtnCode) {
		this.rtnCode = rtnCode;
	}

	public String getRtnDesc() {
		return rtnDesc;
	}

	public void setRtnDesc(String rtnDesc) {
		this.rtnDesc = rtnDesc;
	}

	public String getBusiDate() {
		return busiDate;
	}

	public void setBusiDate(String busiDate) {
		this.busiDate = busiDate;
	}

	public String getStartEndDate() {
		return startEndDate;
	}

	public void setStartEndDate(String startEndDate) {
		this.startEndDate = startEndDate;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
    
    
}