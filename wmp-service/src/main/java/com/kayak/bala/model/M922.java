package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m922Service", table = "bala_cust_sign",firstLineNotDesensitized = true)
@Data
public class M922 {

   @GraphQLField(label = "客户号",field = "cust_no")
   private String custNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;

   @GraphQLField(label = "交易账号",field = "trans_acct_no")
   private String transAcctNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " cust_name = $S{custName} ",field = "cust_name",kkhtmlDefault = true)
   private String custName;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = $S{idType} ",field = "id_type",kkhtmlDefault = true)
   private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code = $S{idCode} ",field = "id_code",kkhtmlDefault = true , desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"bala_sign_status\",\"data-dict-filter\":\"\"}",key = true ,label = "源泉宝签约状态",  sql = " sign_status = $S{signStatus} ",field = "sign_status",kkhtmlDefault = true)
   private String signStatus;


   /**
    * 源泉宝签约日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "源泉宝签约日期", sql = "t.sign_date >= $S{signDate}", field = "sign_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'signEndDate'}")
   private String signDate;

   /**
    * 源泉宝签约结束日期
    */
   @GraphQLField(label = "源泉宝签约结束日期", sql = "t.sign_date <= $S{signEndDate}", field = "sign_date")
   private String signEndDate;

   /**
    * 源泉宝解约日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "源泉宝解约日期", sql = "t.cancel_date >= $S{cancelDate}", field = "cancel_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'cancelEndDate'}")
   private String cancelDate;

   /**
    * 源泉宝解约结束日期
    */
   @GraphQLField(label = "源泉宝解约结束日期", sql = "t.cancel_date <= $S{cancelEndDate}", field = "cancel_date")
   private String cancelEndDate;


   @GraphQLField(label = "签约卡开户机构",field = "trans_orgno")
   private String transOrgno;

   @GraphQLField(label = "签约卡开户机构名称",field = "trans_name")
   private String transName;

   @GraphQLField(label = "签约卡开户区域",field = "branch_code")
   private String branchCode;

   @GraphQLField(label = "签约卡开户区域名称",field = "branch_name")
   private String branchName;

   @GraphQLField(label = "推荐人",field = "referrer")
   private String referrer;

   @GraphQLField(label = "设置金额",field = "auto_amt")
   private String autoAmt;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"auto_status\",\"data-dict-filter\":\"\"}",key = true ,label = "自动转入状态",  sql = " auto_status = $S{autoStatus} ",field = "auto_status",kkhtmlDefault = true)
   private String autoStatus;

   @GraphQLField(label = "自动转入金额类型",field = "auto_type")
   private String autoType;

   /**
    * 自动转入签约日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "自动转入签约日期", sql = "t.auto_sign_date >= $S{autoSignDate}", field = "auto_sign_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'autoSignEndDate'}")
   private String autoSignDate;

   /**
    * 自动转入签约结束日期
    */
   @GraphQLField(label = "自动转入签约结束日期", sql = "t.auto_sign_date <= $S{autoSignEndDate}", field = "auto_sign_date")
   private String autoSignEndDate;

   /**
    * 自动转入解约日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "自动转入解约日期", sql = "t.auto_cancel_date >= $S{autoCancelDate}", field = "auto_cancel_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'autoCancelEndDate'}")
   private String autoCancelDate;

   /**
    * 自动转入解约结束日期
    */
   @GraphQLField(label = "自动转入解约结束日期", sql = "t.auto_cancel_date <= $S{autoCancelEndDate}", field = "auto_cancel_date")
   private String autoCancelEndDate;


   @GraphQLField(label = "创建时间",field = "create_time")
   private String createTime;

   @GraphQLField(label = "修改时间",field = "update_time")
   private String updateTime;

}