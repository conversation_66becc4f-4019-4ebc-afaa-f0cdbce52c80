package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.fina.param.model.Ta3005;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m921Service",firstLineNotDesensitized = true)
public class M921 implements Cloneable{

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " t.cust_name like '%$U{custName}%'",field = "cust_name",kkhtmlDefault = true)
   private String custName;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " t.id_type = '$U{idType}'",field = "id_type",kkhtmlDefault = true)
   private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " t.id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true,desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " t.acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "TA账号",  sql = " t.ta_acct_no like '%$U{taAcctNo}%'",field = "ta_acct_no",kkhtmlDefault = true)
   private String taAcctNo;

   @GraphQLField(label = "TA代码",field = "tano", sql = " t.tano = $S{tano} ")
   private String tano;

   @GraphQLField(label = "TA名称",field = "ta_name")
   private String taName;

   @GraphQLField(label = "交易账号",field = "trans_acct_no")
   private String transAcctNo;

   @GraphQLField(label = "客户编号",field = "cust_no")
   private String custNo;

   public M921 clone() throws CloneNotSupportedException{
      return (M921) super.clone();
   }

   public String getCustName() {
      return custName;
   }

   public void setCustName(String custName) {
      this.custName = custName;
   }

   public String getIdType() {
      return idType;
   }

   public void setIdType(String idType) {
      this.idType = idType;
   }

   public String getIdCode() {
      return idCode;
   }

   public void setIdCode(String idCode) {
      this.idCode = idCode;
   }

   public String getAcctNo() {
      return acctNo;
   }

   public void setAcctNo(String acctNo) {
      this.acctNo = acctNo;
   }

   public String getTaAcctNo() {
      return taAcctNo;
   }

   public void setTaAcctNo(String taAcctNo) {
      this.taAcctNo = taAcctNo;
   }

   public String getTano() {
      return tano;
   }

   public void setTano(String tano) {
      this.tano = tano;
   }

   public String getTransAcctNo() {
      return transAcctNo;
   }

   public void setTransAcctNo(String transAcctNo) {
      this.transAcctNo = transAcctNo;
   }

   public String getCustNo() {
      return custNo;
   }

   public void setCustNo(String custNo) {
      this.custNo = custNo;
   }

   public String getTaName() {
      return taName;
   }

   public void setTaName(String taName) {
      this.taName = taName;
   }
}