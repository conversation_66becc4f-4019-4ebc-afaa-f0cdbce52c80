package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m908Service", table = "bala_check_vol",firstLineNotDesensitized = true)
@Data
public class M908 {
   @GraphQLField(label = "对账流水号",field = "check_serno")
   private String checkSerno;

   @GraphQLField(label = "客户号",field = "cust_no")
   private String custNo;

   @GraphQLField(label = "客户名称",field = "cust_name")
   private String custName;

   @GraphQLField(label = "证件类型",field = "id_type")
   private String idType;

   @GraphQLField(label = "证件号码",field = "id_code", desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(label = "交易账号",field = "trans_acct_no")
   private String transAcctNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true, desensitized = Blankcarddesensitized.class)
   private String acctNo;

   //@GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true, label = "对账日期", sql = "check_date = $S{checkDate}", field = "check_date")
   @GraphQLField(label = "对账日期",field = "check_date",kkhtmlDefault = true)
   private String checkDate;

   @GraphQLField(label = "核心余额",field = "host_balance")
   private String hostBalance;

   @GraphQLField(label = "理财余额",field = "bala_balance")
   private String balaBalance;

   @GraphQLField(label = "处理状态",field = "deal_status")
   private String dealStatus;

   @GraphQLField(label = "更新时间",field = "upd_time")
   private String updTime;

   @GraphQLField(label = "创建时间",field = "crt_time")
   private String crtTime;

}