package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;


@GraphQLModel(fetcher = "m912Service", table = "bala_cust_trans_cfm")
@Data
public class M912 {

    @GraphQLField(kkhtml = "KFieldDate",label = "开始日期", kkhtmlDefault = true,sql="t.ack_date >= $S{startDate}",
            field = "start_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'endDate',\"data-allowblank\":false}")
    private String startDate;
    @GraphQLField(kkhtmlDefault = true,label = "结束日期", sql="t.ack_date <= $S{endDate}",field = "end_date")
    private String endDate;

    @GraphQLField(label = "确认日期",field = "ack_date")
    private String ackDate;

    //@GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true,label = "余额理财产品代码", field = "bala_code")
    @GraphQLField(label = "产品代码",field = "bala_code")
    private String balaCode;

    @GraphQLField(label = "产品名称",field = "bala_name")
    private String balaName;

    @GraphQLField(label = "转入总金额",field = "total_amt")
    private BigDecimal totalAmt;

    @GraphQLField(label = "自动转入总金额",field = "total_auto_amt")
    private BigDecimal totalAutoAmt;

    @GraphQLField(label = "转入合计",field = "tot_in_amt")
    private BigDecimal totInAmt;

    @GraphQLField(label = "转出总金额",field = "total_out_amt")
    private BigDecimal totalOutAmt;

    @GraphQLField(label = "普赎总金额",field = "total_redeem_amt")
    private BigDecimal totalRedeemAmt;

    @GraphQLField(label = "转出合计",field = "tot_out_amt")
    private BigDecimal totOutAmt;

}
