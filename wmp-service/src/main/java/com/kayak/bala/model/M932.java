package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m932Service")
@Data
public class M932 implements Cloneable{

   /** @GraphQLField(kkhtml = "KFieldSelect",
            kkhtmlExt="{\"data-action\":\"M001.findArea\" ,\"data-value-field\":\"orgno\",\"data-display-field\":\"orgname\",\"dataMultiple\":\"true\"}",
            label = "所属区域",field = "belong_area",kkhtmlDefault = true)*/
	@GraphQLField(label = "所属区域",field = "belong_area")
	private String belongArea;

    @GraphQLField(label = "所属机构",field = "belong_orgno")
    private String belongOrgno;

    @GraphQLField(label = "签约客户数",field = "qy_sign_status")
    private Long qySignStatus;

    @GraphQLField(label = "自动转入签约客户数",field = "qy_auto_status")
    private Long qyAutoStatus;

    @GraphQLField(label = "解约客户数",field = "jy_sign_status")
    private Long jySignStatus;

    @GraphQLField(label = "自动转入解约客户数",field = "jy_auto_status")
    private Long jyAutoStatus;


	/**
	 * 签约日期
	 */
	@GraphQLField(label = "签约日期", field = "sign_date")
	private String signDate;

	/**
	 * 解约日期
	 */
	@GraphQLField( label = "解约日期", field = "cancel_date")
	private String cancelDate;


	/**
	 * 自动转入签约日期
	 */
	@GraphQLField(label = "自动转入签约日期", field = "auto_sign_date")
	private String autoSignDate;


	/**
	 * 自动转入解约日期
	 */
	@GraphQLField(label = "自动转入解约日期", field = "auto_cancel_date")
	private String autoCancelDate;


	@GraphQLField(kkhtml = "KFieldDate",label = "开始日期", kkhtmlDefault = true,
			field = "start_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'endDate',\"data-allowblank\":false}")
	private String startDate;
	@GraphQLField(label = "结束日期", field = "end_date")
	private String endDate;


	@GraphQLField(label = "签约卡开户区域",field = "branch_code")
	private String branchCode;

	@GraphQLField(label = "签约卡开户区域名称",field = "branch_name")
	private String branchName;

    public M917 clone() throws CloneNotSupportedException{
        return (M917) super.clone();
    }

	public String getBelongArea() {
		return belongArea;
	}

	public void setBelongArea(String belongArea) {
		this.belongArea = belongArea;
	}

	public String getBelongOrgno() {
		return belongOrgno;
	}

	public void setBelongOrgno(String belongOrgno) {
		this.belongOrgno = belongOrgno;
	}

	public Long getQySignStatus() {
		return qySignStatus;
	}

	public void setQySignStatus(Long qySignStatus) {
		this.qySignStatus = qySignStatus;
	}

	public Long getQyAutoStatus() {
		return qyAutoStatus;
	}

	public void setQyAutoStatus(Long qyAutoStatus) {
		this.qyAutoStatus = qyAutoStatus;
	}

	public Long getJySignStatus() {
		return jySignStatus;
	}

	public void setJySignStatus(Long jySignStatus) {
		this.jySignStatus = jySignStatus;
	}

	public Long getJyAutoStatus() {
		return jyAutoStatus;
	}

	public void setJyAutoStatus(Long jyAutoStatus) {
		this.jyAutoStatus = jyAutoStatus;
	}

	public String getSignDate() {
		return signDate;
	}

	public void setSignDate(String signDate) {
		this.signDate = signDate;
	}


	public String getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(String cancelDate) {
		this.cancelDate = cancelDate;
	}


	public String getAutoSignDate() {
		return autoSignDate;
	}

	public void setAutoSignDate(String autoSignDate) {
		this.autoSignDate = autoSignDate;
	}


	public String getAutoCancelDate() {
		return autoCancelDate;
	}

	public void setAutoCancelDate(String autoCancelDate) {
		this.autoCancelDate = autoCancelDate;
	}


	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
}