package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;

@GraphQLModel(fetcher = "m924Service",firstLineNotDesensitized = true)
public class M924 implements Cloneable{

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " cust_name like '%$U{custName}%'",field = "cust_name",kkhtmlDefault = true)
   private String custName;

    @GraphQLField(kkhtml="KFieldSelect", label = "证件类型", kkhtmlExt="{\"data-dict\": \"id_type\",\"ref\": \"idType\"}",
            kkhtmlDefault = true, sql = "cust.id_type = $S{idType}", field = "id_type")
    private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  field = "acct_no",kkhtmlDefault = true, desensitized = Blankcarddesensitized.class)
   private String acctNo;

  @GraphQLField(label = "TA账号",field = "ta_acct_no",sql = " t1.ta_acct_no= $S{taAcctNo} ")
   private String taAcctNo;

   @GraphQLField(label = "TA代码",field = "tano",sql = " t1.tano= $S{tano} ")
   private String tano;

    @GraphQLField(label = "TA名称" ,field = "taName")
    private String taName;

   @GraphQLField(label = "产品代码",field = "prod_code",sql = " t1.prod_code= $S{prodCode} ")
   private String prodCode;

   @GraphQLField(label = "产品名称",field = "prod_name")
   private String prodName;

   @GraphQLField(label = "持有份额",field = "vol")
   private String vol;

   @GraphQLField(label = "普赎冻结份额",field = "redeem_frozen_vol")
   private String redeemFrozenVol;

   @GraphQLField(label = "司法冻结份额",field = "justice_frozen_vol")
   private String justiceFrozenVol;

   @GraphQLField(label = "上一日收益",field = "last_day_income")
   private String lastDayIncome;

   @GraphQLField(label = "近一周收益",field = "last_week_income")
   private String lastWeekIncome;

   @GraphQLField(label = "近一月收益",field = "last_month_income")
   private String lastMonthIncome;

   @GraphQLField(label = "累计收收益",field = "total_income")
   private String totalIncome;

   @GraphQLField(label = "所属机构",field = "cust_no")
   private String custNo;

    @GraphQLField(label = "所属区域",field = "branch_name")
    private String branchName;

    @GraphQLField(label = "所属机构名称",field = "org_name")
    private String orgName;

    @GraphQLField(label = "交易机构",field = "TRANS_ORGNO")
    private String transOrgno;

    @GraphQLField(label = "总行",field = "BRANCH_CODE")
    private String branchCode;


    @GraphQLField(label = "分行",field = "SUB_BRANCH_CODE")
    private String subBranchCode;

    @GraphQLField(label = "支行",field = "BANK_CODE")
    private String bankCode;


    public String getTransOrgno() {
        return transOrgno;
    }

    public void setTransOrgno(String transOrgno) {
        this.transOrgno = transOrgno;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getSubBranchCode() {
        return subBranchCode;
    }

    public void setSubBranchCode(String subBranchCode) {
        this.subBranchCode = subBranchCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

   public M924 clone() throws CloneNotSupportedException{
      return (M924) super.clone();
   }

   public String getCustNo() {
      return custNo;
   }

   public void setCustNo(String custNo) {
      this.custNo = custNo;
   }

   public String getCustName() {
      return custName;
   }

   public void setCustName(String custName) {
      this.custName = custName;
   }

   public String getIdType() {
      return idType;
   }

   public void setIdType(String idType) {
      this.idType = idType;
   }

   public String getIdCode() {
      return idCode;
   }

   public void setIdCode(String idCode) {
      this.idCode = idCode;
   }

   public String getAcctNo() {
      return acctNo;
   }

   public void setAcctNo(String acctNo) {
      this.acctNo = acctNo;
   }

   public String getTaAcctNo() {
      return taAcctNo;
   }

   public void setTaAcctNo(String taAcctNo) {
      this.taAcctNo = taAcctNo;
   }

   public String getTano() {
      return tano;
   }

   public void setTano(String tano) {
      this.tano = tano;
   }

   public String getProdCode() {
      return prodCode;
   }

   public void setProdCode(String prodCode) {
      this.prodCode = prodCode;
   }

   public String getProdName() {
      return prodName;
   }

   public void setProdName(String prodName) {
      this.prodName = prodName;
   }

   public String getVol() {
      return vol;
   }

   public void setVol(String vol) {
      this.vol = vol;
   }

   public String getJusticeFrozenVol() {
      return justiceFrozenVol;
   }

   public void setJusticeFrozenVol(String justiceFrozenVol) {
      this.justiceFrozenVol = justiceFrozenVol;
   }

   public String getLastDayIncome() {
      return lastDayIncome;
   }

   public void setLastDayIncome(String lastDayIncome) {
      this.lastDayIncome = lastDayIncome;
   }

   public String getLastWeekIncome() {
      return lastWeekIncome;
   }

   public void setLastWeekIncome(String lastWeekIncome) {
      this.lastWeekIncome = lastWeekIncome;
   }

   public String getLastMonthIncome() {
      return lastMonthIncome;
   }

   public void setLastMonthIncome(String lastMonthIncome) {
      this.lastMonthIncome = lastMonthIncome;
   }

   public String getTotalIncome() {
      return totalIncome;
   }

   public void setTotalIncome(String totalIncome) {
      this.totalIncome = totalIncome;
   }



   public String getRedeemFrozenVol() {
      return redeemFrozenVol;
   }

   public void setRedeemFrozenVol(String redeemFrozenVol) {
      this.redeemFrozenVol = redeemFrozenVol;
   }

    public String getTaName() {
        return taName;
    }

    public void setTaName(String taName) {
        this.taName = taName;
    }
}