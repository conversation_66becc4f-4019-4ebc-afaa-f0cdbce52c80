package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m906Service", table = "bala_batch_error")
@Data
public class M906 {
   @GraphQLField(label = "选择数组")
   private String chooseData;
   @GraphQLField(label = "差错流水号",field = "error_serno")
   private String errorSerno;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "理财交易流水号",  sql = " trans_serno like '%$U{transSerno}%'",field = "trans_serno",kkhtmlDefault = true)
   private String transSerno;

   @GraphQLField(label = "客户号",field = "cust_no")
   private String custNo;

   @GraphQLField(label = "交易账号",field = "trans_no")
   private String transNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true)
   private String acctNo;

   @GraphQLField(label = "系统编号",field = "system_no")
   private String systemNo;

   @GraphQLField(label = "TA代码",field = "tano")
   private String tano;

   @GraphQLField(label = "产品代码",field = "prod_code")
   private String prodCode;

   @GraphQLField(label = "产品名称",field = "prod_name")
   private String prodName;



   /**
    * 开始日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "退款日期", sql = " t.refund_date >= $S{refundDate}", field = "refund_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'refundDateEnd'}")
   private String refundDate;

   /**
    * 开始日期(前端查询时间范围使用)
    */
   @GraphQLField(label = "结束日期", sql = " t.refund_date <= $S{refundDateEnd}", field = "refund_date_end")
   private String refundDateEnd;

   @GraphQLField(label = "退款时间",field = "refund_time")
   private String refundTime;

   @GraphQLField(label = "差错金额",field = "error_amt")
   private String errorAmt;

   @GraphQLField(label = "差错份额",field = "error_vol")
   private String errorVol;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"bala_error_type\",\"data-dict-filter\":\"\"}",key = true ,label = "差错类型",  sql = " error_type = '$U{errorType}'",field = "error_type",kkhtmlDefault = true)
   private String errorType;

   @GraphQLField(label = "TA手续费户",field = "ta_fee_acct")
   private String taFeeAcct;

   @GraphQLField(label = "TA申认购账户",field = "ta_collect_acct")
   private String taCollectAcct;

   @GraphQLField(label = "TA申购收款账户",field = "ta_collect_recv_acct")
   private String taCollectRecvAcct;

   @GraphQLField(label = "TA赎回账户",field = "ta_redeem_acct")
   private String taRedeemAcct;

   @GraphQLField(label = "备注",field = "remark")
   private String remark;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "核心交易流水号",  sql = " host_trans_serno like '%$U{hostTransSerno}%'",field = "host_trans_serno",kkhtmlDefault = true)
   private String hostTransSerno;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "核心返款资金流水号",  sql = " host_capital_serno like '%$U{hostCapitalSerno}%'",field = "host_capital_serno",kkhtmlDefault = true)
   private String hostCapitalSerno;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"trans_status\",\"data-dict-filter\":\"F,S,U\"}",key = true ,label = "交易状态",  sql = " trans_status = '$U{transStatus}'",field = "trans_status",kkhtmlDefault = true)
   private String transStatus;

   @GraphQLField(label = "核心返回码",field = "host_rtn_code")
   private String hostRtnCode;

   @GraphQLField(label = "核心返回信息",field = "host_rtn_desc")
   private String hostRtnDesc;

   @GraphQLField(kkhtml = "KFieldSelect",kkhtmlExt="{\"data-dict\":\"trfr_error_status\",\"data-dict-filter\":\"\"}",key = true ,label = "差错处理状态",  sql = " error_status = '$U{errorStatus}'",field = "error_status",kkhtmlDefault = true)
   private String errorStatus;

   @GraphQLField(label = "批量资金流水号",field = "batch_serno")
   private String batchSerno;


   @GraphQLField(label = "更新时间",field = "upd_time")
   private String updTime;

   @GraphQLField(label = "更新时间",field = "error_deal_type")
   private String errorDealType;



   /**
    * 开始日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "创建时间", sql = "to_char(t.create_time,'yyyymmd') >= $S{crtTime}", field = "create_time", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'startEndDate'}")
   private String crtTime;

   /**
    * 开始日期(前端查询时间范围使用)
    */
   @GraphQLField(label = "结束日期", sql = "to_char(t.create_time,'yyyymmd') <= $S{startEndDate}", field = "create_time")
   private String startEndDate;




}