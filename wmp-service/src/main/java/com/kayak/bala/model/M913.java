package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m913Service")
@Data
public class M913 {
    @GraphQLField(label = "统计日期", sql = "t.stat_date = $S{statDate}" ,field = "stat_date",kkhtmlDefault = true)
    private String statDate;

    @GraphQLField(label = "TA代码",  sql = " t.tano = $S{tano}",field = "tano",kkhtmlDefault = true)
    private String tano;

	@GraphQLField(label = "TA名称" ,field = "taName")
	private String taName;

    @GraphQLField(kkhtmlDefault = true,
            label = "产品代码", sql = "t.prod_code = $S{prodCode}" ,field = "prod_code")
    private String prodCode;

    @GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true,
            label = "产品名称", sql = "t.prod_name like '%$U{prodName}%'", field = "prod_name")
    private String prodName;

    @GraphQLField(label = "存量余额", field = "prod_balance")
    private BigDecimal prodBalance;

    @GraphQLField(label = "持有客户数", field = "cust_count")
    private Long custCount;

	public String getStatDate() {
		return statDate;
	}

	public void setStatDate(String statDate) {
		this.statDate = statDate;
	}

	public String getTano() {
		return tano;
	}

	public void setTano(String tano) {
		this.tano = tano;
	}

	public String getProdCode() {
		return prodCode;
	}

	public void setProdCode(String prodCode) {
		this.prodCode = prodCode;
	}

	public String getProdName() {
		return prodName;
	}

	public void setProdName(String prodName) {
		this.prodName = prodName;
	}

	public BigDecimal getProdBalance() {
		return prodBalance;
	}

	public void setProdBalance(BigDecimal prodBalance) {
		this.prodBalance = prodBalance;
	}

	public Long getCustCount() {
		return custCount;
	}

	public void setCustCount(Long custCount) {
		this.custCount = custCount;
	}
    
}
