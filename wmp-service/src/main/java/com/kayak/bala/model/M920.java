package com.kayak.bala.model;

import com.kayak.core.desensitized.BaseDesensitized;
import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m920Service", table = "bala_cust_acct_req",firstLineNotDesensitized = true)
@Data
public class M920 {

   @GraphQLField(label = "流水号",field = "app_serno")
   private String appSerno;


   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " cust_name = $S{custName} ",field = "cust_name",kkhtmlDefault = true)
   private String custName;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = $S{idType} ",field = "id_type",kkhtmlDefault = true)
   private String idType;


   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code = $S{idCode} ",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(label = "交易账号",field = "trans_acct_no")
   private String transAcctNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;

   @GraphQLField(label = "原银行账号",field = "ori_acct_no", desensitized = Blankcarddesensitized.class)
   private String oriAcctNo;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"busi_code\",\"data-dict-filter\":\"001,002,003,004,005,008,009\"}",key = true ,label = "交易类型",  sql = " busi_code = '$U{busiCode}'",field = "busi_code",kkhtmlDefault = true)
   private String busiCode;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"trans_status\",\"data-dict-filter\":\"0,1,3,5\"}",key = true ,label = "交易状态",  sql = " trans_status = '$U{transStatus}'",field = "trans_status",kkhtmlDefault = true)
   private String transStatus;

   @GraphQLField(label = "ta代码",field = "tano")
   private String tano;

   @GraphQLField(label = "ta名称",field = "ta_name")
   private String taName;


   @GraphQLField(label = "交易时间",field = "channel_time")
   private String channelTime;

   @GraphQLField(label = "返回码",field = "rtn_code")
   private String rtnCode;

   @GraphQLField(label = "返回信息",field = "rtn_desc")
   private String rtnDesc;

   /**
    * 开始日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "查询时间", sql = "channel_date >= $S{channelDate}", field = "channel_date",
           kkhtmlExt = "{'data-type':'daterange',endDateFeild:'channelEndDate',\"data-allowblank\":false}")
   private String channelDate;

   /**
    * 开始日期(前端查询时间范围使用)
    */
   @GraphQLField(label = "结束日期", sql = " channel_date <= $S{channelEndDate}", field = "channel_end_date")
   private String channelEndDate;


}