package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@GraphQLModel(fetcher = "m935Service",firstLineNotDesensitized = true)
public class M935 implements Cloneable{


    @GraphQLField(label = "聚合交易流水号",field = "TRANS_SERNO")
    private BigDecimal transSerno;

    @GraphQLField(label = "申请单编号",field = "APP_SERNO")
    private BigDecimal appSerno;

    @GraphQLField(label = "交易码",field = "TRANS_CODE")
    private BigDecimal transCode;

    @GraphQLField(kkhtml = "KFieldText",sql = "trans_name like '%$U{transName}%'",label = "交易名称",field = "TRANS_NAME",kkhtmlDefault = true)
    private BigDecimal transName;

    @GraphQLField(label = "重发URL",field = "URL")
    private BigDecimal url;

    @GraphQLField(kkhtml = "KFieldSelect",kkhtmlDefault = true,label = "状态", field = "STATUS",kkhtmlExt="{\"data-dict\":\"aggregation_status\"}")
    private BigDecimal status;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "创建日期", sql = "CRT_DATE >= $S{crtDate}", field = "CRT_DATE", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'crtEndDate'}")
    private String crtDate;

    @GraphQLField(label = "结束日期", sql = "CRT_DATE <= $S{crtEndDate}", field = "CRT_DATE")
    private String crtEndDate;

    @GraphQLField(label = "创建时间",field = "CRT_TIME")
    private BigDecimal creTime;

    @GraphQLField(label = "更新日期",field = "UPD_DATE")
    private BigDecimal updDate;

    @GraphQLField(label = "更新时间",field = "UPD_TIME")
    private BigDecimal updTime;

    @GraphQLField(label = "请求参数",field = "REQUEST_BODY")
    private BigDecimal requestBody;




    
}