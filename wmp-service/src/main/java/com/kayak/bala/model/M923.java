package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;

@GraphQLModel(fetcher = "m923Service",firstLineNotDesensitized = true)
public class M923 implements Cloneable{

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " cust_name like '%$U{custName}%'",field = "cust_name",kkhtmlDefault = true)
   private String custName;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",label = "证件类型",  sql = " id_type = $S{idType} ",field = "id_type",kkhtmlDefault = true)
   private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
   private String acctNo;

   @GraphQLField(label = "交易账号",field = "trans_acct_no")
   private String transAcctNo;

   @GraphQLField(label = "持有份额",field = "vol")
   private String vol;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "最低份额", field = "min_vol",kkhtmlDefault = true)
   private String minVol;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "最高份额", field = "max_vol",kkhtmlDefault = true)
   private String maxVol;

   @GraphQLField(label = "普赎冻结份额",field = "redeem_frozen_vol")
   private String redeemFrozenVol;

   @GraphQLField(label = "司法冻结份额",field = "justice_frozen_vol")
   private String justiceFrozenVol;

   @GraphQLField(label = "上一日收益",field = "last_day_income")
   private String lastDayIncome;

   @GraphQLField(label = "近一周收益",field = "last_week_income")
   private String lastWeekIncome;

   @GraphQLField(label = "近一月收益",field = "last_month_income")
   private String lastMonthIncome;

   @GraphQLField(label = "累计收收益",field = "total_income")
   private String totalIncome;

   @GraphQLField(label = "交易机构",field = "TRANS_ORGNO")
   private String transOrgno;

   @GraphQLField(label = "分行",field = "BRANCH_CODE")
   private String branchCode;


   @GraphQLField(label = "支行",field = "SUB_BRANCH_CODE")
   private String subBranchCode;

   @GraphQLField(label = "总行",field = "BANK_CODE")
   private String bankCode;

   @GraphQLField(label = "客户号",field = "cust_no",sql = " t.cust_no = $S{custNo}")
   private String custNo;

   /**
    * 开始日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "创建时间", sql = "create_time >= $S{crtTime}", field = "create_time", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'startEndDate'}")
   private String crtTime;

   /**
    * 开始日期(前端查询时间范围使用)
    */
   @GraphQLField(label = "结束日期", sql = "create_time <= $S{startEndDate}", field = "create_time")
   private String startEndDate;

   @GraphQLField(label = "所属区域",field = "branch_name")
   private String branchName;

   @GraphQLField(label = "所属机构名称",field = "org_name")
   private String orgName;

   public String getMinVol() {
      return minVol;
   }

   public void setMinVol(String minVol) {
      this.minVol = minVol;
   }

   public String getMaxVol() {
      return maxVol;
   }

   public void setMaxVol(String maxVol) {
      this.maxVol = maxVol;
   }

   public String getCustName() {
      return custName;
   }

   public void setCustName(String custName) {
      this.custName = custName;
   }

   public String getIdType() {
      return idType;
   }

   public void setIdType(String idType) {
      this.idType = idType;
   }

   public String getIdCode() {
      return idCode;
   }

   public void setIdCode(String idCode) {
      this.idCode = idCode;
   }

   public String getAcctNo() {
      return acctNo;
   }

   public void setAcctNo(String acctNo) {
      this.acctNo = acctNo;
   }

   public String getTransAcctNo() {
      return transAcctNo;
   }

   public void setTransAcctNo(String transAcctNo) {
      this.transAcctNo = transAcctNo;
   }

   public String getVol() {
      return vol;
   }

   public void setVol(String vol) {
      this.vol = vol;
   }

   public String getRedeemFrozenVol() {
      return redeemFrozenVol;
   }

   public void setRedeemFrozenVol(String redeemFrozenVol) {
      this.redeemFrozenVol = redeemFrozenVol;
   }

   public String getJusticeFrozenVol() {
      return justiceFrozenVol;
   }

   public void setJusticeFrozenVol(String justiceFrozenVol) {
      this.justiceFrozenVol = justiceFrozenVol;
   }

   public String getLastDayIncome() {
      return lastDayIncome;
   }

   public void setLastDayIncome(String lastDayIncome) {
      this.lastDayIncome = lastDayIncome;
   }

   public String getLastWeekIncome() {
      return lastWeekIncome;
   }

   public void setLastWeekIncome(String lastWeekIncome) {
      this.lastWeekIncome = lastWeekIncome;
   }

   public String getLastMonthIncome() {
      return lastMonthIncome;
   }

   public void setLastMonthIncome(String lastMonthIncome) {
      this.lastMonthIncome = lastMonthIncome;
   }

   public String getTotalIncome() {
      return totalIncome;
   }

   public void setTotalIncome(String totalIncome) {
      this.totalIncome = totalIncome;
   }

   public String getTransOrgno() {
      return transOrgno;
   }

   public void setTransOrgno(String transOrgno) {
      this.transOrgno = transOrgno;
   }

   public String getBranchCode() {
      return branchCode;
   }

   public void setBranchCode(String branchCode) {
      this.branchCode = branchCode;
   }

   public String getSubBranchCode() {
      return subBranchCode;
   }

   public void setSubBranchCode(String subBranchCode) {
      this.subBranchCode = subBranchCode;
   }

   public String getBankCode() {
      return bankCode;
   }

   public void setBankCode(String bankCode) {
      this.bankCode = bankCode;
   }

   public String getCustNo() {
      return custNo;
   }

   public void setCustNo(String custNo) {
      this.custNo = custNo;
   }

   public String getCrtTime() {
      return crtTime;
   }

   public void setCrtTime(String crtTime) {
      this.crtTime = crtTime;
   }

   public String getStartEndDate() {
      return startEndDate;
   }

   public void setStartEndDate(String startEndDate) {
      this.startEndDate = startEndDate;
   }

   public String getBranchName() {
      return branchName;
   }

   public void setBranchName(String branchName) {
      this.branchName = branchName;
   }

   public String getOrgName() {
      return orgName;
   }

   public void setOrgName(String orgName) {
      this.orgName = orgName;
   }
}