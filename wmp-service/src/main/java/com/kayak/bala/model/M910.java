package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m910Service", table = "bala_transfer_ta")
@Data
public class M910 {
    @GraphQLField(label = "系统代码",field = "system_no")
    private String systemNo;

    @GraphQLField(label = "TA代码",  sql = " tano like '%$U{tano}%'",field = "tano",kkhtmlDefault = true)
    private String tano;

    @GraphQLField(label = "TA名称" ,field = "taName")
    private String taName;

    @GraphQLField(label = "余额理财代码", sql = "bala_code = $S{balaCode}" ,field = "bala_code")
    private String balaCode;

    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate",
            label = "确认日期", field = "ack_date", sql = "ack_date >= $S{ackDate}",
            kkhtmlExt = "{'data-type':'daterange',endDateFeild:'ackEndDate'}")
    private String ackDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", field = "sign_date", sql = "ack_date <= $S{ackEndDate}")
    private String ackEndDate;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "划款状态",field = "transfer_status",sql = "transfer_status = $S{transferStatus}" ,
            kkhtmlExt="{\"data-dict\": \"amt_transfer_status\"}")
    private String transferStatus;

    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
            label = "划款日期", sql = "transfer_date = $S{transferDate}" ,field = "transfer_date")
    private String transferDate;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "交易指令",field = "trans_order",sql = "trans_order = $S{transOrder}" ,
            kkhtmlExt="{\"data-dict\": \"trans_order\"}")
    private String transOrder;

    @GraphQLField(label = "借方账号",field = "out_acct_no")
    private String outAcctNo;

    @GraphQLField(label = "贷方账号",field = "in_acct_no")
    private String inAcctNo;

    @GraphQLField(label = "总金额",field = "total_amt")
    private BigDecimal totalAmt;

    @GraphQLField(label = "总手续费",field = "fee_amt")
    private BigDecimal feeAmt;

    @GraphQLField(label = "应划金额",field = "actual_amt")
    private BigDecimal actualAmt;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "支付状态",field = "payment_status",sql = "payment_status = $S{paymentStatus}" ,
            kkhtmlExt="{\"data-dict\": \"payment_status\"}")
    private String paymentStatus;

    @GraphQLField(label = "支付流水号",field = "payment_serno")
    private String paymentSerno;

    @GraphQLField(label = "备注",field = "remark")
    private String remark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
