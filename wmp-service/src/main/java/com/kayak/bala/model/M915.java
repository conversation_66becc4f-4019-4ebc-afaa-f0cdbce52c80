package com.kayak.bala.model;

import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.math.BigDecimal;

@GraphQLModel(fetcher = "m915Service", table = "bala_transfer_org_list")
@Data
public class M915 {
    @GraphQLField(label = "TA代码",  sql = " tano like '%$U{tano}%'",field = "tano",kkhtmlDefault = true)
    private String tano;

    @GraphQLField(label = "TA名称" ,field = "taName")
    private String taName;

    @GraphQLField(label = "产品代码", sql = " prod_code = $S{prodCode}" ,field = "prod_code",kkhtmlDefault = true)
    private String prodCode;

    @GraphQLField(label = "产品名称",field = "prod_name")
    private String prodName;

    @GraphQLField(label = "机构代码", field = "trans_Orgno")
    private String transOrgno;

    @GraphQLField(label = "机构名称", field = "orgname")
    private String orgname;

    @GraphQLField(label = "所属区域", field = "branch_code")
    private String branchCode;

    @GraphQLField(kkhtml = "KFieldDate",label = "开始日期", kkhtmlDefault = true,sql="start_date >= $S{startDate}",
            field = "start_date", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'endDate',\"data-allowblank\":false}")
    private String startDate;
    @GraphQLField(kkhtmlDefault = true,label = "结束日期", sql="end_date <= $S{endDate}",field = "end_date")
    private String endDate;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlDefault = true,
            label = "划款状态",field = "transfer_status",sql = "transfer_status = $S{transferStatus}" ,
            kkhtmlExt="{\"data-dict\": \"transfer_status\"}")
    private String transferStatus;

    @GraphQLField(kkhtml = "KFieldDate", kkhtmlDefault = true,
            label = "划拨日期", sql = "transfer_date = $S{transferDate}" ,field = "transfer_date")
    private String transferDate;

    @GraphQLField(label = "中收金额",field = "earning_amt")
    private BigDecimal earningAmt;

    @GraphQLField(label = "日均存量",field = "daliy_vol")
    private BigDecimal daliyVol;

    @GraphQLField(kkhtml = "KFieldText", kkhtmlDefault = true,sql = "transfer_serno = $S{transferSerno}",
            label = "划拨序号",field = "transfer_serno")
    private String transferSerno;

    @GraphQLField(label = "系统编号",field = "system_no")
    private String systemNo;

    @GraphQLField(label = "创建时间", field = "create_time")
    private String createTime;

    @GraphQLField(label = "更新时间", field = "update_time")
    private String updateTime;

    public M915 clone() throws CloneNotSupportedException{
        return (M915) super.clone();
    }

    public String getTransOrgno() {
        return transOrgno;
    }

    public void setTransOrgno(String transOrgno) {
        this.transOrgno = transOrgno;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }
}
