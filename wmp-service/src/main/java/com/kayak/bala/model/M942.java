package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m942Service",firstLineNotDesensitized = true)
public class M942 implements Cloneable{

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " cust_name like '%$U{custName}%'",field = "cust_name",kkhtmlDefault = true)
    private String custName;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " id_type = '$U{idType}'",field = "id_type",kkhtmlDefault = true)
    private String idType;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " id_code like '%$U{idCode}%'",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
    private String idCode;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true,desensitized = Blankcarddesensitized.class)
    private String acctNo;

    /**
     * 开始日期
     */
    @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "t1.TRANS_DATE >= $S{transDate}", field = "TRANS_DATE", kkhtmlExt = "{'data-type':'daterange',endDateFeild:'transEndDate'}")
    private String transDate;

    /**
     * 开始日期(前端查询时间范围使用)
     */
    @GraphQLField(label = "结束日期", sql = "t1.TRANS_DATE <= $S{transEndDate}", field = "trans_end_date")
    private String transEndDate;

    @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户号",field = "cust_no")
    private String custNo;

    @GraphQLField(label = "交易金额",field = "APP_AMT")
    private String appAmt;

    @GraphQLField(label = "交易类型",field = "trans_TYPE")
    private String transType;

    @GraphQLField(label = "确认成功金额",field = "cfm_success_amt")
    private String cfmSuccessAmt;

    @GraphQLField(label = "确认失败金额",field = "cfm_fail_amt")
    private String cfmFailAmt;

    @GraphQLField(label = "交易状态",field = "trans_status")
    private String transStatus;

    @GraphQLField(label = "备注",field = "remarks")
    private String remarks;

    @GraphQLField(label = "核心流水号",field = "reference")
    private String reference;

    @GraphQLField(label = "理财流水号",field = "channel_seq_no")
    private String channelSeqNo;

    @GraphQLField(label = "查询证件号码",field = "query_id_code")
    private String queryIdCode;




   public M942 clone() throws CloneNotSupportedException{
      return (M942) super.clone();
   }


}