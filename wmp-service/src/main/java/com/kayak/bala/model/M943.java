package com.kayak.bala.model;

import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;
import sun.nio.cs.ext.DoubleByte;

@Data
@GraphQLModel(fetcher = "m943Service")
public class M943 implements Cloneable{

    @GraphQLField(label = "理财交易流水号",field = "TRANS_SERNO")
    private String transSerno;

    @GraphQLField(label = "交易类型",field = "busi_type")
    private String busiType;

    @GraphQLField(label = "解冻余额",field = "unfrz_bala")
    private String unfrzBala;

    @GraphQLField(label = "扣减余额",field = "subtract_bala")
    private String subtractBala;

    @GraphQLField(label = "返款金额",field = "payment_amt")
    private String paymentAmt;

    @GraphQLField(label = "文件批次号",field = "file_no")
    private String fileNo;

    @GraphQLField(label = "交易日期",field = " busi_date")
    private String busiDate;

    @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"check_flag\",\"data-dict-filter\":\"\"}",key = true ,label = "校验状态",  sql = " check_flag = $S{checkFlag} ",field = "check_flag",kkhtmlDefault = true)
    private String checkFlag;

    @GraphQLField(label = "校验",field = "CHECK_OPRATER")
    private String checkOprater;

    @GraphQLField(label = "校验日期",field = "check_date")
    private String checkDate;

    @GraphQLField(label = "校验时间",field = "check_time")
    private String checkTime;



}