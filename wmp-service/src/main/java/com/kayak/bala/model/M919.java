package com.kayak.bala.model;

import com.kayak.core.desensitized.Blankcarddesensitized;
import com.kayak.core.desensitized.IdCardDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@GraphQLModel(fetcher = "m919Service", table = "bala_cust_sign_trans_log",firstLineNotDesensitized = true)
@Data
public class M919 {

   @GraphQLField(label = "交易流水号",field = "trans_serno")
   private String transSerno;

   @GraphQLField(label = "渠道流水号",field = "channel_serno")
   private String channelSerno;

   @GraphQLField(label = "渠道标识",field = "channel_flag")
   private String channelFlag;

   @GraphQLField(label = "渠道日期",field = "channel_date")
   private String channelDate;

   @GraphQLField(label = "渠道时间",field = "channel_time")
   private String channelTime;

   @GraphQLField(label = "客户号",field = "cust_no")
   private String custNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "客户名称",  sql = " t.cust_name = $S{custName} ",field = "cust_name",kkhtmlDefault = true)
   private String custName;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"id_type\",\"data-dict-filter\":\"\"}",key = true ,label = "证件类型",  sql = " t.id_type = $S{idType} ",field = "id_type",kkhtmlDefault = true)
   private String idType;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "证件号码",  sql = " t.id_code = $S{idCode} ",field = "id_code",kkhtmlDefault = true, desensitized = IdCardDesensitized.class)
   private String idCode;

   @GraphQLField(label = "交易账号",field = "trans_acct_no")
   private String transAcctNo;

   @GraphQLField(label = "原银行账号",field = "ori_acct_no", desensitized = Blankcarddesensitized.class)
   private String oriAcctNo;

   @GraphQLField(kkhtml = "KFieldText",key = true ,label = "银行账号",  sql = " acct_no like '%$U{acctNo}%'",field = "acct_no",kkhtmlDefault = true, desensitized = Blankcarddesensitized.class)
   private String acctNo;

   @GraphQLField(label = "推荐人",field = "referrer")
   private String referrer;

   @GraphQLField(label = "所属区域",field = "belong_area")
   private String belongArea;

   @GraphQLField(label = "所属机构",field = "belong_orgno")
   private String belongOrgno;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"bala_acct_trans_type\"}",key = true ,label = "交易类型",  sql = " t.trans_type = '$U{transType}'",field = "trans_type",kkhtmlDefault = true)
   private String transType;

   @GraphQLField(kkhtml = "KFieldSelect", kkhtmlExt="{\"data-dict\":\"bala_acct_trans_status\"}",key = true ,label = "交易状态",  sql = " t.trans_status = '$U{transStatus}'",field = "trans_status",kkhtmlDefault = true)
   private String transStatus;


   /**
    * 开始日期
    */
   @GraphQLField(kkhtmlDefault=true, kkhtml = "KFieldDate", label = "交易日期", sql = "trans_date >= $S{transDate}", field = "trans_date",
           kkhtmlExt = "{'data-type':'daterange',endDateFeild:'transEndDate',\"data-allowblank\":false}")
   private String transDate;

   /**
    * 开始日期(前端查询时间范围使用)
    */
   @GraphQLField(label = "结束日期", sql = "trans_date <= $S{transEndDate}", field = "trans_date")
   private String transEndDate;

   @GraphQLField(label = "交易时间",field = "trans_time")
   private String transTime;

   @GraphQLField(label = "自动转入金额类型",field = "AUTO_TYPE")
   private String autoType;

   @GraphQLField(label = "设置金额",field = "AUTO_AMT")
   private String autoAmt;

   @GraphQLField(label = "原自动转入金额类型",field = "ORI_AUTO_TYPE")
   private String oriAutoType;

   @GraphQLField(label = "原设置金额",field = "ORI_AUTO_AMT")
   private String oriAutoAmt;

   @GraphQLField(label = "核心返回码",field = "HOST_RTN_CODE")
   private String hostRtnCode;

   @GraphQLField(label = "核心返回信息",field = "HOST_RTN_DESC")
   private String hostRtnDesc;

}