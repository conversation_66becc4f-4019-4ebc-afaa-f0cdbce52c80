package com.kayak.bala.model;

import com.alibaba.fastjson.JSONArray;
import com.kayak.common.base.BaseModel;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@GraphQLModel(fetcher = "m901Service", table = "bala_prod_info")
@Data
public class M901 extends BaseModel {

    @GraphQLField(label = "产品代码", kkhtmlDefault = true, sql = "bala_code = $S{balaCode} ", field = "bala_code")
    private String balaCode;

    @GraphQLField( label = "产品名称", kkhtmlDefault = true, sql = "bala_name = $S{balaName} ", field = "bala_name")
    private String balaName;

    @GraphQLField(field = "trans_channel")
    private String transChannel;

    private JSONArray transChannelList;

    @GraphQLField(field = "trans_org")
    private String transOrg;

    private JSONArray transOrgList;

    @GraphQLField(field = "trans_name")
    private String transName;

    @GraphQLField(field = "prod_risk_level")
    private String prodRiskLevel;

    @GraphQLField(field = "workday_pgm")
    private String workdayPgm;

    @GraphQLField(field = "pay_acct")
    private String payAcct;

    @GraphQLField(field = "collect_acct")
    private String collectAcct;

    @GraphQLField(field = "income_acct")
    private String incomeAcct;

    @GraphQLField(field = "advance_acct")
    private String advanceAcct;

    @GraphQLField(field = "pay_acct_name")
    private String payAcctName;

    @GraphQLField(field = "collect_acct_name")
    private String collectAcctName;

    @GraphQLField(field = "income_acct_name")
    private String incomeAcctName;

    @GraphQLField(field = "advance_acct_name")
    private String advanceAcctName;

    @GraphQLField()
    private String acctType;

    @GraphQLField(field = "acct_name")
    private String acctName;

    @GraphQLField(field = "acct_serno")
    private String acctSerno;

    public JSONArray getTransChannelList() {
        return transChannelList;
    }

    public void setTransChannelList(JSONArray transChannelList) {
        this.transChannelList = transChannelList;
    }

    public JSONArray getTransOrgList() {
        return transOrgList;
    }

    public void setTransOrgList(JSONArray transOrgList) {
        this.transOrgList = transOrgList;
    }
}
