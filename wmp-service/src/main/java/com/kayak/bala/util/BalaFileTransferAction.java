package com.kayak.bala.util;

import com.alibaba.excel.util.StringUtils;
import com.kayak.bala.model.M944;
import com.kayak.core.action.BaseController;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.SysBeans;
import com.kayak.core.util.Tools;
import com.kayak.graphql.annotation.GraphQLModel;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 产品文档文件传输(上传/下载)
 * <AUTHOR>
 * @Date 2020/12/22
 */
@Scope("prototype")
@Controller
public class BalaFileTransferAction extends BaseController {

    @Value("${file.tem.path:null}")
    private String temPath;

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @RequestMapping("bala/file/upload.json")
    @ResponseBody
    public String uploadFile(@RequestParam(value = "file", required = false)MultipartFile file, HttpServletResponse response ) throws Exception {

        Map<String, Object> params = RequestSupport.getParameters();
        String modelClassName = Tools.obj2Str(params.get("modelClassName"));
        String modelPackageName = Tools.obj2Str(params.get("modelPackageName"));

        String modelPackageAndClassName = modelPackageName + "." + modelClassName;
        Class<?> modelClass = getClass(modelPackageAndClassName);
        if (modelClass == null) {
            String errMsg = "实体类["+ modelPackageAndClassName +"]获取失败";
            log.error(errMsg);
            return RequestSupport.updateReturnJson(false, errMsg, null).toString();
        }
        Object serverBean = this.getServerBean(modelClass);
        if (serverBean == null) {
            log.error("获取服务对象失败，实体类["+ modelPackageAndClassName +"]fetcher配置对应实例不存在");
            return RequestSupport.updateReturnJson(false, "无法获取服务", null).toString();
        }


        SqlParam sqlParam = new FetcherData<>(params , modelClass);

        Method findMethod = null;
        Method insertMethod = null;
        Method getVersionMethod = null;

        Class<?> serverClass = serverBean.getClass();

        try {
            getVersionMethod = serverClass.getMethod("getVersion", SqlParam.class);
            String version = (String) getVersionMethod.invoke(serverBean, sqlParam);	//将产品文档信息落地
            params.put("docVersion", version);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }

        try {
            findMethod = serverClass.getMethod("find" + modelClassName, SqlParam.class);
            params.put("docStatus", "0");
            sqlParam = new FetcherData<>(params , modelClass);

            SqlResult<M944> sqlResult = (SqlResult<M944>) findMethod.invoke(serverBean, sqlParam);	//查询是否已有产品文档信息
            List<M944> list = sqlResult.getRows();
            if(list != null && list.size() > 0) {
                return RequestSupport.updateReturnJson(false,
                        "该产品已录入过该类型产品文档信息，如需重新上传文档请在查询列表中上传新文件", null).toString();
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }
        try {
            insertMethod = serverClass.getMethod("add" + modelClassName, SqlParam.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }

        String originalFilename = file.getOriginalFilename();
        String suffix = file.getOriginalFilename().substring(originalFilename.lastIndexOf("."),originalFilename.length());
        //根据参数和命名规则生成名字
        String fileName = this.getUploadFileAbsoluteName(params,suffix);
        //根据系统获取文件存储路径
        String filePath = this.getUploadFileAbsolutePath();
        params.put("docPath", filePath);
        params.put("docName", fileName);
        sqlParam = new FetcherData<>(params , modelClass);
        try {
            insertMethod.invoke(serverBean, sqlParam);	//将产品文档信息落地
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }

        try {
            File path = new File(filePath);
            this.doReadyBeforeTransfer(path,fileName);	//创建目录或清理目录中已有的文件
            File uploadFile = new File(filePath + File.separator + fileName);
            file.transferTo(uploadFile);	//上传文件
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            try {
                SqlResult<M944> sqlResult = (SqlResult<M944>) findMethod.invoke(serverBean, sqlParam);	//查询产品文档信息是否已落地
                List<M944> list = sqlResult.getRows();
                if(list != null && list.size() > 0) {
                    Method deleteMethod = null;
                    deleteMethod = serverClass.getMethod("delete" + modelClassName, SqlParam.class);
                    int effect = (int) deleteMethod.invoke(serverBean, sqlParam);	//文档上传失败，删除该笔产品文档信息记录
                    if(effect != 1) {
                        return RequestSupport.updateReturnJson(false,
                                "产品文档信息已经录入成功, 但文档上传失败, 可手动重新上传" + fileName, null).toString();
                    }
                }
            } catch (Exception e1) {
                log.error(e.getMessage(), e1);
                return RequestSupport.updateReturnJson(false,
                        "产品文档信息已经录入成功, 但文档上传失败, 可手动重新上传" + fileName, null).toString();
            }
            return RequestSupport.updateReturnJson(false, "产品文档上传失败, 请稍后再试", null).toString();
        }

        return RequestSupport.updateReturnJson(true, "已成功上传" + fileName, null).toString();
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @RequestMapping("bala/file/reUpload.json")
    @ResponseBody
    public String reUploadFile(@RequestParam(value = "file", required = false)MultipartFile file, HttpServletResponse response ) throws Exception {

        Map<String, Object> params = RequestSupport.getParameters();
        String modelClassName = Tools.obj2Str(params.get("modelClassName"));
        String modelPackageName = Tools.obj2Str(params.get("modelPackageName"));

        String modelPackageAndClassName = modelPackageName + "." + modelClassName;
        Class<?> modelClass = getClass(modelPackageAndClassName);
        if (modelClass == null) {
            String errMsg = "实体类["+ modelPackageAndClassName +"]获取失败";
            log.error(errMsg);
            return RequestSupport.updateReturnJson(false, errMsg, null).toString();
        }
        Object serverBean = this.getServerBean(modelClass);
        if (serverBean == null) {
            log.error("获取服务对象失败，实体类["+ modelPackageAndClassName +"]fetcher配置对应实例不存在");
            return RequestSupport.updateReturnJson(false, "无法获取服务", null).toString();
        }

        SqlParam sqlParam = new FetcherData<>(RequestSupport.getParameters() , modelClass);

        Method findMethod = null;
        Method updateMethod = null;
        Class<?> serverClass = serverBean.getClass();
        try {
            findMethod = serverClass.getMethod("find" + modelClassName, SqlParam.class);
            SqlResult<M944> sqlResult = (SqlResult<M944>) findMethod.invoke(serverBean, sqlParam);	//查询是否已有产品文档信息
            List<M944> list = sqlResult.getRows();
            if(list == null || list.size() == 0) {
                return RequestSupport.updateReturnJson(false,
                        "该产品文档信息不存在, 可能已经被删除, 请刷新列表后再试", null).toString();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法重新上传产品文档信息, 请稍后再试", null).toString();
        }

        try {
            updateMethod = serverClass.getMethod("update" + modelClassName, SqlParam.class);
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法重新上传产品文档信息, 请稍后再试", null).toString();
        }

        String originalFilename = file.getOriginalFilename();
        String suffix = file.getOriginalFilename().substring(originalFilename.lastIndexOf("."),originalFilename.length());
        String fileName = this.getUploadFileAbsoluteName(params,suffix);
        String filePath = this.getUploadFileAbsolutePath();
        params.put("docPath", filePath);
        params.put("docName", fileName);
        sqlParam = new FetcherData<>(params , modelClass);
        try {
            updateMethod.invoke(serverBean, sqlParam);	//更新产品文档信息
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法重新上传产品文档信息, 请稍后再试", null).toString();
        }

        try {
            File path = new File(filePath);
            this.doReadyBeforeTransfer(path,fileName);	//创建目录或清理目录中已有的文件
            File reUploadFile = new File(filePath + File.separator + fileName);
            file.transferTo(reUploadFile);		//重新上传文件
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false,
                    "产品文档信息已经更新成功, 但文档上传失败, 请稍后再试", null).toString();
        }

        return RequestSupport.updateReturnJson(true, "已成功上传" + fileName, null).toString();
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @RequestMapping("bala/file/download.json")
    public String downdownloadTest(HttpServletResponse response) {
        Map<String, Object> params = RequestSupport.getParameters();
        String filePath = Tools.obj2Str(params.get("docPath"));
        String fileName = Tools.obj2Str(params.get("docName"));
        try(OutputStream os = response.getOutputStream();){
            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", "filename");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setHeader("filename", fileName);
            File file = new File(filePath + fileName);
            if(file.exists()) {
                os.write(FileUtils.readFileToByteArray(file));
                os.flush();
            } else {
                response.setHeader("err", "downloaderror");
                return RequestSupport.updateReturnJson(false, "产品文档下载失败, 请稍后再试", null).toString();
            }
        }catch (Exception e){
            log.error(e.getMessage());
            log.error(e.getMessage(),e);
            return RequestSupport.updateReturnJson(false, "产品文档下载失败, 请稍后再试", null).toString();
        }
        return RequestSupport.updateReturnJson(true, "下载成功", null).toString();
    }

    private Object getServerBean(Class<?> modelClass){
        // 获取操作对象实例
        GraphQLModel graphQLModel = modelClass.getAnnotation(GraphQLModel.class);
        String fetcher = graphQLModel.fetcher();
        return SysBeans.getBean(fetcher);
    }

    private Class<?> getClass(String modelClassName){
        if(StringUtils.isEmpty(modelClassName)){
            return null;
        }
        Class<?> modelClass = null;
        try {
            modelClass =  Class.forName(modelClassName);
        } catch (ClassNotFoundException e) {
            log.error(e.getMessage());
        }
        return modelClass;
    }

    private String getUploadFileAbsolutePath() {
        String osName = System.getProperty("os.name");
        return (osName.toLowerCase().startsWith("win") ? "C:/wmp/" : temPath);
    }

    private String getUploadFileAbsoluteName(Map<String,Object> prodDocumentInfo,String suffix) {
        String name =  prodDocumentInfo.get("tano") + "_" +
                prodDocumentInfo.get("prodCode")   + "_" +
                prodDocumentInfo.get("docType");
        return 	name + suffix;
    }

    private void doReadyBeforeTransfer(File path,String name) {
        if(!path.exists()) {
            path.mkdirs();
        } else {
            try {
                File[] childFiles = path.listFiles();
                if(childFiles != null && childFiles.length > 0) {
                    for(File childFile : childFiles) {
                        if (childFile.getName().equals(name)){
                            childFile.delete();
                        }
                    }
                }
            } catch(Throwable t) {
                log.error(t.getMessage(), t);
            }
        }
    }

}
