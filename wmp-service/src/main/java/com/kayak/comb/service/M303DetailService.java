package com.kayak.comb.service;

import com.kayak.aspect.annotations.APIDefine;
import com.kayak.comb.dao.M303DetailDao;
import com.kayak.comb.model.M303Detail;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "产品组合详情", model = M303Detail.class)
public class M303DetailService {

    @Autowired
    private M303DetailDao m303DetailDao;


    public SqlResult<M303Detail> queryAllCombProdDetail(SqlParam<M303Detail> params) throws Exception {
        params.setMakeSql(true);
        return m303DetailDao.queryAllCombProdDetail(params);
    }
    public SqlResult<M303Detail> queryCombPlanProd(SqlParam<M303Detail> params) throws Exception {
        return m303DetailDao.queryCombPlanProd(params);
    }


}
