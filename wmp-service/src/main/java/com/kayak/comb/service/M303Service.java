package com.kayak.comb.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.comb.dao.M303Dao;
import com.kayak.comb.model.M303;
import com.kayak.comb.model.M303ProdDetail;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.cust.model.M101;
import com.kayak.fund.model.M652;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@APIDefine(desc = "产品组合管理", model = M303.class)
public class M303Service {

    @Autowired
    private M303Dao m303Dao;

    @Autowired
    private M215Service m215Service;


    //界面初始化查询
    @API(desc = "查询产品组合信息", auth = APIAuth.YES)
    public SqlResult<M303> findPlanInfo(SqlParam<M303> params) throws Exception {
        params.setMakeSql(true);
        return m303Dao.findPlanInfos(params);
    }

    //新增产品组合信息，包含产品
    @API(desc = "新增活产品组合信息", auth = APIAuth.YES)
    public String insertPlanInfo(SqlParam<M303> params) throws Exception {
        Map<String,Object> map = new HashMap<>();
        map.put("planNo",params.getModel().getPlanNo());
        SqlParam<M303> queryParams = new FetcherData<>(map, M303.class);
        SqlResult<M303> m303SqlResult = findPlanInfo(queryParams);
        if(m303SqlResult.getRows() != null && m303SqlResult.getRows().size() > 0){
            return RequestSupport.updateReturnJson(false, "新增失败,梦想计划编号："+params.getModel().getPlanNo()+"已存在", null).toString();
        }

        m303Dao.insertPlanInfo(params);
        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }

    //删除组合计划，包括关联产品全部删除
    public int delete(SqlParam<M303> params) throws Exception {
        return m303Dao.delete(params);
    }

    //修改组合计划
    public int updateCombProdInfo(SqlParam<M303> params) throws Exception {
        m303Dao.updateCombProdInfo(params);
        return 1;
    }

    //通过系统编号和产品类型查询查询产品ta、产品代码、产品名称
    @API(desc = "新增活产品组合信息", auth = APIAuth.YES)
    public SqlResult<List<M303ProdDetail>> queryAllProd(SqlParam<M303> params) throws Exception {
        //TODO 后续这返回的数据应该是查询redis获取到的产品信息
        Map<String,Object> map = new HashMap<>();
        map.put("systemNo",params.getModel().getSystemNo());
        if(params.getModel().getSystemNo().equals("FUND")){
            map.put("fundType",params.getModel().getCombProdType());
        }else{
            map.put("prodType",params.getModel().getCombProdType());
        }
        SqlParam<M215> queryParams = new FetcherData<>(map, M215.class);
        List<Map<String, String>> m215List  = m215Service.getProdInfoList(queryParams);

        List<M303ProdDetail> list= new ArrayList<M303ProdDetail>();
        if (m215List != null && m215List.size() > 0){
            for(int i=0;i<m215List.size();i++){
                M303ProdDetail m303ProdDetail =new M303ProdDetail();
                Map<String, String> prodInfo = m215List.get(i);
                m303ProdDetail.setProdName(prodInfo.get("prod_name") == null ? "":prodInfo.get("prod_name"));
                m303ProdDetail.setTano(prodInfo.get("tano") == null ? "":prodInfo.get("tano"));
                m303ProdDetail.setProdCode(prodInfo.get("prod_code") == null ? "":prodInfo.get("prod_code"));
                list.add(m303ProdDetail);
            }
        }
        SqlResult<List<M303ProdDetail>> sqlResult = new SqlResult<>();
        sqlResult.setRows(Collections.singletonList(list));
        sqlResult.setResults(list.size());
        return sqlResult;
    }

    @API(desc = "启用计划", operation = APIOperation.UPDATE)
    public String recoverPlan(SqlParam<M303> params) throws Exception {
        boolean result = m303Dao.recoverPlan(params) > 0;
        return RequestSupport.updateReturnJson(result, result ? "启用成功" : "启用失败", null).toString();
    }

    @API(desc = "停用计划", operation = APIOperation.UPDATE)
    public String stopPlan(SqlParam<M303> params) throws Exception {
        //校验是否可以修改状态，关联PLAN_CUST_PROTOCOL表查询是否有正在定投的组合计划
        //返回true，代表查询到了定投信息，不可以则修改状态为失效
        boolean flag = m303Dao.checkIsUsed(params);
        if (flag){
            throw new RuntimeException("M303该组合计划还有用户在定投！");
        } else {
            boolean result = m303Dao.stopPlan(params) > 0;
            return RequestSupport.updateReturnJson(result, result ? "停用成功" : "停用失败", null).toString();
        }

    }
}
