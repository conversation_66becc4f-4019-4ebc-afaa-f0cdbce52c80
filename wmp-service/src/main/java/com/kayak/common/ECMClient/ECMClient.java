package com.kayak.common.ECMClient;

import com.kayak.common.util.SequenceUtils;
import com.kayakwise.wmp.base.util.Tools;
import com.kayakwise.wmp.pub.pojo.SystemNoAndModuleId;
import com.sunyard.client.SunEcmClientApi;
import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientBatchIndexBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.impl.SunEcmClientSocketApiImpl;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;

import java.io.StringReader;
import java.util.Date;

/**
 * 影像平台API调用示例
 * 依赖ECMClient相关的12个JAR包
 */
@Scope
@Repository
public class ECMClient {

    @Autowired
    private SequenceUtils sequenceUtils;

    // 影像平台服务器地址、用户名、密码、影像服务器组名，由影像平台厂商提供
    @Value("${ecm.ip}")
    private String IP;
    @Value("${ecm.port}")
    private int PORT;
    @Value("${ecm.userName}")
    private String USER_NAME;
    @Value("${ecm.password}")
    private String PASSWORD;
    @Value("${ecm.groupName}")
    private String GROUP_NAME;
    @Value("${ecm.indexModelCode}")
    private String INDEX_MODEL_CODE;
    @Value("${ecm.documentModelCode}")
    private String DOCUMENT_MODEL_CODE;
    @Value("${ecm.contentId}")
    private String CONTENT_ID;
    @Value("${ecm.filePartName}")
    private String FILE_PART_NAME;
    public static final String SUCCESS_FLAG = "SUCCESS"; // 成功标志
    public static final String FAIL_FLAG = "FAIL"; // 错误标志
    public static final String MESSAGE_SPLIT = "<<::>>"; // 消息分隔符
    public static final String MESSAGE_PREFIX = "0001" + MESSAGE_SPLIT; // 消息前缀

    private static final Logger log = LoggerFactory.getLogger(ECMClient.class);

    // 批次查询，根据ContentID(影像平台唯一批次号)查询批次下影像（文档模型）
    public String queryBatch(ClientBatchBean cb) throws Exception {
        // ContentID，必传，影像平台全局唯一标识
        //cb.getIndex_Object().setContentID(ContentID);

        SunEcmClientApi api = new SunEcmClientSocketApiImpl(IP, PORT);
        //cb.setModelCode(INDEX_MODEL_CODE);
        cb.setUser(USER_NAME);
        cb.setPassWord(PASSWORD);

        //添加查询影像批次信息条件 可根据业务系统是否需要
        //ClientBatchFileBean cbfb = new ClientBatchFileBean();
        //cbfb.setFilePartName(DOCUMENT_MODEL_CODE);
        //cbfb.addFilter("BUSI_FILE_TYPE", "12824");
        //cb.addDocument_Object(cbfb);
        // 查询成功返回SUCCESS<<::>>+查询结果报文(XML)
        // 未查询到结果时返回报文仅有root节点
        // 失败返回错误码加失败信息
        String res = null;
        try {
            res = api.queryBatch(cb, GROUP_NAME);
        } catch (NullPointerException e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(res) && null != res) {
            if (res.indexOf("URL=\"") != -1) {
                res = res.substring(res.indexOf("URL=\"") + 5, res.length());
                res = res.substring(0, res.indexOf("\""));
                return res;
            } else {
                log.error("获取失败，返回信息如下：");
                log.error(res);
                return "";
            }
        } else {
            return "";
        }

    }

    //public static void main(String[] args) throws Exception{
    //
    //	ClientBatchBean cb = new ClientBatchBean();
    //	cb.setOperatorUsername("张三");
    //	cb.setOperatorId("452552");
    //	// ContentID，必传，影像平台全局唯一标识
    //	cb.getIndex_Object().setContentID("3B584E87-523F-98D7-6D56-D156445A8D24");
    //	// 业务日期开始日期，必传，用于定位分表
    //	cb.getIndex_Object().addCustomMap("BUSI_SERIAL_NO", "020210913100000001191");
    //	cb.getIndex_Object().addCustomMap("BUSI_START_DATE", "20210331");
    //	log.info(queryBatch(cb));
    //}


    /**
     * @param absolutePath     绝对路径名称 例如：D:\123\test.txt
     * @param oldContentId     原批次号
     * @param oldBusiStartDate 原业务日期
     * @param fileName         文件名称：test.txt
     * @return : java.lang.String
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/29 17:07
     * @desc : 传送文件到非结构化系统文件服务器上
     */
    public String uploadFile(String absolutePath, String oldContentId, String oldBusiStartDate, String fileName) throws Exception {
        SunEcmClientApi clientApi = null; // 建立UA的socket
        SunEcmClientApi clientDMApi = null; // 建立DM的socket
        String contentId;
        log.debug("IP:{}", IP);
        log.debug("PORT:{}", PORT);
        log.debug("USER_NAME:{}", USER_NAME);
        log.debug("GROUP_NAME:{}", GROUP_NAME);
        log.debug("DOCUMENT_MODEL_CODE:{}", DOCUMENT_MODEL_CODE);
        log.debug("FILE_PART_NAME:{}", FILE_PART_NAME);
        try {
            // 1. 建立UA服务器socket连接
            clientApi = new SunEcmClientSocketApiImpl(IP, PORT); // 建立UA的socket

            // 2. 用户登录
            login(clientApi);

            // 3. 建立SunECMDM服务器的socket连接
            clientDMApi = getClientDMApi(clientApi);

            // 4. 有历史数据，先删除文件，无历史数据，则不处理
            if (Tools.isNotBlank(oldContentId) && Tools.isNotBlank(oldBusiStartDate))
                delete(oldContentId, oldBusiStartDate, clientDMApi);

            // 5. 上传文件
            contentId = upload(absolutePath, clientDMApi, fileName);
        } catch (Exception e) {
            log.error(e.getMessage(), e.getCause());
            e.printStackTrace();
            throw new Exception("上传文件到非结构化平台服务器出错" + e);
        } finally {
            try {
                logout(clientApi); // 用户登出
            } catch (Exception e) {
                log.error(e.getMessage(), e.getCause());
            }
        }
        log.info("contentId:" + contentId);
        return contentId;
    }


    /**
     * @param oldContentId
     * @param oldBusiStartDate
     * @return : void
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/29 17:15
     * @desc : 删除非结构化系统文件服务器上的文件
     */
    public void deleteFile(String oldContentId, String oldBusiStartDate) {

        SunEcmClientApi clientApi = null; // 建立UA的socket
        SunEcmClientApi clientDMApi = null; // 建立DM的socket

        try {
            // 1. 建立UA服务器socket连接
            clientApi = new SunEcmClientSocketApiImpl(IP, PORT); // 建立UA的socket

            // 2. 用户登录
            login(clientApi);

            // 3. 建立SunECMDM服务器的socket连接
            clientDMApi = getClientDMApi(clientApi);

            // 4. 有历史数据，先删除文件，无历史数据，则不处理
            delete(oldContentId, oldBusiStartDate, clientDMApi);

        } catch (Exception e) {
            // TODO 删除文件，暂时不报错处理
            log.error(e.getMessage(), e.getCause());
        } finally {
            try {
                logout(clientApi); // 用户登出
            } catch (Exception e) {
                log.error(e.getMessage(), e.getCause());
            }
        }
    }


    /**
     * @param clientApi
     * @return : void
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/29 16:52
     * @desc : 用户登出
     */
    private void logout(SunEcmClientApi clientApi) throws Exception {
        if (Tools.isObjNotBlank(clientApi)) {
            //退出登录
            //String resultLogoutMsg = clientApi.logout(USER_NAME);
            //log.info("#######登出返回的信息[" + resultLogoutMsg + "]#######");
            log.info("#######不走登出#######");
        }
    }

    /**
     * @param absolutePath 绝对路径名称 例如：D:\123\test.txt
     * @param clientDMApi  客户端API对象(DM)
     * @param fileName     文件名称：test.txt
     * @return : java.lang.String
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/29 16:53
     * @desc : 上传文件到非结构化平台(影像平台)
     */
    private String upload(String absolutePath, SunEcmClientApi clientDMApi, String fileName) throws Exception {
        String contentId;
        String fileFormat = fileName.substring(fileName.lastIndexOf(".") + 1); // 文件后缀
        String saveName = fileName.substring(0, fileName.lastIndexOf(".")); // 文件名称

        // 上传文件
        ClientBatchBean clientBatchBean = new ClientBatchBean();
        clientBatchBean.setModelCode(DOCUMENT_MODEL_CODE); // 系统模型代码
        clientBatchBean.setUser(USER_NAME); // 登录影像平台用户
        clientBatchBean.setPassWord(PASSWORD); // 登录影像平台用户密码
        clientBatchBean.setBreakPoint(false); // 是否作为断点续传上传，默认否即可
        clientBatchBean.setOwnMD5(false); // 是否为批次下的文件添加MD5码，默认否即可

        // =========================设置索引对象信息开始=========================
        ClientBatchIndexBean clientBatchIndexBean = new ClientBatchIndexBean();
        clientBatchIndexBean.addCustomMap("AMOUNT", "1"); //上传文件数，与本次需上传的文件数相对应

        // 索引自定义属性，自定义添加必须为该模型系统已关联影像平台中的字段属性，相关关联字段，对接时由影像平台提供
        // 其中BUSI_SERIAL_NO和BUSI_START_DATE为必传字段
        clientBatchIndexBean.addCustomMap("BUSI_SERIAL_NO", sequenceUtils.getSequence("wmp_batch_gropu_info", SystemNoAndModuleId.FINA.getModuleId(), 8)); // 业务流水号
        clientBatchIndexBean.addCustomMap("BUSI_START_DATE", Tools.dt2date(new Date())); // 业务时间
        // =========================设置索引对象信息结束=========================

        // =========================设置文档部件信息开始=========================
        ClientBatchFileBean clientBatchFileBean = new ClientBatchFileBean();
        clientBatchFileBean.setFilePartName(FILE_PART_NAME); //文档部件模型代码，对接时由影像平台提供
        // =========================设置文档部件信息结束=========================

        // =========================start添加文件=========================
        ClientFileBean clientFileBean = new ClientFileBean();
        clientFileBean.setFileName(absolutePath); // 文件本地全路径 类1： D:\temp.jpg 类2：D:\name.txt
        clientFileBean.setFileFormat(fileFormat); // 文件后缀 类1：jpg 类2：txt TODO手机银行同事说默认html
        //文档自定义属性
        clientFileBean.addOtherAtt("BUSI_FILE_TYPE", "0105014"); // BUSI_FILE_TYPE 文件类型编码，若已对接SunIAS的必须传由影像平台提供的文件类型编码，其他未对接的，值可以自定义设置
        clientFileBean.addOtherAtt("BUSI_FILE_PAGENUM", "1"); // BUSI_FILE_PAGENUM 文件在SunIAS页面展示的页码顺序
        clientFileBean.addOtherAtt("BUSI_START_DATE", Tools.dt2date(new Date())); // 必要信息，业务时间，一旦上传成功，后续操作都以这个值为准
        clientFileBean.addOtherAtt("SAVE_NAME", saveName); // 文件保存名，如未设置，影像平台系统会进行默认值 类1：temp 类2：name

        clientBatchFileBean.addFile(clientFileBean);
        //=========================end添加文件=========================
        clientBatchBean.setIndex_Object(clientBatchIndexBean);
        clientBatchBean.addDocument_Object(clientBatchFileBean);
        log.debug("#######startUpload#######");
        String resultUploadMsg = clientDMApi.upload(clientBatchBean, GROUP_NAME);
        log.info("#######上传批次返回的信息[" + resultUploadMsg + "]#######");

        String[] uploadMsgs = resultUploadMsg.split(MESSAGE_SPLIT);
        if (uploadMsgs != null && SUCCESS_FLAG.equals(uploadMsgs[0])) {
            contentId = uploadMsgs[1];
        } else {
            throw new Exception("上传失败, " + resultUploadMsg);
        }
        return contentId;
    }

    /**
     * @param clientApi
     * @return : com.sunyard.client.SunEcmClientApi
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/29 16:57
     * @desc : 获取socket连接对象(客户端API对象(DM))
     */
    public SunEcmClientApi getClientDMApi(SunEcmClientApi clientApi) throws Exception {

        // 均衡负载
        String resultQuireDMMsg = clientApi.inquireDMByGroup(USER_NAME, GROUP_NAME);
        log.info("#######客户端获取内容存储服务器信息[" + resultQuireDMMsg + "]#######");
        String DMMsg = resultQuireDMMsg.replace(MESSAGE_PREFIX, "");

        SAXReader reader = new SAXReader();
        Document document = reader.read(new StringReader(DMMsg));

        Element nodeInfo = document.getRootElement();
        String DMIp = nodeInfo.attributeValue("SERVER_IP"); // DM_IP
        int DMPort = Integer.parseInt(nodeInfo.attributeValue("SOCKET_PORT")); // DM_PORT

        return new SunEcmClientSocketApiImpl(DMIp, DMPort);
    }

    /**
     * @param clientApi
     * @return : void
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/29 16:58
     * @desc : 用户登录
     */
    private void login(SunEcmClientApi clientApi) throws Exception {
        //登录UA服务器
        String resultLoginMsg = clientApi.login(USER_NAME, PASSWORD);
        log.info("#######登录返回的信息[" + resultLoginMsg + "]#######");
        if (resultLoginMsg != SUCCESS_FLAG) {
            throw new Exception("登录服务器失败");
        }
    }

    /**
     * @param oldContentId     原批次号
     * @param oldBusiStartDate 原业务日期
     * @param clientDMApi      客户端API对象(DM)
     * @return : void
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/29 17:00
     * @desc : 删除文件
     */
    public void delete(String oldContentId, String oldBusiStartDate, SunEcmClientApi clientDMApi) {

        ClientBatchBean clientBatchBean = new ClientBatchBean();
        clientBatchBean.setModelCode(DOCUMENT_MODEL_CODE); // 系统模型代码
        clientBatchBean.setPassWord(PASSWORD);// 登录影像平台用户密码
        clientBatchBean.setUser(USER_NAME);// 登录影像平台用户
        // 必要信息， 批次号id，第一次上传影像平台返回的content_id，如若未保留，则可以通过高级查询获取
        clientBatchBean.getIndex_Object().setContentID(oldContentId);
        // 必要信息，业务时间，与第一次上传文件时设置的busi_start_date的值一致
        clientBatchBean.getIndex_Object().addCustomMap("BUSI_START_DATE", oldBusiStartDate);

        try {
            String resultMsg = clientDMApi.delete(clientBatchBean, GROUP_NAME);
            //*	成功  返回成功信息success
            //*	失败  返回失败信息+异常代码,例如: FAIL<<::>>733
            log.debug("#######删除批次返回的信息[" + resultMsg + "]#######");
        } catch (Exception e) {
            log.error(e.getMessage(), e.getCause());
        }
    }
}
