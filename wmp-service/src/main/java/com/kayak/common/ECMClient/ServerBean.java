package com.kayak.common.ECMClient;


import com.thoughtworks.xstream.annotations.XStreamAlias;

@XStreamAlias("ServerBean")
public class ServerBean {

	/**
	 * 服务器IP
	 */
	private String serverIp;

	/**
	 * socket端口
	 */
	private int socketPort;

	/**
	 * 用户名
	 */
	private String userName;

	/**
	 * 密码
	 */
	private String password;

	/**
	 * 组名
	 */
	private String groupName;


	public String getServerIp() {
		return serverIp;
	}

	public void setServerIp(String serverIp) {
		this.serverIp = serverIp;
	}

	public int getSocketPort() {
		return socketPort;
	}

	public void setSocketPort(int socketPort) {
		this.socketPort = socketPort;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Override
	public String toString() {
		return "ServerBean{" +
				"serverIp='" + serverIp + '\'' +
				", socketPort=" + socketPort +
				", userName='" + userName + '\'' +
				", password='" + password + '\'' +
				", groupName='" + groupName + '\'' +
				'}';
	}
}
