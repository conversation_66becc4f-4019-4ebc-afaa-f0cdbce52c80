package com.kayak.common.util;

import com.kayak.common.ECMClient.ECMClient;
import com.kayak.core.action.BaseController;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.SysBeans;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.dao.MC01Dao;
import com.kayak.fina.param.model.MC01;
import com.kayak.fina.param.service.MC01Service;
import com.kayak.fina.param.service.MC02Service;
import com.kayak.graphql.annotation.GraphQLModel;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M205;
import com.kayak.system.model.M006;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.IfFunc;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 产品文档文件传输(上传 / 下载)
 * <AUTHOR>
 * @Date 2020/12/22
 */
@Scope("prototype")
@Controller
public class FileTransferAction extends BaseController {
    @Value("${file.tem.path}")
    private String TEMP_PATH;

    @Value("${file.filePath}")
    private String fileAbsulPath;

    @Value("${file.transferAgreementFileName}")
    private String transferAgreementFileName;

    @Autowired
    private ECMClient ecmClient;

    @Autowired
    private MC01Service mc01Service;

    Map<String, Object> map = new HashMap<>();

    @SuppressWarnings({"unchecked", "rawtypes"})
    @RequestMapping("/prod/file/upload.json")
    @ResponseBody
    public String uploadFile(@RequestParam(value = "file", required = false) MultipartFile file, HttpServletResponse response) throws Exception {

        Map<String, Object> params = RequestSupport.getParameters();
        SqlParam<M205> sqlParam = getSqlParam(M205.class);
        String modelClassName = Tools.obj2Str(params.get("modelClassName"));
        String modelPackageName = Tools.obj2Str(params.get("modelPackageName"));
        String fileName = file.getOriginalFilename();
        String filePath = getUploadFileAbsolutePath();

        if (Tools.isBlank(modelClassName) && Tools.isBlank(modelClassName)) {
            try {
                File path = new File(filePath);
                this.doReadyBeforeTransfer(path, fileName); // 创建目录或清理目录中已有的文件
                File uploadFile = new File(filePath + File.separator + fileName);
                file.transferTo(uploadFile); // 生成文件到本地服务器中
            } catch (Exception e) {
                throw new RuntimeException("上传失败");
            }
            //文件上传到影像平台
            try {
                //上传文件到影像平台得到批次号
                //查询是否有存在批次号
                MC01 mc01 = mc01Service.getMC01("a0000009");
                String paravalue;
                String uploadContentId = null;
                if (mc01 != null) {
                    paravalue = mc01.getParavalue();
                    uploadContentId = paravalue.substring(0, paravalue.indexOf(","));
                }
                String nowTime = Tools.getStringFromDate("yyyy-MM-dd", new Date());
//                String contentId = ecmClient.uploadFile(getUploadFileAbsolutePath() + File.separator + fileName, uploadContentId, nowTime, fileName);
                String contentId = "20220507_56_42_D5EBB828-FA66-9523-910F-76C0EFF79B21-43";
                //更新数据库的批次号
                if (mc01 != null) {
                    map.clear();
                    map.put("paravalue", contentId + "," + nowTime + "," + fileName);
                    map.put("paraid", "a0000009");
                    mc01Service.update(new FetcherData<MC01>(map, MC01.class));
                } else {
                    //数据库没有原批次号数据就新增
                    map.put("paraid", "a0000009");
                    map.put("moduleid", "4");
                    map.put("paravalue", contentId + "," + nowTime + "," + fileName);
                    map.put("paraname", "文件上传批次号维护");
                    map.put("groupparaid", "1");
                    map.put("isdisplay", "1");
                    mc01Service.addSysParam(new FetcherData<MC01>(map, MC01.class));
                }
            } catch (Exception e) {
                throw new RuntimeException("上传失败");
            }
            return RequestSupport.updateReturnJson(true, "已成功上传" + fileName, null).toString();
        }

        Object serverBean = getServerBean(modelPackageName, modelClassName);

        Method findMethod = null;
        Method insertMethod = null;
        Method getVersionMethod = null;

        Class<?> serverClass = serverBean.getClass();

        try {
            getVersionMethod = serverClass.getMethod("getVersion", SqlParam.class);
            String version = (String) getVersionMethod.invoke(serverBean, sqlParam);    //将产品文档信息落地
            sqlParam.getModel().setDocVersion(version);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }

        try {
            findMethod = serverClass.getMethod("find" + modelClassName, SqlParam.class);
            /** @update hyb_黄英玻 2022/3/30 9:30 直接屏蔽，后面没有使用到 */
            //Map<String, Object> map = new HashMap<>();
            //map.put("systemNo", sqlParam.getModel().getSystemNo());
            //map.put("tano", sqlParam.getModel().getTano());
            //map.put("prodCode", sqlParam.getModel().getProdCode());
            //map.put("legalCode", sqlParam.getModel().getLegalCode());
            //map.put("docStatus", "0");
            //map.put("docType", sqlParam.getModel().getDocType());
            //map.put("onlineFlag", sqlParam.getModel().getOnlineFlag());
            //map.put("docVersion", sqlParam.getModel().getDocVersion());
            //FetcherData<M205> fetcherData = new FetcherData<>(map, M205.class);

            SqlResult<M205> sqlResult = (SqlResult<M205>) findMethod.invoke(serverBean, sqlParam); // 查询是否已有产品文档信息
            List<M205> list = sqlResult.getRows();
            if (list != null && list.size() > 0) {
                return RequestSupport.updateReturnJson(false,
                        "该产品已录入过该类型产品文档信息，如需重新上传文档请在查询列表中上传新文件", null).toString();
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }
        try {
            insertMethod = serverClass.getMethod("add" + modelClassName, SqlParam.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }

        String originalFilename = file.getOriginalFilename();
        /**end @update hyb_黄英玻 2022/3/30 9:27 */
        M205 prodDocumentInfo = sqlParam.getModel();
        prodDocumentInfo.setDocPath(filePath);
        prodDocumentInfo.setDocName(fileName);

        try {
            insertMethod.invoke(serverBean, sqlParam); //将产品文档信息落地
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法生成产品文档信息, 请稍后再试", null).toString();
        }

        try {
            File path = new File(filePath);
            this.doReadyBeforeTransfer(path, fileName); // 创建目录或清理目录中已有的文件
            File uploadFile = new File(filePath + File.separator + fileName);
            file.transferTo(uploadFile); // 生成文件到本地服务器中
        } catch (Exception e) {

            log.error(e.getMessage(), e);

            try {
                SqlResult<M205> sqlResult =
                        (SqlResult<M205>) findMethod.invoke(serverBean, sqlParam); //查询产品文档信息是否已落地
                List<M205> list = sqlResult.getRows();
                if (list != null && list.size() > 0) {
                    Method deleteMethod = null;
                    deleteMethod = serverClass.getMethod("delete" + modelClassName, SqlParam.class);
                    int effect = (int) deleteMethod.invoke(serverBean, sqlParam); //文档上传失败，删除该笔产品文档信息记录
                    if (effect != 1) {
                        return RequestSupport.updateReturnJson(false,
                                "产品文档信息已经录入成功, 但文档上传失败, 可手动重新上传" + fileName, null).toString();
                    }
                }

            } catch (Exception e1) {
                log.error(e.getMessage(), e1);
                return RequestSupport.updateReturnJson(false,
                        "产品文档信息已经录入成功, 但文档上传失败, 可手动重新上传" + fileName, null).toString();
            }
            return RequestSupport.updateReturnJson(false, "产品文档上传失败, 请稍后再试", null).toString();
        }

        return RequestSupport.updateReturnJson(true, "已成功上传" + fileName, null).toString();
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @RequestMapping("prod/file/reUpload.json")
    @ResponseBody
    public String reUploadFile(@RequestParam(value = "file", required = false) MultipartFile file, HttpServletResponse response) throws Exception {

        SqlParam<M205> sqlParam = getSqlParam(M205.class);

        Map<String, Object> params = RequestSupport.getParameters();
        String modelClassName = Tools.obj2Str(params.get("modelClassName"));
        String modelPackageName = Tools.obj2Str(params.get("modelPackageName"));

        Object serverBean = getServerBean(modelPackageName, modelClassName);

        Method findMethod = null;
        Method updateMethod = null;
        Class<?> serverClass = serverBean.getClass();
        try {
            findMethod = serverClass.getMethod("find" + modelClassName, SqlParam.class);
            SqlResult<M205> sqlResult = (SqlResult<M205>) findMethod.invoke(serverBean, sqlParam); //查询是否已有产品文档信息
            List<M205> list = sqlResult.getRows();
            if (list == null || list.size() == 0) {
                return RequestSupport.updateReturnJson(false,
                        "该产品文档信息不存在, 可能已经被删除, 请刷新列表后再试", null).toString();
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法重新上传产品文档信息, 请稍后再试", null).toString();
        }

        try {
            updateMethod = serverClass.getMethod("update" + modelClassName, SqlParam.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法重新上传产品文档信息, 请稍后再试", null).toString();
        }

        String originalFilename = file.getOriginalFilename();
        /**start @update hyb_黄英玻 2022/3/30 9:27  */
        //String suffix = file.getOriginalFilename().substring(originalFilename.lastIndexOf("."), originalFilename.length());
        //String fileName = this.getUploadFileAbsoluteName(sqlParam.getModel(), suffix); // 先不用自定义文件名称
        String fileName = originalFilename; // 使用送的文件名称
        String filePath = this.getUploadFileAbsolutePath();
        /**end @update hyb_黄英玻 2022/3/30 9:27  */
        M205 prodDocumentInfo = sqlParam.getModel();
        prodDocumentInfo.setDocPath(filePath);
        prodDocumentInfo.setDocName(fileName);

        try {
            updateMethod.invoke(serverBean, sqlParam); //更新产品文档信息
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "无法重新上传产品文档信息, 请稍后再试", null).toString();
        }

        try {
            File path = new File(filePath);
            this.doReadyBeforeTransfer(path, fileName); //创建目录或清理目录中已有的文件
            File reUploadFile = new File(filePath + File.separator + fileName);
            file.transferTo(reUploadFile); //重新生成文件到本地服务器中

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false,
                    "产品文档信息已经更新成功, 但文档上传失败, 请稍后再试", null).toString();
        }

        return RequestSupport.updateReturnJson(true, "已成功上传" + fileName, null).toString();
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @RequestMapping("prod/file/download.json")
    public String downloadTest(HttpServletResponse response) {
        Map<String, Object> params = RequestSupport.getParameters();
        String filePath;
        String fileName;
        //没有参数就是转让协议下载，手动设置固定的文件路径和名称
        if (params.size() <= 0) {
            filePath = getUploadFileAbsolutePath();
            try {
                String paravalue = mc01Service.getMC01("a0000009").getParavalue();
                fileName = paravalue.substring(paravalue.lastIndexOf(",") + 1);
            } catch (Exception e) {
                throw new RuntimeException("下载失败，请检查文件是否已上传");}
        } else {
            filePath = Tools.obj2Str(params.get("docPath"));
            fileName = Tools.obj2Str(params.get("docName"));
        }
        try (OutputStream os = response.getOutputStream();) {
            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", "filename");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setHeader("filename", fileName);

            File file = new File(filePath + File.separator + fileName);
            if (file.exists()) {
                os.write(FileUtils.readFileToByteArray(file));
                os.flush();
            } else {
                response.setHeader("err", "产品文档不存在, 请上传文件后再下载");
                return RequestSupport.updateReturnJson(false, "产品文档下载失败, 请稍后再试", null).toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "产品文档下载失败, 请稍后再试", null).toString();
        }
        return RequestSupport.updateReturnJson(true, "下载成功", null).toString();
    }

    private Object getServerBean(Class<?> modelClass) {
        // 获取操作对象实例
        GraphQLModel graphQLModel = modelClass.getAnnotation(GraphQLModel.class);
        String fetcher = graphQLModel.fetcher();
        return SysBeans.getBean(fetcher);
    }

    private Class<?> getClass(String modelClassName) {
        if (StringUtils.isEmpty(modelClassName)) {
            return null;
        }
        Class<?> modelClass = null;
        try {
            modelClass = Class.forName(modelClassName);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return modelClass;
    }

    private String getUploadFileAbsolutePath() {
        String osName = System.getProperty("os.name");
        return (osName.toLowerCase().startsWith("win") ? fileAbsulPath : TEMP_PATH);
    }

    private String getUploadFileAbsoluteName(M205 prodDocumentInfo, String suffix) {

        String name = "";

        if ("A".equals(prodDocumentInfo.getDocType())) {
            name = prodDocumentInfo.getSystemNo() + "_" +
                    prodDocumentInfo.getTano() + "_" +
                    prodDocumentInfo.getProdCode() + "_" +
                    prodDocumentInfo.getOnlineFlag() + "_" +
                    prodDocumentInfo.getDocVersion();
        } else {
            name = prodDocumentInfo.getTano() + "_" +
                    prodDocumentInfo.getProdCode() + "_" +
                    prodDocumentInfo.getDocType();
        }

        return name + suffix;
    }

    private void doReadyBeforeTransfer(File path, String name) {

        if (!path.exists()) {
            path.mkdirs();
        } else {
            try {
                File[] childFiles = path.listFiles();
                if (childFiles != null && childFiles.length > 0) {
                    for (File childFile : childFiles) {
                        if (childFile.getName().equals(name)) {
                            childFile.delete();
                        }
                    }
                }
            } catch (Throwable t) {
                log.error(t.getMessage(), t);
            }
        }
    }

    public static boolean deleteFile(File file) {

        if (!file.exists()) {
            return true;
        }

        if (file.isDirectory()) {
            File[] childFiles = file.listFiles();
            if (childFiles != null && childFiles.length > 0) {
                for (int i = 0; i < childFiles.length; i++) {
                    boolean result = deleteFile(childFiles[i]);
                    if (!result) {
                        return result;
                    }
                }
            }
        }
        return file.delete();
    }

    /**
     * @param modelClass
     * @return : com.kayak.core.sql.SqlParam<T>
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/30 9:53
     * @desc : 获取SqlParam(可以变成公共的获取SqlParam的方法)
     */
    private <T> SqlParam<T> getSqlParam(Class<T> modelClass) {
        SqlParam<T> sqlParam = null;

        try {
            sqlParam = new FetcherData<>(RequestSupport.getParameters(), modelClass);
        } catch (Exception e) {
            log.error(e.getMessage(), e.getCause());
        }
        return sqlParam;
    }

    /**
     * @param modelPackageName 模型包名
     * @param modelClassName   模型名称
     * @return : java.lang.Object
     * <AUTHOR> hyb_黄英玻
     * @date : 2022/3/30 9:52
     * @desc : 获取模型对象
     */
    private Object getServerBean(String modelPackageName, String modelClassName) {
        String modelPackageAndClassName = modelPackageName + "." + modelClassName;
        Class<?> modelClass = getClass(modelPackageAndClassName);
        if (modelClass == null) {
            String errMsg = "实体类[" + modelPackageAndClassName + "]获取失败";
            log.error(errMsg);
            return RequestSupport.updateReturnJson(false, errMsg, null).toString();
        }
        Object serverBean = this.getServerBean(modelClass);
        if (serverBean == null) {
            log.error("获取服务对象失败，实体类[" + modelPackageAndClassName + "]fetcher配置对应实例不存在");
            return RequestSupport.updateReturnJson(false, "无法获取服务", null).toString();
        }
        return serverBean;
    }

}
