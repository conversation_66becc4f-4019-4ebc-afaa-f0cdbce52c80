package com.kayak.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;


public class HttpClientUtil {

	private final static Logger log = LoggerFactory.getLogger(HttpClientUtil.class);

	/*public static String excutePost(String data,String url){

		CloseableHttpClient httpclient = HttpClients.createDefault();

		HttpPost post = new HttpPost(url);
		post.setHeader("Connection","keep-alive");

		try {
			 // 构建消息实体
	        StringEntity entity = new StringEntity(data, Charset.forName("UTF-8"));
	        entity.setContentEncoding("UTF-8");
	        // 发送Json格式的数据请求
	        entity.setContentType("application/json");
	        post.setEntity(entity);

			CloseableHttpResponse httpResponse = httpclient.execute(post);
			HttpEntity entity2 = httpResponse.getEntity();
			String result = EntityUtils.toString(entity2);
			System.out.println(result);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			throw new TransException("","---------------链接："+url+" 处理请求失败--------------");
		}finally {
			if(httpclient!=null) {
				try {
					httpclient.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}*/

	public static String sendPost(String url, byte[] body, int timeout) {
		PrintWriter out = null;
		BufferedReader in = null;
		StringBuffer result = new StringBuffer();
		try {
			URL realUrl = new URL(url);
			log.info(String.format("send http POST request [%s]", url));
			// 打开和URL之间的连接
			URLConnection conn = realUrl.openConnection();
			conn.setReadTimeout(timeout);
			// 设置通用的请求属性
			conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/5.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1)");
//			conn.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 6.1; WOW64)");
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			// 获取URLConnection对象对应的输出流
			conn.getOutputStream().write(body);
			conn.getOutputStream().flush();
			// 获取所有响应头字段
			Map<String, List<String>> map = conn.getHeaderFields();
			// 遍历所有的响应头字段
//			for (String key : map.keySet()) {
//				if (result.length() > 0) {
//					result.append("\n");
//				}
//				result.append(String.format("%s:%s", key, map.get(key)));
//			}
			// 定义BufferedReader输入流来读取URL的响应
			in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			char[] buffer = new char[1024];
			int len;
			while ((len = in.read(buffer, 0, 1024)) > 0) {
				result.append(buffer, 0, len);
			}
			return result.toString();
		} catch (Exception e) {
			log.error(String.format("发送POST请求出现异常：%s", e.getMessage()), e);
			return null;
		}
		// 使用finally块来关闭输出流、输入流
		finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				log.error(ex.getMessage(), ex);
			}
		}
	}

}
