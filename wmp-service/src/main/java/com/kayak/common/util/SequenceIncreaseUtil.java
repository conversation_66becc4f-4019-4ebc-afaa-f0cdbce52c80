package com.kayak.common.util;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.model.SysSequence;
import com.kayak.core.sql.SqlParam;
import com.kayak.graphql.model.FetcherData;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Repository
public class SequenceIncreaseUtil  extends ComnDao {


    /**
     * 指定数据库databaseName，根据序列自增表的key值查询当前序列，后加一
     * @param sequenceKey
     * @param databaseName
     * @return
     */
    public String increaseNum(String sequenceKey,Integer databaseName) throws Exception{
        String sql = "select maxid from sys_sequence where tablename = $S{tableName}";
        Map<String,String> map = new HashMap<>();
        map.put("tableName",sequenceKey);
        SqlParam<SysSequence> param = new FetcherData(map,SysSequence.class);
        param.getModel().setTableName(sequenceKey);
        List<SysSequence> result = super.findRows(sql, databaseName, param).getRows();
        String updSql = "";
        Integer id = 0;
        if(result.size()>=1) {
            id = Integer.parseInt(result.get(0).getMaxid()) + 1;
            // 修改
            updSql = "update sys_sequence set maxid = $S{maxid} where tablename = $S{tableName}";
            param.getModel().setMaxid(id.toString());
        }else {
            // 新增
            updSql = "insert into sys_sequence values($S{tableName} ,$S{maxid})";
            param.getModel().setMaxid(id+"");
        }
        super.update(updSql,databaseName,param.getModel());
        return param.getModel().getMaxid();

    }
}
