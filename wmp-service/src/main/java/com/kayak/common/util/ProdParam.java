package com.kayak.common.util;

import java.util.Map;

/**
 * @Description 产品参数
 * <AUTHOR>
 * @Date 2021/5/6
 */
public class ProdParam {

	/**
	 * [>] - 大于
	 */
	public static final String MORE_THAN = "[>]";
	/**
	 * [>=] - 大于等于
	 */
	public static final String MORE_THAN_OR_EQUAL = "[>=]";
	/**
	 * [<] - 小于
	 */
	public static final String LESS_THAN = "[<]";
	/**
	 * [<=] - 小于等于
	 */
	public static final String LESS_THAN_OR_EQUAL = "[<=]";
	/**
	 * [==] - 等于
	 */
	public static final String EQUAL = "[==]";
	/**
	 * [!=] - 不等于
	 */
	public static final String NOT_EQUAL = "[!=]";
	/**
	 * [%like%] - 模糊匹配(包含)
	 */
	public static final String INCLUDE = "[%like%]";
	/**
	 * [like%] - 模糊匹配(固定开头)
	 */
	public static final String START_WITH = "[like%]";
	/**
	 * [%like] - 模糊匹配(固定结尾)
	 */
	public static final String END_WITH = "[%like]";
	/**
	 * [&] - 并且
	 */
	public static final String AND = "[&]";


	public static String MoreThan(String param) {
		checkParamNotEmpty(param);
		return MORE_THAN + param.trim();
	}

	public static String MoreThanOrEqual(String param) {
		checkParamNotEmpty(param);
		return MORE_THAN_OR_EQUAL + param.trim();
	}

	public static String LessThan(String param) {
		checkParamNotEmpty(param);
		return LESS_THAN + param.trim();
	}

	public static String LessThanOrEqual(String param) {
		checkParamNotEmpty(param);
		return LESS_THAN_OR_EQUAL + param.trim();
	}

	public static String Equal(String param) {
		checkParamNotEmpty(param);
		return EQUAL + param.trim();
	}

	public static String NotEqual(String param) {
		checkParamNotEmpty(param);
		return NOT_EQUAL + param.trim();
	}

	public static String Include(String param) {
		checkParamNotEmpty(param);
		return INCLUDE + param.trim();
	}

	public static String StartWith(String param) {
		checkParamNotEmpty(param);
		return START_WITH + param.trim();
	}

	public static String EndWith(String param) {
		checkParamNotEmpty(param);
		return END_WITH + param.trim();
	}

	public static String And(String... params) {

		if(params == null || params.length == 0) {
			throw new IllegalArgumentException("参数错误");
		}

		StringBuilder stringBuilder = new StringBuilder();

		for(int i=0; i<=params.length-1; i++) {

			stringBuilder.append(params[i].trim());

			if(i < params.length-1) {
				stringBuilder.append(AND);
			}
		}
		return stringBuilder.toString();
	}

	private static void checkParamNotEmpty(String param) {
		if(param == null || "".equals(param.trim())) {
			throw new IllegalArgumentException("参数错误");
		}
	}

}