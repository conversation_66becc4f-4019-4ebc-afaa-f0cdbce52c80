package com.kayak.common.util;


import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Map;

public class ReflectUtils {

    public final static String DECIMAL = "java.math.BigDecimal";
    public final static String STRING = "java.lang.String";
    public final static String INT = "int";
    public final static String CHAR = "char";
    public final static String DOUBLE = "double";

    /**
     * 根据将传入的Map映射成实体类
     * @param map
     * @param clazz
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     * @throws InvocationTargetException
     */
    public static Object analysis(Map<String, Object> map, Class clazz) throws IllegalAccessException, InstantiationException, InvocationTargetException {
        final Object o = clazz.newInstance();
        for (Method declaredMethod : clazz.getDeclaredMethods()) {
            String fieldName;
            Object fieldValue;
            //遍历所有的set方法
            if (declaredMethod.getName().startsWith("set")) {
                fieldName = declaredMethod.getName().substring(3);
                fieldValue = map.get(fieldName);
                //当从Map获取不到对应的值时跳过
                if (fieldValue == null) {
                    continue;
                }
                Object obj = new Object();
                switch (declaredMethod.getParameterTypes()[0].getName()) {
                    case STRING:
                    case CHAR:
                    case DOUBLE:
                    case INT:
                        obj = fieldValue;
                        break;
                    case DECIMAL:
                        obj = new BigDecimal(fieldValue.toString());
                        break;
                }
                declaredMethod.invoke(o, obj);
            }
        }


        return o;
    }


}