package com.kayak.common.util;

import com.kayak.base.dao.ComnDao;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.core.sql.SqlRow;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
@Repository
public class SequenceUtils extends ComnDao {

    private final static Logger log = LoggerFactory.getLogger(SequenceUtils.class);

    /**
     * 应用序列自增长对象缓存
     */
    private final Map<String, AtomicLong> cacheSeqAtomic = new ConcurrentHashMap<>();

    private Integer redisSequenceSaveFrequency = 10;

    /**
     * 应用序列本地最大值缓存
     */
    private final Map<String, Long> cacheSeq = new ConcurrentHashMap<>();

    private volatile ThreadLocal<Long> seqThreadLocal = new ThreadLocal<Long>();




    /**
     * 获取序列号
     *
     * @param sequenceName   使用REDIS生成序列的时候拼到KEY里，使用数据库生成序列的时候表示表名使用 SequenceContents 常量类
     * @param sequenceLength 序列号的长度，如果序列号不足长度位前补零，如果序列号超过长度位则从0开始
     * @return
     * @throws Exception
     */
    public String getSequence(String sequenceName,String moduleid, int sequenceLength) throws Exception {
        if (sequenceLength < 1) {
            throw new RuntimeException("序列号长度不能小于1");
        }
        String key = "wmp:sequence" + sequenceName.toLowerCase()+":"+moduleid;
        // 检查是否存在 Sequence 缓存
        if (!cacheSeqAtomic.containsKey(key)) {
            // 同步锁，防止并发
            synchronized (this) {
                // DCL double check lock
                if (!cacheSeqAtomic.containsKey(key)) {
                    // 获取 Sequence
                    long redisSeq = getSequenceFromDB(key,moduleid);
                    // 缓存redis获取到的最大步长值
                    cacheSeq.put(key, redisSeq);
                    /* 记录起始 Sequence = Redis Sequence - 增长步长
                     * 缓存自增长对象
                     */
                    cacheSeqAtomic.put(key, new AtomicLong(redisSeq - redisSequenceSaveFrequency));
                }
            }
        }

        // 获取本地缓存 Sequence
        AtomicLong atomicLong = cacheSeqAtomic.get(key);
        Long seq = null;
        synchronized (atomicLong) {
            seq = atomicLong.incrementAndGet();
            if (seq > cacheSeq.get(key)) {
                long redisSeq = getSequenceFromDB(key,moduleid);
                // 重新缓存最大 Sequence
                cacheSeq.put(key, redisSeq);
                // 记录新 Sequence = Redis Sequence - 增长步长
                atomicLong.set(redisSeq - redisSequenceSaveFrequency);
                seq = atomicLong.incrementAndGet();
            }
        }

        String sequence = seq.toString();
        int len = sequence.length();
        if (len < sequenceLength) {
            //小于指定长度，前补0
            // sequence = String.format("%0" + sequenceLength + "d", seq);
            sequence = alignRight(sequence, sequenceLength, "0");
        } else if (len > sequenceLength) {
            sequence = sequence.substring(len - sequenceLength);
        }
        return sequence;
    }

    private long getSequenceFromDB(String key,String moduleid) throws Exception {
        // 记录序列号
        DaoUtil.doTrans(() -> {
                    // 重试
                    for (int i = 0; i <= 1; i++) {
                        Long dbMaxid = null;
                        // 更新数据库最大值
                        String SYSTMU32 = "UPDATE sys_sequence" +
                                "               SET maxid = maxid + " + redisSequenceSaveFrequency +
                                "             WHERE tablename = '" + key + "'";
                        super.update(SYSTMU32, DbOfModuleidUtils.selectDbByModuleid(moduleid), null);
                        // 查询数据库当前最大值
                        String SYSTMQ03 = "SELECT maxid FROM sys_sequence WHERE tablename = '"+key+"'";
                        SqlRow row = super.findRow(SYSTMQ03, DbOfModuleidUtils.selectDbByModuleid(moduleid), null);
                        if (row != null) {
                            dbMaxid =  row.getLong("maxid");
                        }
                        if (dbMaxid == null) {
                            log.info("数据库不存在,准备 Insert Key:[{}]", key);
                            try {
                                String SYSTMU28 = "INSERT INTO SYS_SEQUENCE (tablename, maxid) VALUES ('" + key + "', 0)";
                                // 不存在就插入
                                super.update(SYSTMU28, DbOfModuleidUtils.selectDbByModuleid(moduleid), null);

                                // 查询数据库现有序列最大值
                                SqlRow row1 = super.findRow(SYSTMQ03, DbOfModuleidUtils.selectDbByModuleid(moduleid), null);
                                if (row1 != null) {
                                    dbMaxid = row1.getLong("maxid");
                                } else {
                                    throw new RuntimeException("数据库异常");
                                }
                            } catch (SQLException e) {
                                String msg = e.getMessage();
                                log.warn("并发新增序列异常 Key:[{}] Msg:[{}]", key, e.getMessage());
                                if (StringUtils.containsAny(msg, "-803", "ORA-00001")) {
                                    continue;
                                }
                                throw e;
                            }
                        }
                        seqThreadLocal.set(dbMaxid);
                        // 正常获取直接跳出
                        break;
                    }
                });
        // 序列号检查
        Long seq = seqThreadLocal.get();
        if (seq == null || seq < 0L) {
            throw new RuntimeException("获取序列号失败");
        }
        seqThreadLocal.remove();
        return seq;
    }



    /**
     * 左补字符串
     * 中文字符按字节计算
     *
     * @param str    字符串
     * @param size   长度
     * @param padStr 补位字符串
     * @return
     */
    private  String alignRight(String str, int size, String padStr) {
        if (str == null) {
            return null;
        }

        if ((padStr == null) || (padStr.length() == 0)) {
            padStr = " ";
        }

        int padLen = padStr.length();
        int strLen = str.length();
        int pads = size - strLen;

        if (pads <= 0) {
            return str;
        }

        if (pads == padLen) {
            return padStr.concat(str);
        } else if (pads < padLen) {
            return padStr.substring(0, pads).concat(str);
        } else {
            char[] padding = new char[pads];
            char[] padChars = padStr.toCharArray();

            for (int i = 0; i < pads; i++) {
                padding[i] = padChars[i % padLen];
            }

            return new String(padding).concat(str);
        }
    }
}
