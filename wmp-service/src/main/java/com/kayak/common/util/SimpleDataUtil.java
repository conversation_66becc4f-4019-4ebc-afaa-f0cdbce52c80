package com.kayak.common.util;

import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * @Description 时间工具类
 * <AUTHOR>
 * @Date 2020/12/10
 */
public class SimpleDataUtil {

    /**
     * @Description 解析timeStamp获取年份
     * @param
     * @return
     * <AUTHOR>
     * @Date 2020/12/10
     **/
    public static String parseTimeStampGetYear(String timeStamp) throws ParseException {
        if(StringUtils.isNotEmpty(timeStamp)){
            SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.US);
            return new SimpleDateFormat("yyyyMMdd").format(sdf.parse(timeStamp));
        }else{
            return "";
        }
    }
    public static String parseTimeStampGetTime(String timeStamp) throws ParseException {
        if(StringUtils.isNotEmpty(timeStamp)){
            SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.US);
            return new SimpleDateFormat("HHmmss").format(sdf.parse(timeStamp));
        }else{
            return "";
        }
    }

    /**
     * 获取日期,日期格式yyyyMMdd
     * @param date
     * @return
     */
    public static String getDateFormat(Date date) {
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        return dateFormat.format(date);
    }

    /**
     * 获取日期,日期格式yyyyMMdd
     * @param date
     * @return
     */
    public static String getTimeFormat(Date date) {
        DateFormat dateFormat = new SimpleDateFormat("HHmmss");
        return dateFormat.format(date);
    }
    /**
     　　 *字符串的日期格式的计算
     　　 */
    public static int daysBetween(String smdate,String bdate) throws ParseException{
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf1=new SimpleDateFormat("yyyyMMdd");
        Calendar cal = Calendar.getInstance();
        long time1 = 0l;
        long time2 = 0l;
        try {
            cal.setTime(sdf.parse(smdate));
            time1 = cal.getTimeInMillis();
            cal.setTime(sdf.parse(bdate));
            time2 = cal.getTimeInMillis();
        }catch (ParseException e){
            //尝试用另一种日期格式解析yyyyMMdd
            cal.setTime(sdf1.parse(smdate));
            time1 = cal.getTimeInMillis();
            cal.setTime(sdf1.parse(bdate));
            time2 = cal.getTimeInMillis();
        }
        long between_days=(time2-time1)/(1000*3600*24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     　　 *字符串的日期格式的计算
     　　 */
    public static int daysBetween2(String smdate,String bdate) throws ParseException{
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.parse(smdate));
        long time1 = cal.getTimeInMillis();
        cal.setTime(sdf.parse(bdate));
        long time2 = cal.getTimeInMillis();
        long between_days=(time2-time1)/(1000*3600*24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     　　 *字符串的日期比较计算
     　　 */
    public static boolean compareDate(String maxDate,String minDate) throws ParseException{
        boolean flag = false;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date max = sdf.parse(maxDate);
        Date min = sdf.parse(minDate);
        if(max.before(min)){
            flag = true;
        }
        return  flag;
    }


}
