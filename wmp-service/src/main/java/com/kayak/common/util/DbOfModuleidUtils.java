package com.kayak.common.util;

import com.kayak.common.constants.SubDatabase;
import org.apache.commons.lang3.StringUtils;

/**
 * 根据moduleid选择数据库
 *
 * <AUTHOR>
 * @date 2021-06-22 19:26
 */
public class DbOfModuleidUtils {

    public static int selectDbByModuleid(String moduleid) {
        //公共服务中心
        if (StringUtils.equals("0",moduleid)){
            return SubDatabase.DATABASE_SYS_CENTER;
        } else if (StringUtils.equals("1", moduleid)) {
            //客户中心
            return SubDatabase.DATABASE_CUST_CENTER;
        } else if (StringUtils.equals("2", moduleid)) {
            //产品中心
            return SubDatabase.DATABASE_SYS_CENTER;
        } else if (StringUtils.equals("5", moduleid)) {
            //理财代销中心
            return SubDatabase.DATABASE_FINA_CENTER;
        } else if (StringUtils.equals("6", moduleid)) {
            //基金代销
            return SubDatabase.DATABASE_FUND_CENTER;
        } else if (StringUtils.equals("9", moduleid)) {
            //余额理财
            return SubDatabase.DATABASE_BALA_CENTER;
        } else if (StringUtils.equals("4", moduleid)) {
            //定投转让
            return SubDatabase.DATABASE_PLAN_CENTER;
        }else {
            //其他统一返回第一个库
            return SubDatabase.DATABASE_SYS_CENTER;
        }
    }

    /**
     * <AUTHOR>
     * @Description 根据moduleid(系统编号)获取数据库
     * @Date 2022/3/7
     * @Param [moduleid]
     * @return int
     **/
    public static int selectDbByModuleids(String moduleid) {

        if (StringUtils.equals("FINA",moduleid)){
            //理财代销中心
            return SubDatabase.DATABASE_FINA_CENTER;
        } else if (StringUtils.equals("FUND", moduleid)) {
            //基金代销
            return SubDatabase.DATABASE_FUND_CENTER;
        } else if (StringUtils.equals("BALA", moduleid)) {
            //余额理财
            return SubDatabase.DATABASE_BALA_CENTER;
        } else if (StringUtils.equals("COMP", moduleid)) {
            //梦想计划(组合中心)
            return SubDatabase.DATABASE_PLAN_CENTER;
        } else {
            //其他统一返回第一个库
            return SubDatabase.DATABASE_SYS_CENTER;
        }
    }


}
