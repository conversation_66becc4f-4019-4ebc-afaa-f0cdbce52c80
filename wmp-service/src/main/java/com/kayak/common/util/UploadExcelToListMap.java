package com.kayak.common.util;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class UploadExcelToListMap {
    /**
     * @Description 读取EXCEL文件，以表头作为key值，拼接成list map对象
     * @param file_path 文件路径
     **/
    public static List<Map> readFile(String file_path) throws IOException, InvalidFormatException {

        Workbook wb = WorkbookFactory.create(new FileInputStream(new File(file_path)));
        // 获得模板总sheet页数
        int totalSheets = wb.getNumberOfSheets();

        ArrayList<Map> result = new ArrayList<>();

        for (int j = 0; j < totalSheets; j++) {
            Sheet sheet = wb.getSheetAt(j);
            int lastRowNum = sheet.getLastRowNum();

            // 第0行为excel表头，作为list map的key值。
            Row row0 = sheet.getRow(0);
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                short lastCellNum = row.getLastCellNum();
                Map map = new HashMap();
                for (int k = 0; k < lastCellNum; k++) {
                    CellType cellTypeEnum = row.getCell(k).getCellTypeEnum();
                    switch (cellTypeEnum){
                        case NUMERIC:
                            map.put(row0.getCell(k).getStringCellValue(),row.getCell(k).getNumericCellValue());
                            break;
                        case ERROR:
                            map.put(row0.getCell(k).getStringCellValue(),"error in cell");
                            break;
                        case BOOLEAN:
                            map.put(row0.getCell(k).getStringCellValue(),row.getCell(k).getBooleanCellValue());
                            break;
                        case FORMULA:
                            // 忽略
                            break;
                        default: // case STRING 和 case _NONE 都设置为 字符型值。
                            map.put(row0.getCell(k).getStringCellValue(),row.getCell(k).getStringCellValue());
                    }

                }
                result.add(map);
            }
        }
        // 关文件
        wb.close();
        return result;
    }
}
