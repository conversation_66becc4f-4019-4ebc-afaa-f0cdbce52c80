package com.kayak.common.util;

// import com.kayak.bala.dao.M902Dao;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.SysUtil;
import com.kayak.core.util.Tools;
import com.kayak.fina.trans.dao.M521Dao;
import com.kayak.fund.dao.M230Dao;
import com.kayak.fund.dao.M231Dao;
import com.kayak.fund.model.M230;
import com.kayak.graphql.model.FetcherData;
import com.kayak.paym.dao.PaymCapitalLogDao;
import com.kayak.paym.model.PaymCapitalLog;
import com.kayak.prod.model.M215;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.dao.M003Dao;
import com.kayak.system.dao.M006Dao;
import com.kayak.system.model.M001;
import com.kayak.system.model.M003;
import com.kayak.system.model.M006;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 报表工具类
 * <AUTHOR>
 * @Date 2021/11/22
 */
@Service
public class ReportformUtil {

    @Autowired
    private M006Dao m006Dao;

    @Autowired
    private M001Dao m001Dao;

    @Autowired
    private M003Dao m003Dao;

    @Autowired
    private M230Dao m230Dao;

    @Autowired
    private M521Dao m521Dao;

    @Autowired
    private M231Dao m231Dao;

    @Autowired
    private M215Service m215Service;

    @Autowired
    private PaymCapitalLogDao paymCapitalLogDao;

    /**
     * 把当前用户登录信息参数集保存在SESSION中的key
     */
    public static final String SYS_USER_PARAMS_SESSION_KEY = "session.sys.user.params";
    /**
     * @Description 根据当前登陆人获取机构信息
     * @param
     * @return
     * <AUTHOR>
     * @Date 2020/12/10
     **/
    public  String getOrgIdForOrgLevel(String sqlName,String userId) throws Exception {
            String str = "";
            // 用户登录信息参数，先尝试从SESSION中取得
//            if (RequestSupport.getLocalRequest() == null) {
//                return null;
//            }
            if(Tools.isBlank(sqlName)){
                sqlName="";
            }
            Map<String,Object> map = new HashMap<>();
            map.put("userid",userId);
            SqlParam<M006> sqlParam = new FetcherData<M006>(map,M006.class);
            SqlResult<M006> users = m006Dao.findUsers(sqlParam);
        if(users != null && users.getRows().size() >0){
            String orgNo = users.getRows().get(0).getOrgno();
            if(Tools.isNotBlank(orgNo)){
                M001 m001 = m001Dao.get(orgNo);

                if(Tools.isNotBlank(m001.getOrglevel()) && m001.getOrglevel().equals("0")){
                    str = "";
                }
//                else if(Tools.isNotBlank(m001.getOrglevel()) && m001.getOrglevel().equals("1")){
//                    str = " and "+sqlName+" BANK_CODE = '"+m001.getOrgno()+"'";
//                }else if(Tools.isNotBlank(m001.getOrglevel()) && m001.getOrglevel().equals("2")){
//                    str = " and "+sqlName+" BRANCH_CODE = '"+m001.getOrgno()+"'";
//                }else{
//                    str = " and "+sqlName+" SUB_BRANCH_CODE = '"+m001.getOrgno()+"'";
//                }
            }
        }
        return str;
    }

    /**
     * @Description 根据输入信息获取custno
     * @param
     * @return
     * <AUTHOR>
     * @Date 2020/12/10
     **/
    public  String getCustNoForCustInfo(String custName,String idCode,String idType,String acctNo) throws Exception {
        String str = "0";
        String custNo = "0";
        if (StringUtils.isNotBlank(acctNo)){
            custNo = m230Dao.getCustNoByAcctNo(acctNo);
        }

        if(Tools.isNotBlank(custName) || Tools.isNotBlank(idCode)){
            Map<String,Object> map = new HashMap<>();
            map.put("custName",custName);
            map.put("idCode",idCode);
            map.put("idType",idType);
            SqlParam<M230> custParams = new FetcherData<>(map, M230.class);
            custParams.setMakeSql(true);
            List<M230> list = m230Dao.findCusts(custParams);
            if (list != null && list.size() > 0 ){
                StringBuffer sb = new StringBuffer();
                if(!custNo.equals("0")){
                    for(int i=0;i<list.size();i++){
                        if(custNo.equals(list.get(i).getCustNo())){
                            str = custNo;
                            break;
                        }
                    }
                }else{
                    if(list.size() == 1){
                        sb.append(list.get(0).getCustNo());
                    }else{
                        for(int i=0;i<list.size();i++){
                            if(Tools.isNotBlank(list.get(i).getCustNo())){
                                if(i == 0) {
                                    sb.append(list.get(i).getCustNo()+"',");
                                }else if( i == list.size() -1){
                                    sb.append("'"+list.get(i).getCustNo());
                                }else{
                                    sb.append("'"+list.get(i).getCustNo()+"',");
                                }
                            }
                        }
                    }

                    str = sb.toString();
                }
            }
        }else if(Tools.isNotBlank(custNo) && !custNo.equals("0")&&Tools.isNotBlank(acctNo)){
            str = custNo;
        }
        if(Tools.isBlank(idCode) && Tools.isBlank(custName) && Tools.isBlank(acctNo)){
            str = null;
        }
        return str;
    }

    /**
     * <AUTHOR>
     * @Description  根据输入信息获取custno
     * @Date 2022/1/13
     * @Param [paraCustNo, custName, idCode, idType, acctNo]
     * @return java.lang.String
     **/
    public  String getCustNoForCustInfos(String paraCustNo,String custName,String idCode,String idType,String acctNo) throws Exception {
        String str = "0";
        String custNo = "0";
        String custNoAc = "";
        //若查询条件的客户号不为空，则custNo为paraCustNo
        if(StringUtils.isNotBlank(paraCustNo)){
            custNo=paraCustNo;
        }
        //根据查询条件的资金账号获得的客户号，若与custNo不相等，则返回0
        if (StringUtils.isNotBlank(acctNo)){
            custNoAc = m230Dao.getCustNoByAcctNo(acctNo);
            if(StringUtils.isNotBlank(custNoAc)){
                if(custNo.equals("0")){
                    custNo=custNoAc;
                }else{
                    if(!custNoAc.equals(custNo)){
                        custNo="0";
                        return str;
                    }
                }
            }else{
                custNo="0";
                return str;
            }
        }
        //根据查询条件的客户名称、证件号和证件类型，获得的客户，若与custNo、custNoAc不相等，则返回0
        if(Tools.isNotBlank(custName) || Tools.isNotBlank(idCode)){
            Map<String,Object> map = new HashMap<>();
            map.put("custName",custName);
            map.put("idCode",idCode);
            map.put("idType",idType);
            SqlParam<M230> custParams = new FetcherData<>(map, M230.class);
            custParams.setMakeSql(true);
            List<M230> list = m230Dao.findCusts(custParams);
            if (list != null && list.size() > 0 ){
                StringBuffer sb = new StringBuffer();
                if(!custNo.equals("0")){
                    List<String> cusList=new ArrayList<>();//根据客户名称、证件号和证件类型，查出的客户号List
                    for(int i=0;i<list.size();i++){
                        cusList.add(list.get(i).getCustNo());
                        if(custNo.equals(list.get(i).getCustNo())){
                            str = custNo;
                            break;
                        }
                    }
                    if(!cusList.contains(custNo)){//若根据资金账号查出了客户号，且不包含在cusList中，则custNo返回0
                        str = "0";
                    }
                }else{
                    if(list.size() == 1){
                        sb.append(list.get(0).getCustNo());
                    }else{
                        for(int i=0;i<list.size();i++){
                            if(Tools.isNotBlank(list.get(i).getCustNo())){
                                if(i == 0) {
                                    sb.append(list.get(i).getCustNo()+"',");
                                }else if( i == list.size() -1){
                                    sb.append("'"+list.get(i).getCustNo());
                                }else{
                                    sb.append("'"+list.get(i).getCustNo()+"',");
                                }
                            }
                        }
                    }

                    str = sb.toString();
                }
            }else{
                str = "0";
            }
        }else if(Tools.isNotBlank(custNo) && !custNo.equals("0")){
            str = custNo;
        }
        if(Tools.isBlank(idCode) && Tools.isBlank(custName) && Tools.isBlank(acctNo) && (Tools.isBlank(custNo)||custNo.equals("0"))){
            str = null;
        }
        return str;
    }

    public List<SqlRow> findParamVal(String val) throws Exception {
        return m003Dao.findParamVal(val);
    }

    public void checkMaxExcel(int dataMaxSize) throws Exception {
        //获取EXCEL最大导出数M926
        List<SqlRow> paramList = findParamVal("max_excel");
        if(paramList != null && paramList.size() > 0 && paramList.get(0).get("paravalue") != null){
            int maxExcel = Integer.parseInt(paramList.get(0).get("paravalue").toString());
            if(dataMaxSize > maxExcel){
                //throw new PromptException("当前EXCEL超出最大限制条数"+maxExcel+"，请加入查询条件减少数据");
                throw new PromptException(String.valueOf(maxExcel));
            }
        }
    }

    public String getFinaTaName(String tano) throws Exception {
        if(Tools.isBlank(tano)){
            return "";
        }
        return m521Dao.getTaNameByTano(tano);
    }

    public String getFundTaName(String tano) throws Exception {
        if(Tools.isBlank(tano)){
            return "";
        }
        return m231Dao.findTaName(tano);
    }

    public String getOrgName(String orgno) throws Exception {
        String orgName = "";
        if(Tools.isBlank(orgno)){
            return "";
        }
        Map<String,Object> map = new HashMap<>();
        map.put("orgno",orgno);
        SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
        SqlResult<M001> m001Info = m001Dao.find(dateParams);
        if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
            orgName = m001Info.getRows().get(0).getOrgname();
        }
        return orgName;
    }

    public String getProdName(String systemNo,String tano,String prodCode) throws Exception {
        String prodName = "";
        Map<String, Object> prodParam = new HashMap<>();
        if(Tools.isNotBlank(prodCode)){
            prodParam.put("prodCode", prodCode);
            if(Tools.isNotBlank(systemNo)){
                prodParam.put("systemNo", systemNo);
            }
            if(Tools.isNotBlank(tano)){
                prodParam.put("supplyCode", tano);
            }
            prodParam.put("legalCode", "1000");
            List<Map<String, String>> prodParaList = m215Service.getProdInfoList(new FetcherData<>(prodParam, M215.class));
            if(prodParaList != null && prodParaList.size() > 0){
                Map<String, String> prodPara = prodParaList.get(0);
                prodName = prodPara.get("prod_name");
            }
        }
        return prodName;
    }

    /**
     * <AUTHOR>
     * @Description 查询资金类流水表信息
     * @Date 2022/2/16
     * @Param [params]
     * @return String
     **/
    public String findPaymCapitalLog(SqlParam<PaymCapitalLog> params) throws Exception {
        String capitalStatus="";
        SqlResult<PaymCapitalLog> sqlResult= paymCapitalLogDao.findPaymCapitalLog(params);
        if(sqlResult.getRows() != null && sqlResult.getRows().size() > 0){
            capitalStatus=sqlResult.getRows().get(0).getCapitalStatus();
        }
        return capitalStatus;
    }

    /**
     * <AUTHOR>
     * @Description 获取余额TA名称
     * @Date 2022/2/22
     * @Param [tano]
     * @return java.lang.String
     **/
    public String getBalaTaName(String tano) throws Exception {
        String taName="";
        if(Tools.isBlank(tano)){
            return "";
        }
        taName=m231Dao.findTaName(tano);
        if(StringUtils.isBlank(taName)){
            taName=m521Dao.getTaNameByTano(tano);
        }
        return taName;
    }

    /**
     * <AUTHOR>
     * @Description 获取余额产品名称
     * @Date 2022/2/22
     * @Param [prodCode]
     * @return java.lang.String
     **/
    public String getBalaProdName(String prodCode){
        // START wangzj 2022/3/10 移除BAlA模块
        String prodName="";
        return prodName;
        // END ####~.~
    }

    /**
     * <AUTHOR>
     * @Description 根据客户名称、证件号和证件类型查询底层交易申请流水的客户号
     * @Date 2022/3/1
     * @Param [custName, idCode, idType]
     * @return java.lang.String
     **/
    public  String getCustNoForCust(String custName,String idCode,String idType) throws Exception {
        String str = "0";
        String custNo = "0";

        if(Tools.isNotBlank(custName) || Tools.isNotBlank(idCode)){
            Map<String,Object> map = new HashMap<>();
            map.put("custName",custName);
            map.put("idCode",idCode);
            map.put("idType",idType);
            SqlParam<M230> custParams = new FetcherData<>(map, M230.class);
            custParams.setMakeSql(true);
            List<M230> list = m230Dao.findCusts(custParams);
            if (list != null && list.size() > 0 ){
                StringBuffer sb = new StringBuffer();
                if(!custNo.equals("0")){
                    for(int i=0;i<list.size();i++){
                        if(custNo.equals(list.get(i).getCustNo())){
                            str = custNo;
                            break;
                        }
                    }
                }else{
                    if(list.size() == 1){
                        sb.append(list.get(0).getCustNo());
                    }else{
                        for(int i=0;i<list.size();i++){
                            if(Tools.isNotBlank(list.get(i).getCustNo())){
                                if(i == 0) {
                                    sb.append(list.get(i).getCustNo()+"',");
                                }else if( i == list.size() -1){
                                    sb.append("'"+list.get(i).getCustNo());
                                }else{
                                    sb.append("'"+list.get(i).getCustNo()+"',");
                                }
                            }
                        }
                    }

                    str = sb.toString();
                }
            }
        }else if(Tools.isNotBlank(custNo) && !custNo.equals("0")){
            str = custNo;
        }
        if(Tools.isBlank(idCode) && Tools.isBlank(custName) ){
            str = null;
        }
        return str;
    }

    /**
     * @Description 根据当前登陆人获取机构信息
     * @param
     * @return
     * <AUTHOR>
     * @Date 2020/12/10
     **/
    public  String getOrgIdForOrgLevel(String sqlName) throws Exception {
        String str = "";
        // 用户登录信息参数，先尝试从SESSION中取得
        if (RequestSupport.getLocalRequest() == null) {
            return null;
        }
        if(Tools.isBlank(sqlName)){
            sqlName="";
        }
        String userId = SysUtil.getSysUserParamValue("sys_user_userid").toString();
        Map<String,Object> map = new HashMap<>();
        map.put("userid",userId);
        SqlParam<M006> sqlParam = new FetcherData<M006>(map,M006.class);
        SqlResult<M006> users = m006Dao.findUsers(sqlParam);
        if(users != null && users.getRows().size() >0){
            String orgNo = users.getRows().get(0).getOrgno();
            if(Tools.isNotBlank(orgNo)){
                M001 m001 = m001Dao.get(orgNo);

                if(Tools.isNotBlank(m001.getOrglevel()) && m001.getOrglevel().equals("0")){
                    str = "";
                }else if(Tools.isNotBlank(m001.getOrglevel()) && m001.getOrglevel().equals("1")){
                    str = " and "+sqlName+" BANK_CODE = '"+m001.getOrgno()+"'";
                }else if(Tools.isNotBlank(m001.getOrglevel()) && m001.getOrglevel().equals("2")){
                    str = " and "+sqlName+" BRANCH_CODE = '"+m001.getOrgno()+"'";
                }else{
                    str = " and "+sqlName+" SUB_BRANCH_CODE = '"+m001.getOrgno()+"'";
                }
            }
        }
        return str;
    }


    /**
     * <AUTHOR>
     * @Description 获取余额产品名称
     * @Date 2022/2/22
     * @Param [prodCode]
     * @return java.lang.String
     **/
    public String getWorkDate() throws Exception {
        String workDate = "";
        Map<String,Object> map = new HashMap<>();
        map.put("paraid","a0000002");
        SqlParam<M003> dateParams = new FetcherData<>(map, M003.class);
        dateParams.setMakeSql(true);
        SqlResult<M003> m001Info = m003Dao.find(dateParams);
        if(m001Info != null && m001Info.getRows().size() > 0){
            workDate = m001Info.getRows().get(0).getParavalue();
        }else{
            workDate = SimpleDataUtil.getDateFormat(new Date());
        }
        return workDate;
    }

}
