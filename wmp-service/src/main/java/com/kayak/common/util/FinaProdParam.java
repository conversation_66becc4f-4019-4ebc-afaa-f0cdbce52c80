package com.kayak.common.util;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 理财代销 产品参数工具类
 *
 * <AUTHOR>
 * @date 2021-06-22 17:02
 */
public class FinaProdParam {

    /**
     * 系统编号
     */
    public static String getSystemNo(Map<String, String> prodInfo) {
        return prodInfo.get("system_no");
    }

    /**
     * 供应商代码
     */
    public static String getSupplyCode(Map<String, String> prodInfo) {
        return prodInfo.get("supply_code");
    }

    /**
     * 产品代码
     */
    public static String getProdCode(Map<String, String> prodInfo) {
        return prodInfo.get("prod_code");
    }

    /**
     * 法人代码
     */
    public static String getLegalCode(Map<String, String> prodInfo) {
        return prodInfo.get("legal_code");
    }

    /**
     * 产品名称
     */
    public static String getProdName(Map<String, String> prodInfo) {
        return prodInfo.get("prod_name");
    }

    /**
     * 认购开始日
     */
    public static String getSubsBeginDate(Map<String, String> prodInfo) {
        return prodInfo.get("subs_begin_date");
    }

    /**
     * 认购结束日
     */
    public static String getSubsEndDate(Map<String, String> prodInfo) {
        return prodInfo.get("subs_end_date");
    }

    /**
     * 成立日
     */
    public static String getEstablishDate(Map<String, String> prodInfo) {
        return prodInfo.get("establish_date");
    }

    /**
     * 开放开始日
     */
    public static String getOpenBeginDate(Map<String, String> prodInfo) {
        return prodInfo.get("open_begin_date");
    }

    /**
     * 开发结束日
     */
    public static String getOpenEndDate(Map<String, String> prodInfo) {
        return prodInfo.get("open_end_date");
    }

    /**
     * 到期日
     */
    public static String getEndDate(Map<String, String> prodInfo) {
        return prodInfo.get("end_date");
    }

    /**
     * 清盘日
     */
    public static String getWindingDate(Map<String, String> prodInfo) {
        return prodInfo.get("winding_date");
    }

    /**
     * 产品类型
     */
    public static String getProdType(Map<String, String> prodInfo) {
        return prodInfo.get("prod_type");
    }

    /**
     * 产品模型
     */
    public static String getProdMode(Map<String, String> prodInfo) {
        return prodInfo.get("prod_mode");
    }

    /**
     * 产品模型号
     */
    public static String getprodTemplateCode(Map<String, String> prodInfo) {
        return prodInfo.get("prod_template_code");
    }
    /**
     * 销售状态
     */
    public static String getSaleStatus(Map<String, String> prodInfo) {
        return prodInfo.get("sale_status");
    }

    /**
     * 产品风险等级
     */
    public static String getProdRiskLevel(Map<String, String> prodInfo) {
        return prodInfo.get("prod_risk_level");
    }

    /**
     * 募集方式
     */
    public static String getRasieType(Map<String, String> prodInfo) {
        return prodInfo.get("rasie_type");
    }

    /**
     * 业绩比较基准
     */
    public static String getBenchmarks(Map<String, String> prodInfo) {
        return prodInfo.get("benchmarks");
    }

    /**
     * 币种
     */
    public static String getCur(Map<String, String> prodInfo) {
        return prodInfo.get("cur");
    }

    /**
     * 产品额度控制方式
     */
    public static String getQuotaCtrlMode(Map<String, String> prodInfo) {
        return prodInfo.get("quota_ctrl_mode");
    }

    /**
     * 是否合格投资者产品
     */
    public static String getInvestflag(Map<String, String> prodInfo) {
        return prodInfo.get("invest_flag");
    }

    /**
     * 是否需要验证客户风险等级
     */
    public static String getIsCheckRisk(Map<String, String> prodInfo) {
        return prodInfo.get("is_check_risk");
    }

    /**
     * 是否需要客户风险等级大于等于产品风险等级
     */
    public static String getIsCheckRiskOk(Map<String, String> prodInfo) {
        return prodInfo.get("is_check_risk_ok");
    }



    // ============================= 产品限制信息获取 start ==================================
    /** 巨额赎回比例 */
    public static BigDecimal getHugeRedeemRatio(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("huge_redeem_ratio"));};
    /** 产品单日可赎回份额 */
    public static BigDecimal getDailyRedeemVol(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("daily_redeem_vol"));};
    /** 单客户自助渠道预约上限 */
    public static int getMaxSingleSelfBooking(Map<String, String> prodInfo){return str2int(prodInfo.get("max_single_self_booking"));};
    /** 最高持有人数 */
    public static int getMaxHoldPeoples(Map<String, String> prodInfo){return str2int(prodInfo.get("max_hold_peoples"));};
    /** 最低持有人数 */
    public static int getMinHoldPeoples(Map<String, String> prodInfo){return str2int(prodInfo.get("min_hold_peoples"));};
    /** 最高持有天数 */
    public static int getMaxHoldDays(Map<String, String> prodInfo){return str2int(prodInfo.get("max_hold_days"));};
    /** 最低持有天数 */
    public static int getMinHoldDays(Map<String, String> prodInfo){return str2int(prodInfo.get("min_hold_days"));};
    /** 年龄下限 */
    public static int getMinAge(Map<String, String> prodInfo){return str2int(prodInfo.get("min_age"));};
    /** 年龄上限 */
    public static int getMaxAge(Map<String, String> prodInfo){return str2int(prodInfo.get("max_age"));};
    /** 产品实时赎回总限额 工作时段 */
    public static BigDecimal getMaxReal(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real"));};
    /** 产品实时赎回总限额 非工作时段 */
    public static BigDecimal getMaxRealOff(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_off"));};
    /** 产品单日累计购买上限（个人） */
    public static BigDecimal getMaxProdDailyBuyP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_prod_daily_buy_p"));};
    /** 个人累计购买上限 */
    public static BigDecimal getMaxTotalBuyP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_total_buy_p"));};
    /** 个人单日累计购买上限 */
    public static BigDecimal getMaxDailyBuyP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_daily_buy_p"));};
    /** 个人最高持有金额 */
    public static BigDecimal getMaxHoldAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_hold_amt_p"));};

    /** 个人认购首次最低金额 */
    public static BigDecimal getMinSubsFirstAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_subs_first_amt_p"));};
    /** 个人认购单笔最大金额 */
    public static BigDecimal getMaxSubsSingleAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_subs_single_amt_p"));};
    /** 个人认购追加起点金额 */
    public static BigDecimal getMinSubsAddAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_subs_add_amt_p"));};
    /** 个人认购金额单位 */
    public static BigDecimal getSubsAmtUnitP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("subs_amt_unit_p"));};
    /** 个人申购金额单位 */
    public static BigDecimal getApplyAmtUnitP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("apply_amt_unit_p"));};
    /** 个人申购首次最低金额 */
    public static BigDecimal getMinApplyFirstAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_apply_first_amt_p"));};
    /** 个人申购追加起点金额 */
    public static BigDecimal getMinApplyAddAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_apply_add_amt_p"));};
    /** 个人申购最高金额 */
    public static BigDecimal getMaxApplyAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_apply_amt_p"));};
    /** 产品单日累计赎回上限（个人） */
    public static BigDecimal getMaxProdDailyRedeemP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_prod_daily_redeem_p"));};
    /** 个人赎回单日累计最大份数 */
    public static BigDecimal getMaxDailyRedeemP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_daily_redeem_p"));};
    /** 个人赎回最高份数 */
    public static BigDecimal getMaxTotalRedeemP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_total_redeem_p"));};
    /** 个人赎回单笔最少份数 */
    public static BigDecimal getMinRedeemSingleVolP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_redeem_single_vol_p"));};
    /** 个人赎回单笔最大份额 */
    public static BigDecimal getMaxRedeemSingleVolP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_redeem_single_vol_p"));};
    /** 个人赎回份额单位 */
    public static BigDecimal getRedeemVolUnitP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("redeem_vol_unit_p"));};
    /** 个人最低持有份数 */
    public static BigDecimal getMinHoldVolP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_hold_vol_p"));};
    /** 个人实时赎回单笔最大金额 */
    public static BigDecimal getMaxRealSingleAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_single_amt_p"));};
    /** 个人实时赎回单日最大金额 工作时段 */
    public static BigDecimal getMaxRealDailyAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_daily_amt_p"));};
    /** 个人实时赎回单笔最大金额 */
    public static BigDecimal getMaxRealSingleAmtOffP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_single_amt_off_p"));};
    /** 个人实时赎回单日最大金额 非工作时段 */
    public static BigDecimal getMaxRealDailyAmtOffP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_daily_amt_off_p"));};
    /** 个人最低理财转换份额 */
    public static BigDecimal getMinConvertVolP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_convert_vol_p"));};
    /** 单客户最高持有金额（个人） */
    public static BigDecimal getMaxSingleHoldAmtP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_single_hold_amt_p"));};
    /** 单客户最高持有比例（个人） */
    public static BigDecimal getMaxSingleHoldRatioP(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_single_hold_ratio_p"));};
    /** 产品单日累计购买上限（机构） */
    public static BigDecimal getMaxProdDailyBuyM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_prod_daily_buy_m"));};
    /** 机构累计购买上限 */
    public static BigDecimal getMaxTotalBuyM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_total_buy_m"));};
    /** 机构单日累计购买上限 */
    public static BigDecimal getMaxDailyBuyM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_daily_buy_m"));};
    /** 机构最高持有金额 */
    public static BigDecimal getMaxHoldAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_hold_amt_m"));};
    /** 机构认购首次最低金额 */
    public static BigDecimal getMinSubsFirstAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_subs_first_amt_m"));};
    /** 机构认购单笔最大金额 */
    public static BigDecimal getMaxSubsSingleAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_subs_single_amt_m"));};
    /** 机构认购追加起点金额 */
    public static BigDecimal getMinSubsAddAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_subs_add_amt_m"));};
    /** 机构认购金额单位 */
    public static BigDecimal getSubsAmtUnitM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("subs_amt_unit_m"));};
    /** 机构申购金额单位 */
    public static BigDecimal getApplyAmtUnitM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("apply_amt_unit_m"));};
    /** 机构申购首次最低金额 */
    public static BigDecimal getMinApplyFirstAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_apply_first_amt_m"));};
    /** 机构申购单笔最大金额 */
    public static BigDecimal getMaxApplySingleAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_apply_single_amt_m"));};
    /** 机构申购追加起点金额 */
    public static BigDecimal getMinApplyAddAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_apply_add_amt_m"));};
    /** 机构申购最高金额 */
    public static BigDecimal getMaxApplyAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_apply_amt_m"));};
    /** 产品单日累计赎回上限 */
    public static BigDecimal getMaxProdDailyRedeemM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_prod_daily_redeem_m"));};
    /** 机构赎回单日累计最大份数 */
    public static BigDecimal getMaxDailyRedeemM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_daily_redeem_m"));};
    /** 机构赎回最高份数 */
    public static BigDecimal getMaxTotalRedeemM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_total_redeem_m"));};
    /** 机构赎回单笔最少份数 */
    public static BigDecimal getMinRedeemSingleVolM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_redeem_single_vol_m"));};
    /** 机构赎回单笔最大份额 */
    public static BigDecimal getMaxRedeemSingleVolM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_redeem_single_vol_m"));};
    /** 机构赎回份额单位 */
    public static BigDecimal getRedeemVolUnitM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("redeem_vol_unit_m"));};
    /** 机构最低持有份数 */
    public static BigDecimal getMinHoldVolM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_hold_vol_m"));};
    /** 机构实时赎回单笔最大金额 */
    public static BigDecimal getMaxRealSingleAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_single_amt_m"));};
    /** 机构实时赎回单日最大金额 */
    public static BigDecimal getMaxRealDailyAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_daily_amt_m"));};
    /** 机构实时赎回单笔最大金额 */
    public static BigDecimal getMaxRealSingleAmtOffM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_single_amt_off_m"));};
    /** 机构实时赎回单日最大金额 */
    public static BigDecimal getMaxRealDailyAmtOffM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_real_daily_amt_off_m"));};
    /** 机构最低理财转换份额 */
    public static BigDecimal getMinConvertVolM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("min_convert_vol_m"));};
    /** 单客户最高持有金额（机构） */
    public static BigDecimal getMaxSingleHoldAmtM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_single_hold_amt_m"));};
    /** 单客户最高持有比例（机构） */
    public static BigDecimal getMaxSingleHoldRatioM(Map<String, String> prodInfo){return str2BigDecimal(prodInfo.get("max_single_hold_ratio_m"));};

    // ============================= 产品限制信息获取 end ==================================


    /** 产品简称 */
    public static String getProdShortName(Map<String,String> prodInfo){return prodInfo.get("prod_short_name");}
    /** 产品登记编码 */
    public static String getRegistCode(Map<String,String> prodInfo){return prodInfo.get("regist_code");}
    /** 父产品代码 */
    public static String getParentCode(Map<String,String> prodInfo){return prodInfo.get("parent_code");}

    /** 管理人 */
    public static String getManagerCode(Map<String,String> prodInfo){return prodInfo.get("manager_code");}
    /** 托管人 */
    public static String getTruteeCode(Map<String,String> prodInfo){return prodInfo.get("trutee_code");}

    /** 分红方式：0-红利再投；1-现金分红 */
    public static String getDefDivMethod(Map<String,String> prodInfo){return prodInfo.get("def_div_method");}
    /** 分红方式是否可修改：0-不可，1-可，默认不可修改 */
    public static String getDivChgFlag(Map<String,String> prodInfo){return prodInfo.get("div_chg_flag");}
    /** 产品面值 */
    public static String getFaceValue(Map<String,String> prodInfo){return prodInfo.get("face_value");}
    /** 发行价格 */
    public static String getPrice(Map<String,String> prodInfo){return prodInfo.get("price");}
    /** 产品净值 */
    public static String getNav(Map<String,String> prodInfo){return prodInfo.get("nav");}
    /** 净值日期 */
    public static String getNavDate(Map<String,String> prodInfo){return prodInfo.get("nav_date");}
    /** 是否可进行额度预留：0-否，1-是 */
    public static String getCanReserve(Map<String,String> prodInfo){return prodInfo.get("can_reserve");}
    /** 预留开始日 */
    public static String getReserveBeginDate(Map<String,String> prodInfo){return prodInfo.get("reserve_begin_date");}
    /** 预留失效日 */
    public static String getReserveInvalidDate(Map<String,String> prodInfo){return prodInfo.get("reserve_invalid_date");}

    /** 认购开始时间 */
    public static String getSubsBeginTime(Map<String,String> prodInfo){return prodInfo.get("subs_begin_time");}

    /** 认购结束时间 */
    public static String getSubsEndTime(Map<String,String> prodInfo){return prodInfo.get("subs_end_time");}

    /** 起息日 */
    public static String getIncomeDate(Map<String,String> prodInfo){return prodInfo.get("income_date");}

    /** 提前到期日 */
    public static String getActualEndDate(Map<String,String> prodInfo){return prodInfo.get("actual_end_date");}

    /** 开市时间（货币型产品实时赎回计算工作时段用） */
    public static String getOpenTime(Map<String,String> prodInfo){return prodInfo.get("open_time");}
    /** 收市时间 */
    public static String getCloseTime(Map<String,String> prodInfo){return prodInfo.get("close_time");}
    /** 折扣方案代码 */
    public static String getDiscountCode(Map<String,String> prodInfo){return prodInfo.get("discount_code");}
    /** 募集资金交收时间 */
    public static String getSubsPayDays(Map<String,String> prodInfo){return prodInfo.get("subs_pay_days");}
    /** TA代码 */
    public static String getApplyPayDays(Map<String,String> prodInfo){return prodInfo.get("apply_pay_days");}
    /** 赎回交收天数 */
    public static String getRedeemPayDays(Map<String,String> prodInfo){return prodInfo.get("redeem_pay_days");}
    /** 认购退款交收天数 */
    public static String getSubsPaybackDays(Map<String,String> prodInfo){return prodInfo.get("subs_payback_days");}
    /** 分红交收天数 */
    public static String getDivPayDays(Map<String,String> prodInfo){return prodInfo.get("div_pay_days");}
    /** 发行失败交收天数 */
    public static String getFailPayDays(Map<String,String> prodInfo){return prodInfo.get("fail_pay_days");}
    /** 终止交收天数 */
    public static String getEndPayDays(Map<String,String> prodInfo){return prodInfo.get("end_pay_days");}
    /** 代理费是否留存：0-否；1-是 */
    public static String getProxyFeeFlag(Map<String,String> prodInfo){return prodInfo.get("proxy_fee_flag");}
    /** 产品发行规模 */
    public static String getProdSize(Map<String,String> prodInfo){return prodInfo.get("prod_size");}
    /** 销售服务费率 */
    public static String getServiceFeeRate(Map<String,String> prodInfo){return prodInfo.get("service_fee_rate");}
    /** 认购资金处理模式：0-冻结，1-扣款 */
    public static String getSubsCapitalMode(Map<String,String> prodInfo){return prodInfo.get("subs_capital_mode");}
    /** 申购资金处理模式：0-冻结，1-扣款，2-日间扣款，日末冻结 */
    public static String getApplyCapitalMode(Map<String,String> prodInfo){return prodInfo.get("apply_capital_mode");}
    /** 首次购买判断标准：0-未曾持有，1-份额为0 */
    public static String getFirstBuyFlag(Map<String,String> prodInfo){return prodInfo.get("first_buy_flag");}
    /** 产品总份数 */
    public static BigDecimal getProdTotalVol(Map<String,String> prodInfo){return str2BigDecimal(prodInfo.get("prod_total_vol"));}
    /** 基金转换状态：0-可转入，可转出；1-只可转入2-只可转出；3-不可转换 */
    public static String getConvertStatus(Map<String,String> prodInfo){return prodInfo.get("convert_status");}
    /** 转托管状态：0-允许所有转托管，1-仅允许场外转托管，2-仅允许跨市场, 3-不允许转托管 */
    public static String getTransferAgencyStatus(Map<String,String> prodInfo){return prodInfo.get("transfer_agency_status");}
    /** 分红发放日 */
    public static String getDivDate(Map<String,String> prodInfo){return prodInfo.get("div_date");}
    /** 权益登记日期 */
    public static String getEquityRegistDate(Map<String,String> prodInfo){return prodInfo.get("equity_regist_date");}
    /** 每日交易允许起始时间 */
    public static String getTransStartTime(Map<String,String> prodInfo){return prodInfo.get("trans_start_time");}
    /** 每日交易允许结束时间 */
    public static String getTransEndTime(Map<String,String> prodInfo){return prodInfo.get("trans_end_time");}
    /** 是否允许打折：0-非；1-是 */
    public static String getDiscountFlag(Map<String,String> prodInfo){return prodInfo.get("discount_flag");}
    /** 转换交收天数 */
    public static String getChangeDays(Map<String,String> prodInfo){return prodInfo.get("change_days");}
    /** 首次上线日期 */
    public static String getFirstOnlineDate(Map<String,String> prodInfo){return prodInfo.get("first_online_date");}
    /** 首次上线时间 */
    public static String getFirstOnlineTime(Map<String,String> prodInfo){return prodInfo.get("first_online_time");}
    /** 产品排序序号 */
    public static String getProdOrder(Map<String,String> prodInfo){return prodInfo.get("prod_order");}
    /** 预约交易提前天数 */
    public static String getAdvanceDays(Map<String,String> prodInfo){return prodInfo.get("advance_days");}
    /** 预约交易开始时间 */
    public static String getAdvanceBeginTime(Map<String,String> prodInfo){return prodInfo.get("advance_begin_time");}
    /** 是否允许质押冻结：0-否；1-是 */
    public static String getImpawnFrozenFlag(Map<String,String> prodInfo){return prodInfo.get("impawn_frozen_flag");}
    /** 是否允许实时赎回：0-否；1-是 */
    public static String getRealtimeRedeemFlag(Map<String,String> prodInfo){return prodInfo.get("realtime_redeem_flag");}
    /** 收市后是否允许实时赎回：0-否；1-是 */
    public static String getCloseRealredeemFlag(Map<String,String> prodInfo){return prodInfo.get("close_realredeem_flag");}
    /** 收市后实时赎回回款方式：0-立即；1-顺延 */
    public static String getCloseRealredeemRepayMethod(Map<String,String> prodInfo){return prodInfo.get("close_realredeem_repay_method");}
    /** 产品工作日方案 */
    public static String getWorkdayPgm(Map<String,String> prodInfo){return prodInfo.get("workday_pgm");}
    /** 投资期限（天） */
    public static String getInvestTerm(Map<String,String> prodInfo){return prodInfo.get("invest_term");}
    /** 认购扣划时间：0-认购接受；1-认购结果 */
    public static String getSubsCutFlag(Map<String,String> prodInfo){return prodInfo.get("subs_cut_flag");}
    /** 创建时间 */
    public static String getCrtTime(Map<String,String> prodInfo){return prodInfo.get("crt_time");}
    /** 更新时间 */
    public static String getUpdTime(Map<String,String> prodInfo){return prodInfo.get("upd_time");}
    /** 是否需要双录：0-否；1-是 */
    public static String getDoubleFlag(Map<String,String> prodInfo){return prodInfo.get("double_flag");}
    /** 产品年天数：360；365；366 */
    public static String getYearDays(Map<String,String> prodInfo){return prodInfo.get("year_days");}
    /** 申购确认N值 */
    public static String getApplyCfmDays(Map<String,String> prodInfo){return prodInfo.get("apply_cfm_days");}
    /** 赎回确认N值 */
    public static String getRedeemCfmDays(Map<String,String> prodInfo){return prodInfo.get("redeem_cfm_days");}
    /** 客户周期净值产品持有份额到期处理方式：0-赎回；1-滚存；2-赎回或滚存 */
    public static String getExpireMode(Map<String,String> prodInfo){return prodInfo.get("expire_mode");}
    /** 认购导出模式：0-当日导出；1-末日导出 */
    public static String getExportMode(Map<String,String> prodInfo){return prodInfo.get("export_mode");}
    /** 冷静期天数 */
    public static String getCoolDays(Map<String,String> prodInfo){return prodInfo.get("cool_days");}
    /** 赎回份额明细处理顺序 */
    public static String getRedeemDetailOrder(Map<String,String> prodInfo){return prodInfo.get("redeem_detail_order");}
    /** 投资标的 */
    public static String getInvestment(Map<String,String> prodInfo){return prodInfo.get("investment");}
    /** 部门编号 */
    public static String getDeptno(Map<String,String> prodInfo){return prodInfo.get("deptno");}
    /** 费用精度处理方式 */
    public static String getFeePrecisionMth(Map<String,String> prodInfo){return prodInfo.get("fee_precision_mth");}
    /** 金额精度处理方式 */
    public static String getMoneyPrecisionMth(Map<String,String> prodInfo){return prodInfo.get("money_precision_mth");}
    /** 份额精度处理方式 */
    public static String getVolPrecisionMth(Map<String,String> prodInfo){return prodInfo.get("vol_precision_mth");}
    /** 分红精度处理方式 */
    public static String getDivPrecisionMth(Map<String,String> prodInfo){return prodInfo.get("div_precision_mth");}
    /** 利息精度处理方式 */
    public static String getInstPrecisionMth(Map<String,String> prodInfo){return prodInfo.get("inst_precision_mth");}
    /** 归资产精度处理方式 */
    public static String getProfitPrecisionMth(Map<String,String> prodInfo){return prodInfo.get("profit_precision_mth");}
    /** 收益率精度处理方式 */
    public static String getRatePrecisionMth(Map<String,String> prodInfo){return prodInfo.get("rate_precision_mth");}
    /** 周期类型 */
    public static String getPeriodType(Map<String,String> prodInfo){return prodInfo.get("period_type");}
    /** 产品基本状态 */
    public static String getProdBaseStatus(Map<String,String> prodInfo){return prodInfo.get("prod_base_status");}


    /** 认购是否允许撤单 */
    public static String getSubsCancelFlag(Map<String,String> prodInfo){return prodInfo.get("subs_cancel_flag");}

    /** 基金转出最低份额 */
    public static BigDecimal getVolLowerLimit(Map<String,String> prodInfo){return str2BigDecimal(prodInfo.get("vol_lower_limit"));}

    /** 基金转出最高份额 */
    public static BigDecimal getVolUpperLimit(Map<String,String> prodInfo){return str2BigDecimal(prodInfo.get("vol_upper_limit"));}

    /** 募集开始日期 */
    public static String getIPOStartDate(Map<String,String> prodInfo){return prodInfo.get("ipo_start_date");}

    /** 募集结束日期 */
    public static String getIPOEndDate(Map<String,String> prodInfo){return prodInfo.get("ipo_end_date");}


    /**
     * 字符串转换成Double类型
     */
    public static Double str2Double(String str) {
        try {
            return Double.parseDouble(str);
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 字符串转换成BigDecimal类型
     */
    public static BigDecimal str2BigDecimal(String str) {
        try {
            return new BigDecimal(str);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 字符串转换成int类型
     */
    public static int str2int(String s) {
        try {
            return Integer.parseInt(s);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
