package com.kayak.common.util;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 基金代销  产品参数工具类
 *
 * <AUTHOR>
 * @date 2021-06-22 19:26
 */
public class FundProdParam {

    /**
     * 系统编号
     */
    public static String getSystemNo(Map<String, String> prodInfo) {
        return prodInfo.get("system_no");
    }

    /**
     * 供应商代码
     */
    public static String getSupplyCode(Map<String, String> prodInfo) {
        return prodInfo.get("supply_code");
    }

    /**
     * 基金代码
     */
    public static String getFundCode(Map<String, String> prodInfo) {
        return prodInfo.get("fund_code");
    }

    /**
     * 法人代码
     */
    public static String getLegalCode(Map<String, String> prodInfo) {
        return prodInfo.get("legal_code");
    }

    //申购金额上限
    public static BigDecimal getBuyUpperAmount(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("buy_upper_amount"));
    }
    //基金转换转入金额上限
    public static BigDecimal getCovertInUpperAmount(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("covert_in_upper_amount"));
    }
    //定时定额申购金额上限
    public static BigDecimal getPeriodSubUpperAmount(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("period_sub_upper_amount"));
    }
    //法人追加认购金额
    public static BigDecimal getInstAppSubsAmnt(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("inst_app_subs_amnt"));
    }
    //法人追加认购份数
    public static BigDecimal getInstAppSubsVol(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("inst_app_subs_vol"));
    }
    //法人首次认购最低金额
    public static BigDecimal getMinAmountByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_amount_by_inst"));
    }
    //法人首次认购最低份数
    public static BigDecimal getMinVolByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_vol_by_inst"));
    }
    //结算币种
    public static String getCurrencyType(Map<String, String> prodInfo) {
        return prodInfo.get("currency_type");
    }
    //定时定额申购的金额
    public static BigDecimal getAmountOfPeriodicSubs(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("amount_of_periodic_subs"));
    }
    //定时定额申购日期
    public static BigDecimal getDateOfPeriodicSubs(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("date_of_periodic_subs"));
    }
    //基金最高赎回份数
    public static BigDecimal getMaxRedemptionVol(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("max_redemption_vol"));
    }
    //基金最低持有份数
    public static BigDecimal getMinAccountBalance(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_account_balance"));
    }
    //个人追加认购份数
    public static BigDecimal getIndiAppSubsVol(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("indi_app_subs_vol"));
    }
    //个人追加认购金额
    public static BigDecimal getIndiAppSubsAmount(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("indi_app_subs_amount"));
    }
    //个人首次认购最低份数
    public static BigDecimal getMinSubsVolByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_subs_vol_by_indi"));
    }
    //个人首次认购最低金额
    public static BigDecimal getMinSubsAmountByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_subs_amount_by_indi"));
    }
    //个人首次申购最低金额
    public static BigDecimal getMinBidsAmountByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_bids_amount_by_indi"));
    }
    //法人首次申购最低金额
    public static BigDecimal getMinBidsAmountByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_bids_amount_by_inst"));
    }
    //个人追加申购最低金额
    public static BigDecimal getMinAppBidsAmountByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_app_bids_amount_by_indi"));
    }
    //法人追加申购最低金额
    public static BigDecimal getMinAppBidsAmountByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_app_bids_amount_by_inst"));
    }
    //基金最少赎回份数
    public static BigDecimal getMinRedemptionVol(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_redemption_vol"));
    }
    //最低基金转换份数
    public static BigDecimal getMinInterconvertVol(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("min_interconvert_vol"));
    }
    //个人最大申购金额
    public static BigDecimal getIndiMaxPurchase(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("indi_max_purchase"));
    }
    //法人最大申购金额
    public static BigDecimal getInstMaxPurchase(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("inst_max_purchase"));
    }
    //个人当日累计购买最大金额
    public static BigDecimal getIndiDayMaxSumBuy(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("indi_day_max_sum_buy"));
    }
    //法人当日累计购买最大金额
    public static BigDecimal getInstDayMaxSumBuy(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("inst_day_max_sum_buy"));
    }
    //个人当日累计赎回最大份额
    public static BigDecimal getIndiDayMaxSumRedeem(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("indi_day_max_sum_redeem"));
    }
    //法人当日累计赎回最大份额
    public static BigDecimal getInstDayMaxSumRedeem(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("inst_day_max_sum_redeem"));
    }
    //个人最大赎回份额
    public static BigDecimal getIndiMaxRedeem(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("indi_max_redeem"));
    }
    //法人最大赎回份额
    public static BigDecimal getInstMaxRedeem(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("inst_max_redeem"));
    }
    // 个人最高认购份数
    public static BigDecimal getMaxSubsVolByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("max_subs_vol_by_indi"));
    }
    // 个人最高认购金额
    public static BigDecimal getMaxSubsAmountByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("max_subs_amount_by_indi"));
    }
    // 法人最高认购份数
    public static BigDecimal getMaxSubsVolByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("max_subs_vol_by_inst"));
    }
    // 法人最高认购金额
    public static BigDecimal getMaxSubsAmountByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("max_subs_amount_by_inst"));
    }
    // 个人认购份数单位
    public static BigDecimal getUnitSubsVolByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("unit_subs_vol_by_indi"));
    }
    // 个人认购金额单位
    public static BigDecimal getUnitSubsAmountByIndi(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("unit_subs_amount_by_indi"));
    }
    // 法人认购份数单位
    public static BigDecimal getUnitSubsVolByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("unit_subs_vol_by_inst"));
    }
    // 法人认购金额单位
    public static BigDecimal getUnitSubsAmountByInst(Map<String, String> prodInfo) {
        return str2BigDecimal(prodInfo.get("unit_subs_amount_by_inst"));
    }
    /** 产品净值 */
    public static String getNav(Map<String,String> prodInfo){return prodInfo.get("nav");}

    /**
     * 字符串转换成Double类型
     */
    public static Double str2Double(String str) {
        try {
            return Double.parseDouble(str);
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 字符串转换成BigDecimal类型
     */
    public static BigDecimal str2BigDecimal(String str) {
        try {
            return new BigDecimal(str);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 字符串转换成int类型
     */
    public static int str2int(String s) {
        try {
            return Integer.parseInt(s);
        } catch (NumberFormatException e) {
            return 0;
        }
    }



}
