package com.kayak.common.base;

import com.kayak.cache.util.CacheUtil;
import com.kayak.common.constants.GlobalConstants;


public class BaseModel {

    //sys_param中的最高权限法人代码
    private String superLegalCode = CacheUtil.getSystemParam(GlobalConstants.SUPER_LEGAL_CODE);

    /**
     * 登录用户ID
     * add by wangzj
     */
    private String userid;

    public String getSuperLegalCode() {
        return superLegalCode;
    }

    public void setSuperLegalCode(String superLegalCode) {
        this.superLegalCode = superLegalCode;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }
}
