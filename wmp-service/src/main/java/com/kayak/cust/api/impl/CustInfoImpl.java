package com.kayak.cust.api.impl;

import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.api.ICustInfo;
import com.kayak.cust.dao.M101Dao;
import com.kayak.cust.model.M101;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 客户中心-联机服务-客户信息查询
 * <AUTHOR>
 * @Date 2020/11/30
 */
@Service
public class CustInfoImpl implements ICustInfo {
    @Autowired
    private M101Dao m101Dao;


    @Override
    public M101 getCustInfo(String custNo, String exceptionNoticeFlags) throws Exception{
        Map<String,Object> map = new HashMap<>();
        map.put("custNo",custNo);
        SqlParam<M101> params = new FetcherData<>(map, M101.class);
        params.setMakeSql(true);
        try {
            SqlResult<M101> custInfos = m101Dao.findCustInfos(params,"");
            return custInfos.getRows().size()>0 ? custInfos.getRows().get(0) : null;
        } catch (Exception e) {
            if(exceptionNoticeFlags.equals("0")){
                throw new Exception(e.getMessage());
            }else{
                return null;
            }
        }
    }

    /**
     * @Description 根据客户号或资金账号或证件类型和证件号码 获取客户资金账号信息
     * @param custNo 客户号
     * @param acctNo 资金账号
     * @param idType 证件类型
     * @param idCode 证件号码
     * @return
     * <AUTHOR>
     * @Date 2020/12/9
     **/
    @Override
    public List<M101> getCustAcctInfo(String custNo, String acctNo, String idType, String idCode) throws Exception {
        String sql = "select info.cust_type,info.cust_no,info.cust_name,info.id_type,info.id_code,acct.acct_no,acct.cur,acct.trans_acct_no " +
                " from cust_info info inner join cust_trans_acct acct on info.cust_no=acct.cust_no where 1=1 ";
        if(StringUtils.isNotEmpty(custNo)){
            sql = sql.concat(" and info.cust_no = '"+custNo+"'");
        }
        if(StringUtils.isNotEmpty(acctNo)){
            sql = sql.concat(" and acct.acct_no = '"+acctNo+"'");
        }
        if(StringUtils.isNotEmpty(idType) && StringUtils.isNotEmpty(idCode)){
            sql = sql.concat(" and info.id_type = '"+idType+"'");
            sql = sql.concat(" and info.id_code = '"+idCode+"'");
        }
        Map<String, Object> params = new HashMap<>();
        FetcherData<M101> custInfoFetcherData = new FetcherData<>(params, M101.class);
        SqlResult<M101> custInfos = m101Dao.getCustAcctInfo(custInfoFetcherData,sql);
        return custInfos.getRows().stream().collect(Collectors.toList());
    }

    /**
     * @Description 根据客户号或资金账号 获取客户资金账号信息
     * @param custNo 客户号
     * @param acctNo 资金账号
     * @return
     * <AUTHOR>
     * @Date 2020/12/9
     **/
    @Override
    public List<M101> queryCustAcctInfo(String custNo, String acctNo, String custName, String custType) throws Exception {
        String sql = "select info.cust_type,info.cust_no,info.cust_name,info.id_type,info.id_code,acct.acct_no,acct.cur,acct.trans_acct_no " +
                " from cust_info info inner join cust_trans_acct acct on info.cust_no=acct.cust_no where 1=1 ";
        if(StringUtils.isNotEmpty(custNo)){
            sql = sql.concat(" and info.cust_no = '"+custNo+"'");
        }
        if(StringUtils.isNotEmpty(acctNo)){
            sql = sql.concat(" and acct.acct_no = '"+acctNo+"'");
        }
        if(StringUtils.isNotEmpty(custName)){
            sql = sql.concat(" and info.cust_name like '%"+custName+"%'");
        }
        if(StringUtils.isNotEmpty(custType)){
            sql = sql.concat(" and info.cust_type = '"+custType+"'");
        }
        Map<String, Object> params = new HashMap<>();
        FetcherData<M101> custInfoFetcherData = new FetcherData<>(params, M101.class);
        SqlResult<M101> custInfos = m101Dao.getCustAcctInfo(custInfoFetcherData,sql);
        return custInfos.getRows().stream().collect(Collectors.toList());
    }

}
