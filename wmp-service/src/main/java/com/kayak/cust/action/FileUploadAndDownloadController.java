package com.kayak.cust.action;

import com.alibaba.excel.util.StringUtils;
import com.kayak.common.util.ExcelUtils;
import com.kayak.common.util.UploadExcelToListMap;
import com.kayak.core.action.BaseController;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.system.SysBeans;
import com.kayak.core.util.Tools;
import com.kayak.graphql.annotation.GraphQLModel;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.dao.M221Dao;
import com.kayak.prod.model.M221;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @Description 文件上传与下载
 * <AUTHOR>
 * @Date 2020/12/1
 */
@Scope
@RestController
public class FileUploadAndDownloadController extends BaseController {

    @Value("${upload.path:null}")
    private String temPath;

    @Autowired
    private M221Dao m221Dao;

    @PostMapping("/excel/downGroupTemplate.json")
    @ResponseBody
    public void download(HttpServletResponse response) {
        Map<String, Object> params = RequestSupport.getParameters();
//        String filePath = Tools.obj2Str(params.get("interfaceFileRoute"));
        String filePath = temPath;
        String fileName = Tools.obj2Str(params.get("interfaceFileName"));
        String fileUploadDate = Tools.obj2Str(params.get("interfaceUploadDate"));
        String fileInterfaceFileRoute = Tools.obj2Str(params.get("interfaceFileRoute"));
        String fileUploadPath = "";
        try (OutputStream os = response.getOutputStream();) {
            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", "filename");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setHeader("filename", fileName);
            if(Tools.isNotBlank(fileInterfaceFileRoute)){
                fileUploadPath = fileInterfaceFileRoute;
            }else if (Tools.isNotBlank(fileUploadDate)){
                fileUploadPath = filePath + "/"+fileUploadDate+ "/" + fileName;
            }else{
                fileUploadPath = filePath + "/" + fileName;
            }

            File file = new File(fileUploadPath);
            os.write(FileUtils.readFileToByteArray(file));
            os.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @PostMapping("/excel/downsite.json")
    @ResponseBody
    public void download1(HttpServletResponse response) throws Exception {
        Map<String, Object> params = RequestSupport.getParameters();
//        String filePath = Tools.obj2Str(params.get("interfaceFileRoute"));
        M221 m221 = m221Dao.get(params.get("stencilid").toString());


        String fileName = Tools.obj2Str(m221.getUploadurl().substring(10));

        String filePath = temPath + m221.getUploadurl().substring(0, 9);
        //String fileName = Tools.obj2Str(params.get("interfaceFileName"));
        try (OutputStream os = response.getOutputStream()) {
            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", "filename");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setHeader("filename", fileName);
            File file = new File(filePath + "/" + fileName);
            os.write(FileUtils.readFileToByteArray(file));
            os.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @PostMapping(value = "/excel/uploadGroupData.json")
    @ResponseBody
    public String uploadExcel(@RequestParam(value = "file", required = false) MultipartFile file, HttpServletResponse response) {
        Map<String, Object> params = RequestSupport.getParameters();
        String modelClassName = Tools.obj2Str(params.get("modelClassName"));
//        String uploadDir = Tools.obj2Str(params.get("path")); //放置路径与名称
        String osName = System.getProperty("os.name");
        String uploadDir = osName.toLowerCase().startsWith("win") ? "C:/wmp/" : temPath;
        String action = Tools.obj2Str(params.get("action"));
        // 0 表示仅上传文件；1 表示上传且将数据解析存入数据表
        Integer isInsert = Tools.obj2Int(params.get("isInsert"));
        Class<?> modelClass = getClass(modelClassName);
        if (isInsert == 1 && modelClass == null) {
            log.error("获取class失败：" + modelClassName);
            return RequestSupport.updateReturnJson(false, "获取class失败", null).toString();
        }
        Object fetcherBean = this.getBean(modelClass);
        if (isInsert == 1 && fetcherBean == null) {
            log.error("获取操作对象失败，无fetcher配置对应实例，路径：" + modelClassName);
            return RequestSupport.updateReturnJson(false, "无法获取service", null).toString();
        }
        String fileName = file.getOriginalFilename();

        //为了方便整理生成指定文件名称 ： uploadDir/YYMMDD/xxxx.extension
        String uploadFileName = ExcelUtils.buildFilePathByExtension(uploadDir, fileName.substring(fileName.lastIndexOf(".") + 1));

        File uploadFile = null;
        try {

            uploadFile = new File(uploadFileName);
            uploadFile.mkdirs();
            //保存文件
            file.transferTo(uploadFile);

            if (isInsert == 1) { // 将数据存入数据表
                //文件后缀
                String extension = null;
                if (fileName.contains(".")) {
                    extension = fileName.substring(fileName.lastIndexOf(".") + 1);
                }
                if (!"xlsx".equals(extension) && !"xls".equals(extension)) {
                    return RequestSupport.updateReturnJson(false, "请上传Excel文件" + fileName, null).toString();
                }

                if (Tools.strIsEmpty(uploadDir)) {// 如果有指定上传的文件夹，这使用上传的文件夹作为上传目录
                    return RequestSupport.updateReturnJson(false, "请指定文件上传目录" + fileName, null).toString();
                }

                List<Map> maps = UploadExcelToListMap.readFile(uploadFileName);

                // 为每一条数据集路添加groupCode字段
                for (Map map : maps) {
                    map.put("groupCode", Tools.obj2Str(params.get("groupCode")));
                }
//            for (Map map : maps){
//                return (String)this.execAction(modelClass,action,fetcherBean,map);//插入表
//            }
                return (String) this.execActionGen(action, fetcherBean, maps);
            }


        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return RequestSupport.updateReturnJson(false, "文件上传失败！" + fileName, null).toString();
    }

    private Object getBean(Class<?> modelClass) {
        // 获取操作对象实例
        GraphQLModel graphQLModel = modelClass.getAnnotation(GraphQLModel.class);
        String fetcher = graphQLModel.fetcher();
        return SysBeans.getBean(fetcher);
    }

    private Class<?> getClass(String modelClassName) {
        if (StringUtils.isEmpty(modelClassName)) {
            return null;
        }
        Class<?> modelClass = null;
        try {
            modelClass = Class.forName(modelClassName);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return modelClass;
    }

    private Object execAction(Class<?> modelClass, String action, Object fetcherBean, Map<String, Object> _params) {
        Method method = null;
        try {
            method = fetcherBean.getClass().getMethod(action, SqlParam.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        try {
            return method.invoke(fetcherBean, new FetcherData(_params, modelClass));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param action
     * @return
     * @Description
     * <AUTHOR>
     * @Date 2020/12/2
     **/
    private Object execActionGen(String action, Object fetcherBean, List<Map> maps) {
        Method method = null;
        try {
            method = fetcherBean.getClass().getMethod(action, List.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        try {
            return method.invoke(fetcherBean, maps);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @SuppressWarnings({"rawtypes", "unchecked"})
    @PostMapping(value = "getImage.json")
    public void getImage(@RequestParam(value = "url") String urlString, HttpServletResponse response) {
        URL url = null;
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            url = new URL(urlString);
            // 打开连接
            URLConnection con = url.openConnection();
            // 请求超时:5s
            con.setConnectTimeout(5 * 1000);
            inputStream = con.getInputStream();

            byte[] bytes = new byte[1024];
            // 读取到的数据长度
            int length;
            outputStream = response.getOutputStream();
            // 读取
            while ((length = inputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, length);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}

