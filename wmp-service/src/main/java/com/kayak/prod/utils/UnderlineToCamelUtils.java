package com.kayak.prod.utils;

import com.kayak.core.sql.SqlRow;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UnderlineToCamelUtils {
    /**
     * 下划线格式字符串转换为驼峰格式字符串2
     *
     * @param param
     * @return
     */
    public static String underlineToCamel(String param) {
        if (param == null || "".equals(param.trim())) {
            return "";
        }
        StringBuilder sb = new StringBuilder(param);
        Matcher mc = Pattern.compile("_").matcher(param);
        int i = 0;
        while (mc.find()) {
            int position = mc.end() - (i++);
            sb.replace(position - 1, position + 1, sb.substring(position, position + 1).toUpperCase());
        }
        return sb.toString();
    }

    public static List<SqlRow> sqlRowToCamel(List<SqlRow> list) {
        List<SqlRow> sqlRowList = new ArrayList<>();
        if (list != null){
            for (SqlRow sqlRow : list){
                SqlRow newSqlRow = new SqlRow();
                for (String key : sqlRow.keySet()){
                    newSqlRow.put(underlineToCamel(key),sqlRow.get(key));
                }
                sqlRowList.add(newSqlRow);
            }
        }
        return sqlRowList;
    }
}
