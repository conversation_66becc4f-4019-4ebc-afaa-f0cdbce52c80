package com.kayak.prod.utils;

import com.dcfs.fts.client.*;
import com.dcfs.fts.common.FtpException;
import com.dcfs.fts.constant.FTPConfig;
import com.kayak.plan.model.MD03;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

/**
 * <AUTHOR>
 * @desc 泉州银行云服务上传、下载（只适用于泉州银行）
 * @date 2021-07-23
 */
public class QZFileUtils {
    public static Logger logger = LoggerFactory.getLogger(QZFileUtils.class);

    public static final int MODE = 0;


    /**
     * 将远程文件下载到本地
     * @param remoteFilePath 远程文件路径
     * @param remoteFileName 远程文件名
     * @param localFile 本地文件
     * @param TransCode 传输代码：节点组名称+服务码+场景码
     * @param mode 0：不加密；1：加密；
     * 节点组名称：4位大写字母，字母来源为行内系统英文简称
     * 服务码（11位）：业务类别（4位）+性质（1位）+预留位（2位）+顺序号（4位）
     *      业务类别：存款类0100；贷款类0200；支付类：0300；外汇类0400；理财类0500；资金类：0600；
     *      银行卡类：0700；现金管理类0800；现金凭证类0900；国际结算类1000；综合类1100；中间业务类：1200；
     *      性质：金融类1；非金融类2；查询类3；冲正类4；文件类5
     *      预留位：用于服务的版本号跟踪，默认值00
     *      顺序号：数字编号，从0001开始
     * 场景码：2位 01-99
     * @return 是否下载成功
     */
    public static boolean downloadFile(String remoteFilePath, String remoteFileName, File localFile, String TransCode, int mode) throws IOException, FtpException {
        String remotePath = remoteFilePath + remoteFileName;
        FTPConfig.config = false;
        FTPConfig.serverConnect="168.168.241.158:6002";
        FTPConfig.uid="FASM";
        FTPConfig.passwd="123456";
        logger.info("服务器地址:"+remotePath);
        logger.info("本地存放地址:"+localFile.toString());
        logger.info("传输码:"+TransCode);
        FtpGetStream ftpGetStream = new FtpGetStream(remotePath, TransCode, mode, FtpClientConfig.getInstance());
        // 调用下载
        if (ftpGetStream.doGetFile()) {
            logger.info("下载文件成功");
            // 获取文件内容
            byte[] fileBytes = ftpGetStream.getFileBytes();
            FileOutputStream fileOutputStream = new FileOutputStream(localFile);
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
            bufferedOutputStream.write(fileBytes);
            bufferedOutputStream.flush();
        }else {
            logger.error("下载文件失败");
            return false;
        }
        return true;
    }


    /**
     * 将本地文件上传到服务器
     * @param localFile 本地文件
     * @param remoteFileName 远程文件名
     * @param TransCode 传输代码：节点组名称+服务码+场景码
     * @param mode 0：不加密；1：加密；
     * 节点组名称：4位大写字母，字母来源为行内系统英文简称
     * 服务码（11位）：业务类别（4位）+性质（1位）+预留位（2位）+顺序号（4位）
     *      业务类别：存款类0100；贷款类0200；支付类：0300；外汇类0400；理财类0500；资金类：0600；
     *      银行卡类：0700；现金管理类0800；现金凭证类0900；国际结算类1000；综合类1100；中间业务类：1200；
     *      性质：金融类1；非金融类2；查询类3；冲正类4；文件类5
     *      预留位：用于服务的版本号跟踪，默认值00
     *      顺序号：数字编号，从0001开始
     * 场景码：2位 01-99
     * @return 远程文件全路径
     * @throws IOException
     * @throws FtpException
     */
    public static String uploadFile(File localFile, String remoteFileName, String TransCode, int mode) throws IOException, FtpException {
        FileReader fileReader = new FileReader(localFile);
        BufferedReader bufferedReader = new BufferedReader(fileReader);
        byte[] fileBytes = IOUtils.toByteArray(bufferedReader, "UTF-8");

        FTPConfig.config = false;
        FTPConfig.serverConnect="168.168.241.158:6002";
        FTPConfig.uid="FASM";
        FTPConfig.passwd="123456";
        FtpPutStream ftpPutStream = new FtpPutStream(fileBytes, remoteFileName, TransCode, mode, FtpClientConfig.getInstance());

        // 获取远程文件全路径
        String path = ftpPutStream.doPutFile();
        logger.info("远程文件path：{}", path);
        return path;
    }

    /**
     * 将本地文件上传到服务器
     * @param localFile 本地文件
     * @param remoteFileName 远程文件名
     * @param transCode 每个服务的交易码参考esb文档
     * @return 远程文件全路径
     * @throws IOException
     * @throws FtpException
     */
    public static String uploadFile(File localFile, String remoteFileName, String transCode) throws IOException, FtpException {
        return uploadFile(localFile, remoteFileName, transCode, MODE);
    }



    /**
     * 文件上传
     * @param localPath 本地文件路径（包含文件名）：为绝对路径
     * @param remotePath 上传到数据节点的远程文件路径（包含文件名）：为相对路径
     * @param transCode 传输代码
     * @param mode 传输类型; 0：不处理	1:加密  2：压缩  3：加密+压缩  4:分片  5： 加密+分片  6：压缩+分片  7：加密+压缩+分片。
     */
    public static String uploadFile(String localPath, String remotePath, String transCode, int mode) throws IOException, FtpException {
        FTPConfig.config = false;
        FTPConfig.serverConnect="168.168.241.158:6002";
        FTPConfig.uid="FASM";
        FTPConfig.passwd="123456";
        FtpPut ftpPut = new FtpPut(localPath, remotePath, transCode, mode, FtpClientConfig.getInstance());
        String path = ftpPut.doPutFile();
        return path;
    }

    /**
     * 文件下载
     * @param remotePath 本地文件路径（包含文件名）：为绝对路径
     * @param localPath 本地文件路径（包含文件名）：为绝对路径
     * @param transCode 传输代码
     * @param mode 传输类型; 0：不处理	1:加密  2：压缩  3：加密+压缩  4:分片  5： 加密+分片  6：压缩+分片  7：加密+压缩+分片。
     */
    public static boolean downloadFile(String remotePath, String localPath, String transCode, int mode) throws Exception {
        FTPConfig.config = false;
        FTPConfig.serverConnect="168.168.241.158:6002";
        FTPConfig.uid="FASM";
        FTPConfig.passwd="123456";
        FtpGet ftpGet = new FtpGet(remotePath, localPath, transCode, mode, FtpClientConfig.getInstance());
        return ftpGet.doGetFile();
    }

}
