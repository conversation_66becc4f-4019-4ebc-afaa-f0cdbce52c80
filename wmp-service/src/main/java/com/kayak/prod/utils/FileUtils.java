package com.kayak.prod.utils;

import java.io.*;
import java.nio.charset.Charset;
import java.util.*;
import java.util.regex.Pattern;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import com.kayak.core.sql.SqlResult;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.CharEncoding;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

/**
 * <p>
 * 文件操作工具类
 * </p>
 *
 * <AUTHOR>
 */
public class FileUtils {

	private static Logger log = LoggerFactory.getLogger(FileUtils.class);

	/**
	 * 定义缓存大小
	 */
	private static final int CACHE_SIZE = 1024;

	/**
	 * 定义GBk(使用GBK编码可以避免压缩中文文件名乱码)
	 */
	private static final String CHINESE_CHARSET = "GBK";

	/**
	 * 定义UTF-8作为默认编码
	 */
	private static final String UTF_CHARSET = CharEncoding.UTF_8;

	/**
	 * 自动补全目录格式
	 *
	 * @param path
	 *            文件夹路径
	 * @return 返回完整的目录格式
	 */
	public static String completedDir(String path) {
		// 判断目录是否有分隔符结束
		/*
		 * if (!path.endsWith(File.separator)) { path = path + File.separator; }
		 */

		return path;
	}


	/**
	 * 将传入的List<LinkedHashMap>数据生成以分隔符分割的文件
	 * @param absolutePath  文件绝对路径
	 * @param mapList  有序存到LinkedHashMap的数据
	 * @param separator  分隔符
	 * @param isAddHead  是否需要在头部添加字段名
	 * @return 返回文件是否成功
	 * @throws IOException
	 */
	public static boolean writeFileDate(String absolutePath, List<LinkedHashMap<String,Object>> mapList , String separator, boolean isAddHead) throws IOException {
		if (mapList == null || mapList.size()==0) {
			log.error("写入文件数据为空！");
			return false;
		}
		FileOutputStream out = null;
		OutputStreamWriter fw = null;
		BufferedWriter output = null;
		try {
			File file = new File(absolutePath);
			File dirFile = file.getParentFile();
			if (!dirFile.exists()) {
				dirFile.mkdirs();
			}
			file.createNewFile();

			out = new FileOutputStream(file);
			fw = new OutputStreamWriter(out, UTF_CHARSET);
			output = new BufferedWriter(fw);

			StringBuffer sb = new StringBuffer("");
			if (isAddHead) {
				mapList.get(0).entrySet().stream().forEach(o -> {
					sb.append(o.getKey());
					sb.append(separator);
				});
				sb.deleteCharAt(sb.length() - separator.length());
				output.write(sb.toString());
				output.write(System.getProperty("line.separator"));
				sb.setLength(0);
			}
			//写入文件每行数据
			for (int i=0; i < mapList.size(); i++) {
				mapList.get(i).entrySet().stream().forEach(o -> {
					sb.append(o.getValue());
					sb.append(separator);
				});
				sb.deleteCharAt(sb.length() - separator.length());
				output.write(sb.toString());
				output.write(System.getProperty("line.separator"));
				sb.setLength(0);
			}
		} finally {
			if (output != null) {
				output.close();
			}
			if (fw != null) {
				fw.close();
			}
			if (out != null) {
				out.close();
			}
		}
		return true;
	}


	/**
	 * 获取文件总行数
	 *
	 * @param file
	 *            文件对象
	 * @return 总行数
	 * @throws IOException
	 */
	public static int getCountFileLines(File file) throws IOException {

		// 定义变量Lines作为行号
		int lines = 0;

		// 定义文件流
		InputStreamReader in = new InputStreamReader(new FileInputStream(file), UTF_CHARSET);

		LineNumberReader reader = new LineNumberReader(in);

		String s = reader.readLine();
		// 如果有内容就加一行
		while (s != null) {
			lines++;
			s = reader.readLine();
		}
		reader.close();
		return lines;
	}

	/**
	 * 得到文件对象
	 *
	 * @param absolutePath
	 *            文件完整路径
	 * @return 文件对象
	 */
	public static File getFileObj(String absolutePath) {
		return new File(absolutePath);
	}

	/**
	 * 得到文件对象
	 *
	 * @param path
	 *            文件目录
	 * @param filename
	 *            文件名称
	 * @return 文件对象
	 */
	public static File getFileObj(String path, String filename) {
		path = completedDir(path);
		return getFileObj(path + filename);
	}

	/**
	 * 构建目录
	 * 
	 * @param outputDir
	 * @param subDir
	 */
	public static void createDirectory(String outputDir, String subDir) {
		File file = new File(outputDir);
		if (!(subDir == null || "".equals(subDir.trim()))) {// 子目录不为空
			file = new File(outputDir + "/" + subDir);
		}
		if (!file.exists()) {
			if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
			file.mkdirs();
		}
	}

	/**
	 * 创建目录(若改目录已经存在则创建失败)
	 *
	 * @param file
	 *            文件对象
	 * @return 创建文件夹成功反正true, 否则返回false
	 */
	public static boolean createDir(File file) {

		// 判断目录是否存在
		if (file.exists()) {
			log.error("文件目录已经存在:" + file.getAbsolutePath());
			return false;
		} else {
			if (file.mkdirs()) {
				log.info("创建文件目录成功,路径为:" + file.getAbsolutePath());
				return true;
			} else {
				return false;
			}
		}

	}

	/**
	 * 创建目录(若改目录已经存在则创建失败)
	 *
	 * @param path
	 *            文件目录路径
	 * @return 创建文件夹成功反正true, 否则返回false
	 */
	public static boolean createDir(String path) {
		return createDir(getFileObj(path));
	}

	/**
	 * 删除目录(删除该目录,并删除目录里面的所有文件)
	 *
	 * @param file
	 *            文件对象
	 * @return 如果删除成功则返回true, 失败则返回false
	 */
	public static boolean removeDir(File file) {

		// 定义标记flag
		boolean flag = true;

		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!file.exists() || !file.isDirectory()) {
			log.error("文件目录不存在,或者不是一个目录:" + file.getAbsolutePath());
			return false;
		}
		// 获得传入路径下的所有文件
		File[] files = file.listFiles();

		if (files != null) {

			// 循环遍历删除文件夹下的所有文件(包括子目录)
			for (int i = 0; i < files.length; i++) {

				// 如果是文件则删除文件 如果是文件夹则递归删除里面的文件(包括文件夹)
				if (files[i].isFile()) {
					flag = removeFile(files[i].getAbsolutePath());
					// 如果删除失败，则跳出
					if (!flag) {
						break;
					}
				} else {
					flag = removeDir(files[i].getAbsolutePath());
					// 如果删除失败，则跳出
					if (!flag) {
						break;
					}
				}
			}
		}

		if (!flag) {
			return false;
		}
		// 删除当前目录
		if (file.delete()) {
			log.info("删除目录成功!" + file.getAbsolutePath());
			return true;
		} else {
			log.error("删除目录失败!" + file.getAbsolutePath());
			return false;
		}

	}

	/**
	 * 删除目录(删除该目录,并删除目录里面的所有文件)
	 *
	 * @param path
	 *            文件目录
	 * @return 如果删除成功则返回true, 失败则返回false
	 */
	public static boolean removeDir(String path) {
		return removeDir(getFileObj(path));
	}

	/**
	 * 删除当前目录下所有指定后缀结尾的文件(不包含子目录里面的文件)
	 *
	 * @param file
	 *            文件对象
	 * @param suffix
	 *            文件后缀
	 * @return 如果删除成功则返回true, 失败则返回false
	 */
	public static boolean removeCurrentDirSpecialFile(File file, String suffix) {

		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!file.exists() || !file.isDirectory()) {
			log.error("文件目录不存在,或者不是一个目录:" + file.getAbsolutePath());
			return false;
		}

		// 获取当前文件下的所有文件
		String[] fileLists = file.list();

		if (fileLists != null) {
			for (String fileList : fileLists) {
				File tmpFile = new File(file, fileList);
				// 判断是否是文件,并且文件后缀满足要求则匹配
				if (tmpFile.isFile() && tmpFile.getName().endsWith(suffix)) {
					if (!tmpFile.delete()) {
						log.error("删除该文件异常" + tmpFile.getAbsolutePath());
						return false;
					}
					log.error("删除该文件成功" + tmpFile.getAbsolutePath());
				} else if (tmpFile.isDirectory()) {
					continue;
				}
			}
		}

		return true;
	}

	/**
	 * 删除当前目录下所有指定后缀结尾的文件(不包含子目录里面的文件)
	 *
	 * @param path
	 *            文件目录
	 * @param suffix
	 *            文件后缀
	 * @return 如果删除成功则返回true, 失败则返回false
	 */
	public static boolean removeCurrentDirSpecialFile(String path, String suffix) {
		return removeCurrentDirSpecialFile(getFileObj(path), suffix);
	}

	/**
	 * 删除当前目录下所有指定后缀结尾的文件(包含子目录里面的文件)
	 *
	 * @param file
	 *            文件对象
	 * @param suffix
	 *            文件后缀
	 * @return 如果删除成功则返回true, 失败则返回false
	 */
	public static boolean removeAllSpecialFile(File file, String suffix) {

		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!file.exists() || !file.isDirectory()) {
			log.error("文件目录不存在,或者不是一个目录:" + file.getAbsolutePath());
			return false;
		}

		// 获取所有文件对象
		String[] fileLists = file.list();

		if (fileLists != null) {
			for (String fileList : fileLists) {
				File tmpFile = new File(file, fileList);
				// 判断是否是文件,并且文件后缀满足要求则匹配
				if (tmpFile.isFile() && tmpFile.getName().endsWith(suffix)) {
					if (!tmpFile.delete()) {
						log.error("删除该文件异常" + tmpFile.getAbsolutePath());
						return false;
					}
					log.error("删除该文件成功" + tmpFile.getAbsolutePath());
				} else if (tmpFile.isDirectory()) {
					removeAllSpecialFile(tmpFile, suffix);
				}
			}
		}

		return true;
	}

	/**
	 * 删除当前目录下所有指定后缀结尾的文件(包含子目录里面的文件)
	 *
	 * @param path
	 *            文件目录
	 * @param suffix
	 *            文件后缀
	 * @return 如果删除成功则返回true, 失败则返回false
	 */
	public static boolean removeAllSpecialFile(String path, String suffix) {
		return removeAllSpecialFile(getFileObj(path), suffix);
	}

	/**
	 * 创建单个文件(如果该文件名已经存在,则覆盖)
	 *
	 * @param file
	 *            文件对象
	 * @return 创建成功则返回true, 否则返回false
	 * @throws IOException
	 */
	public static boolean createFile(File file) throws IOException {

		// 判断目标文件所在的目录是否存在
		if (!file.getParentFile().exists()) {
			// 如果目标文件所在的文件夹不存在，则创建父文件夹
			log.info("目标文件所在目录不存在，准备创建它！");
			// 判断创建目录是否成功
			if (!file.getParentFile().mkdirs()) {
				log.error("创建目标文件所在的目录失败！");
				return false;
			}
		}

		// 如果文件存在,先删除文件再创建文件,如果不存在直接创建文件
		if (file.exists()) {
			if (!file.delete()) {
				throw new IOException();
			}
		}
		if (file.createNewFile()) {
			log.info("创建文件成功:" + file.getAbsolutePath());
			return true;
		} else {
			log.error("创建文件失败！" + file.getAbsolutePath());
			return false;
		}

	}

	/**
	 * 创建单个文件(如果该文件名已经存在,则覆盖)
	 *
	 * @param absolutePath
	 *            文件完整的路径
	 * @return 创建成功则返回true, 否则返回false
	 * @throws IOException
	 */
	public static boolean createFile(String absolutePath) throws IOException {
		return createFile(getFileObj(absolutePath));
	}

	/**
	 * 创建单个文件(如果该文件名已经存在,则覆盖)
	 *
	 * @param path
	 *            文件目录
	 * @param filename
	 *            文件名称
	 * @return 创建成功则返回true, 否则返回false
	 * @throws IOException
	 */
	public static boolean createFile(String path, String filename) throws IOException {
		path = completedDir(path);
		return createFile(path + filename);
	}

	/**
	 * 删除单个文件
	 *
	 * @param file
	 *            文件对象
	 * @return 删除成功返回true, 否则返回false
	 */
	public static boolean removeFile(File file) {

		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!file.exists() || file.isDirectory()) {
			log.error("文件不存在,或者是一个目录:" + file.getAbsolutePath());
			return false;
		}
		// 删除成功则退出
		if (file.delete()) {
			log.info("删除文件成功!" + file.getAbsolutePath());
			return true;
		}
		return false;
	}

	/**
	 * 删除单个文件
	 *
	 * @param absolutePath
	 *            文件完整路径
	 * @return 删除成功返回true, 否则返回false
	 */
	public static boolean removeFile(String absolutePath) {
		return removeFile(getFileObj(absolutePath));
	}

	/**
	 * 删除单个文件
	 *
	 * @param path
	 *            文件目录
	 * @param filename
	 *            文件名称
	 * @return 删除成功返回true, 否则返回false
	 */
	public static boolean removeFile(String path, String filename) {
		path = completedDir(path);
		return removeFile(path + filename);
	}

	/**
	 * 读取文件内容(默认以utf-8的字符集来读取)
	 *
	 * @param file
	 *            文件对象
	 * @return 返回该文件全部内容
	 * @throws IOException
	 */
	public static List<String> readFile(File file) throws IOException {
		return readFile(file, UTF_CHARSET);
	}

	/**
	 * 读取文件内容
	 *
	 * @param file
	 *            文件对象
	 * @param charset
	 *            字符集
	 * @return 返回该文件全部内容
	 * @throws IOException
	 */
	public static List<String> readFile(File file, String charset) throws IOException {

		List<String> list = new ArrayList<String>();

		// 返回的内容
		String content = "";
		// 代表一行的字符串内容
		String tmp;

		InputStream fr = new FileInputStream(file);
		BufferedReader input = new BufferedReader(new InputStreamReader(fr, charset));
		while ((tmp = input.readLine()) != null) {
			list.add(tmp);
		}
		input.close();

		return list;

	}

	/**
	 * 读取文件一行内容
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            行号
	 * @return 返回这一行的内容
	 * @throws IOException
	 */
	public static String readFileOneLine(File file, int line) throws IOException {
		return readFileOneLine(file, line, UTF_CHARSET);
	}

	/**
	 * 读取文件一行内容
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            行号
	 * @param charset
	 *            字符集
	 * @return 返回这一行的内容
	 * @throws IOException
	 */
	public static String readFileOneLine(File file, int line, String charset) throws IOException {

		List<String> strList = new ArrayList<String>();

		InputStreamReader read = new InputStreamReader(new FileInputStream(file), charset);
		BufferedReader reader = new BufferedReader(read);
		String lineContent;
		// 如果读不到内容就结束
		while ((lineContent = reader.readLine()) != null) {
			strList.add(lineContent);
		}
		reader.close();
		int size = strList.size();
		return (size >= (line - 1)) ? strList.get(line - 1) : "";
	}

	/**
	 * 读取文件自定义区间行数据(比如从第3行到第5行的数据)
	 *
	 * @param file
	 *            文件对象
	 * @param startLine
	 *            起始行号
	 * @param endLine
	 *            结束行号
	 * @return 返回从起始行到结束行的内容
	 * @throws IOException
	 */
	public static List<String> readFileIntervalLine(File file, int startLine, int endLine) throws IOException {
		return readFileIntervalLine(file, startLine, endLine, UTF_CHARSET);
	}

	/**
	 * 读取文件自定义区间行数据(比如从第3行到第5行的数据)
	 *
	 * @param file
	 *            文件对象
	 * @param startLine
	 *            起始行号
	 * @param endLine
	 *            结束行号
	 * @param charset
	 *            字符集
	 * @return 返回从起始行到结束行的内容
	 * @throws IOException
	 */
	public static List<String> readFileIntervalLine(File file, int startLine, int endLine, String charset)
			throws IOException {

		String content = "";
		List<String> returnList = new ArrayList<String>();

		if (startLine > endLine) {
			log.error("结束行号不能大于起始行号");
			return null;
		}

		List<String> strList = new ArrayList<String>();

		InputStreamReader read = new InputStreamReader(new FileInputStream(file), charset);
		BufferedReader reader = new BufferedReader(read);

		String lineContent;
		while ((lineContent = reader.readLine()) != null) {
			strList.add(lineContent);
		}

		reader.close();
		read.close();

		int size = strList.size();

		if (size >= (endLine - 1)) {
			for (int i = startLine - 1; i < endLine; i++) {
				returnList.add(strList.get(i));
			}
		}

		return returnList;
	}

	/**
	 * 往文件里面写入数据
	 *
	 * @param file
	 *            文件对象
	 * @param content
	 *            写入内容
	 * @param isAppend
	 *            是否追加(true 往最后一行追加, false 直接覆盖原内容)
	 * @return 写入成功返回true, 否则返回false
	 * @throws IOException
	 */
	public static boolean writeFile(File file, String content, Boolean isAppend) throws IOException {

		if (!file.exists()) {
			if (!file.createNewFile()) {
				return false;
			}
		}

		OutputStreamWriter fw = new OutputStreamWriter(new FileOutputStream(file, isAppend.booleanValue()),
				UTF_CHARSET);

		Writer output = new BufferedWriter(fw);
		output.write(content);

		output.close();
		return true;

	}

	/**
	 * 往文件里面写入数据
	 *
	 * @param file
	 *            文件对象
	 * @param contents
	 *            写入内容集合
	 * @param isAppend
	 *            是否追加(true 往最后一行追加, false 直接覆盖原内容)
	 * @throws IOException
	 */
	public static boolean writeFile(File file, List<String> contents, Boolean isAppend) throws IOException {

		if (!file.exists()) {
			if (!file.createNewFile()) {
				return false;
			}
		}
		OutputStreamWriter fw = new OutputStreamWriter(new FileOutputStream(file, isAppend.booleanValue()),
				UTF_CHARSET);

		BufferedWriter output = new BufferedWriter(fw);
		for (String content : contents) {
			output.write(content);
			output.write(System.getProperty("line.separator"));
		}
		output.close();
		return true;
	}

	/**
	 * 往文件指定行号后添加数据
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            指定行号
	 * @param contents
	 *            文件内容(字符串形式)
	 * @throws IOException
	 */
	public static void writeFileOneLine(File file, int line, List<String> contents) throws IOException {
		writeFileOneLine(file, line, contents, UTF_CHARSET);
	}

	/**
	 * 往文件指定行号后添加数据
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            指定行号
	 * @param contents
	 *            插入的内容(字符串形式)
	 * @param charset
	 *            字符集
	 * @throws IOException
	 */
	public static void writeFileOneLine(File file, int line, List<String> contents, String charset) throws IOException {

		List<String> fileLists = readFile(file, charset);
		for (int i = 0; i < contents.size(); i++) {
			fileLists.add(line + i, contents.get(i));
		}
		writeFile(file, fileLists, false);
	}

	/**
	 * 往文件指定行号后添加数据
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            指定行号
	 * @param contents
	 *            文件内容(字符串形式)
	 * @throws IOException
	 */
	public static void writeFileOneLine(File file, int line, String contents) throws IOException {
		writeFileOneLine(file, line, contents, UTF_CHARSET);
	}

	/**
	 * 往文件指定行号后添加数据
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            指定行号
	 * @param contents
	 *            插入的内容(字符串形式)
	 * @param charset
	 *            字符集
	 * @throws IOException
	 */
	public static void writeFileOneLine(File file, int line, String contents, String charset) throws IOException {

		List<String> fileLists = readFile(file, charset);
		fileLists.add(line, contents);
		writeFile(file, fileLists, false);
	}

	/**
	 * 修改指定文件行号数据
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            行号
	 * @param content
	 *            修改后的内容
	 * @throws IOException
	 */
	public static void updateFileOneLine(File file, int line, String content) throws IOException {
		List<String> list = readFile(file);

		if (line < 0) {
			log.error("输入的行号必须为大于0的整数!");
		}
		for (int i = 0; i < list.size(); i++) {
			if (line == i + 1) {
				list.set(i, content);
			}
		}
		writeFile(file, list, false);
	}

	/**
	 * 清空文件全部内容
	 *
	 * @param file
	 *            文件对象
	 * @throws IOException
	 */
	public static void clearFile(File file) throws IOException {
		OutputStreamWriter fileWriter = new OutputStreamWriter(new FileOutputStream(file), UTF_CHARSET);
		fileWriter.write("");
		fileWriter.flush();
		fileWriter.close();
	}

	/**
	 * 清除文件指定某一行
	 *
	 * @param file
	 *            文件对象
	 * @param line
	 *            行号
	 * @throws IOException
	 */
	public static void removeFileOneLine(File file, int line) throws IOException {

		List<String> tmp = new ArrayList<String>();

		// 先读取文件到
		List<String> list = readFile(file);

		for (int i = 0; i < list.size(); i++) {
			if (!(i == line - 1)) {
				tmp.add(list.get(i));
			}
		}
		writeFile(file, tmp, false);
	}

	/**
	 * 清除文件区间行
	 *
	 * @param file
	 *            文件对象
	 * @param startLine
	 *            起始行号
	 * @param endLine
	 *            结束行号
	 * @throws IOException
	 */
	public static void removeFileIntervalLine(File file, int startLine, int endLine) throws IOException {

		if (startLine > endLine) {
			log.error("起始行号不能大于结束行号");
		}

		List<String> tmp = new ArrayList<String>();

		// 先读取文件到
		List<String> list = readFile(file);
		for (int i = 0; i < list.size(); i++) {
			if (!(i >= startLine - 1 && i <= endLine - 1)) {
				tmp.add(list.get(i));
			}
		}
		writeFile(file, tmp, false);
	}

	/**
	 * 复制文件
	 *
	 * @param fileFromPath
	 *            要被复制的文件(完整路径)
	 * @param fileToPath
	 *            复制到哪里(完整路径)
	 * @throws IOException
	 */
	public static void copyFile(String fileFromPath, String fileToPath) throws IOException {

		try (FileInputStream in = new FileInputStream(fileFromPath);
				FileOutputStream out = new FileOutputStream(fileToPath)) {
			byte[] bt = new byte[CACHE_SIZE];
			int count;
			while ((count = in.read(bt)) > 0) {
				out.write(bt, 0, count);
			}
		}
	}

	/**
	 * 得到该路径下的所有文件对象集合
	 *
	 * @param file
	 *            文件对象
	 * @return 返回该文件对象下所有对象的集合
	 */
	public static List<File> getSubFiles(File file) {
		List<File> ret = new ArrayList<File>();
		File[] tmp = file.listFiles();
		if (tmp != null) {
			if (tmp.length == 0) {
				ret.add(file);
			}

			for (int i = 0; i < tmp.length; i++) {
				if (tmp[i].isFile()) {
					ret.add(tmp[i]);
				}
				if (tmp[i].isDirectory()) {
					ret.addAll(getSubFiles(tmp[i]));
				}
			}
		}
		return ret;
	}



	/**
	 * 批量递归压缩文件
	 *
	 * @param parentFile
	 *            文件对象
	 * @param basePath
	 *            文件路径
	 * @param zos
	 *            压缩输出流对象
	 * @param pattern
	 *            正则表达式
	 * @throws IOException
	 */
	private static void batchZipFile(File parentFile, String basePath, ZipOutputStream zos, String pattern)
			throws IOException {
		File[] files;

		if (parentFile.isDirectory()) {
			files = parentFile.listFiles();
		} else {
			files = new File[1];
			files[0] = parentFile;
		}
		String pathName = "";
		InputStream is = null;
		BufferedInputStream bis = null;
		byte[] cache = new byte[CACHE_SIZE];

		if (files != null) {

			for (File file : files) {
				if (file.isDirectory()) {
					pathName = file.getPath().substring(basePath.length() + 1) + File.separator;

					// 如果文件名匹配通配符则加入压缩队列
					if (!Pattern.matches(pattern, pathName)) {
						batchZipFile(file, basePath, zos, pattern);
					} else {
						zos.putNextEntry(new ZipEntry(pathName));
					}
					log.info("---------------1.文件夹pathName:" + pathName);

				} else {
					pathName = file.getPath().substring(basePath.length() + 1);

					// 如果文件名匹配通配符则加入压缩队列
					if (!Pattern.matches(pattern, pathName)) {
						continue;
					}

					log.info("----------------2.文件pathName:" + pathName);

					is = new FileInputStream(file);
					bis = new BufferedInputStream(is);
					zos.putNextEntry(new ZipEntry(pathName));
					int nRead = 0;
					while ((nRead = bis.read(cache, 0, CACHE_SIZE)) != -1) {
						zos.write(cache, 0, nRead);
					}

				}
			}
		}

		if (zos != null) {
			zos.close();
		}
		if (bis != null) {
			bis.close();
		}
		if (is != null) {
			is.close();
		}

	}

	/**
	 * 压缩文件
	 *
	 * @param sourceFolder
	 *            压缩文件夹
	 * @param zipFilePath
	 *            压缩文件输出路径
	 * @throws IOException
	 */
	public static void zip(String sourceFolder, String zipFilePath) throws IOException {

		String basePath = "";

		OutputStream out = new FileOutputStream(zipFilePath);
		BufferedOutputStream bos = new BufferedOutputStream(out);
		ZipOutputStream zos = new ZipOutputStream(bos, Charset.forName(CHINESE_CHARSET));
		// 解决中文文件名乱码
		File file = new File(sourceFolder);
		if (file.isDirectory()) {
			basePath = file.getPath();
			log.info("文件夹 basePath：" + basePath);
		} else {
			basePath = file.getParent().substring(0, file.getParent().length() - 1);
			log.info("文件 basePath：" + basePath);
		}
		zipFile(file, basePath, zos);
	}

	/**
	 * 递归压缩文件
	 *
	 * @param parentFile
	 *            文件对象
	 * @param basePath
	 *            文件路径
	 * @param zos
	 *            ZipOutputStream对象
	 * @throws IOException
	 */
	private static void zipFile(File parentFile, String basePath, ZipOutputStream zos) throws IOException {
		File[] files;

		if (parentFile.isDirectory()) {
			files = parentFile.listFiles();
		} else {
			files = new File[1];
			files[0] = parentFile;
		}
		String pathName = "";
		InputStream is = null;
		BufferedInputStream bis = null;
		byte[] cache = new byte[CACHE_SIZE];
		if (files != null) {
			for (File file : files) {
				if (file.isDirectory()) {
					pathName = file.getPath().substring(basePath.length() + 1) + File.separator;
					log.info("---------------1.文件夹pathName:" + pathName);

					zos.putNextEntry(new ZipEntry(pathName));

					zipFile(file, basePath, zos);
				} else {
					pathName = file.getPath().substring(basePath.length() + 1);
					log.info("----------------2.文件pathName:" + pathName);

					is = new FileInputStream(file);
					bis = new BufferedInputStream(is);
					zos.putNextEntry(new ZipEntry(pathName));
					int nRead = 0;
					while ((nRead = bis.read(cache, 0, CACHE_SIZE)) != -1) {
						zos.write(cache, 0, nRead);
					}

				}
			}
		}
		if (zos != null) {
			zos.close();
		}
		if (bis != null) {
			bis.close();
		}
		if (is != null) {
			is.close();
		}

	}

	/**
	 * 解压压缩包
	 *
	 * @param zipFilePath
	 *            压缩包的位置
	 * @param destDir
	 *            压缩包释放目录
	 * @throws IOException
	 */
	public static void unZip(String zipFilePath, String destDir) throws IOException {

		BufferedInputStream bis = null;
		FileOutputStream fos = null;
		BufferedOutputStream bos = null;
		File file = null;
		File parentFile = null;
		ZipEntry entry = null;

		ZipFile zipFile = new ZipFile(zipFilePath, Charset.forName(CHINESE_CHARSET));
		Enumeration<?> emu = zipFile.entries();

		// 定义缓存
		byte[] cache = new byte[CACHE_SIZE];
		while (emu.hasMoreElements()) {
			entry = (ZipEntry) emu.nextElement();
			if (entry.isDirectory()) {
				boolean mkdirsFlag = new File(destDir, entry.getName()).mkdirs();
				if (!mkdirsFlag) {
					throw new IOException();
				}
				continue;
			}
			bis = new BufferedInputStream(zipFile.getInputStream(entry));
			file = new File(destDir, entry.getName());
			parentFile = file.getParentFile();
			if (parentFile != null && (!parentFile.exists())) {
				if (!parentFile.mkdirs()) {
					throw new IOException();
				}
			}
			fos = new FileOutputStream(file);
			bos = new BufferedOutputStream(fos, CACHE_SIZE);
			int nRead = 0;
			while ((nRead = bis.read(cache, 0, CACHE_SIZE)) != -1) {
				fos.write(cache, 0, nRead);
			}
			bos.flush();
		}

		fos.close();
		bos.close();
		zipFile.close();
		bis.close();

	}

	/**
	 * 移动文件
	 * 
	 * @param
	 * @throws IOException
	 *             mv OF[CDJ]_*_*_*[._][CK]* /home/<USER>/file/D001/20190522/ mv
	 *             OF[CJ]* /home/<USER>/file/D001/20190522/ mv OF[D]*07.TXT
	 *             /home/<USER>/file/D001/20190522/
	 */
	public static void mvFile(String localPath, String targetPath) throws IOException {
		// 由于所有文件格式太相似无法直接执行通配符文件进行移动操作
		// 先删除通配符为OF[CDJ]_*_*_*[._][CK]*的文件在删除 OF[CJ]* 最后删除OF[D]*07.TXT
		String shell = new StringBuffer().append("mv ").append(localPath).append("OF[CDJ]_*_*_*[._][CK]*.TXT")
				.append(" ").append(targetPath).toString();
		String shell1 = new StringBuffer().append("mv ").append(localPath).append("OF[J]*.TXT").append(" ")
				.append(targetPath).toString();
		String shell2 = new StringBuffer().append("mv ").append(localPath).append("OF[D]*07.TXT").append(" ")
				.append(targetPath).toString();
		String shell3 = new StringBuffer().append("mv ").append(localPath).append("OF[C]*.TXT").append(" ")
				.append(targetPath).toString();
		log.info("shell=" + shell);
		StringBuffer sb = new StringBuffer();
		BufferedReader br = null;
		String[] shells = new String[] { "/bin/sh", "-c", shell };
		Process process = Runtime.getRuntime().exec(shells);

		try {
			String line = null;
			br = new BufferedReader(new InputStreamReader(process.getErrorStream()));
			line = null;
			while ((line = br.readLine()) != null) {
				System.out.println("errorS" + line);
			}
			System.out.println(sb);
			br = new BufferedReader(new InputStreamReader(process.getInputStream()));
			line = null;
			while ((line = br.readLine()) != null) {
				System.out.println("inputS" + line);
			}
			int value = process.waitFor();
			if (value == 0) {
				log.info("value=" + value);
				log.info("shell1=" + shell1);
				String[] shell1s = new String[] { "/bin/sh", "-c", shell1 };
				process = Runtime.getRuntime().exec(shell1s);
				br = new BufferedReader(new InputStreamReader(process.getErrorStream()));
				line = null;
				while ((line = br.readLine()) != null) {
					System.out.println("errorS" + line);
				}
				System.out.println(sb);
				br = new BufferedReader(new InputStreamReader(process.getInputStream()));
				line = null;
				while ((line = br.readLine()) != null) {
					System.out.println("inputS" + line);
				}
				int value1 = process.waitFor();
				if (value1 == 0) {
					log.info("value1=" + value1);
					log.info("shell2=" + shell2);
					String[] shell2s = new String[] { "/bin/sh", "-c", shell2 };
					process = Runtime.getRuntime().exec(shell2s);
					br = new BufferedReader(new InputStreamReader(process.getErrorStream()));
					line = null;
					while ((line = br.readLine()) != null) {
						System.out.println("errorS" + line);
					}
					System.out.println(sb);
					br = new BufferedReader(new InputStreamReader(process.getInputStream()));
					line = null;
					while ((line = br.readLine()) != null) {
						System.out.println("inputS" + line);
					}
					int value2 = process.waitFor();
					if(value2 == 0) {
						String[] shell3s = new String[] { "/bin/sh", "-c", shell3 };
						process = Runtime.getRuntime().exec(shell3s);
						br = new BufferedReader(new InputStreamReader(process.getErrorStream()));
						line = null;
						while ((line = br.readLine()) != null) {
							System.out.println("errorS" + line);
						}
						System.out.println(sb);
						br = new BufferedReader(new InputStreamReader(process.getInputStream()));
						line = null;
						while ((line = br.readLine()) != null) {
							System.out.println("inputS" + line);
						}
						int value3 = process.waitFor();
					}
				}
			}
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}


	/**
	 * 文件重命名
	 *
	 * @param path
	 *            文件目录
	 * @param oldName
	 *            原来的文件名
	 * @param newName
	 *            新文件名
	 * @return 重命名成功则返回true, 否则返回false
	 */
	public static boolean renameFile(String path, String oldName, String newName) {

		boolean isSuccess = false;
		// 新的文件名和以前文件名不同时,才有必要进行重命名
		if (!oldName.equals(newName)) {
			File oldFile = new File(path + File.separator + oldName);
			File newFile = new File(path + File.separator + newName);

			// 重命名文件不存在
			if (!oldFile.exists()) {
				log.error("文件不存在:" + oldFile.getAbsolutePath());
				return isSuccess;
			}
			// 若在该目录下已经有一个文件和新文件名相同，则不允许重命名
			if (newFile.exists()) {
				log.error("已有相同的文件存在" + oldFile.getAbsolutePath());
				return isSuccess;
			} else {
				if (!oldFile.renameTo(newFile)) {
					return isSuccess;
				}
			}
		} else {
			log.error("文件重命名失败!");
			return isSuccess;
		}
		isSuccess = true;
		return isSuccess;
	}

	/**
	 * 批量创建文件
	 *
	 * @param fileNumber
	 *            新建文件数量
	 * @param path
	 *            文件目录
	 * @param prefix
	 *            文件前缀
	 * @param suffix
	 *            文件后缀
	 * @return 返回文件对象集合(顺序)
	 * @throws IOException
	 */
	public static List<File> batchCreateFile(int fileNumber, String path, String prefix, String suffix)
			throws IOException {

		List<File> fileList = new ArrayList<File>();
		for (int i = 0; i < fileNumber; i++) {
			if (createFile(path, prefix + i + suffix)) {
				fileList.add(getFileObj(path, prefix + i + suffix));
			}
		}
		return fileList;
	}

	/**
	 * 判断文件是否存在
	 * 
	 * @param filepath
	 * @param filename
	 * @return
	 */
	public static boolean isFileExists(String filepath, String filename) {
		if (!filepath.endsWith("/") && !filepath.endsWith("\\")) {
			filepath = filepath + "/";
		}

		File file = new File(filepath + filename);
		if (!file.exists()) {
			log.error("999999", "文件" + filename + "不存在");
			return false;
		}
		return true;
	}

	/**
	 * 文件传输
	 * 
	 * @param localpath
	 * @param filename
	 * @param targetpath
	 * @param updown
	 * @throws IOException
	 * @throws Exception
	 */
	public static void sendFileByEftp(String localpath, String filename, String targetpath, String targetname,
			String updown, String eftpStartSh, String systemNo) throws IOException {
		StringBuffer sb = new StringBuffer();
		BufferedReader br = null;

		try {
			// 检查eftp启动情况
			Process process = Runtime.getRuntime().exec(eftpStartSh);
			process.waitFor();

			// 发送文件
			String shell = new StringBuffer().append("EFTP_CLIENT -d ").append(updown).append(" -h ").append(systemNo)
					.append(" -n ").append("02").append(" -l ").append(localpath).append(filename).append(" -r ")
					.append(targetpath).append(targetname).append(" -t trans ").toString();
			process = Runtime.getRuntime().exec(shell);
			process.waitFor();

			if (process.getErrorStream() != null) {
				br = new BufferedReader(new InputStreamReader(process.getErrorStream()), 4096);
				String line = null;
				while ((line = br.readLine()) != null) {
					sb.append(line);
				}
				throw new TransException("HE0019", "文件传输失败:" + sb.toString());
			}

			br = new BufferedReader(new InputStreamReader(process.getInputStream()), 4096);
			String line = null;
			while ((line = br.readLine()) != null) {
				sb.append(line);
			}
			if (!"0".equals(sb.toString())) {
				throw new TransException("HE0020", "文件传输失败:" + sb.toString());
			}
		} catch (TransException e) {
			e.printStackTrace();
			throw e;
		} catch (Exception e) {
			e.printStackTrace();
			throw new TransException("HE0021", "文件传输失败:" + e.getMessage());
		} finally {
			if (br != null) {
				br.close();
			}
		}
	}


	/**
	 * 功能描述: 复制于ta系统的FileUtils.java文件 校验文件路径最后是否为/如果不是则拼接/
	 * @auther: zwy
	 */
	public static String filePathJoint(String filePath){
		if (filePath.lastIndexOf("/") != filePath.length() - 1){
			filePath += "/";
		}
		return filePath;
	}


	/**
	 * 文件转MultipartFile
	 * @param picPath
	 * @return
	 */
	public static MultipartFile getMulFileByPath(String picPath) {
		FileItem fileItem = createFileItem(picPath);
		MultipartFile mfile = new CommonsMultipartFile(fileItem);
		return mfile;
	}

	public static FileItem createFileItem(String filePath)
	{
		FileItemFactory factory = new DiskFileItemFactory(16, null);
		String textFieldName = "textField";
		int num = filePath.lastIndexOf(".");
		String extFile = filePath.substring(num);
		FileItem item = factory.createItem(textFieldName, "text/plain", true,
				"MyFileName" + extFile);
		File newfile = new File(filePath);
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		try
		{
			FileInputStream fis = new FileInputStream(newfile);
			OutputStream os = item.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192))
					!= -1)
			{
				os.write(buffer, 0, bytesRead);
			}
			os.close();
			fis.close();
		}
		catch (IOException e)
		{
			e.printStackTrace();
		}
		return item;
	}
}
