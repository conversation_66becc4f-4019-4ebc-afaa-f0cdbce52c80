package com.kayak.prod.utils;



/**
 * @Description 异常信息响应
 * <AUTHOR>
 * @Date 2020/12/9
 */
public class TransException extends RuntimeException{

    private String transSerno;
    private String code;
    private String message;

    public final static String SEND_EXCEPTION = "1";
    public final static String RECV_EXCEPTION = "2";
    public final static String OUT_TIME_EXCEPTION = "00001";//gxp前置转发成功，未收到核心返回，当成超时处理
    public final static String FAIL_EXCEPTION = "00002";//gxp前置转发失败，直接失败处理
    public final static String INNER_EXCEPTION = "99999";//gxp前置系统内部错误，直接失败处理
    public final static String EMPTY_EXCEPTION = null;


    public TransException() {
        super();
    }

    public TransException(Exception e) {
        super(e);
        this.code = "9999";
        if (e.getMessage() != null && e.getMessage().getBytes().length > 256){
            this.code = new String(e.getMessage().substring(0, 256));
        } else {
            this.message = e.getMessage();
        }
    }

    public TransException(String transSerno, String rtnCode, String rtnDesc) {
        super(rtnDesc);
        this.transSerno = transSerno;
        this.code = rtnCode;
        this.message = rtnDesc;
    }

    public TransException(String rtnCode, String rtnDesc, Throwable cause){
        super(rtnDesc, cause);
        this.code = rtnCode;
        this.message = rtnDesc;
    }

    public TransException(String code, String message) {
        super(message);
        this.message = message;
        this.code = code;
    }

    public TransException(String message) {
        this.code = "9999";
        this.message = message;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTransSerno() {
        return transSerno;
    }

    public void setTransSerno(String transSerno) {
        this.transSerno = transSerno;
    }
}
