package com.kayak.prod.dao;

import com.alibaba.fastjson.JSON;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.component.RedisUtil;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M215;
import com.kayak.prod.model.M216;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
public class M216Dao extends ComnDao {

	@Autowired
    private RedisUtil redisUtil;

	/**
	 * 查询模版参数信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M216> findM216(SqlParam<M216> params) throws Exception {
		return super.findRows("select tpl_id ,tpl_name,model_id from prod_template_info",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	/**
	 * 查询模版参数信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M216> findTemplateInfo(SqlParam<M216> params) throws Exception {
		return super.findRows("select distinct pti.*, ptp.para_code, ptp.def_para_value, ptp.edit_flag, ptp.max_value, ptp.min_value, ptp.blank_rely,ptp.order_no," +
						" pool.para_name,pool.field_length,pool.field_precision,pool.group_id,pool.func_type,pool.dict" +
						" from prod_template_info pti " +
						" left join prod_template_para ptp on pti.tpl_id = ptp.tpl_id " +
						" left join prod_para_pool pool on ptp.para_code = pool.para_code  where pti.tpl_id = $S{tplId} order by ptp.order_no",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}


	public UpdateResult addTemplate(M216 params) throws Exception {
		return super.update("INSERT INTO  prod_template_para  (tpl_id,para_code,model_id,def_para_value,edit_flag,max_value,min_value,dict,blank_rely) " +
						"VALUES($S{tplId},$S{paraCode},$S{modelId},$S{defParaValue},$S{editFlag},$S{maxValue},$S{minValue},$S{dict},$S{blankRely},)",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

    public UpdateResult updatePara(SqlParam<M216> params) throws Exception {
        return super.update("UPDATE prod_template_info  SET tpl_name=$S{tplName} , model_id=$S{modelId}  WHERE tpl_id=$S{tplId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

	/**
	 * 删除模版信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public UpdateResult deleteTemplateInfo(SqlParam<M216> params) throws Exception {
		return super.update("DELETE FROM prod_template_info WHERE  tpl_id=$S{tplId} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	/**
	 * 删除模版参数信息
	 * @param tplId
	 * @return
	 * @throws Exception
	 */
	public UpdateResult deleteTemplateParam(String tplId) throws Exception {
		return super.update("DELETE FROM prod_template_para WHERE  tpl_id='"+tplId+"' ",
				SubDatabase.DATABASE_SYS_CENTER, tplId);
	}

	/**
	 * 新增模板明细信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
    public Map addProdParameterDetailInfo(SqlParam<M216> params) throws Exception {
        Map<String, String> map = new HashMap<>();

		int countResult = 0;
		List<M215> m215List = params.getModel().getM215List();
		for (M215 m215 : m215List) {
			countResult += super.update("INSERT INTO  prod_para_detail  (para_code ,pk_value ,model_id ,para_value ,exceed_flag ,tpl_id) " +
							"VALUES($S{paraCode} ,$S{pkValue} ,$S{modelId} ,$S{paraValue} ,$S{exceedFlag} ,$S{tplId})",
					SubDatabase.DATABASE_SYS_CENTER, m215).getEffect();
		}

		if (!m215List.isEmpty()) {
			Map<String, M215> collect = m215List.stream().collect(Collectors.toMap(M215::getParaCode, m215 -> m215));
			redisUtil.set("ProdInfo:" + m215List.get(0).getPkValue(), JSON.toJSONString(collect));
			// 存入产品四要素(系统编号+ta+法人代码+产品代码)+产品名称
			String prodCode = collect.getOrDefault("prod_code", new M215()).getParaValue();
			String prodName = collect.getOrDefault("prod_name", new M215()).getParaValue();
			String systemNo = collect.getOrDefault("system_no", new M215()).getParaValue();
			String tano = collect.getOrDefault("tano", new M215()).getParaValue();
			String legalCode = collect.getOrDefault("legal_code", new M215()).getParaValue();
			redisUtil.set((systemNo==null?"":systemNo) + ":" + (tano==null?"":tano) + ":"
						+ (legalCode==null?"":legalCode) + ":" + (prodCode==null?"":prodCode) + ":" + (prodName==null?"":prodName), "");
		}
        map.put("status", "200");
        map.put("rtnDesc", "0000");

        return map;
    }


	public UpdateResult addPara(SqlParam<M216> params) throws Exception {
		return super.update("INSERT INTO  prod_template_info  (tpl_id,tpl_name,model_id) VALUES($AUTOIDS{tplId},$S{tplName},$S{modelId})",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}


}
