package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import com.kayak.prod.model.M210;
import com.kayak.prod.model.M212;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M212Dao extends ComnDao {

    /************* M212Service *************/

    public SqlResult<M212> findComponent(SqlParam<M212> params) throws Exception {

        return super.findRows("SELECT comp_id, comp_name, comp_type, req_class, service_class, in_class,lifecycle_type, system_no FROM prod_comp_info",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    /**
     * 查询指定模型所有组件信息 - used
     * @param param
     * @return
     * @throws Exception
     */
    public SqlResult<M212> findAllComp(SqlParam<M212> param) throws Exception {
        String sql = "SELECT pci.comp_id, pci.comp_name, pci.comp_type, pci.req_class, pci.service_class, pci.in_class, pci.system_no, pci.lifecycle_type, " +
                " (CASE WHEN pci.comp_id IN (SELECT comp_id FROM prod_model_comp pmc WHERE pmc.model_id = '" + param.getModel().getModelId() + "') THEN '1' ELSE '0' END) AS in_comp_set" +
                " FROM prod_comp_info  pci";
        //排除没有关联参数的组件
        sql += " WHERE EXISTS ( SELECT 1 FROM prod_comp_para WHERE comp_id = pci.comp_id ) ";
        return super.findRows(sql,SubDatabase.DATABASE_SYS_CENTER, param);
    }

    public SqlResult<M210> findParamsByCompId(SqlParam<M210> param) throws Exception {
        String sql = "";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, param);
    }

    /**
     * 查询指定模型的组件信息
     * @param param
     * @return
     * @throws Exception
     */
    public SqlResult<M212> findCompByModelId(SqlParam<M212> param) throws Exception {
        String sql = "SELECT pci.comp_id, pci.comp_name, pci.comp_type, pci.req_class, pci.service_class, pci.in_class, pci.system_no, pci.lifecycle_type" +
                " FROM prod_comp_info  pci" +
                " LEFT JOIN prod_model_comp  pmc" +
                " ON pci.comp_id = pmc.comp_id" +
                " WHERE pmc.model_id = '" + param.getModel().getModelId() + "'";

        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, param);
    }

    /**
     * 查询产品组件信息
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M212> findComponentByCompType(SqlParam<M212> params) throws Exception {
        String compType = params.getModel().getCompType();
        if (Tools.isBlank(compType)) {
            return null;
        }
        return super.findRows("SELECT comp_id, comp_name, comp_type, req_class, service_class, in_class, system_no FROM prod_comp_info WHERE comp_type = '" + compType + "' order by comp_type * 1 ",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    /**
     * 查询指定模型下的组件信息
     * @param param
     * @return
     * @throws Exception
     */
    public SqlResult<M212> findCompInfoByModelId(SqlParam<M212> param) throws Exception {
        String sql = "";

        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, param);
    }

    /**
     * 查询当前组件有哪些模型使用
     * @param compId
     * @return
     * @throws Exception
     */
    public List<SqlRow> findCompUseedByModel(String compId) throws Exception {
        String sql = "SELECT model_id" +
                " FROM prod_model_comp" +
                " WHERE comp_id = '"+compId+"'";
        return super.findRows(sql,  SubDatabase.DATABASE_SYS_CENTER, compId);
    }

    /**
     * 查询产品组件类型
     * @param params
     * @return
     * @throws Exception
     */
    public List<SqlRow> findCompTypes(SqlParam<M212> params) throws Exception {
        String sql = "SELECT comp_type from prod_comp_info GROUP BY comp_type";
        return super.findRows(sql,  SubDatabase.DATABASE_SYS_CENTER);
    }

    public List<SqlRow> findParaByComp(String compId) throws Exception {
        String sql = "SELECT para_code FROM prod_comp_para WHERE comp_id = '"+compId+"'";
        return super.findRows(sql,  SubDatabase.DATABASE_SYS_CENTER, compId);
    }

    public List<SqlRow> findComponentByModelId(String modelId) throws Exception {
        String sql = "SELECT comp_id FROM prod_model_comp WHERE model_id = '"+modelId+"'";
        return super.findRows(sql,  SubDatabase.DATABASE_SYS_CENTER, modelId);
    }

    public List<SqlRow> findComponentNameByModelId(String modelId) throws Exception {
        String sql = "SELECT pci.comp_name, pci.comp_id" +
                " FROM prod_model_comp  pmc" +
                " LEFT JOIN prod_comp_info pci" +
                " ON pmc.comp_id = pci.comp_id" +
                " WHERE model_id = '"+modelId+"'";
        return super.findRows(sql,  SubDatabase.DATABASE_SYS_CENTER, modelId);
    }

    public UpdateResult deleteComponentByModelId(String modelId) throws Exception {
        return super.update("DELETE FROM prod_model_comp WHERE  model_id = '" + modelId + "'", SubDatabase.DATABASE_SYS_CENTER,modelId);
    }

    public void batchGroupInfo(List<String> groupList, String modelId) throws Exception {
        super.update("DELETE FROM prod_model_group WHERE  model_id = '" + modelId + "'", SubDatabase.DATABASE_SYS_CENTER,modelId);

        M212 m212 = null;
        for (int i=0; i<groupList.size(); i++){
            m212 = new M212();
            m212.setGroupId(groupList.get(i));
            m212.setModelId(modelId);
            m212.setOrderNo(i+1);
            super.update("INSERT INTO prod_model_group(model_id, group_id, order_no) VALUES($S{modelId},$S{groupId},$I{orderNo})",
                    SubDatabase.DATABASE_SYS_CENTER, m212);
        }
    }

    public void batchComponentInfo(List<M212> m212List, String modelId) throws Exception {

        // 先删除模型与产品的关联
        super.update("DELETE FROM prod_model_comp WHERE  model_id = '" + modelId + "'", SubDatabase.DATABASE_SYS_CENTER,modelId);

        for (M212 m212 : m212List){
            super.update("INSERT INTO prod_model_comp(model_id, comp_id) VALUES($S{modelId},$S{compId})",
                    SubDatabase.DATABASE_SYS_CENTER, m212);
        }
    }

    public UpdateResult addComponentByModelId(M212 m212) throws Exception {
        return super.update("INSERT INTO prod_model_comp(model_id, comp_id) VALUES($S{modelId},$S{compId})",
                SubDatabase.DATABASE_SYS_CENTER, m212);
    }

    public UpdateResult deleteParamsByCompId(String compId) throws Exception {
        return super.update("DELETE FROM prod_comp_para WHERE  comp_id = '" + compId + "'", SubDatabase.DATABASE_SYS_CENTER,compId);
    }

    public UpdateResult addParamsByCompId(M212 m212) throws Exception {
        return super.update("INSERT INTO prod_comp_para(comp_id, para_code, blank_flag, max_value, min_value,  conf_option, para_desc, default_value) " +
                        " VALUES($S{compId}, $S{paraCode}, $S{blankFlag}, $S{maxValue}, $S{minValue},  $S{confOption}, $S{paraDesc}, $S{defaultValue})",
                SubDatabase.DATABASE_SYS_CENTER, m212);
    }

    public UpdateResult deleteParamsByModelId(String modelId) throws Exception {
        return super.update("DELETE FROM prod_model_para WHERE model_id = '" + modelId + "'", SubDatabase.DATABASE_SYS_CENTER, modelId);
    }

    public UpdateResult addParamsByModelId(String modelId) throws Exception {
        return super.update("insert into prod_model_para(model_id, para_code, para_name, field_length, field_precision, dict,group_id, pk_flag, blank_flag, order_no)" +
                        " SELECT DISTINCT model_id, pool.para_code, para_name, field_length, field_precision, dict, group_id, '1' as 'pk_flag', '0' as 'blank_flag', '1' as 'order_no'" +
                        " FROM prod_para_pool as pool, prod_model_comp as pmc, prod_comp_para as pcp" +
                        " WHERE pool.para_code = pcp.para_code " +
                        " AND pcp.comp_id = pmc.comp_id" +
                        " AND pmc.model_id = '"+ modelId + "'",
                SubDatabase.DATABASE_SYS_CENTER, modelId);
    }

    /**
     * 添加产品组件信息
     * @param params
     * @return
     * @throws Exception
     */
    public UpdateResult addComponent(SqlParam<M212> params) throws Exception {
        return super.update("INSERT INTO prod_comp_info(comp_id, comp_name, comp_type, req_class, service_class, in_class, system_no,lifecycle_type) VALUES($S{compId},$S{compName},$S{compType},$S{reqClass},$S{serviceClass},$S{inClass},$S{systemNo},$S{lifecycleType})",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    /**
     * 通过组件编号，修改产品组件信息
     * @param params
     * @return
     * @throws Exception
     */
    public UpdateResult updateComponent(SqlParam<M212> params) throws Exception {
        return super.update("UPDATE prod_comp_info SET comp_name=$S{compName}, comp_type=$S{compType}, req_class=$S{reqClass}, service_class=$S{serviceClass}, in_class=$S{inClass}, system_no=$S{systemNo},lifecycle_type=$S{lifecycleType} WHERE comp_id=$S{compId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    /**
     * 通过组件编号，删除产品组件信息
     * @param params
     * @return
     * @throws Exception
     */
    public UpdateResult deleteComponent(SqlParam<M212> params) throws Exception {
        String sql = "select 1 from prod_model_comp " +
                "where comp_id=$S{compId} limit 1";
        SqlRow row = super.findRow(sql, params.getModel());
        if (row != null) {
            throw new PromptException("该组件已被模型引用了，暂时不支持删除");
        }
        sql = "select 1 from prod_comp_relation " +
                "where relation_type='2' and match_comp_id=$S{compId} limit 1";
        row = super.findRow(sql, params.getModel());
        if (row != null) {
            throw new PromptException("该组件已被其他组件依赖，请先解除依赖关系");
        }
        return super.update("DELETE FROM prod_comp_info WHERE comp_id=$S{compId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    /**
     *根据组件id查询组件信息
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M212> findComponentByComId(SqlParam<M212> params) throws Exception {

        return super.findRows("SELECT pap.comp_id,pap.para_code,ppp.para_name,pap.blank_flag,pap.max_value , pap.min_value,pap.conf_option,pap.para_desc,\n" +
                        "ppp.dict, ppp.func_type, pap.default_value FROM prod_comp_para  pap" +
                        " left join prod_para_pool  ppp ON pap.para_code = ppp.para_code where pap.comp_id= '" + params.getModel().getCompId() + "' ",
                SubDatabase.DATABASE_SYS_CENTER, params);

    }

    /**
     *检查同一个业务编号下是否存在相同的组件名称
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M212> checkCompNameBySystemNo(SqlParam<M212> params) throws Exception {
        if(params.getModel().getType().equals("0")){
            return super.findRows("SELECT count(1) nums FROM prod_comp_info " +
                            " where system_no= '" + params.getModel().getSystemNo() + "' and comp_name='"+params.getModel().getCompName()+"'",
                    SubDatabase.DATABASE_SYS_CENTER, params);
        }else {
            return super.findRows("SELECT count(1) nums FROM prod_comp_info " +
                            " where system_no= '" + params.getModel().getSystemNo() + "' and comp_name='"+params.getModel().getCompName()+"' and comp_id != '"+params.getModel().getCompId()+"'",
                    SubDatabase.DATABASE_SYS_CENTER, params);
        }
    }

    /**
     *查询最大的组件编号
     * @return
     * @throws Exception
     */
    public SqlRow seleMaxCompId() throws Exception {

        return super.findRow("select max(comp_id) max_comp_id from prod_comp_info where comp_id like  '1%'",
                SubDatabase.DATABASE_SYS_CENTER);

    }
}
