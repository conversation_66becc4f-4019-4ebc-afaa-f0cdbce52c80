package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M203;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.kayak.prod.utils.UnderlineToCamelUtils.sqlRowToCamel;

@Repository
public class M203Dao extends ComnDao {

	public SqlResult<M203> findProdLabels(SqlParam<M203> params) throws Exception {
		return super.findRows("SELECT label_code,label_name,label_type,label_index,legal_code FROM prod_label order by label_index desc",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public UpdateResult addProdLabel(SqlParam<M203> params) throws Exception {
		return super.update("INSERT INTO prod_label(label_code,label_name,label_type,label_index,legal_code) VALUES($S{labelCode},$S{labelName},$S{labelType},$I{labelIndex},$S{legalCode})",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}
	
	public UpdateResult updateProdLabel(SqlParam<M203> params) throws Exception {
		return super.update("UPDATE prod_label SET label_name=$S{labelName} ,label_type=$S{labelType} ,label_index=$I{labelIndex}  WHERE  label_code=$S{labelCode} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}
	
	public UpdateResult deleteProdLabel(SqlParam<M203> params) throws Exception {
		return super.update("DELETE FROM prod_label WHERE  label_code=$S{labelCode} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public SqlResult findLabelsByProd(SqlParam<M203> params) throws Exception {
		String sql = "SELECT" +
				" t1.system_no system_no," +
				" t1.tano tano," +
				" t1.prod_code prod_code," +
				" t1.legal_code legal_code," +
				" t1.label_code label_code," +
				" t1.label_desc label_desc," +
				" t2.label_name label_name," +
				" t2.label_type label_type" +
				" FROM" +
				" prod_label_relation t1" +
				" LEFT JOIN prod_label t2 ON t1.label_code = t2.label_code " +
				" WHERE" +
				" t1.system_no = $S{systemNo} " +
				" AND t1.tano = $S{tano} " +
				" AND t1.prod_code = $S{prodCode} " +
				" AND t1.legal_code = $S{legalCode}";
		List<SqlRow> sqlRows = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
		List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
		return SqlResult.build(sqlRowList);
	}

}
