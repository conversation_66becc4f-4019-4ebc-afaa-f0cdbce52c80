package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import com.kayak.prod.model.M213;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M213Dao extends ComnDao {

    public List<SqlRow> findParamsByComp(String comps, String modelId, String systemNo) throws Exception {
        StringBuffer sb = new StringBuffer("");
        //查询该模型已经设置过的参数信息
        sb.append("( SELECT t2.comp_id compId, t1.para_code paraCode, t1.para_name paraName, t1.field_length fieldLength, t1.field_precision fieldPrecision, t1.dict, t1.group_id groupId, t1.func_type funcType, t1.para_desc paraDesc, t1.pk_flag pkFlag, t1.blank_flag blankFlag, t1.order_no orderNo, t1.max_value `maxValue`, t1.min_value minValue, t1.edit_flag editFlag  " +
                " FROM prod_model_para t1 " +
                " LEFT JOIN prod_comp_para t2 ON t2.para_code = t1.para_code " +
                " WHERE t2.comp_id IN "+ comps +" AND t1.model_id = '"+ modelId +"' " +
                " ORDER BY t1.order_no, t1.group_id ASC  LIMIT 999999999 ) ");
        sb.append(" UNION ALL  ");
        //查询选择的组件下所有的参数信息，排除产品模型中已经存在的
        sb.append("( SELECT t1.comp_id compId, t1.para_code paraCode, t2.para_name paraName, t2.field_length fieldLength, t2.field_precision fieldPrecision, t2.dict , t2.group_id groupId, t2.func_type funcType, t1.para_desc paraDesc, '' pkFlag, t1.blank_flag blankFlag, t2.order_no orderNo, t1.max_value `maxValue`, t1.min_value minValue, '2' as editFlag " +
                " FROM prod_comp_para t1" +
                " LEFT JOIN prod_para_pool t2 ON t1.para_code = t2.para_code" +
                (Tools.isNotBlank(systemNo) ? (" and t2.system_no='" + systemNo + "' ") : " ") +
                " WHERE t1.comp_id IN " + comps +
                " AND NOT EXISTS ( SELECT 1 FROM prod_model_para a WHERE a.model_id = '" + modelId + "' and a.para_code = t1.para_code ) " +
                " ORDER BY t2.order_no, t2.group_id ASC  LIMIT 999999999 ) ");
        String sql = "SELECT DISTINCT tt.paraCode," +
                " tt.compId," +
                " tt.paraName," +
                " tt.fieldLength," +
                " tt.fieldPrecision," +
                " tt.dict," +
                " tt.groupId," +
                " tt.funcType," +
                " tt.paraDesc," +
                " tt.pkFlag," +
                " tt.blankFlag," +
                " tt.orderNo," +
                " tt.maxValue," +
                " tt.minValue," +
                " tt.editFlag , t2.comp_name as compName FROM ( "+ sb.toString() + " ) tt " +
                " INNER JOIN prod_comp_info t2 ON tt.compId = t2.comp_id ";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, comps);
    }

    public SqlResult<M213> findProdModel(SqlParam<M213> params) throws Exception {
        return super.findRows("SELECT system_no, model_id, model_name FROM prod_model_info",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public UpdateResult addProdModel(SqlParam<M213> params) throws Exception {
        return super.update("INSERT INTO prod_model_info(system_no, model_id, model_name) VALUES($S{systemNo},$S{modelId},$S{modelName})",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult addModelParams(M213 m213) throws Exception {
        return super.update("INSERT INTO prod_model_para" +
                "(model_id, para_code, para_name, para_desc, group_id, field_length, " +
                "field_precision, pk_flag, blank_flag, order_no, max_value, min_value, blank_rely, dict, " +
                " func_type) " +
                "VALUES " +
                "($S{modelId}, $S{paraCode}, $S{paraName}, $S{paraDesc}, $S{groupId}, " +
                "$S{fieldLength}, $S{fieldPrecision}, $S{pkFlag}, $S{blankFlag}, $I{orderNo}, $S{maxValue}, " +
                "$S{minValue}, $S{blankRely}, $S{dict}, $S{funcType})", SubDatabase.DATABASE_SYS_CENTER, m213);
    }

    public UpdateResult updateProdModel(SqlParam<M213> params) throws Exception {
        return super.update("UPDATE prod_model_info SET system_no=$S{systemNo} ,model_name=$S{modelName}  WHERE  model_id=$S{modelId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public void deleteProdModel(SqlParam<M213> params) throws Exception {
        doTrans(() -> {
            super.update("DELETE FROM prod_model_info WHERE  model_id=$S{modelId} ",
                    SubDatabase.DATABASE_SYS_CENTER, params.getModel());

            super.update("DELETE FROM prod_model_comp WHERE  model_id=$S{modelId} ",
                    SubDatabase.DATABASE_SYS_CENTER, params.getModel());

            super.update("DELETE FROM prod_model_para WHERE  model_id=$S{modelId} ",
                    SubDatabase.DATABASE_SYS_CENTER, params.getModel());

            super.update("DELETE FROM prod_model_group WHERE  model_id=$S{modelId} ",
                    SubDatabase.DATABASE_SYS_CENTER, params.getModel());

            super.update("DELETE FROM prod_model_para_relation WHERE  model_id=$S{modelId} ",
                    SubDatabase.DATABASE_SYS_CENTER, params.getModel());
        });
    }

    /**
     * 判断模型是否已经存在产品使用
     * @param params
     * @return
     * @throws Exception
     */
    public boolean isExistProdByModelId(SqlParam<M213> params) throws Exception {
        String sql = "SELECT model_id as modelId FROM prod_para_detail WHERE model_id = $S{modelId} GROUP BY model_id ";
        if (StringUtils.isNotBlank(params.getModel().getCompId())){
            sql = "SELECT model_id as modelId FROM prod_para_detail WHERE model_id = $S{modelId} " +
                    " AND EXISTS (select 1 from prod_model_comp where model_id = $S{modelId} and comp_id = $S{compId} ) " +
                    "GROUP BY model_id ";
        }
        SqlRow  sqlRow = findRow(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
        if (sqlRow != null && StringUtils.isNotBlank(sqlRow.getString("modelId"))){
            return true;
        }
        return false;
    }

    public SqlResult<M213> findProdModelBySystemNo(SqlParam<M213> params) throws Exception {
        return super.findRows("SELECT system_no, model_id, model_name FROM prod_model_info",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }
}
