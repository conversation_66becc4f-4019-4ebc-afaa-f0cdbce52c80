package com.kayak.prod.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M233;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;

/**
 * 银行账号维护， 原名 FundAcctInfoDao
 */
@Repository
public class M233Dao extends ComnDao {

    public SqlResult<M233> findFundAcctInfos(SqlParam<M233> params) throws Exception {
        return super.findRows("SELECT 'FUND' as system_no, acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code," +
                "inter_code,acct_status,remark,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,TANO,PROD_ACCT_TYPE,IN_OUT_FLAG FROM fund_acct_info order by tano, acct_serno,OPEN_PROVINCE ", SubDatabase.DATABASE_FUND_CENTER,params);
    }
    public List<M233> listFundAcctInfosByNo(M233 param) throws Exception {
        return super.findRows(M233.class,"SELECT 'FUND' as system_no, acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code," +
                "inter_code,acct_status,remark,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,TANO,PROD_ACCT_TYPE,IN_OUT_FLAG FROM fund_acct_info where acct_no = '"+param.getAcctNo()+"'", SubDatabase.DATABASE_FUND_CENTER,param);
    }
    public List<M233> listFundAcctInfosByName(M233 param) throws Exception {
        return super.findRows(M233.class,"SELECT 'FUND' as system_no, acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code," +
                "inter_code,acct_status,remark,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO FROM fund_acct_info where acct_name = '"+param.getAcctName()+"'", SubDatabase.DATABASE_FUND_CENTER,param);
    }
    public UpdateResult addFundAcctInfo(SqlParam<M233> params) throws Exception {
        return super.update("INSERT INTO fund_acct_info(acct_serno,acct_no,acct_name,open_bank,open_province,open_city,exchange_code,inter_code,acct_status,remark,CUR,HOST_PROD_TYPE,HOST_ACCT_SEQ_NO,HOST_ACCT_TYPE,HOST_ORGNO,TANO,PROD_ACCT_TYPE,IN_OUT_FLAG ) " +
                        "VALUES($AUTOIDS{acctSerno},$S{acctNo},$S{acctName},$S{openBank},$S{openProvince},$S{openCity},$S{exchangeCode},$S{interCode},$S{acctStatus},$S{remark},$S{cur},$S{hostProdType},$S{hostAcctSeqNo},$S{hostAcctType},$S{hostOrgno},$S{tano},$S{prodAcctType},$S{inOutFlag} )",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult updateFundAcctInfo(SqlParam<M233> params) throws Exception {
        return super.update("UPDATE fund_acct_info SET acct_no=$S{acctNo} ,acct_name=$S{acctName} ,open_bank=$S{openBank} ,open_province=$S{openProvince} ,open_city=$S{openCity} ,exchange_code=$S{exchangeCode} ,inter_code=$S{interCode} ,acct_status=$S{acctStatus} ,remark=$S{remark},CUR=$S{cur},HOST_PROD_TYPE=$S{hostProdType} ,HOST_ACCT_SEQ_NO=$S{hostAcctSeqNo} ,HOST_ACCT_TYPE=$S{hostAcctType} " +
                        ",HOST_ORGNO=$S{hostOrgno},TANO=$S{tano},PROD_ACCT_TYPE=$S{prodAcctType},IN_OUT_FLAG=$S{inOutFlag}  WHERE  acct_serno=$S{acctSerno} ",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult deleteFundAcctInfo(SqlParam<M233> params) throws Exception {
        return super.update("DELETE FROM fund_acct_info WHERE  acct_serno=$S{acctSerno} ",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public List<SqlRow> getProdAcct(String acctSerno)throws Exception {
        String sql = "select * from fund_prod_acct_info where acct_serno = '" + acctSerno+"'";
        return super.findRows(SqlRow.class,sql, SubDatabase.DATABASE_FUND_CENTER, null);
    }

}
