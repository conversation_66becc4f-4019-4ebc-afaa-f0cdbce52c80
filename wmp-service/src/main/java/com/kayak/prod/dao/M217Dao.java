package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.prod.model.M217;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M217Dao extends ComnDao {

	public SqlResult<M217> findM217(SqlParam<M217> params) throws Exception {
		M217 m217 = params.getModel();
		String busiCode = m217.getBusiCode();
		String prodCode = m217.getProdCode();
		String acctNo = m217.getAcctNo();
		String busiType = m217.getBusiType();

		SqlResult<M217> sqlResult = super.findRows("select \n" +
					"cur,\n"+
					"cust_no,\n"+
					"acct_no,\n"+
					"trans_acct_no,\n"+
					"prod_code,\n"+
					"onway_amt,\n"+
					"busi_code,\n"+
					"busi_type,\n"+
					"tran_date,\n "+
					"app_serno,\n "+
					"ori_app_serno\n "+
				"from (\n"+
				"select\n"+
					"a.cur,\n"+
					"a.cust_no,\n"+
					"a.acct_no,\n"+
					"a.trans_acct_no,\n"+
					"a.prod_code,\n"+
					"a.app_amt as onway_amt,\n"+
					"busi_code,\n"+
					"'2' as busi_type,\n"+
					"a.busi_date as tran_date,\n"+
					"a.app_serno,\n "+
					"a.ori_app_serno\n "+
				"from\n"+
					"fina_cust_trans_req_log a\n"+
				"where 1=1\n"+
					"and a.trans_status = '0'\n"+
					"and a.capital_status = 'G'\n" +
				(StringUtils.isNotBlank(busiCode)?"and a.busi_code = $S{busiCode}\n":"and a.busi_code in('020', '022')\n") +
				(StringUtils.isNotBlank(busiType)&&!"2".equals(busiType)?"and 1 = 2\n":"") +
				(StringUtils.isNotBlank(acctNo)?"and a.acct_no = $S{acctNo}\n":"") +
				(StringUtils.isNotBlank(prodCode)?"and a.prod_code = $S{prodCode}\n":"") +
				"union all\n "+
				"select\n "+
					"b.cur,\n"+
					"b.cust_no,\n"+
					"b.acct_no,\n"+
					"b.trans_acct_no,\n"+
					"b.prod_code,\n"+
					"b.ack_amt as onway_amt,\n"+
					"busi_code,\n"+
					"'1' as busi_type,\n"+
					"b.ack_date as tran_date,\n"+
					"b.app_serno,\n "+
					"b.app_serno as ori_app_serno\n "+
				"from\n"+
					"fina_cust_trans_cfm_log b\n"+
				"where 1=1\n"+
					"and b.trans_status = '3'\n"+
					"and b.capital_status not in('6','L')\n"+
				(StringUtils.isNotBlank(busiCode)?"and b.busi_code = $S{busiCode}\n":"and b.busi_code in ('124', '142','143', '150')\n") +
				(StringUtils.isNotBlank(busiType)&&!"1".equals(busiType)?"and 1 = 2\n":"") +
				(StringUtils.isNotBlank(acctNo)?"and b.acct_no = $S{acctNo}\n":"") +
				(StringUtils.isNotBlank(prodCode)?"and b.prod_code = $S{prodCode}\n":"") +
				//"union all\n "+
				//"select\n "+
				//	"a.cur,\n"+
				//	"a.cust_no,\n"+
				//	"a.acct_no,\n"+
				//	"a.trans_acct_no,\n"+
				//	"a.prod_code\n,"+
				//	"a.app_amt as onway_amt\n,"+
				//	"'512' as busi_code\n,"+
				//	"a.busi_date as tran_date\n,"+
				//	"a.app_serno,\n "+
				//	"a.ori_app_serno\n "+
				//"from\n"+
				//	"fina_cust_trans_req_log a\n"+
				//"where a.busi_code = '033'\n"+
				//	"and a.trans_status = '0'\n"+
				//	"and a.app_amt > 0\n"+
				//(StringUtils.isNotBlank(busiCode)?"and a.busi_code = $S{busiCode}\n":"") +
				//(StringUtils.isNotBlank(acctNo)?"and a.acct_no = $S{acctNo}\n":"") +
				//(StringUtils.isNotBlank(prodCode)?"and a.prod_code = $S{prodCode}\n":"") +
				//"union all\n "+
				//"select\n "+
				//	"b.cur,\n"+
				//	"a.cust_no,\n"+
				//	"a.acct_no,\n"+
				//	"a.trans_acct_no,\n"+
				//	"a.prod_code,\n"+
				//	"a.trfr_amt - a.sale_charge as onway_amt,\n"+
				//	"'513' as busi_code,\n"+
				//	"a.busi_date as tran_date,\n"+
				//	"a.trfr_serno as app_serno,\n "+
				//	"a.trfr_serno as ori_app_serno\n "+
				//"from\n"+
				//	"trfr_cust_trans_log a\n"+
				//"left join fina_cust_trans_req_log b on a.rtn_trans_serno = b.app_serno\n"+
				//"where\n"+
				//	"trfr_status = '9'\n"+
				//(StringUtils.isNotBlank(busiCode)?"and a.busi_code = $S{busiCode}\n":"") +
				//(StringUtils.isNotBlank(acctNo)?"and a.acct_no = $S{acctNo}\n":"") +
				//(StringUtils.isNotBlank(prodCode)?"and a.prod_code = $S{prodCode}\n":"") +
				"   ) t order by tran_date desc,prod_code,cust_no,acct_no, app_serno", SubDatabase.DATABASE_FINA_CENTER,params);
		sqlResult.setDesensitized(false);

		return sqlResult;
	}
}