package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.prod.model.M208Detail;
import com.kayak.prod.model.M208Info;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 产品活动信息Dao
 *
 * <AUTHOR>
 * @date 2021-04-15 11:11
 */
@Repository
public class M208InfoDao extends ComnDao {

    public SqlResult<M208Info> findProdActiveInfoInfo(SqlParam<M208Info> params) throws Exception {
        return super.findRows("SELECT plan_no,share_class,plan_type,raise_due,discount,max_rate,plan_name,plan_status,crt_date,crt_time,upd_date,upd_time,discount_type,discount_info,active_start_time,active_end_time FROM prod_active_info ",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public SqlResult<M208Info> checkPlanNo(SqlParam<M208Info> params) throws Exception{
        String planNo = params.getModel().getPlanNo();
        return super.findRows("SELECT plan_no from prod_active_info where plan_no = '"+planNo+"' ", SubDatabase.DATABASE_SYS_CENTER, params);
    }


    public void insertProdActiveInfoInfo(SqlParam<M208Info> params) throws Exception {
        List<M208Detail> discountInfos = params.getModel().getDiscountInfos();
        Date temp = new Date();
        String dateStr = new SimpleDateFormat("YYYYMMdd").format(temp);
        String timeStr = new SimpleDateFormat("HHmmss").format(temp);
        params.getModel().setCrtDate(dateStr);
        params.getModel().setCrtTime(timeStr);
        params.getModel().setUpdDate(dateStr);
        params.getModel().setUpdTime(timeStr);

        DaoUtil.doTrans(() -> {
            super.update("INSERT INTO prod_active_info(plan_no,share_class,plan_type,raise_due,discount,max_rate,plan_name,plan_status,crt_date,crt_time,upd_date,upd_time,discount_type,discount_info,active_start_time,active_end_time) VALUES($S{planNo},$S{shareClass},$S{planType},$S{raiseDue},$S{discount},$S{maxRate},$S{planName},$S{planStatus},$S{crtDate},$S{crtTime},$S{updDate},$S{updTime},$S{discountType},$S{discountInfo},$S{activeStartTime},$S{activeEndTime})",
                    SubDatabase.DATABASE_SYS_CENTER, params.getModel());
            for (M208Detail detail : discountInfos) {
                detail.setPlanNo(params.getModel().getPlanNo());
                // 明细编号自增
                super.update("INSERT INTO prod_active_detail(plan_no,addition_type, float_value,dimension_min,dimension_max,discount_value)" +
                                " VALUES($S{planNo},$S{additionType},$D{floatValue},$D{dimensionMin},$D{dimensionMax},$D{discountValue})",
                        SubDatabase.DATABASE_SYS_CENTER, detail);
            }
        }, SubDatabase.DATABASE_SYS_CENTER);

    }

    public void updateProdActiveInfoInfo(SqlParam<M208Info> params) throws Exception {
        Date temp = new Date();
        String dateStr = new SimpleDateFormat("YYYYMMdd").format(temp);
        String timeStr = new SimpleDateFormat("HHmmss").format(temp);
        params.getModel().setUpdDate(dateStr);
        params.getModel().setUpdTime(timeStr);

        List<M208Detail> discountInfos = params.getModel().getDiscountInfos();
        DaoUtil.doTrans(() -> {
            super.update("UPDATE prod_active_info SET plan_name=$S{planName},discount_type=$S{discountType},discount_info = $S{discountInfo},plan_status=$S{planStatus},active_start_time=$S{activeStartTime},active_end_time = $S{activeEndTime},upd_date=$S{updDate},upd_time=$S{updTime} where plan_no = $S{planNo}", SubDatabase.DATABASE_SYS_CENTER, params.getModel());
            // 先删除
            super.update("DELETE FROM prod_active_detail WHERE plan_no = $S{planNo}", SubDatabase.DATABASE_SYS_CENTER, params.getModel());
            // 再新增
            for (M208Detail detail : discountInfos) {
                detail.setPlanNo(params.getModel().getPlanNo());
                // 明细编号自增
                super.update("INSERT INTO prod_active_detail(plan_no,addition_type, float_value,dimension_min,dimension_max,discount_value)" +
                                " VALUES($S{planNo},$S{additionType},$D{floatValue},$D{dimensionMin},$D{dimensionMax},$D{discountValue})",
                        SubDatabase.DATABASE_SYS_CENTER, detail);
            }
        }, SubDatabase.DATABASE_SYS_CENTER);
    }

    public int delete(SqlParam<M208Info> params) throws Exception {
        String planNo = params.getModel().getPlanNo();
        int num = super.findRow("SELECT count(*) as num FROM prod_active_relation WHERE plan_no = '"+planNo+"' ", SubDatabase.DATABASE_SYS_CENTER, params).getInteger("num");
        //判断是否存在产品关联了活动
        if (num > 0){
            throw new PromptException("该营销活动绑定了产品，请先解除产品活动绑定！");
        }
        int result0 = super.update("DELETE FROM prod_active_info WHERE plan_no = $S{planNo}", SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
        int result1 = super.update("DELETE FROM prod_active_detail WHERE plan_no = $S{planNo}", SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
        return (result0 > 0 && result1 > 0) ? 1 : 0;
    }
}
