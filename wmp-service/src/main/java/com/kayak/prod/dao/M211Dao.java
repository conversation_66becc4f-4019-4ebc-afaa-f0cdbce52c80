package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M211;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.kayak.fina.global.utils.Tools.formatSystemNo;

@Repository
public class M211Dao extends ComnDao {

	/************* M211Service *************/

	/**
	 * 查询分组信息
	 * @param params NULL
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M211> findGroups(SqlParam<M211> params) throws Exception {
		String sql = "SELECT" +
				" system_no,group_id, group_name, order_no" +
				" FROM prod_para_group_info where system_no in "+formatSystemNo(params.getModel().getSystemNoList()) +//busiessNo
				" ORDER BY order_no";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
	}

	/**
	 * 新增参数时，根据系统编号查询分组信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M211> findGroupsBySystemNo(SqlParam<M211> params) throws Exception {
		String sql = "SELECT" +
				" system_no,group_id, group_name, order_no" +
				" FROM prod_para_group_info where system_no='"+ params.getModel().getSystemNo()+"'" +
				" ORDER BY order_no";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
	}

	/**
	 * 新增分组信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public UpdateResult addGroup(SqlParam<M211> params) throws Exception {
		String sql = "INSERT INTO" +
				" prod_para_group_info" +
				" (system_no,group_id, group_name, order_no)" +
				" VALUES(" +
				" $S{systemNo}, $S{groupId}, $S{groupName}, $I{orderNo})";
		return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	/**
	 * 更新分组信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public UpdateResult updateGroup(SqlParam<M211> params) throws Exception {
		String sql = "UPDATE prod_para_group_info" +
				" SET group_name = $S{groupName}," +
				" order_no = $I{orderNo} " +
				" WHERE group_id = $S{groupId}";
		return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	/**
	 * 删除分组信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public UpdateResult deleteGroup(SqlParam<M211> params) throws Exception {
		String sql = "DELETE FROM prod_para_group_info WHERE group_id = $S{groupId}";
		return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	/**
	 *查询最大的分组编号
	 * @return
	 * @throws Exception
	 */
	public SqlRow selectMaxGroupId() throws Exception {

		return super.findRow("select max(group_id) max_group_id from prod_para_group_info where group_id like  '1%'",
				SubDatabase.DATABASE_SYS_CENTER);

	}

	/**
	 *检查同一个业务编号下是否存在相同的分组名称
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M211> checkGroupNameBySystemNo(SqlParam<M211> params) throws Exception {
		if(params.getModel().getType().equals("0")){
			return super.findRows("SELECT count(1) nums FROM prod_para_group_info " +
							" where system_no= '" + params.getModel().getSystemNo() + "' and group_name='"+params.getModel().getGroupName()+"'",
					SubDatabase.DATABASE_SYS_CENTER, params);
		}else {
			return super.findRows("SELECT count(1) nums FROM prod_para_group_info " +
							" where system_no= '" + params.getModel().getSystemNo() + "' and group_name='"+params.getModel().getGroupName()+"' and group_id != '"+params.getModel().getGroupId()+"'",
					SubDatabase.DATABASE_SYS_CENTER, params);
		}
	}

	/**
	 *查询是分组否被使用
	 * @return
	 * @throws Exception
	 */
	public List<SqlRow> selectGroupId() throws Exception{
		return super.findRows("select distinct group_id groupId from prod_model_group where model_id in (select distinct model_id from prod_para_detail)",
				SubDatabase.DATABASE_SYS_CENTER);
	}

}
