package com.kayak.prod.dao;

import com.kayak.aspect.annotations.API;
import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.M202;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

import static com.kayak.prod.utils.UnderlineToCamelUtils.sqlRowToCamel;

@Repository
public class M202Dao extends ComnDao {

	public List<SqlRow> findProdSaleTargets(FetcherData<M202> params) throws Exception {

		return super.findRows(
				"select t1.system_no,t1.tano,t1.prod_code,t1.legal_code,"
						+ "t1.target_type,t1.target_value,t1.show_type "
						+ "from prod_sale_target t1 "
						+ "where (t1.legal_code = $S{legalCode} or $S{legalCode} = '"+ params.getModel().getSuperLegalCode() +"') "
						+ (Tools.isEmptyObjOrString(params.getParamsDirect().get("systemNo")) ? "" : "and t1.system_no = $S{systemNo} ")
						+ (Tools.isEmptyObjOrString(params.getParamsDirect().get("tano")) ? "" : "and t1.tano = $S{tano} ")
						+ (Tools.isEmptyObjOrString(params.getParamsDirect().get("prodCode")) ? "" : "and t1.prod_code = $S{prodCode} ")
						+ (Tools.isEmptyObjOrString(params.getParamsDirect().get("targetType")) ? "" : "and t1.target_type = $S{targetType} ")
						+ (Tools.isEmptyObjOrString(params.getParamsDirect().get("targetValue")) ? "" : "and t1.target_value = $S{targetValue} ")
						+ "order by t1.system_no,t1.tano,t1.prod_code,t1.legal_code,t1.target_type,t1.target_value",
				SubDatabase.DATABASE_SYS_CENTER, params.getParamsDirect());
	}

	@API(desc = "额度服务调用销售对象查询")
	public SqlResult<M202> findProdSaleTargetsForQuota(SqlParam<M202> params) throws Exception {

		return super.findRows(
				" select t1.system_no,\n" +
						"       t1.tano,\n" +
						"       t1.prod_code,\n" +
						"       t1.legal_code,\n" +
						"       t1.target_type,\n" +
						"       t1.target_value,\n" +
						"       t1.show_type\n" +
						"  from prod_sale_target t1\n" +
						" where (t1.legal_code = $S{legalCode} or\n" +
						"       $S{legalCode} = '+ params.getModel().getSuperLegalCode() +')\n" +
						"   and t1.system_no = $S{systemNo}\n" +
						"   and t1.tano = $S{tano}\n" +
						"   and t1.prod_code = $S{prodCode}\n" +
						"   and t1.target_type = $S{targetType}\n" +
						"   and t1.show_type = $S{showType}\n" +
						" order by t1.system_no, t1.tano, t1.prod_code, t1.legal_code ",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public UpdateResult addProdSaleTarget(Map<String, Object> map) throws Exception {
		return super.update("insert into prod_sale_target(system_no,tano,prod_code,legal_code,target_type,target_value,show_type) VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{targetType},$S{targetValue},$S{showType})",
				SubDatabase.DATABASE_SYS_CENTER, map);
	}
	
	public UpdateResult updateProdSaleTarget(SqlParam<M202> params) throws Exception {
		return super.update("update prod_sale_target set show_type=$S{showType}, target_value=$S{targetValue} where system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and (legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode}) and target_type=$S{targetType} and target_value=$S{oldTargetValue} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}
	
	public UpdateResult deleteProdSaleTarget(SqlParam<M202> params) throws Exception {
		return super.update("delete from prod_sale_target where system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and (legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode}) and target_type=$S{targetType} and target_value=$S{targetValue}",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	/**
	 * 修改SQL兼容MySql
	 * modify by wangzj
	 * @date 2022年3月3日16:00:32
	 */
	public SqlResult findM202ByProd(SqlParam<M202> params) throws Exception {
		String sql = "SELECT" +
				" system_no system_no," +
				" tano tano," +
				" prod_code prod_code," +
				" legal_code legal_code," +
				" target_type target_type," +
				" group_concat(target_value) target_value," +
				" show_type show_type" +
				" FROM" +
				" prod_sale_target " +
				" WHERE" +
				" system_no = $S{systemNo} " +
				" AND tano = $S{tano} " +
				" AND prod_code = $S{prodCode} " +
				" AND legal_code = $S{legalCode}" +
				" GROUP BY system_no,tano,prod_code,legal_code,target_type,show_type";
		List<SqlRow> rows = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
		List<SqlRow> sqlRowList = sqlRowToCamel(rows);
		return SqlResult.build(sqlRowList);
	}
	
}
