package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.constants.SystemNo;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M503;
import com.kayak.fina.param.model.ProdAcctInfo;
import com.kayak.graphql.model.FetcherData;
import com.kayak.prod.model.Discount;
import com.kayak.prod.model.M202;
import com.kayak.prod.model.M204;
import com.kayak.prod.model.M210;
import com.kayak.prod.model.M215;
import com.kayak.prod.model.ProdIndexInfo;
import com.kayak.prod.model.ProdOpenRule;
import com.kayakwise.wmp.base.exception.TransException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class M215Dao extends ComnDao {

    /**
     * 查询产品参数明细信息
     *
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M215> findM215(SqlParam<M215> params) throws Exception {
        return super.findRows("select * from prod_para_detail",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public UpdateResult addM215(M215 m215) throws Exception {
        return super.update("INSERT INTO prod_para_detail " +
                        " (para_code, pk_value, model_id, para_value, exceed_flag) " +
                        " VALUES ($S{paraCode}, $S{pkValue}, $S{modelId}, $S{paraValue},$S{exceedFlag})",
                SubDatabase.DATABASE_SYS_CENTER, m215);
    }

    public UpdateResult batchAddM215(M215 m215) throws Exception {
        UpdateResult result = new UpdateResult();
        String prodName = null;
        String systemNo = null;
        for (M215 prod : m215.getList()) {
            if ("prod_name".equals(prod.getParaCode())) {
                prodName = prod.getParaValue();
            } else if ("system_no".equals(prod.getParaCode())) {
                systemNo = prod.getParaValue();
            }
        }
        // 校验产品名是否重复
        if (prodName != null) {
            String query = "select 1 from prod_para_detail " +
                    "where pk_value in" +
                    " (SELECT pk_value FROM prod_para_detail " +
                    "WHERE para_code ='prod_name' " +
                    "AND para_value='" + prodName + "') " +
                    "and para_code='system_no' " +
                    "and para_value='" + systemNo + "' ";
            SqlRow row = super.findRow(query, null);
            if (row != null) {
                throw new PromptException("该系统下已经存在同名的产品参数：prod_name-" + prodName);
            }
        }
        // 校验产品代码是否重复
        String query = "SELECT 1 " +
                "FROM prod_para_detail " +
                "WHERE " +
                " pk_value='" + m215.getList().get(0).getPkValue() + "' ";
        SqlRow row = super.findRow(query, null);
        if (row != null) {
            throw new PromptException("产品代码已存在，请重新定义");
        }
        // 事务插入
        doTrans(() -> {
            String sql = "INSERT INTO prod_para_detail " +
                    " (para_code, pk_value, model_id, para_value, exceed_flag) " +
                    " VALUES ($S{paraCode}, $S{pkValue}, $S{modelId}, $S{paraValue},$S{exceedFlag})";
            // 插入产品参数
            for (M215 model : m215.getList()) {
                super.update(sql, SubDatabase.DATABASE_SYS_CENTER, model);
            }
            // 插入产品标签
            if (m215.getLabelList() != null && m215.getLabelList().size() > 0) {
                String sql1 = "INSERT INTO prod_label_relation(system_no,tano,prod_code,legal_code,label_code,label_desc) VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{labelCode},$S{labelDesc})";
                for (M204 m204 : m215.getLabelList()) {
                    super.update(sql1, SubDatabase.DATABASE_SYS_CENTER, m204);
                }
            }

            // 插入理财账号
            if (m215.getBankCardNoList() != null && m215.getBankCardNoList().size() > 0) {
                for (ProdAcctInfo prodAcctInfo : m215.getBankCardNoList()) {
                    String sql3 = "INSERT INTO " + prodAcctInfo.getSystemNo() + "_prod_acct_info(prod_code, tano, legal_code, prod_acct_type, acct_serno, remark, create_time,update_time)" +
                            " VALUES($S{prodCode}, $S{tano}, $S{legalCode}, $S{prodAcctType}, $S{acctSerno}, $S{remark},sysdate(),sysdate())";
                    super.update(sql3, SubDatabase.DATABASE_SYS_CENTER, prodAcctInfo);
                }
            }
            // 插入销售对象
            if (m215.getTargetList() != null && m215.getTargetList().size() > 0) {
                String sql4 = "insert into prod_sale_target(system_no,tano,prod_code,legal_code,target_type,target_value,show_type) VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{targetType},$S{targetValue},$S{showType})";
                for (M202 m202 : m215.getTargetList()) {
                    String[] targetValues = m202.getTargetValue().split(",");
                    for (String targetValue : targetValues) {
                        m202.setTargetValue(targetValue);
                        super.update(sql4, SubDatabase.DATABASE_SYS_CENTER, m202);
                    }
                }
            }

            String system_no = m215.getSystemNo();
            String tano = m215.getTano();
            String prod_code = m215.getProdCode();
            String legal_code = m215.getLegalCode();
            // 插入产品周期规则
            if (m215.getOpenRuleList() != null && m215.getOpenRuleList().size() > 0) {
                // 插入
                String sql5 = "INSERT INTO prod_open_rule(system_no,tano,prod_code,legal_code,number,open_rule,weekend_rule,open_date,open_type,index_key)" +
                        " VALUES('" + system_no + "', '" + tano + "', '" + prod_code + "', '" + legal_code + "'," +
                        "$I{number}, $S{openRule}, $S{weekendRule}, $S{openDate}, $S{openType}, $I{indexKey})";
                List<ProdOpenRule> openRuleList = m215.getOpenRuleList();
                for (int i = 0; i < openRuleList.size(); i++) {
                    ProdOpenRule prodOpenRule = openRuleList.get(i);
                    prodOpenRule.setIndexKey(i);
                    super.update(sql5, SubDatabase.DATABASE_SYS_CENTER, prodOpenRule);
                }
            }
            // 插入产品开放周期日历
            if (m215.getProdCalendar() != null && m215.getProdCalendar().size() > 0) {
                // 插入产品开放周期
                String sql6 = "INSERT INTO fina_prod_calendar(tano, prod_code, legal_code, system_no, sys_date," +
                        " reserve_date_flag, reserve_invalid_da, order_date_flag, subs_date_flag," +
                        " establish_date_flag, value_date_flag, apply_date_flag, apply_ack_date_flag," +
                        " redeem_date_flag, redeem_ack_date_flag, registered_date_flag, convert_date_flag," +
                        " winding_date_flag, pay_date_flag)" +
                        " VALUES ('" + tano + "', '" + prod_code + "', '" + legal_code + "', '" + system_no + "', $S{sysDate}," +
                        " $S{reserveDateFlag}, $S{reserveInvalidDa}, $S{orderDateFlag}, $S{subsDateFlag}," +
                        " $S{establishDateFlag}, $S{valueDateFlag}, $S{applyDateFlag}, $S{applyAckDateFlag}," +
                        " $S{redeemDateFlag}, $S{redeemAckDateFlag}, $S{registeredDateFlag}, $S{convertDateFlag}," +
                        " $S{windingDateFlag}, $S{payDateFlag})";
                List<M503> prodCalendar = m215.getProdCalendar();
                for (M503 m503 : prodCalendar) {
                    super.update(sql6, SubDatabase.DATABASE_FINA_CENTER, m503);
                }
            }
        });
        return new UpdateResult();
    }

    public UpdateResult modifyM215(M215 m215) throws Exception {
        String pkValue = m215.getList().get(0).getPkValue();
        String prodName = null;
        for (M215 prod : m215.getList()) {
            if ("prod_name".equals(prod.getParaCode())) {
                prodName = prod.getParaValue();
            }
        }
        // 校验产品名是否重复
        if (prodName != null) {
            String query = "SELECT pk_value FROM prod_para_detail " +
                    "WHERE para_code = 'prod_name' " +
                    " AND para_value='" + prodName + "' ";
            SqlRow row = super.findRow(query, null);
            if (row != null && !pkValue.equals(row.getString("pk_value"))) {
                throw new PromptException("产品名称已存在，请重新命名");
            }
        }
        doTrans(() -> {
            // 删除产品参数
            deleteM215(m215.getPkValue());
            String sql = "INSERT INTO prod_para_detail " +
                    " (para_code, pk_value, model_id, para_value, exceed_flag) " +
                    " VALUES ($S{paraCode}, $S{pkValue}, $S{modelId}, $S{paraValue},$S{exceedFlag})";
            // 插入产品参数
            for (M215 model : m215.getList()) {
                super.update(sql, SubDatabase.DATABASE_SYS_CENTER, model);
            }
            // 插入产品标签
            if (m215.getLabelList() != null) {
                // 删除产品标签关联关系
                String sql1 = "DELETE FROM prod_label_relation " +
                        " WHERE" +
                        " system_no = $S{systemNo} " +
                        " AND tano = $S{tano} " +
                        " AND prod_code = $S{prodCode} " +
                        " AND legal_code = $S{legalCode}";
                super.update(sql1, SubDatabase.DATABASE_SYS_CENTER, m215);
                // 插入
                sql1 = "INSERT INTO prod_label_relation(system_no,tano,prod_code,legal_code,label_code,label_desc)" +
                        " VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{labelCode},$S{labelDesc})";
                for (M204 m204 : m215.getLabelList()) {
                    super.update(sql1, SubDatabase.DATABASE_SYS_CENTER, m204);
                }
            }
            // 插入产品折扣率方案
            // 删除产品关联的折扣率方案 ,不管数量是否大于0，都应先执行删除 --20220303
            String sqlDiscount = "";//折扣率执行sql
            if ("FINA".equals(m215.getSystemNo())) {
                sqlDiscount = "DELETE FROM fina_prod_discount " +
                        "WHERE " +
                        " tano = $S{tano} " +
                        " AND prod_code = $S{prodCode} ";
                super.update(sqlDiscount, SubDatabase.DATABASE_FINA_CENTER, m215);
                if (m215.getDiscountList() != null && m215.getDiscountList().size() > 0) {
                    // 插入产品折扣率方案
                    sqlDiscount = "INSERT INTO fina_prod_discount(prod_code, tano, discount_code,busi_code, begin_date)" +
                            " VALUES ($S{prodCode}, $S{tano}, $S{discountCode},$S{busiCode}, $S{beginDate})";
                    for (Discount discount : m215.getDiscountList()) {
                        super.update(sqlDiscount, SubDatabase.DATABASE_FINA_CENTER, discount);
                    }
                }
            } else {
                sqlDiscount = "DELETE FROM fund_prod_discount " +
                        "WHERE " +
                        " tano = $S{tano} " +
                        " AND prod_code = $S{prodCode} ";
                super.update(sqlDiscount, SubDatabase.DATABASE_FUND_CENTER, m215);
                if (m215.getDiscountList() != null && m215.getDiscountList().size() > 0) {
                    // 插入产品折扣率方案
                    sqlDiscount = "INSERT INTO fund_prod_discount(prod_code, tano, discount_code, busi_code, begin_date)" +
                            " VALUES ($S{prodCode}, $S{tano}, $S{discountCode},$S{busiCode}, $S{beginDate})";
                    for (Discount discount : m215.getDiscountList()) {
                        super.update(sqlDiscount, SubDatabase.DATABASE_FUND_CENTER, discount);
                    }
                }
            }
            // 插入理财账号
            // TODO 产品理财账号表可能需要加入系统编号字段
            if (m215.getBankCardNoList() != null && m215.getBankCardNoList().size() > 0) {
                String flag = "1";
                if (m215.getBankCardNoList().size() == 1) {
                    List<ProdAcctInfo> bankCardNoList = m215.getBankCardNoList();
                    if (null != bankCardNoList.get(0).getProdAcctType() && !bankCardNoList.get(0).getProdAcctType().equals("0")
                            && !bankCardNoList.get(0).getProdAcctType().equals("1")) {
                        flag = "0";
                        throw new PromptException("账户信息仅有一条时,账号类型必须为归集户或还款户!");
                    }
                }
                if (flag.equals("1")) {
                    if ("FINA".equals(m215.getSystemNo())) {
                        // 删除
                        String sql3 = "DELETE FROM fina_prod_acct_info " +
                                "WHERE" +
                                " prod_code=$S{prodCode}" +
                                " AND tano=$S{tano}" +
                                " AND legal_code=$S{legalCode}";
                        super.update(sql3, SubDatabase.DATABASE_FINA_CENTER, m215);
                        // 插入
                        // START wangzj 2022/3/23 兼容MySql
                        sql3 = "INSERT INTO fina_prod_acct_info(prod_code, tano, legal_code, prod_acct_type, acct_serno, remark, create_time,update_time)" +
                                " VALUES($S{prodCode}, $S{tano}, $S{legalCode}, $S{prodAcctType}, $S{acctSerno}, $S{remark},sysdate(),sysdate())";
                        // END ####~.~
                        for (ProdAcctInfo prodAcctInfo : m215.getBankCardNoList()) {
                            super.update(sql3, SubDatabase.DATABASE_FINA_CENTER, prodAcctInfo);
                        }
                    } else {
                        // 删除
                        String sql3 = "DELETE FROM fund_prod_acct_info " +
                                "WHERE" +
                                " prod_code=$S{prodCode}" +
                                " AND tano=$S{tano}" +
                                " AND legal_code=$S{legalCode}";
                        super.update(sql3, SubDatabase.DATABASE_FUND_CENTER, m215);
                        // 插入
                        sql3 = "INSERT INTO fund_prod_acct_info(prod_code, tano, legal_code, prod_acct_type, acct_serno, remark, create_time,update_time)" +
                                " VALUES($S{prodCode}, $S{tano}, $S{legalCode}, $S{prodAcctType}, $S{acctSerno}, $S{remark},sysdate(),sysdate())";
                        for (ProdAcctInfo prodAcctInfo : m215.getBankCardNoList()) {
                            super.update(sql3, SubDatabase.DATABASE_FUND_CENTER, prodAcctInfo);
                        }
                    }
                }
            } else {
                throw new PromptException("请先添加一条账户信息");
            }
            // 插入销售对象
            String sql4 = "";
            //无论有没有销售对象列表，都先执行删除  --2022.01.08
            // 删除
            sql4 = "DELETE FROM prod_sale_target " +
                    "WHERE" +
                    " system_no = $S{systemNo} " +
                    " AND tano = $S{tano} " +
                    " AND prod_code = $S{prodCode} " +
                    " AND legal_code = $S{legalCode}";
            super.update(sql4, SubDatabase.DATABASE_SYS_CENTER, m215);
            if (m215.getTargetList() != null && m215.getTargetList().size() > 0) {
                List<M202> targetList = m215.getTargetList();
                targetList.stream().forEach(M202 -> {
                    if (StringUtils.isNotBlank(M202.getTargetValue())) {
                        M202.setTargetValue(M202.getTargetValue().replaceAll("\\[|\\]", "").replaceAll("\"", ""));
                    }
                });
                // 插入
                sql4 = "insert into prod_sale_target(system_no,tano,prod_code,legal_code,target_type,target_value,show_type) VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{targetType},$S{targetValue},$S{showType})";
                for (M202 m202 : m215.getTargetList()) {
                    String[] targetValues = m202.getTargetValue().split(",");
                    for (String targetValue : targetValues) {
                        m202.setTargetValue(targetValue);
                        super.update(sql4, SubDatabase.DATABASE_SYS_CENTER, m202);
                    }
                }
            }

            String system_no = m215.getSystemNo();
            String tano = m215.getTano();
            String prod_code = m215.getProdCode();
            String legal_code = m215.getLegalCode();
            // 插入产品周期规则
//			if (m215.getOpenRuleList() != null) {
//				// 删除
//				String sql5 = "DELETE FROM prod_open_rule " +
//						"WHERE" +
//						" system_no = $S{systemNo} " +
//						" AND tano = $S{tano} " +
//						" AND prod_code = $S{prodCode} " +
//						" AND legal_code = $S{legalCode}";
//				super.update(sql5, SubDatabase.DATABASE_SYS_CENTER, m215);
//				// 插入
//				sql5 = "INSERT INTO prod_open_rule(system_no,tano,prod_code,legal_code,number,open_rule,weekend_rule,open_date,open_type,index_key)" +
//						" VALUES('" + system_no + "', '" + tano + "', '" + prod_code + "', '" + legal_code + "'," +
//						"$I{number}, $S{openRule}, $S{weekendRule}, $S{openDate}, $S{openType}, $I{indexKey})";
//				List<ProdOpenRule> openRuleList = m215.getOpenRuleList();
//				for (int i = 0; i < openRuleList.size(); i++) {
//					ProdOpenRule prodOpenRule = openRuleList.get(i);
//					prodOpenRule.setIndexKey(i);
//					super.update(sql5, SubDatabase.DATABASE_SYS_CENTER, prodOpenRule);
//				}
//			}
            // 插入产品开放周期日历
            if (m215.getProdCalendar() != null && !m215.getProdCalendar().isEmpty()) {
                // 删除产品开放周期
                String sql6 = "DELETE FROM fina_prod_calendar " +
                        "WHERE" +
                        " tano = $S{tano} " +
                        " AND prod_code = $S{prodCode} " +
                        " AND legal_code = $S{legalCode}";
                super.update(sql6, SubDatabase.DATABASE_FINA_CENTER, m215);
                // 插入产品开放周期
                sql6 = "INSERT INTO fina_prod_calendar(tano, prod_code, legal_code, sys_date," +
                        " reserve_date_flag, reserve_invalid_da, order_date_flag, subs_date_flag," +
                        " establish_date_flag, value_date_flag, apply_date_flag, apply_ack_date_flag," +
                        " redeem_date_flag, redeem_ack_date_flag, registered_date_flag, convert_date_flag," +
                        " winding_date_flag, pay_date_flag)" +
                        " VALUES ('" + tano + "', '" + prod_code + "', '" + legal_code + "', $S{sysDate}," +
                        " $S{reserveDateFlag}, $S{reserveInvalidDa}, $S{orderDateFlag}, $S{subsDateFlag}," +
                        " $S{establishDateFlag}, $S{valueDateFlag}, $S{applyDateFlag}, $S{applyAckDateFlag}," +
                        " $S{redeemDateFlag}, $S{redeemAckDateFlag}, $S{registeredDateFlag}, $S{convertDateFlag}," +
                        " $S{windingDateFlag}, $S{payDateFlag})";
                List<M503> prodCalendar = m215.getProdCalendar();
                for (M503 m503 : prodCalendar) {
                    super.update(sql6, SubDatabase.DATABASE_FINA_CENTER, m503);
                }

            }
        });
        return new UpdateResult();
    }

    /**
     * 效率太慢，已弃用
     */
    public SqlResult<M215> findM215All(SqlParam<M215> params) throws Exception {
        return super.findRows("SELECT max(IF(para_code='legal_code',para_value,NULL)) as legal_code, " +
                        " max(IF(para_code='parent_code',para_value,NULL)) as parent_code," +
                        " max(IF(para_code='system_no',para_value,NULL)) as system_no," +
                        " max(IF(para_code='prod_code',para_value,NULL)) as prod_code," +
                        " max(IF(para_code='prod_name',para_value,NULL)) as prod_name," +
                        " max(IF(para_code='prod_short_name',para_value,NULL)) as prod_short_name," +
                        " max(IF(para_code='prod_type',para_value,NULL)) as prod_type," +
                        " max(IF(para_code='regist_code',para_value,NULL)) as regist_code," +
                        " max(IF(para_code='sale_status',para_value,NULL)) as sale_status," +
                        " max(IF(para_code='tano',para_value,NULL)) as tano" +
                        " FROM  prod_para_detail" +
                        " GROUP BY pk_value",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    public int deleteM215(String pkValue) throws Exception {
        return super.update("delete from prod_para_detail where pk_value = '" + pkValue + "'").getEffect();
    }

    public int saleOnProd(String pkValue) throws Exception {
        String sql = "update prod_para_detail set para_value = '1' where pk_value = '" + pkValue + "' and para_code = 'prod_status'";
        return super.update(sql).getEffect();
    }

    public int saleOffProd(String pkValue) throws Exception {
        String sql = "update prod_para_detail set para_value = '0' where pk_value = '" + pkValue + "' and para_code = 'prod_status'";
        return super.update(sql).getEffect();
    }

    public M215 getProdstatus(String pkValue) throws Exception {
        String sql = "select * from prod_para_detail where pk_value = '" + pkValue + "' and para_code = 'prod_status'";
        M215 m215 = super.findRow(M215.class, sql, SubDatabase.DATABASE_SYS_CENTER, null);
        return m215;
    }

    /**
     * 查询产品基本参数
     *
     * @param param
     * @return
     * @throws Exception
     */
    public SqlResult<M210> findAllBaseParams(SqlParam<M215> param) throws Exception {
        String systemNo = param.getModel().getSystemNo();
        // 默认查询理财代销的产品基本组件参数
        if (Tools.isBlank(systemNo)) {
            systemNo = SystemNo.FINA;
        }
        if (!SystemNo.FINA.equals(systemNo)) {
            throw new TransException("M21501","系统编号错误");
        }
		/*String sql = "SELECT para_code, para_name, dict" +
				" FROM prod_para_pool " +
				" WHERE" +
				" para_code IN ( SELECT para_code FROM prod_comp_para WHERE comp_id = ( SELECT comp_id FROM prod_comp_info WHERE comp_type = '0' AND system_no = '" + systemNo + "' LIMIT 1 ) ) " +
				" AND system_no = '" + systemNo + "' " +
				" ORDER BY order_no";*/
        String sql = "SELECT para_code, para_name, dict, show_flag" +
                " FROM prod_para_pool " +
                " WHERE system_no = '" + systemNo + "' AND show_flag='1'" +
                " ORDER BY order_no";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, new FetcherData<>(new HashMap<>(), M210.class));
    }

    public List<ProdOpenRule> findProdOpenRules(SqlParam<M215> param) throws Exception {
        String sql = "select system_no, tano, prod_code, legal_code, number, open_rule," +
                " weekend_rule, open_date, open_type, index_key" +
                " from prod_open_rule" +
                " where system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and legal_code=$S{legalCode}" +
                " order by index_key";
        return super.findRows(ProdOpenRule.class, sql, SubDatabase.DATABASE_SYS_CENTER, param.getModel());
    }

    public List<Map> findTano(String systemNO) throws Exception {
        if ("FINA".equalsIgnoreCase(systemNO)) {
            String sql = "select tano as no, ta_name as name from " + systemNO + "_TA_INFO where TA_STATUS='1'";
            return super.findRows(Map.class, sql, SubDatabase.DATABASE_FINA_CENTER, null);
        } else if ("FUND".equalsIgnoreCase(systemNO)) {
            String sql = "select tano as no, ta_name as name from " + systemNO + "_TA_INFO where TA_STATUS='1'";
            return super.findRows(Map.class, sql, SubDatabase.DATABASE_FUND_CENTER, null);
        } else {
            throw new TransException("99999", "TA代码查询失败：" + systemNO);
        }

    }

    public List<Map> findTanoSep(String systemNO) throws Exception {
        if ("FINA".equalsIgnoreCase(systemNO)) {
            String sql = "select tano as no, ta_name as name from " + systemNO + "_TA_INFO ";
            return super.findRows(Map.class, sql, SubDatabase.DATABASE_FINA_CENTER, null);
        } else if ("FUND".equalsIgnoreCase(systemNO)) {
            String sql = "select tano as no, ta_name as name from " + systemNO + "_TA_INFO ";
            return super.findRows(Map.class, sql, SubDatabase.DATABASE_FUND_CENTER, null);
        } else {
            throw new TransException("99999", "TA代码查询失败：" + systemNO);
        }

    }

    /**
     * 获取去重pkValue
     */
    public List<String> listPkValue() throws Exception {
        String sql = "select distinct pk_value from prod_para_detail";
        return super.findRows(String.class, sql, SubDatabase.DATABASE_SYS_CENTER, null);
    }

    public List<M215> listParams(String pkValue) throws Exception {
        String sql = "select * from prod_para_detail where pk_value = '" + pkValue + "'";
        return super.findRows(M215.class, sql, SubDatabase.DATABASE_SYS_CENTER, null);
    }

    public List<ProdIndexInfo> getProdIndexInfos(SqlParam<M215> param) throws Exception {
        String sql = "select * from prod_index_info where 1=1 ";
        M215 model = param.getModel();
        if (StringUtils.isNotBlank(model.getTano())) {
            sql += " and tano=$S{tano} ";
        }
        if (StringUtils.isNotBlank(model.getProdCode())) {
            sql += " and prod_code=$S{prodCode} ";
        }
        if (StringUtils.isNotBlank(model.getModelId())) {
            sql += " and model_id=$S{modelId} ";
        }
        if (StringUtils.isNotBlank(model.getProdStatus())) {
            sql += " and prod_status=$S{prodStatus} ";
        }
        if (StringUtils.isNotBlank(model.getProdRiskLevel())) {
            sql += " and prod_risk_level=$S{prodRiskLevel} ";
        }
        if (StringUtils.isNotBlank(model.getSaleStatus())) {
            sql += " and sale_status=$S{saleStatus} ";
        }
        if (StringUtils.isNotBlank(model.getCanBuy())) {
            sql += " and can_buy=$S{canBuy} ";
        }
        if (StringUtils.isNotBlank(model.getCanRedeem())) {
            sql += " and can_redeem=$S{canRedeem} ";
        }
        int start = param.getStart();
        int limit = param.getLimit();
        sql += " order by establish_date desc limit " + start + " , " + limit;
        return super.findRows(ProdIndexInfo.class, sql, SubDatabase.DATABASE_SYS_CENTER, model);

    }

    public int findProdIndexInfoCounts(SqlParam<M215> param) throws Exception {
        String sql = "SELECT COUNT(1) count FROM prod_index_info where 1=1 ";
        M215 model = param.getModel();
        if (StringUtils.isNotBlank(model.getTano())) {
            sql += " and tano=$S{tano} ";
        }
        if (StringUtils.isNotBlank(model.getProdCode())) {
            sql += " and prod_code=$S{prodCode} ";
        }
        if (StringUtils.isNotBlank(model.getModelId())) {
            sql += " and model_id=$S{modelId} ";
        }
        if (StringUtils.isNotBlank(model.getProdStatus())) {
            sql += " and prod_status=$S{prodStatus} ";
        }
        if (StringUtils.isNotBlank(model.getProdRiskLevel())) {
            sql += " and prod_risk_level=$S{prodRiskLevel} ";
        }
        if (StringUtils.isNotBlank(model.getSaleStatus())) {
            sql += " and sale_status=$S{saleStatus} ";
        }
        if (StringUtils.isNotBlank(model.getCanRedeem())) {
            sql += " and can_redeem=$S{canRedeem} ";
        }
        if (StringUtils.isNotBlank(model.getCanBuy())) {
            sql += " and can_buy=$S{canBuy} ";
        }
        List<SqlRow> datas = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, param.getModel());
        return datas.get(0).getInteger("count");
    }
}
