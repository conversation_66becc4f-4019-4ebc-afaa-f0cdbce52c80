package com.kayak.prod.dao;

import com.kayak.prod.model.M205;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.common.constants.SubDatabase;

@Repository
public class M205Dao extends ComnDao {

	/**
	 * @update hyb_黄英玻 2022/3/28 15:38 mysql用date_format，Oracle用to_char, %Y-%m-%d
	 * @update hyb_黄英玻 2022/4/1 16:04 新增批次号字段查询
	 * */
	public SqlResult<M205> findProdDocumentInfo(SqlParam<M205> params) throws Exception {
		return super.findRows("SELECT system_no,tano,prod_code,legal_code,doc_type,online_flag,doc_version,doc_path,doc_status,doc_name,date_format(update_time,'%Y-%m-%d') AS update_time,content_id FROM prod_document_info where 1=1  order by update_time desc",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	/** @update hyb_黄英玻 2022/3/28 17:16 mysql用now(),oracle用sysdate */
	public UpdateResult addProdDocumentInfo(SqlParam<M205> params) throws Exception {
		return super.update("INSERT INTO prod_document_info(system_no,tano,prod_code,legal_code,doc_type,online_flag,doc_version,doc_path,doc_status,doc_name,update_time) VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{docType},$S{onlineFlag},$S{docVersion},$S{docPath},$S{docStatus},$S{docName},now())",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	/** @update hyb_黄英玻 2022/3/28 17:16 mysql用now(),oracle用sysdate */
	public UpdateResult updateProdDocumentInfo(SqlParam<M205> params) throws Exception {
		return super.update("UPDATE prod_document_info SET doc_path=$S{docPath}, doc_name=$S{docName}, update_time=now() WHERE system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND (legal_code = $S{legalCode} OR $S{legalCode} = $S{superLegalCode}) AND doc_type=$S{docType} AND doc_version=$S{docVersion} AND online_flag=$S{onlineFlag} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	/**
	 * @update hyb_黄英玻 2022/3/29 19:01 mysql用now(),oracle用sysdate
	 * @update hyb_黄英玻 2022/4/1 16:00 SQL新增批次号更新
	 * */
	public UpdateResult updateStatus(M205 params) throws Exception {
		return super.update("UPDATE prod_document_info SET doc_status=$S{docStatus}, content_id=$S{contentId}, update_time=now() WHERE system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND (legal_code = $S{legalCode} OR $S{legalCode} = $S{superLegalCode}) AND doc_type=$S{docType} AND doc_version=$S{docVersion} AND online_flag=$S{onlineFlag} ",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}
	
	public UpdateResult deleteProdDocumentInfo(SqlParam<M205> params) throws Exception {
		return super.update("DELETE FROM prod_document_info WHERE  system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND (legal_code = $S{legalCode} OR $S{legalCode} = $S{superLegalCode}) AND doc_type=$S{docType} AND doc_version=$S{docVersion} AND online_flag=$S{onlineFlag} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

}
