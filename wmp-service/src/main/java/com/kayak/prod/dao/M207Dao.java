package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M207;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M207Dao extends ComnDao {

    public SqlResult<M207> findProdActiveInfos(SqlParam<M207> params) throws Exception {
        SqlResult<M207> result =super.findRows("select a.prod_code,a.supply_code,a.prod_name,a.prod_type,b.active_type,c.plan_no,c.plan_name,c.plan_status,c.active_start_time,c.active_end_time from prod_info a inner join prod_active_relation b on a.supply_code = b.supply_code and a.prod_code = b.prod_code inner join prod_active_info c on b.plan_no = c.plan_no", SubDatabase.DATABASE_SYS_CENTER, params);
        // 维护总条数
        return result;
    }

    public UpdateResult addProdActiveInfo(SqlParam<M207> params) throws Exception {
        return super.update("INSERT INTO prod_active_relation(supply_code,prod_code,active_type,plan_no) VALUES($S{supplyCode},$S{prodCode},$S{activeType},$S{planNo})",
                SubDatabase.DATABASE_SYS_CENTER,params.getModel());
    }

/*    public UpdateResult updateProdActiveInfo(SqlParam<CustGroupInfo> params) throws Exception {
        return super.update("UPDATE prod_active_relation SET WHERE  supply_code=$S{supplyCode} and prod_code=$S{prodCode} and active_type=$S{activeType} and plan_no=$S{planNo}",
                SubDatabase.DATABASE_SYS_CENTER,params.getModel());
    }*/

    public UpdateResult deleteProdActiveInfo(SqlParam<M207> params) throws Exception {
        return super.update("DELETE FROM prod_active_relation WHERE  supply_code=$S{supplyCode} and prod_code=$S{prodCode} and active_type=$S{activeType} and plan_no=$S{planNo} ",
                SubDatabase.DATABASE_SYS_CENTER,params.getModel());
    }

    public SqlResult<M207> findPlanNos(SqlParam<M207> params) throws Exception {
        SqlResult<M207> result = new SqlResult<>();
        String activeType = params.getModel().getActiveType();
        String activeName = "";
        if (StringUtils.isNotBlank(activeType)){
            switch (activeType){
                case "0" :
                    activeName = "红包";
                    break;
                case "1" :
                    activeName = "团购";
                    break;
                case "2" :
                    activeName = "健步";
                    break;
                case "3" :
                    activeName = "分享";
                    break;
                default:
                    break;
            }
        }
        if (StringUtils.isNotBlank(activeName)){
            result = super.findRows("select plan_no,plan_name from prod_active_info where plan_type = '"+activeName+"' ", SubDatabase.DATABASE_SYS_CENTER, params);
        }else{
            result = super.findRows("select plan_no,plan_name from prod_active_info", SubDatabase.DATABASE_SYS_CENTER, params);
        }
        return result;
    }
}
