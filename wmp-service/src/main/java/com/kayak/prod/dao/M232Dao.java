package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.prod.model.M232;
import org.springframework.stereotype.Repository;

@Repository
public class M232Dao extends ComnDao {

	/**
	 * 新增基金账号划款信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int addM232(SqlParam<M232> params) throws Exception {
		return super.update("INSERT INTO fund_prod_acct_info(tano,prod_code,legal_code,prod_acct_type,acct_serno) VALUES($S{tano},$S{prodCode},$S{legalCode},$S{prodAcctType},$S{acctSerno})", SubDatabase.DATABASE_FUND_CENTER,params.getModel()).getEffect();
	}
	/**
	 * 修改基金账号划款信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int updateM232(SqlParam<M232> params) throws Exception {
		return super.update("UPDATE fund_prod_acct_info SET acct_serno=$S{acctSerno} where tano=$S{tano} AND  prod_code=$S{prodCode} AND legal_code=$S{legalCode}  AND prod_acct_type=$S{prodAcctType} ", SubDatabase.DATABASE_FUND_CENTER,params.getModel()).getEffect();
	}

	/**
	 * 删除基金账号划款信息
	 * @return
	 * @throws Exception
	 */
	public int deleteM232(SqlParam<M232> params) throws Exception {
		return super.update("DELETE FROM fund_prod_acct_info WHERE tano=$S{tano} AND  prod_code=$S{prodCode} AND legal_code=$S{legalCode}  AND prod_acct_type=$S{prodAcctType}",SubDatabase.DATABASE_FUND_CENTER,params.getModel()).getEffect();
	}

	/**
	 * 查询基金账号划款信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M232> find(SqlParam<M232> params) throws Exception {
		return super.findRows("SELECT tano,prod_code,legal_code,to_char(create_time,'YYYY-MM-dd HH:MM:ss') as create_time,to_char(update_time,'YYYY-MM-dd HH:MM:ss') as update_time,prod_acct_type,acct_serno FROM fund_prod_acct_info ", SubDatabase.DATABASE_FUND_CENTER,params);
	}

	public M232 get(M232 m232) throws Exception {
		return super.findRow(M232.class, "SELECT * FROM fund_prod_acct_info a WHERE tempid = $S{tempid}",SubDatabase.DATABASE_FUND_CENTER, m232);
	}
}
