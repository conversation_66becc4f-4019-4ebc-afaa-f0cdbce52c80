package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M204;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Repository
public class M204Dao extends ComnDao {

	public SqlResult<M204> findProdLabelRelations(SqlParam<M204> params) throws Exception {
		return super.findRows(
			"SELECT t1.system_no,t1.tano,t1.prod_code,t1.legal_code," +
					"t1.label_code,t1.label_desc," +
					"t2.label_name,t2.label_type,t2.label_index " +
				"FROM prod_label_relation t1 " +
				"LEFT JOIN prod_label t2 " +
				"ON t1.label_code = t2.label_code ",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public UpdateResult addProdLabelRelation(SqlParam<M204> params) throws Exception {
		return super.update("INSERT INTO prod_label_relation(system_no,tano,prod_code,legal_code,label_code,label_desc) VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{labelCode},$S{labelDesc})",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public UpdateResult updateProdLabelRelation(SqlParam<M204> params) throws Exception {
		return super.update("UPDATE prod_label_relation SET label_desc=$S{labelDesc}  WHERE  system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND (legal_code = $S{legalCode} OR $S{legalCode} = $S{superLegalCode}) AND label_code=$S{labelCode} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}
	
	public UpdateResult deleteProdLabelRelation(SqlParam<M204> params) throws Exception {
		return super.update("DELETE FROM prod_label_relation WHERE  system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND (legal_code = $S{legalCode} OR $S{legalCode} = $S{superLegalCode}) AND label_code=$S{labelCode} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public int batchAddProdLabelRelation(List<M204> list) throws Exception {
		AtomicInteger effect = new AtomicInteger();
		doTrans(() -> {
			for (M204 m204 : list) {
				effect.addAndGet(super.update("INSERT INTO prod_label_relation(system_no,tano,prod_code,legal_code,label_code,label_desc) VALUES($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{labelCode},$S{labelDesc})",
						SubDatabase.DATABASE_SYS_CENTER, m204).getEffect());
			}
		});
		return effect.get();
	}
}
