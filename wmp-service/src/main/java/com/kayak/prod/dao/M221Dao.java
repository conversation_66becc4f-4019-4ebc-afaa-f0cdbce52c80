package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.prod.model.M221;
import org.springframework.stereotype.Repository;

@Repository
public class M221Dao extends ComnDao {


	/**
	 * 新增模版公告
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int addM299(SqlParam<M221> params) throws Exception {
		return super.update("INSERT INTO prod_announce_template(tempid,temp_type,title,supply_code,uploadurl,create_date,annfilename) VALUES($AUTOIDS{tempid},$S{tempType},$S{title},$S{supplyCode},$S{uploadurl},$S{createDate},$S{annfilename})",params.getModel()).getEffect();
	}










	/**
	 * 删除模版公告
	 * @param tempid
	 * @return
	 * @throws Exception
	 */
	public int deleteM299(String tempid) throws Exception {
		return super.update("DELETE FROM prod_announce_template WHERE tempid ='"+tempid+"'").getEffect();
	}

	/**
	 * 修改模版信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public int updateM299(SqlParam<M221> params) throws Exception {
		return super.update("UPDATE prod_announce_template SET temp_type=$S{tempType},title=$S{title},uploadurl=$S{uploadurl},edit_date=$S{editDate},annfilename=$S{annfilename} WHERE tempid=$S{tempid}", SubDatabase.DATABASE_SYS_CENTER,params.getModel()).getEffect();
	}

	/**
	 * 查询模版信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M221> find(SqlParam<M221> params) throws Exception {
		return super.findRows("SELECT tempid,temp_type,title,supply_code,uploadurl,create_date,edit_date,annfilename FROM prod_announce_template ", SubDatabase.DATABASE_SYS_CENTER,params);
	}




	public M221 get(String stencilid) throws Exception {
		return super.findRow(M221.class, "SELECT * FROM prod_announce_template a WHERE tempid = $S{tempid}",SubDatabase.DATABASE_SYS_CENTER, stencilid);
	}


}
