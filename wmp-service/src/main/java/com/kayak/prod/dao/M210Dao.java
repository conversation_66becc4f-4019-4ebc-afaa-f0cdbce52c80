package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.prod.model.M210;
import com.kayak.prod.model.M213;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.kayak.prod.utils.UnderlineToCamelUtils.sqlRowToCamel;

/**
 * 产品参数池维护 - 数据层
 */
@Repository
public class M210Dao extends ComnDao {

	@Autowired
	private M210RealtionDao m210RealtionDao;

	/************* M210Service *************/

	/**
	 * 新增参数池信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public String addParam(SqlParam<M210> params) throws Exception {
		Integer orderNo = params.getModel().getOrderNo();
		String sql;
		M210 model = params.getModel();
		sql = "SELECT para_code, para_name FROM prod_para_pool WHERE (para_code = $S{paraCode} OR para_name = $S{paraName})  AND system_no = $S{systemNo} ";
		SqlRow sqlRow = super.findRow(sql, SubDatabase.DATABASE_SYS_CENTER, model);
		if (sqlRow != null ) {
			return RequestSupport.updateReturnJson(false, "已存在同名参数【"+sqlRow.getString("paraCode")+"-"+sqlRow.getString("paraName")+"】", null).toString();
		}
		if ("component".equals(model.getFuncType())) {
			sql = "SELECT 1 " +
					"FROM prod_para_pool " +
					"WHERE " +
					"group_id = $S{groupId} " +
					"AND system_no = $S{systemNo} " +
					"AND func_type = $S{funcType} " +
					"LIMIT 1";
			SqlRow row = super.findRow(sql, params.getModel());
			if (row!=null) {
				return RequestSupport.updateReturnJson(false, "该分组下已经存在自定义组件", null).toString();
			}
		}
		// 如果为空，则查询出最大序号并加1作为新序号
		if (orderNo == null) {
			sql = "SELECT nvl(max(order_no), 0)+1 as order_no FROM prod_para_pool WHERE system_no=$S{systemNo}";
			SqlRow row = super.findRow(sql, params.getModel());
			orderNo = row.getInteger("order_no");
		}
		// 定义数组来暂存变量，因为函数式中不支持直接传入变量
		Integer[] orderNoArray = {orderNo};
		doTrans(() -> {
			String update;
			if (params.getModel().getOrderNo() != null) {
				// 序号后移
				update = "UPDATE prod_para_pool " +
						"SET order_no=(order_no+1) " +
						"WHERE order_no >= " + orderNoArray[0] + " AND system_no= $S{systemNo}";
				super.update(update, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
			}
			// TODO 因为底层框架传入Integer为null报错
			model.setFieldLength(StringUtils.isBlank(model.getFieldLength()) ? "0" : model.getFieldLength());
			model.setFieldPrecision(StringUtils.isBlank(model.getFieldPrecision()) ? "0" : model.getFieldPrecision());

			update = "INSERT INTO " +
					" prod_para_pool " +
					" (system_no,para_code, para_name, field_length," +
					" field_precision, dict, data_way, group_id, func_type, order_no, show_flag) " +
					" VALUES($S{systemNo},$S{paraCode}, $S{paraName}, $I{fieldLength}," +
					" $I{fieldPrecision}, $S{dict}, $S{dataWay}, $S{groupId}, $S{funcType}, " + orderNoArray[0] + ", $S{showFlag})";
			super.update(update, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
		});
		return RequestSupport.updateReturnJson(true, "操作成功", null).toString();
	}

	/**
	 * 更新参数池信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public UpdateResult updateParam(SqlParam<M210> params) throws Exception {
		// 新序号
		Integer orderNo = params.getModel().getOrderNo();
		// 原序号
		Integer oriOrderNo = params.getModel().getOriOrderNo();
		String existSql = "SELECT para_code as paraCode, para_name as paraName FROM prod_para_pool WHERE  para_name = $S{paraName} AND para_code != $S{paraCode}  AND system_no = $S{systemNo}";
		SqlRow sqlRow = super.findRow(existSql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
		if (sqlRow != null && sqlRow.size() > 0) {
			throw new PromptException("已存在同名参数【"+sqlRow.getString("paraCode")+"-"+sqlRow.getString("paraName")+"】");
		}
		if (orderNo == null) {
			String sql = "SELECT nvl(max(order_no), 1) as order_no FROM prod_para_pool WHERE system_no=$S{systemNo}";
			SqlRow row = super.findRow(sql, params.getModel());
			orderNo = row.getInteger("order_no");
		}
		Integer[] orders = {orderNo, oriOrderNo};

		doTrans(() -> {
			String sql = "";
			// 新序号小于原序号，则新序号后面，原序号前面的序号都要+1
			if (orders[0] < orders[1]) {
				sql = "UPDATE prod_para_pool " +
						" SET order_no=(order_no+1) " +
						" WHERE order_no >= " + orders[0] +
						" AND order_no < $I{oriOrderNo} AND system_no=$S{systemNo}";
				super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
			} else if (orders[0] > orders[1]) {
				// 新序号大于原序号，则原序号后面，新序号前面的序号都要-1
				sql = "UPDATE prod_para_pool " +
						" SET order_no=(order_no-1) " +
						" WHERE order_no > $I{oriOrderNo} AND order_no <= " + orders[0] +
						" AND system_no=$S{systemNo}";
				super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
			}
			sql = "UPDATE prod_para_pool " +
					" SET system_no = $S{systemNo} ," +
					" para_name = $S{paraName} ," +
					" field_length = $I{fieldLength} ," +
					" field_precision = $I{fieldPrecision} ," +
					" group_id = $S{groupId} ," +
					" dict = $S{dict}," +
					" data_way = $S{dataWay}, " +
					" func_type = $S{funcType}," +
					" order_no=" + orders[0] + "," +
					" show_flag=$S{showFlag}" +
					" WHERE para_code = $S{paraCode} AND system_no=$S{systemNo} ";
			super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
		});
		return new UpdateResult();
	}

	/**
	 * 删除参数池信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public UpdateResult deleteParam(SqlParam<M210> params) throws Exception {
		String selSql = "SELECT t1.comp_id as comp_id, t1.comp_name as comp_name  FROM prod_comp_info t1 " +
				" LEFT JOIN prod_comp_para t2 ON t1.comp_id = t2.comp_id " +
				" WHERE t2.para_code = $S{paraCode} AND t1.system_no=$S{systemNo} ";
		List<SqlRow> sqlRows = findRows(selSql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
		if (sqlRows != null && sqlRows.size() > 0){
			SqlRow sqlRow = sqlRows.get(0);
			throw new PromptException("该参数被组件【"+sqlRow.getString("comp_id")+"-"+sqlRow.getString("comp_name")+"】添加，无法删除");
		}

		doTrans(() -> {
			// 序号前移
			String sql = "UPDATE prod_para_pool " +
					"SET order_no=(order_no-1) " +
					"WHERE order_no > $I{orderNo} and system_no=$S{systemNo}";
			super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
			sql = "DELETE FROM prod_para_pool WHERE para_code = $S{paraCode} AND system_no=$S{systemNo}";
			super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel());
		});
		return new UpdateResult();
	}

	/**
	 * M210Service
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M210> findAllParams(SqlParam<M210> param) throws Exception {
		// oracle下如果加了as，会报 ORA-00933 错误。
		String sql = "SELECT ppp.system_no, ppp.para_code, ppp.para_name, ppp.field_length, ppp.field_precision, ppp.dict, ppp.data_way," +
				" ppp.group_id, ppp.func_type, ppp.order_no, ppp.show_flag, ppgi.group_name" +
				" FROM prod_para_pool ppp" +
				" LEFT JOIN prod_para_group_info ppgi" +
				" ON ppp.group_id = ppgi.group_id" +
				" ORDER BY ppp.system_no,ppp.order_no";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, param);
	}

	/************* Not Used *************/
	public SqlResult<M210> findGroupInfo(SqlParam<M210> param) throws Exception {
		return super.findRows("SELECT group_id, group_name" +
				" FROM prod_para_group_info", SubDatabase.DATABASE_SYS_CENTER, param);
	}


	public SqlResult<M210> findParamsByGroupId(SqlParam<M210> param) throws Exception {
		return super.findRows("SELECT para_code, para_name, field_length, field_precision, dict, data_way, func_type, group_id" +
				" FROM prod_para_pool ppp" +
				" WHERE group_id = $S{groupId}", SubDatabase.DATABASE_SYS_CENTER, param);
	}



	public SqlResult<M210> findParaInfos(SqlParam<M210> params) throws Exception {
//		return super.findRows("SELECT " +
//						" t1.para_code,t1.para_name,t1.field_type,t1.field_length,t1.field_precision,t1.dict, " +
//						" t1.system_no,t1.group_id,t2.group_name " +
//						" FROM prod_parameter_pool t1 " +
//						" LEFT JOIN prod_para_group_info t2 ON t1.group_id = t2.group_id",
		return super.findRows("SELECT t.* FROM (SELECT " +
						" t1.para_code,t1.para_name,t1.field_length,t1.field_precision,t1.dict, " +
						" t1.group_id,t2.group_name ,t1.group_id as upper_code" +
						" FROM prod_para_pool t1 " +
						" LEFT JOIN prod_para_group_info t2 ON t1.group_id = t2.group_id " +
						" UNION " +
						" SELECT t3.group_id as para_code,t3.group_name as para_name," +
						" '-','-'," +
						" '-',t3.group_id,t3.group_name,'-' " +
						" FROM prod_para_group_info t3) t",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}


	public List<SqlRow> findM210(String modelId) throws Exception {

		String sql = "SELECT pmp.model_id as model_id," +
				" pmp.para_code as para_code," +
				" pmp.para_name as para_name," +
				" pmp.para_desc as para_desc," +
				" pmp.group_id as group_id," +
				" pmp.field_length as field_length," +
				" pmp.field_precision as field_precision," +
				" pmp.pk_flag as pk_flag," +
				" pmp.blank_flag as blank_flag," +
				" pmp.order_no as order_no," +
				" pmp.max_value as max_value," +
				" pmp.min_value as min_value," +
				" ppl.dict as dict," +
				" pmp.edit_flag as edit_flag," +
				" ppl.func_type as func_type," +
				" ppl.data_way as data_way" +
				" FROM prod_model_para pmp " +
				" LEFT JOIN prod_para_group_info ppg on pmp.group_id=ppg.group_id  " +
				" LEFT JOIN prod_para_pool ppl on pmp.para_code=ppl.para_code and ppl.system_no=(" +
				" SELECT system_no from prod_model_info WHERE model_id='" + modelId + "') " +
				" WHERE pmp.model_id='" + modelId + "' and pmp.show_flag='1' " +
				" ORDER BY pmp.group_id,pmp.order_no";
		List<SqlRow> sqlRows = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, modelId);
		List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
		return sqlRowList;
	}

	public List<SqlRow> findRelations(String modelId) throws Exception {
		String sql = "select para_code," +
				" tabs_id," +
				" para_val," +
				" link_para_code," +
				" show_flag," +
				" blank_flag," +
				" dict," +
				" default_val," +
				" edit_flag" +
				" from prod_model_para_relation" +
				" where model_id='" + modelId + "'";
		List<SqlRow> sqlRows = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER);
		List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
		return sqlRowList;
	}

	public UpdateResult updateM210(M213 m213) throws Exception {
		return super.update("UPDATE prod_model_para " +
						" SET pk_flag=$S{pkFlag} ," +
						" blank_flag=$S{blankFlag} ," +
						" order_no=$I{orderNo} ," +
						" max_value=$S{maxValue} ," +
						" min_value=$S{minValue} ," +
						" blank_rely=$S{blankRely} ," +
						" dict=$S{dict}" +
						" WHERE para_code=$S{paraCode} and model_id = $S{modelId} ",
				SubDatabase.DATABASE_SYS_CENTER, m213);
	}

	/**
	 * 查询分组信息
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M210> selectGroup(SqlParam<M210> params) throws Exception {
		String sql = "SELECT ppg.group_id, ppg.group_name" +
				" FROM prod_para_group_info ppg  " +
			    " INNER JOIN prod_model_para pmp ON pmp.group_id=ppg.group_id " +
			    " WHERE model_id = $S{modelId} " +
			    " GROUP BY ppg.group_id,ppg.group_name " +
			    " ORDER BY ppg.order_no ASC";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
	}

	/**
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M210> selectGroup2(SqlParam<M210> params) throws Exception {
		String sql = "SELECT ppg.group_id,ppg.group_name FROM prod_para_group_info ppg " +
				     " GROUP BY ppg.group_id,ppg.group_name " +
				     " ORDER BY ppg.order_no ASC";
		return super.findRows(sql,
				SubDatabase.DATABASE_SYS_CENTER, params);
	}


	/**
	 * @return
	 * @throws Exception
	 */
	public List<SqlRow> selectGroup3() throws Exception {
		String sql = "select group_name as \"desc\", group_id as id, CONCAT('item-', group_id) as icon_class," +
				" CONCAT('selected-', group_id) as active_class, order_no as order_no" +
				" from prod_para_group_info" +
				" order by order_no asc;";
		List<SqlRow> sqlRows = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER);
		List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
		return sqlRowList;
	}

	/**
	 * @return
	 * @throws Exception
	 */
	public List<SqlRow> selectGroup4(String modelId) throws Exception {
		//查询模型中已经存在分组
		String sql = " SELECT t2.group_name as \"desc\", t1.group_id as id, '' AS mouse_over, cast('FALSE' as char) AS \"alive\", cast('TRUE' as char) AS \"validate\", CONCAT( 'item-', t1.group_id ) AS icon_class, CONCAT( 'selected-', t1.group_id ) AS active_class,t1.order_no " +
				" FROM prod_model_group t1 " +
				" INNER JOIN prod_para_group_info t2 ON t2.group_id =t1.group_id"  +
				" WHERE t1.model_id = '"+modelId+"'";
		sql += " UNION ALL ";
		//查询除模型中存在的分组，其他分组
		sql += " SELECT t1.group_name as \"desc\", t1.group_id as \"id\", '' AS \"mouse_over\", cast('FALSE' as char) AS \"alive\",   cast('TRUE' as char) AS \"validate\", CONCAT( 'item-', t1.group_id ) AS \"icon_class\", CONCAT( 'selected-', t1.group_id ) AS \"active_class\",t1.order_no " +
				" FROM prod_para_group_info t1 " +
				" WHERE NOT EXISTS (SELECT 1 FROM prod_model_group t2 WHERE t2.group_id = t1.group_id AND t2.model_id = '"+modelId+"') " ;


		List<SqlRow> sqlRows = super.findRows("SELECT tt.* FROM ( " + sql + " ) tt",
				SubDatabase.DATABASE_SYS_CENTER, modelId);
		List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
		return sqlRowList;
	}

	/**
	 * 通过模型id查询分组
	 * @param modelId
	 * @return
	 * @throws Exception
	 */
	public List<SqlRow> findGroupsByModelId(String modelId) throws Exception {
		String sql = "select t1.group_name as group_name, t1.group_id as group_id, CONCAT('item-', t1.group_id) as icon_class," +
				" CONCAT('selected-', t1.group_id) as active_class, t1.component as component, t2.order_no as order_no" +
				" FROM prod_para_group_info t1" +
				" LEFT JOIN prod_model_group t2 ON t1.group_id=t2.group_id" +
				" WHERE t2.model_id = '" + modelId + "'"+
				" ORDER BY t2.order_no";
		List<SqlRow> sqlRows = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER,modelId);
		List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
		return sqlRowList;
	}

	/**
	 * 查询模型参数存在哪些模型中
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M210> findParaExistModel(SqlParam<M210> params) throws Exception {
		String sql = "select model_id from prod_model_para group by model_id";
		return super.findRows(sql,
				SubDatabase.DATABASE_SYS_CENTER, params);
	}


	public void batchUpdateModelPara(List<M210> params, String modelId) throws Exception {
		//先删除模型关联的所有参数
		super.update("DELETE FROM prod_model_para WHERE model_id = '" + modelId + "'", SubDatabase.DATABASE_SYS_CENTER, modelId);

		for (int i=0;i<params.size();i++){
			M210 m210 = params.get(i);
			m210.setOrderNo(i+1);
			//插入产品模型参数
			super.update("INSERT INTO prod_model_para" +
					"(model_id, para_code, para_name, para_desc, group_id, field_length, " +
					"field_precision, pk_flag, blank_flag, order_no, max_value, min_value, dict, " +
					" func_type, edit_flag ) " +
					"VALUES " +
					"($S{modelId}, $S{paraCode}, $S{paraName}, $S{paraDesc}, $S{groupId}, " +
					"$S{fieldLength}, $S{fieldPrecision}, $S{pkFlag}, $S{blankFlag}, $I{orderNo}, $S{maxValue}, " +
					"$S{minValue}, $S{dict}, $S{funcType}, $S{editFlag})", SubDatabase.DATABASE_SYS_CENTER, m210);

			//删除产品模型参数的所有关系
			super.update("DELETE FROM prod_model_para_relation WHERE model_id=$S{modelId} AND para_code=$S{paraCode} ", SubDatabase.DATABASE_SYS_CENTER, m210);
			//更新产品模型参数的关系
			m210RealtionDao.batchUpdateParaReal(m210.getRealtionList());
		}
	}

	public List<SqlRow> findParams(SqlParam<M210> param) throws Exception {
		StringBuilder sb = new StringBuilder("SELECT para_code, para_name, order_no");
		sb.append(" FROM prod_para_pool");
		sb.append(" WHERE system_no=$S{systemNo}");
		if (param.getModel().getOrderNo() != null) {
			sb.append(" AND order_no != $I{orderNo}");
		}
		sb.append(" ORDER BY order_no");
		List<SqlRow> sqlRows = super.findRows(sb.toString(), SubDatabase.DATABASE_SYS_CENTER, param.getModel());
		List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
		return sqlRowList;
	}


}
