package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M234;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M234Dao extends ComnDao {

    public SqlResult<M234> findM234s(SqlParam<M234> params) throws Exception {
        return super.findRows("SELECT * FROM PROD_SALE_NUMBER_CONTROL ",
                SubDatabase.DATABASE_SYS_CENTER,params);
    }

    //添加
    public UpdateResult addM234(SqlParam<M234> params) throws Exception {
        String sql = "INSERT INTO PROD_SALE_NUMBER_CONTROL\n" +
                "(system_no,tano,prod_code,legal_code,control_amt_flag,\n" +
                "begin_amt,end_amt,control_number,sale_number)\n" +
                "VALUES ($S{systemNo},$S{tano},$S{prodCode},$S{legalCode},$S{controlAmtFlag},\n" +
                "$S{beginAmt},$S{endAmt},$S{controlNumber},$S{saleNumber})";
        return super.update(sql,SubDatabase.DATABASE_SYS_CENTER,params.getModel());
    }
    //修改
    public UpdateResult updateM234(SqlParam<M234> params) throws Exception {
        M234 m234 = params.getModel();
        String sql = "UPDATE PROD_SALE_NUMBER_CONTROL SET control_number=$S{controlNumber}\n" +
                "WHERE system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and legal_code=$S{legalCode} and begin_amt=$S{beginAmt} and end_amt=$S{endAmt}";
        UpdateResult updateResult = super.update(sql,SubDatabase.DATABASE_SYS_CENTER,m234);
        return updateResult;
    }

    //删除
    public UpdateResult deleteM234(SqlParam<M234> params) throws Exception {
        String sql = "DELETE FROM PROD_SALE_NUMBER_CONTROL \n" +
                "WHERE system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and legal_code=$S{legalCode} and begin_amt=$S{beginAmt} and end_amt=$S{endAmt}";
        return super.update(sql,SubDatabase.DATABASE_SYS_CENTER,params.getModel());
    }

    /**
     * 查询某个系统参数
     *
     * @param params
     * @return
     * @throws Exception
     */
    public M234 findOne(SqlParam<M234> params) throws Exception {
        // oracle 不支持limit 1
        String sql = "SELECT * FROM PROD_SALE_NUMBER_CONTROL ";
        SqlResult<M234> row = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            return row.getRows().get(0);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Description 按交易金额控制，获取记录数
     * @Date 2022/3/29
     * @Param [m234]
     * @return java.util.List<com.kayak.prod.model.M234>
     **/
    public List<M234> getListByAmtFlag(M234 m234) throws Exception {

        StringBuffer sb=new StringBuffer(" SELECT * FROM PROD_SALE_NUMBER_CONTROL where 1=1 ");
        if(StringUtils.isNotBlank(m234.getSystemNo())){
            sb.append(" and SYSTEM_NO = '").append(m234.getSystemNo()+"' ");
        }
        if(StringUtils.isNotBlank(m234.getLegalCode())){
            sb.append(" and LEGAL_CODE = '").append(m234.getLegalCode()+"' ");
        }
        if(StringUtils.isNotBlank(m234.getTano())){
            sb.append(" and TANO = '").append(m234.getTano()+"' ");
        }
        if(StringUtils.isNotBlank(m234.getProdCode())){
            sb.append(" and PROD_CODE = '").append(m234.getProdCode()+"' ");
        }
        //产品销售人数控制 只能有一种控制方法(是否按交易金额控制，是或否只能存在一种)
        if(StringUtils.isNotBlank(m234.getControlAmtFlag())){
            if("1".equals(m234.getControlAmtFlag())){
                sb.append(" and CONTROL_AMT_FLAG = '0'");
            }else{
                sb.append(" and CONTROL_AMT_FLAG = '1'");
            }
        }

        return super.findRows(M234.class,sb.toString(), SubDatabase.DATABASE_SYS_CENTER,m234);
    }

    /**
     * <AUTHOR>
     * @Description 当是否按交易金额控制为否时，不能重复添加
     * @Date 2022/3/29
     * @Param [m234]
     * @return java.util.List<com.kayak.prod.model.M234>
     **/
    public List<M234> getList(M234 m234) throws Exception {

        StringBuffer sb=new StringBuffer(" SELECT * FROM PROD_SALE_NUMBER_CONTROL where 1=1 ");
        if(StringUtils.isNotBlank(m234.getSystemNo())){
            sb.append(" and SYSTEM_NO = '").append(m234.getSystemNo()+"' ");
        }
        if(StringUtils.isNotBlank(m234.getLegalCode())){
            sb.append(" and LEGAL_CODE = '").append(m234.getLegalCode()+"' ");
        }
        if(StringUtils.isNotBlank(m234.getTano())){
            sb.append(" and TANO = '").append(m234.getTano()+"' ");
        }
        if(StringUtils.isNotBlank(m234.getProdCode())){
            sb.append(" and PROD_CODE = '").append(m234.getProdCode()+"' ");
        }
        if(StringUtils.isNotBlank(m234.getControlAmtFlag())){
            sb.append(" and CONTROL_AMT_FLAG = '").append(m234.getControlAmtFlag()+"' ");
        }
        return super.findRows(M234.class,sb.toString(), SubDatabase.DATABASE_SYS_CENTER,m234);
    }

}


