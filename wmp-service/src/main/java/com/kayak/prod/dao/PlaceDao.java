package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlRow;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-20 20:06
 */
@Repository
public class PlaceDao extends ComnDao {

    public List<SqlRow> getCitiesByProvince(String province) throws Exception {
        if (province == null) {
            return new ArrayList<>();
        }
        return super.findRows("SELECT itemkey, itemval " +
                "FROM sys_dict_item " +
                "WHERE dict = 'city' AND itemkey LIKE '" + province + "-%'");
    }

    public List<SqlRow> selectProvinces() throws Exception {
        return super.findRows("SELECT itemkey, itemval " +
                "FROM sys_dict_item " +
                "WHERE dict = 'city' AND itemkey LIKE '1-%'");
    }
}
