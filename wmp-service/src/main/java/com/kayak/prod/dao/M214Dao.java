package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M214;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
public class M214Dao extends ComnDao {

    /**
     * 查询组件关系
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M214> findCompRelation(SqlParam<M214> params) throws Exception {
        return super.findRows("SELECT pcr.system_no, pcr.comp_id, pci.comp_name, pci.comp_type, pcr.relation_type, pcr.match_comp_id, pci2.comp_name as match_comp_name, pci2.comp_type as match_comp_type" +
                        " FROM prod_comp_relation pcr" +
                        " LEFT JOIN prod_comp_info pci" +
                        " ON pcr.comp_id = pci.comp_id" +
                        " LEFT JOIN prod_comp_info pci2" +
                        " ON pcr.match_comp_id = pci2.comp_id",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    /**
     * 查询组件是否已存在
     * @param compId
     * @return
     * @throws Exception
     */
    public Integer existCompId(String compId, String matchCompId) throws Exception {
        return super.findRow(Integer.class, "SELECT COUNT(*) FROM prod_comp_relation WHERE (comp_id = '" + compId + "' AND match_comp_id='" + matchCompId + "')" +
                " OR (comp_id = '" + matchCompId + "' AND match_comp_id='" + compId + "')", SubDatabase.DATABASE_SYS_CENTER, compId);
    }

    /**
     * 查询关联组件ID 通过 组件ID
     * @param compId
     * @return
     * @throws Exception
     */
    public List<M214> findMatchCompId(String compId) throws Exception {
        return super.findRows(M214.class,"SELECT pci.comp_name, pcr.relation_type, pci.comp_id" +
                " FROM prod_comp_relation pcr" +
                " LEFT JOIN prod_comp_info pci" +
                " ON pcr.match_comp_id = pci.comp_id" +
                " WHERE pcr.comp_id =  '" + compId + "'", SubDatabase.DATABASE_SYS_CENTER, compId);
    }

    /**
     * 查询组件类型
     * @return
     * @throws Exception
     */
    public List<SqlRow> findCompType(SqlParam<M214> params) throws Exception {
        String sql = "SELECT comp_type" +
                " FROM prod_comp_info where system_no = '"+params.getModel().getSystemNo()+"'" +
                " GROUP BY comp_type ";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER);
    }

    /**
     * 通过类型查询组件
     * @param compType
     * @return
     * @throws Exception
     */
    public List<SqlRow> findCompsByType(String compType) throws Exception {
        String sql = "SELECT pci.comp_id, pci.comp_name, pci.comp_type" +
                " FROM prod_comp_info pci" +
                " WHERE pci.comp_type = '" + compType + "'";
        return super.findRows(sql, compType);
    }

    /**
     * 添加组件关系
     * @param params
     * @return
     * @throws Exception
     */
    public UpdateResult addCompRelation(SqlParam<M214> params) throws Exception {
        return super.update("INSERT INTO prod_comp_relation (system_no,comp_id, relation_type, match_comp_id)" +
                        " VALUES ($S{systemNo},$S{compId}, $S{relationType}, $S{matchCompId})",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult updateCompRelation(SqlParam<M214> params) throws Exception {
        return super.update("UPDATE prod_comp_relation SET relation_type=$S{relationType} ,match_comp_id=$S{matchCompId}  WHERE  comp_id=$S{compId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult deleteCompRelation(SqlParam<M214> params) throws Exception {
        return super.update("DELETE FROM prod_comp_relation WHERE  comp_id=$S{compId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public Map<String, Map<String,List<String>>> getCompRealtion(String systemNo) throws Exception {
        String sql = "SELECT system_no, comp_id compId, relation_type as relationType, match_comp_id as matchCompId FROM prod_comp_relation WHERE system_no = '"+ systemNo +"' ";
        List<SqlRow> sqlRowList = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, systemNo);
        //获取依赖关系数据
        List<SqlRow> relyList = sqlRowList.stream().filter(sqlRow -> "2".equals(sqlRow.getString("relationType"))).collect(Collectors.toList());
        //获取互斥关系数据
        List<SqlRow> mutexList = sqlRowList.stream().filter(sqlRow -> "1".equals(sqlRow.getString("relationType"))).collect(Collectors.toList());

        //获取存在关系的字段
        List<String> compList = new ArrayList<>();
        sqlRowList.stream().forEach(sqlRow -> {
            String compId = sqlRow.getString("compId");
            String matchCompId =sqlRow.getString("matchCompId");
            if (!compList.contains(compId)){
                compList.add(compId);
            }
            if (!compList.contains(matchCompId)){
                compList.add(matchCompId);
            }
        });

        Map<String, Map<String,List<String>>> returnMap = new HashMap<>();
        compList.stream().forEach(s -> {
            Map<String, List<String>> map = new HashMap<>();
            List<String> relyCompIds = new ArrayList<>();
            this.getRelyList(relyList,relyCompIds, s);
            relyCompIds.remove(s);

            List<String> mutexCompIds = new ArrayList<>();
            this.getMutexList(mutexList, mutexCompIds, s);

            map.put("rely", relyCompIds);
            map.put("mutex", mutexCompIds);
            returnMap.put(s , map);
        });

        return returnMap;
    }

    /**
     * 通过组件id,获取该组件存在互斥关系的组件数据
     * 不存在互斥传递
     */
    private void getMutexList(List<SqlRow> mutexList, List<String> mutexCompIds, String compId){
        mutexList.stream().forEach(sqlRow -> {
            String matchCompId = "";
            if (compId.equals(sqlRow.getString("compId"))){
                matchCompId = sqlRow.getString("matchCompId");
            }else if (compId.equals(sqlRow.getString("matchCompId"))){
                matchCompId = sqlRow.getString("compId");
            }
            if (StringUtils.isNotBlank(matchCompId) && (!mutexCompIds.contains(matchCompId)) && (!matchCompId.equals(compId))){
                mutexCompIds.add(matchCompId);
            }
        });
    }

    /**
     * 通过组件id，获取该组件存在的依赖关系的组件数据
     * 存在依赖传递
     */
    private void getRelyList(List<SqlRow> relyList, List<String> relyCompIds, String compId){
        relyList.stream().forEach(sqlRow -> {
            String matchCompId = "";
            if (compId.equals(sqlRow.getString("compId"))){
                matchCompId = sqlRow.getString("matchCompId");
            }
//            else if (compId.equals(sqlRow.getString("matchCompId"))){
//                matchCompId = sqlRow.getString("compId");
//            }
            if (StringUtils.isNotBlank(matchCompId) && (!relyCompIds.contains(matchCompId)) && (!matchCompId.equals(compId))){
                relyCompIds.add(matchCompId);
               this.getRelyList(relyList, relyCompIds, matchCompId);
            }
        });
    }

    public SqlRow inOneModel(SqlParam<M214> params) throws Exception {
        String sql = "SELECT model_id " +
                "FROM prod_model_comp " +
                "WHERE" +
                " model_id IN ( SELECT model_id FROM prod_model_comp WHERE comp_id = $S{compId} ) " +
                " AND comp_id = $S{matchCompId} " +
                " LIMIT 1";
        SqlRow row = super.findRow(sql, params.getModel());
        return row;
    }

    public Set<String> existSameCompRelation(SqlParam<M214> params) throws Exception {
        String sql = "SELECT comp_id id " +
                "FROM prod_comp_relation " +
                "WHERE match_comp_id = $S{compId} " +
                "UNION " +
                "SELECT match_comp_id id " +
                "FROM prod_comp_relation " +
                "WHERE comp_id = $S{compId}";
        List<SqlRow> rows = super.findRows(sql, params.getModel());
        Set<String> set1 = new HashSet<>();
        rows.forEach(row -> set1.add(row.getString("id")));

        sql = "SELECT comp_id id " +
                "FROM prod_comp_relation " +
                "WHERE match_comp_id = $S{matchCompId} " +
                "UNION " +
                "SELECT match_comp_id id " +
                "FROM prod_comp_relation " +
                "WHERE comp_id = $S{matchCompId}";
        rows = super.findRows(sql, params.getModel());
        Set<String> set2 = new HashSet<>();
        rows.forEach(row -> set2.add(row.getString("id")));

        // 求交集，交集结果就是 set1
        set1.retainAll(set2);
        return set1;
    }

    public Set<String> notInOneModel(SqlParam<M214> params) throws Exception {
        String sql = "SELECT model_id " +
                "FROM prod_model_comp " +
                "WHERE comp_id = $S{compId}";
        List<SqlRow> rows = super.findRows(sql, params.getModel());
        // 存储依赖组件所在的模型
        Set<String> set1 = new HashSet<>();
        rows.forEach(row -> set1.add(row.getString("model_id")));

        sql = "SELECT model_id " +
                "FROM prod_model_comp " +
                "WHERE comp_id = $S{matchCompId} ";
        rows = super.findRows(sql, params.getModel());
        // 存储被依赖组件所在的模型
        Set<String> set2 = new HashSet<>();
        rows.forEach(row -> set2.add(row.getString("model_id")));
        // 取差集，剩下来的就是差集，也就是存在产品模型没有同时包含这两个组件
        set1.removeAll(set2);
        return set1;
    }

    /**
     * 判断两个组件是否互斥
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M214> selectCompExclusive(SqlParam<M214> params) throws Exception{
        return super.findRows("select  relation_type from prod_comp_relation where match_comp_id = '"+params.getModel().getCompId()+"' and comp_id = '"+params.getModel().getMatchCompId()+"'",
                SubDatabase.DATABASE_SYS_CENTER, params);

    }
}
