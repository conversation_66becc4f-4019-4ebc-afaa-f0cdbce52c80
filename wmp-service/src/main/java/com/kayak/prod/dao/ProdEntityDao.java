package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.ProdEntityAttr;
import com.kayak.prod.model.ProdEntityDetail;
import com.kayak.prod.model.ProdEntityInfo;

import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public class ProdEntityDao extends ComnDao {

    public SqlResult<ProdEntityInfo> findProdEntityInfos(SqlParam<ProdEntityInfo> params) throws Exception {
        return super.findRows("SELECT  entity_id,system_no,entity_name  FROM  prod_entity_info  ",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }


    public UpdateResult addProdEntityInfo(SqlParam<ProdEntityInfo> params) throws Exception {
        params.getModel().setEntityId(UUID.randomUUID().toString().substring(10));
        return super.update("insert into prod_entity_info(entity_id,system_no,entity_name) values ($S{entityId},$S{systemNo},$S{entityName})",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult updateProdEntityInfo(SqlParam<ProdEntityInfo> params) throws Exception {
        return super.update("update prod_entity_info set entity_name=$S{entityName} where entity_id=$S{entityId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public UpdateResult deleteProdEntityInfo(SqlParam<ProdEntityInfo> params) throws Exception {
        return super.update("delete from  prod_entity_info where entity_id=$S{entityId} ",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }

    public SqlResult<ProdEntityAttr> findProdEntityAttrs(SqlParam<ProdEntityAttr> params) throws Exception {
        return super.findRows("SELECT  *  FROM  prod_entity_attr where entity_id=$S{entityId} order by order_no ",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }


    public SqlResult<ProdEntityDetail> findProdEntityDetails(SqlParam<ProdEntityDetail> params) throws Exception {
        return super.findRows("SELECT t1.* FROM prod_entity_detail  t1 LEFT JOIN  prod_entity_info t2 ON t1.entity_id=t2.entity_id WHERE 1=1 ",
                SubDatabase.DATABASE_SYS_CENTER, params);




    }
}
