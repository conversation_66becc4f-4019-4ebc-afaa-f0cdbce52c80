package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M208Detail;
import org.springframework.stereotype.Repository;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2021-04-15 11:15
 */
@Repository
public class M208DetailDao extends ComnDao {

    public UpdateResult insertProdActiveDetail(SqlParam<M208Detail> params) throws Exception {
        // 明细编号自增
        return super.update("INSERT INTO prod_active_detail(plan_no,addition_type,float_value,dimension_min,dimension_max,discount_value)" +
                        " VALUES($S{planNo},$S{additionType},$D{dimensionMin},$D{dimensionMax},$D{discountValue})",
                SubDatabase.DATABASE_SYS_CENTER, params.getModel());
    }



    public SqlResult<M208Detail> selectProdActiveDetail(SqlParam<M208Detail> params) throws Exception {
        return super.findRows("SELECT plan_detail_no,plan_no,addition_type,float_value,dimension_min,dimension_max,discount_value FROM prod_active_detail",
                SubDatabase.DATABASE_SYS_CENTER, params);
    }
}
