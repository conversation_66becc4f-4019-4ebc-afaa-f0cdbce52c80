package com.kayak.prod.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M206Info;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;
import java.util.Map;

/**
 * 产品额度服务类，原名ProdQuotaInfoDao
 */
@Repository
public class M206InfoDao extends ComnDao {

	public SqlResult<M206Info> findProdQuotaInfos(SqlParam<M206Info> params) throws Exception {
		return super.findRows("SELECT system_no,tano,prod_code,quota_busi_type,diff_cust_type,dim_one_type,dim_two_type,total_quota/10000 as total_quota,expire_date FROM prod_quota_info",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public SqlResult<M206Info> findProdQuotaInfoBykey(SqlParam<M206Info> params) throws Exception {
		String sql="select quota_id,system_no,tano,prod_code,quota_busi_type,diff_cust_type,dim_one_type,dim_two_type,total_quota/10000 as total_quota,expire_date,legal_code FROM prod_quota_info " +
				" where system_no=$S{systemNo} and tano=$S{tano} and prod_code=$S{prodCode} and quota_busi_type=$S{quotaBusiType} and (legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode}) ";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);	}

	public List<SqlRow> findProdQuotaInfos(Map<String,Object> params) throws Exception {
		return super.findRows("SELECT count(1) as num FROM prod_quota_info where prod_code=$S{prodCode} and quota_busi_type=$S{quotaBusiType}",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public UpdateResult addProdQuotaInfo(SqlParam<M206Info> params) throws Exception {
		return super.update("INSERT INTO prod_quota_info(quota_id,system_no,tano,prod_code,quota_busi_type,legal_code,diff_cust_type,dim_one_type,dim_two_type,total_quota,expire_date) " +
						"VALUES($S{quotaId},$S{systemNo},$S{tano},$S{prodCode},$S{quotaBusiType},$S{legalCode},$S{diffCustType},$S{dimOneType},$S{dimTwoType},$S{totalQuota}*10000,$S{expireDate})",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public UpdateResult addProdQuotaInfoDetail(Map<String,Object> params) throws Exception {
		return super.update("INSERT INTO prod_quota_info(system_no,tano,prod_code,quota_busi_type,diff_cust_type,dim_one_type,dim_two_type,total_quota) " +
						"VALUES( $S{systemNo},$S{tano},$S{prodCode},$S{quotaBusiType},$S{diffCustType},$S{dimOneType},$S{dimTwoType},$S{totalQuota}*10000)",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}
	
	public UpdateResult updateProdQuotaInfo(SqlParam<M206Info> params) throws Exception {
		return super.update("UPDATE prod_quota_info SET diff_cust_type=$S{diffCustType} ,dim_one_type=$S{dimOneType} ,dim_two_type=$S{dimTwoType} ,total_quota=$null{totalQuota}*10000 ,expire_date=$S{expireDate}  WHERE  system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND quota_busi_type=$S{quotaBusiType} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public UpdateResult updateProdQuotaDetailExpireDate(Map<String,Object> params) throws Exception {
		return super.update("UPDATE prod_quota_info SET expire_date=$S{expireDate} where quota_id=$S{quotaId}",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}
	
	public UpdateResult deleteProdQuotaInfo(SqlParam<M206Info> params) throws Exception {
		return super.update("DELETE FROM prod_quota_info WHERE  system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND quota_busi_type=$S{quotaBusiType} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public UpdateResult deleteProdQuotaInfoByQuotaId(SqlParam<M206Info> params) throws Exception {
		params.setMakeSql(false);
		return super.update("DELETE FROM prod_quota_info WHERE quota_id=$S{quotaId} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

}
