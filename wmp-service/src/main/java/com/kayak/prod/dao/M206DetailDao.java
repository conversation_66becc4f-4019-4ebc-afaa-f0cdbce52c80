package com.kayak.prod.dao;

import com.alibaba.fastjson.JSONObject;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.prod.model.M206Detail;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;
import java.util.Map;
/**
 * 额度明细服务，原名ProdQuotaDetailDao
 */
@Repository
public class M206DetailDao extends ComnDao {

	public SqlResult<M206Detail> findProdQuotaDetails(SqlParam<M206Detail> params) throws Exception {
		return super.findRows("SELECT pd.system_no,\n" +
						"       pd.tano,\n" +
						"       pd.prod_code,\n" +
						"       pd.legal_code,\n" +
						"       pd.quota_busi_type,\n" +
						"       pd.diff_cust_type,\n" +
						"       pd.total_quota / 10000 AS total_quota,\n" +
						"       pd2.expire_date,\n" +
						"       pd2.dim_one_type,\n" +
						"       pd2.dim_two_type\n" +
						"  FROM prod_quota_detail pd\n" +
						"  LEFT JOIN prod_quota_info pd2 on pd.quota_id = pd2.quota_id\n",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public SqlResult<M206Detail> findProdQuotaInfoAndDetails(SqlParam<M206Detail> params) throws Exception {
		String sql="select p1.quota_id,p1.system_no,p1.tano,p1.prod_code,p1.quota_busi_type,p1.diff_cust_type,p1.dim_type,p1.dim_type_value,p1.quota_no,p1.upper_no,p1.dim_level,p1.org_level,round(p1.total_quota/10000) as total_quota,round(p1.give_quota/10000) as give_quota_display,round(p1.give_quota/10000) as give_quota,round(p1.keep_quota/10000) as keep_quota_display,round(p1.keep_quota/10000) as keep_quota,round(p1.share_quota/10000) as share_quota,round(p1.person_quota/10000) as person_quota,round(p1.public_quota/10000) as public_quota,round(p1.trade_quota/10000) as trade_quota,round(p1.prod_quota/10000) as prod_quota,p1.legal_code,p2.expire_date , p2.quota_version, 0 as cost_share_quota, 0 as cost_person_quota,0 as cost_public_quota, 0 as cost_trade_quota, 0 as cost_prod_quota from prod_quota_detail p1 left join prod_quota_info p2 on p1.quota_id=p2.quota_id where p1.system_no=$S{systemNo} and p1.prod_code=$S{prodCode} and p1.quota_busi_type=$S{quotaBusiType} and (p2.legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode})";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public SqlResult<M206Detail> findProdQuotaInfoByKey(SqlParam<M206Detail> params) throws Exception {
		String sql="select quota_id,system_no,tano,prod_code,quota_busi_type,diff_cust_type,dim_one_type,dim_two_type,total_quota/10000 as total_quota,expire_date,legal_code FROM prod_quota_info " +
				" where system_no=$S{system_no} and tano=$S{tano} and prod_code=$S{prod_code} and quota_busi_type=$S{quota_busi_type} and legal_code=$S{legal_code} ";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public List<SqlRow> findProdQuotaDetailsOrg(Map<String,Object> params) throws Exception {
		String sql="select dim_type_value,quota_no from prod_quota_detail where dim_type='3' and quota_no_tree like '%$U{quotaNo}%'";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public SqlResult<M206Detail> findParentProdQuotaDetails(SqlParam<M206Detail> params) throws Exception {
		String sql="select * from prod_quota_detail where quota_no=$S{upperNo}";
		return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public UpdateResult addProdQuotaDetail(SqlParam<M206Detail> params) throws Exception {
		return super.update("INSERT INTO prod_quota_detail(quota_id,system_no,tano,prod_code,quota_busi_type,legal_code,diff_cust_type,dim_type,dim_type_value," +
						"quota_no,upper_no,dim_level,org_level,total_quota,give_quota,keep_quota,share_quota,person_quota,public_quota,trade_quota,prod_quota) " +
						"VALUES($S{quotaId},$S{systemNo},$S{supplyCode},$S{prodCode},$S{quotaBusiType},$S{legalCode},$S{diffCustType},$S{dimType},$S{dimTypeValue},$S{quotaNo},$S{upperNo},$S{dimLevel},$S{orgLevel},$S{totalQuota}*10000,$S{giveQuota}*10000,$S{keepQuota}*10000,$S{shareQuota}*10000,$S{personQuota}*10000,$S{publicQuota}*10000,$S{tradeQuota}*10000,$S{prodQuota}*10000)",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public UpdateResult updateProdQuotaDetail(JSONObject params) throws Exception {
		return super.update("UPDATE prod_quota_detail SET diff_cust_type=$S{diffCustType}, dim_level=$S{dimLevel} ,org_level=$S{orgLevel} ,total_quota=$S{totalQuota} ,give_quota=$S{giveQuota} ,keep_quota=$S{keepQuota} ,share_quota=$S{shareQuota} ,person_quota=$S{personQuota} ,public_quota=$S{publicQuota} ,trade_quota=$S{tradeQuota} ,prod_quota=$S{prodQuota} where quota_no=$S{quotaNo}",
				SubDatabase.DATABASE_SYS_CENTER, params);
	}


	public UpdateResult addOrg(JSONObject params) throws Exception {

		String sql="INSERT INTO prod_quota_detail(quota_id,system_no,tano,prod_code,quota_busi_type,diff_cust_type,dim_type,dim_type_value,quota_no_tree,quota_no,upper_no,dim_level,org_level,total_quota,give_quota,keep_quota,share_quota,person_quota,public_quota,trade_quota,prod_quota) " +
				"VALUES($S{quotaId},$S{systemNo},$S{tano},$S{prodCode},$S{quotaBusiType},$S{diffCustType},$S{dimType},$S{dimTypeValue},$S{quotaNoTree},$S{quotaNo},$S{upperNo},$S{dimLevel},$S{orgLevel},$S{totalQuota},$S{giveQuota},$S{keepQuota},$S{shareQuota},$S{personQuota},$S{publicQuota},$S{tradeQuota},$S{prodQuota})";
		return super.update(sql,SubDatabase.DATABASE_SYS_CENTER, params);
	}

	public UpdateResult deleteProdQuotaDetail(SqlParam<M206Detail> params) throws Exception {
		return super.update("DELETE FROM prod_quota_detail WHERE  system_no=$S{systemNo} AND tano=$S{tano} AND prod_code=$S{prodCode} AND quota_busi_type=$S{quotaBusiType} AND dim_type=$S{dimType} AND dim_type_value=$S{dimTypeValue} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

	public UpdateResult deleteProdQuotaDetailByQuotaId(SqlParam<M206Detail> params) throws Exception {
		params.setMakeSql(false);
		return super.update("DELETE FROM prod_quota_detail WHERE quota_id=$S{quotaId} ",
				SubDatabase.DATABASE_SYS_CENTER, params.getModel());
	}

}
