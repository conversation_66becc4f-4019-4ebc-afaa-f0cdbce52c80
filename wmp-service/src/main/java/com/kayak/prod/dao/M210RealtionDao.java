package com.kayak.prod.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.prod.model.M210Realtion;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * 产品模型参数关系
 *
 * <AUTHOR>
 * @date 2021-05-20 16:18
 */
@Repository
public class M210RealtionDao extends ComnDao {

    /**
     * M210RealtionService
     * @param param
     * @return
     * @throws Exception
     */
    public SqlResult<M210Realtion> findAll(SqlParam<M210Realtion> param) throws Exception {
        String sql = "SELECT model_id, para_code, tabs_id, para_val, link_para_code, show_flag, blank_flag, dict, default_val, edit_flag FROM prod_model_para_relation ";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, param);
    }


    public void updateM210Realtions(SqlParam<M210Realtion> params) throws Exception {
        List<M210Realtion> m210RealtionList = params.getModel().getList();
        DaoUtil.doTrans(() -> {
            super.update("DELETE FROM prod_model_para_relation WHERE model_id=$S{modelId} AND para_code=$S{paraCode} ", SubDatabase.DATABASE_SYS_CENTER, m210RealtionList.get(0));

            for (M210Realtion m210Realtion: m210RealtionList) {
                m210Realtion.setTabsId(UUID.randomUUID().toString().replaceAll("-",""));
                super.update("INSERT INTO prod_model_para_relation (model_id, para_code, tabs_id, para_val, link_para_code, show_flag, blank_flag, dict, default_val, edit_flag) " +
                                " VALUES($S{modelId}, $S{paraCode}, $S{tabsId}, $S{paraVal}, $S{linkParaCode}, $S{showFlag}, $S{blankFlag}, $S{dict}, $S{defaultVal}, $S{editFlag})",
                        SubDatabase.DATABASE_SYS_CENTER, m210Realtion);
            }
            }, SubDatabase.DATABASE_SYS_CENTER);
    }

    public List<SqlRow> getParaRealList(String modelId, String compIds) throws Exception {
        String sql = "SELECT model_id modelId, para_code paraCode, tabs_id tabsId, para_val paraVal, link_para_code linkParaCode, show_flag showFlag, blank_flag blankFlag, dict, default_val defaultVal, edit_flag editFlag FROM prod_model_para_relation WHERE model_id ='"+modelId+"' " +
                " AND EXISTS ( SELECT 1 FROM prod_comp_para a WHERE comp_id IN "+compIds+" and a.para_code = para_code )";
        return super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, modelId);
    }


    /**
     * 批量更新产品模型参数关系
     */
    public void batchUpdateParaReal(List<M210Realtion> m210RealtionList) throws Exception {
        if (m210RealtionList != null){
            for (M210Realtion m210Realtion: m210RealtionList) {
                m210Realtion.setTabsId(UUID.randomUUID().toString().replaceAll("-",""));
                super.update("INSERT INTO prod_model_para_relation (model_id, para_code, tabs_id, para_val, link_para_code, show_flag, blank_flag, dict, default_val, edit_flag) " +
                                " VALUES($S{modelId}, $S{paraCode}, $S{tabsId}, $S{paraVal}, $S{linkParaCode}, $S{showFlag}, $S{blankFlag}, $S{dict}, $S{defaultVal}, $S{editFlag})",
                        SubDatabase.DATABASE_SYS_CENTER, m210Realtion);
            }
        }
    }
}
