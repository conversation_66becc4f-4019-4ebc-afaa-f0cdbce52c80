package com.kayak.plan.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;

import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.graphql.model.FetcherData;
import com.kayak.plan.dao.MD04Dao;
import com.kayak.plan.model.MD04;
import com.kayak.prod.service.M206DetailService;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "定投申购业务统计表-按机构", model = MD04.class)
public class MD04Service {
	protected static final Logger log = LoggerFactory.getLogger(M206DetailService.class);
	@Autowired
	private MD04Dao mD04Dao;

	@Autowired
	private M001Dao m001Dao;


	@API(desc = "定投申购业务统计表-按机构", auth = APIAuth.YES)
	public SqlResult<MD04> findPlanCustExecLog(SqlParam<MD04> params) throws Exception {
		SqlResult<MD04> sqlResult = mD04Dao.findPlanCustExecLog(params);
		sqlResult.setRows(sqlResult.getRows().stream().map(item->{
			try{
				M001 m001 = m001Dao.get(item.getTransSubBranch());
				if (m001 != null){
					item.setOrgName(m001.getOrgname());
				}

				params.getModel().setOrgno(item.getTransSubBranch());

				SqlResult<MD04> md04One = mD04Dao.findCustDeductionOneNum(params);
				item.setDeductionOneCust(md04One.getRows().get(0).getDeductionOneCust());
				SqlResult<MD04> md04Three = mD04Dao.findCustDeductionThreeNum(params);
				item.setDeductionThreeCust(md04Three.getRows().get(0).getDeductionThreeCust());
				SqlResult<MD04> md04CustNum = mD04Dao.findAddCustNum(params);
				item.setAddCustNum(md04CustNum.getRows().get(0).getAddCustNum());
			} catch (Exception e) {
				log.error("MD04出现错误："+e.getMessage());
				throw new RuntimeException("MD04出现错误："+e.getMessage());
			}
			return item;
		}).collect(Collectors.toList()));
		return sqlResult;
	}

}
