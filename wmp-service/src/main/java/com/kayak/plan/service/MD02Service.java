package com.kayak.plan.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.plan.dao.MD02Dao;
import com.kayak.plan.model.MD02;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@APIDefine(desc = "瀹氭姇鍗忚鏌ヨ", model = MD02.class)
@RequiredArgsConstructor
@Slf4j
public class MD02Service {
    @Autowired
    private final MD02Dao mD02Dao;

    @Autowired
    private final ReportformUtil reportformUtil;


    @API(desc = "瀹氭姇鍗忚鏌ヨ")
    public SqlResult<MD02> findMD02(SqlParam<MD02> params) throws Exception {
        params.setMakeSql(true);
        SqlResult<MD02> md02SqlResult = mD02Dao.findMD02s(params);
        reportformUtil.checkMaxExcel(md02SqlResult.getRows().size());
        md02SqlResult.setDesensitized(false);
        return md02SqlResult;
    }

}


