package com.kayak.plan.service;

import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.model.M102;
import com.kayak.graphql.model.FetcherData;
import com.kayak.plan.dao.MD05Dao;
import com.kayak.plan.model.MD05;
import com.kayak.prod.service.M206DetailService;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@APIDefine(desc = "客户定投申购计划查询", model = MD05.class)
public class MD05Service {
	protected static final Logger log = LoggerFactory.getLogger(M206DetailService.class);
	@Autowired
	private MD05Dao MD05Dao;

	@Autowired
	private M001Dao m001Dao;

	private final String amtType = "固定金额";

	@API(desc = "客户定投申购计划查询", auth = APIAuth.YES)
	public SqlResult<MD05> findPlanCustProtocol(SqlParam<MD05> params) throws Exception {
		params.setMakeSql(false);
		SqlResult<MD05> sqlResult = MD05Dao.findPlanCustProtocol(params);
		List<MD05> rows = sqlResult.getRows();
		if (rows != null){
			for (MD05 md05 : rows){
				Map<String,Object> map = new HashMap<>();
				map.put("planNo",md05.getPlanNo());
				SqlParam<MD05> planCustCumAmtParm = new FetcherData<>(map, MD05.class);
				SqlResult<MD05> cumAmt = MD05Dao.findPlanCustCumAmt(planCustCumAmtParm);
				if (cumAmt != null && cumAmt.getRows().size() > 0){
					md05.setCumFailAmt(cumAmt.getRows().get(0).getCumFailAmt());
					md05.setCumSuccAmt(cumAmt.getRows().get(0).getCumSuccAmt());
				}
				md05.setAmtType(amtType);
			}
		}
		return sqlResult;
	}

}
