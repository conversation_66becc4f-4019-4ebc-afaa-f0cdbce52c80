package com.kayak.paym.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hundsun.jrescloud.common.exception.BaseBizException;
import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.common.util.ExcelUtils;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.paym.dao.P099Dao;
import com.kayak.paym.model.P099;
import com.kayakwise.fina.api.P099DubboDecorator;
import com.kayakwise.paym.req.P099Request;
import com.kayakwise.paym.resp.P099Response;
import com.kayakwise.wmp.pub.pojo.GlobalContents;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 资金补发
 * <AUTHOR>
 * @Date 2022-07-13
 */
@Service
@APIDefine(desc = "资金补发", model = P099.class)
public class P099Service {

    private final static int MAX_SIZE = 200;

    @CloudReference
    private P099DubboDecorator p099DubboDecorator;

    @Autowired
    private P099Dao p099Dao;

    private final static Logger log = LoggerFactory.getLogger(P099Service.class);

    @Value("${upload.path}")
    private String uploadPath;

    @API(desc = "查询补发资金流水表信息", auth = APIAuth.YES)
    public SqlResult<P099> findLogs(SqlParam<P099> params) throws Exception {
        return p099Dao.findLogs(params);
    }

    @API(desc = "新增补发资金流水", auth = APIAuth.YES,operation = APIOperation.UPDATE)
    public String add(SqlParam<P099> params) throws Exception {
        params.getModel().setAppSerno(UUID.randomUUID().toString().replaceAll("-", ""));
        int count = p099Dao.add(params);
        if(count < 1){
            throw new PromptException("新增失败");
        }
        return RequestSupport.updateReturnJson(true, "新增成功", null).toString();
    }

    @API(desc = "删除补发资金流水", auth = APIAuth.YES,operation = APIOperation.UPDATE)
    public String delete(SqlParam<P099> params) throws Exception {
        int count = p099Dao.delete(params);
        if(count < 1){
            throw new PromptException("删除失败");
        }
        return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
    }

    @API(desc = "修改补发资金流水", auth = APIAuth.YES,operation = APIOperation.UPDATE)
    public String edit(SqlParam<P099> params) throws Exception {
        int count = p099Dao.update(params);
        if(count < 1){
            throw new PromptException("修改失败");
        }
        return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
    }

    @API(desc = "补发", auth = APIAuth.YES,operation = APIOperation.UPDATE)
    public String repay(SqlParam<P099> params) throws Exception {
        //获取管理台选中的数据
        String chooseData = (String) params.getParams().get("chooseData");
        String dealType = (String) params.getParams().get("dealType");
        JSONArray json = JSONArray.parseArray(chooseData);
        P099Request p099Request = new P099Request(){{
            setDealType(dealType);
            setItems(json.stream().map(i -> JSONObject.parseObject(i.toString(), P099Request.P099Item.class)).collect(Collectors.toList()));
        }};
        P099Response p099Response = null;
        try{
            p099Response = p099DubboDecorator.execute(p099Request);
            if(GlobalContents.RTN_CODE_SUCCESS.equals(p099Response.getRtnCode())){
                return RequestSupport.updateReturnJson(true, "资金补发处理成功", null).toString();
            }else{
                log.error("资金补发处理失败,核心返回[{}]", p099Response);
                return RequestSupport.updateReturnJson(false, "资金补发处理失败", null).toString();
            }
        }catch (BaseBizException e){
            return RequestSupport.updateReturnJson(false, e.getErrorMessage(), null).toString();
        }catch (Exception e){
            log.error("资金补发处理异常", e);
            return RequestSupport.updateReturnJson(false, "资金补发处理失败", null).toString();
        }
    }

    @API(desc="文件上传", auth = APIAuth.YES)
    public String parseExcel(SqlParam<P099> params) throws Exception {
        String excelFileNo = params.getModel().getExcelFileNo();
        if (StringUtils.isBlank(excelFileNo)) {
            log.error("文件路径参数[{}]不存在，上传文件目录[{}]", excelFileNo, uploadPath);
            return RequestSupport.updateReturnJson(false, "文件不存在", null).toString();
        }
        String strPath = uploadPath + excelFileNo;
        File file = new File(strPath);
        if(!file.exists()){
            log.error("文件[{}]不存在]", strPath);
            return RequestSupport.updateReturnJson(false, "文件不存在", null).toString();
        }
        FileInputStream fileInputStream = new FileInputStream(strPath);
        List<List<Object>> data = ExcelUtils.getBankListByExcel(fileInputStream, strPath);
        log.info("文件内容[{}]", JSONObject.toJSON(data).toString());
        if(data.isEmpty()){
            return RequestSupport.updateReturnJson(false, "文件内容不允许为空", null).toString();
        }
        int count = 0;
        int updateCount = 0;
        int insertCount = 0;
        for (int i = 0; i < data.size(); i++) {
            List<Object> row = data.get(i);
            String appSerno = (String)row.get(0);
            if(StringUtils.isBlank(appSerno)){
                continue;
            }
            count++;
            String oriCapitalSerno = (String) row.get(1);
            String oriCfmSerno = (String) row.get(2);
            String custNo = (String) row.get(3);
            String transAmt = (String) row.get(4);
            String toAcctNo = (String) row.get(5);
            String fromAcctNo = (String) row.get(6);
            String remark = (String) row.get(7);
            if(StringUtils.isBlank(appSerno))
                return RequestSupport.updateReturnJson(false, "申请流水号不存在", null).toString();
            if(StringUtils.isBlank(oriCapitalSerno))
                return RequestSupport.updateReturnJson(false, "申请流水号[" + appSerno + "]原资金流水号不存在", null).toString();
            if(StringUtils.isBlank(oriCfmSerno))
                return RequestSupport.updateReturnJson(false, "申请流水号[" + appSerno + "]原确认流水号不存在", null).toString();
            if(StringUtils.isBlank(custNo))
                return RequestSupport.updateReturnJson(false, "申请流水号[" + appSerno + "]客户号不存在", null).toString();
            if(StringUtils.isBlank(transAmt) || !Pattern.matches("^[1-9][0-9]{0,15}$|^[1-9][0-9]{0,15}\\.[0-9]{1,2}$", transAmt))
                return RequestSupport.updateReturnJson(false, "申请流水号[" + appSerno + "]交易金额不存在或格式不正确", null).toString();
            if(StringUtils.isBlank(toAcctNo))
                return RequestSupport.updateReturnJson(false, "申请流水号[" + appSerno + "]转入账号不存在", null).toString();
            if(StringUtils.isBlank(fromAcctNo))
                return RequestSupport.updateReturnJson(false, "申请流水号[" + appSerno + "]转出账号不存在", null).toString();
            params.getModel().setAppSerno(appSerno);
            params.getModel().setOriCapitalSerno(oriCapitalSerno);
            params.getModel().setOriCfmSerno(oriCfmSerno);
            params.getModel().setCustNo(custNo);
            params.getModel().setTransAmt(transAmt);
            params.getModel().setToAcctNo(toAcctNo);
            params.getModel().setFromAcctNo(fromAcctNo);
            params.getModel().setRemark(remark);
            SqlResult<P099> result = p099Dao.findByAppSerno(params);
            if(result.getRows().size() > 0){ // 更新
                updateCount += p099Dao.update(params);
            }else{ // 新增
                insertCount += p099Dao.add(params);
            }
        }
        return RequestSupport.updateReturnJson(true,
                String.format("资金补发处理成功，共[%d]条，新增[%d]条，更新[%d]条", count, insertCount, updateCount)
                , null).toString();
    }
}
