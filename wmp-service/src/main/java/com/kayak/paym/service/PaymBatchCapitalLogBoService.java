package com.kayak.paym.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hundsun.jrescloud.common.exception.BaseBizException;
import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.common.constants.RtnCodeStatus;
import com.kayak.common.constants.SystemNo;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.cust.dao.M102Dao;
import com.kayak.graphql.model.FetcherData;
import com.kayak.paym.constants.BatchDealType;
import com.kayak.paym.dao.PaymBatchCapitalLogBoDao;
import com.kayak.paym.model.PaymBatchCapitalLogBo;
import com.kayak.prod.service.M206DetailService;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import com.kayakwise.cust.api.T148DubboDecorator;
import com.kayakwise.cust.req.T148ServiceRequest;
import com.kayakwise.cust.resp.T148ServiceResponse;
import com.kayakwise.fina.api.T544DubboDecorator;
import com.kayakwise.paym.req.T544ServiceRequest;
import com.kayakwise.paym.resp.T544ServiceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@APIDefine(desc = "批量资金流水表服务", model = PaymBatchCapitalLogBo.class)
public class PaymBatchCapitalLogBoService {
	protected static final Logger log = LoggerFactory.getLogger(M206DetailService.class);

	@Autowired
	private PaymBatchCapitalLogBoDao paymBatchCapitalLogBoDao;

	@CloudReference
	private T544DubboDecorator t544ServiceDecorator;

	@Autowired
	private M102Dao m102Dao;

	@Autowired
	private M215Service m215Service;
	@Autowired
	private ReportformUtil reportformUtil;
	@Autowired
	private M001Dao m001Dao;

	@CloudReference
	private T148DubboDecorator t148DubboDecorator;

	@API(desc = "查询批量资金流水表信息", auth = APIAuth.YES)
	public SqlResult<PaymBatchCapitalLogBo> findFailurePaymBatchCapitalLogBos(SqlParam<PaymBatchCapitalLogBo> params) throws Exception {
		//params.setMakeSql(true);
		SqlResult<PaymBatchCapitalLogBo> sqlResult= paymBatchCapitalLogBoDao.findFailurePaymBatchCapitalLogBos(params);
		//获取产品名称、TA名称
		if(null!=sqlResult&&sqlResult.getRows().size()>0){
			List<PaymBatchCapitalLogBo> sqlList=sqlResult.getRows();
			for(PaymBatchCapitalLogBo paymBatchCapitalLogBo:sqlList){
				Map<String, String> prodInfoMap = m215Service.getProdInfo(paymBatchCapitalLogBo.getLegalCode(), SystemNo.FINA,paymBatchCapitalLogBo.getTano(),paymBatchCapitalLogBo.getProdCode());
				if(prodInfoMap != null && prodInfoMap.size() > 0){
					paymBatchCapitalLogBo.setProdName(prodInfoMap.get("prod_name"));
				}
				if(null!=paymBatchCapitalLogBo){
					paymBatchCapitalLogBo.setTaName(reportformUtil.getFinaTaName(paymBatchCapitalLogBo.getTano()));
				}
				if(Tools.isNotBlank(paymBatchCapitalLogBo.getTransOrgno())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",paymBatchCapitalLogBo.getTransOrgno());
					SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(dateParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						paymBatchCapitalLogBo.setTransOrgName(m001Info.getRows().get(0).getOrgname());
					}
				}
			}
		}
		return sqlResult;
	}
	
	@API(desc = "修改资金账号", params = "acct_no", auth = APIAuth.YES,operation = APIOperation.UPDATE)
	public String updateAcctNo(SqlParam<PaymBatchCapitalLogBo> params) throws Exception {
		//先比对该账号是否属于该用户

		String modifyAcctNo = params.getModel().getModifyAcctNo();
		String custNo = params.getModel().getCustNo();
		String message = "";
		if(Tools.isNotBlank(custNo)&&Tools.isNotBlank(modifyAcctNo)){
			/**Map<String,Object> map = new HashMap<>();
			map.put("custNo",params.getModel().getCustNo());
			map.put("acctNo",params.getModel().getModifyAcctNo());
			SqlParam<M102> custParams = new FetcherData<>(map, M102.class);
			custParams.setMakeSql(true);
			SqlResult<M102> cusInfo = m102Dao.findCustTransAccts(custParams);
			if(cusInfo.getRows() != null && cusInfo.getRows().size() > 0){
				paymBatchCapitalLogBoDao.updateAcctNo(params).getEffect();
				message = params.getModel().getCustName()+"已经成功修改"+params.getModel().getModifyAcctNo();
			}else{
				log.error(params.getModel().getCustName()+"修改的资金账号"+params.getModel().getModifyAcctNo()+"不属于该客户");
				message = "修改信息失败："+params.getModel().getCustName()+"修改的资金账号"+params.getModel().getModifyAcctNo()+"不属于该客户";
			}*/
			T148ServiceRequest t148ServiceRequest = new T148ServiceRequest();
			t148ServiceRequest.setCur("CNY");
			t148ServiceRequest.setAcctNo(modifyAcctNo);
			T148ServiceResponse t148ServiceResponse = t148DubboDecorator.excute(t148ServiceRequest);
			if(t148ServiceResponse != null && t148ServiceResponse.getRtnCode().equals(RtnCodeStatus.RTNCODE)){
				if(t148ServiceResponse.getBaseAcctInfoQueryResp().getClientNo().equals(custNo)){
					paymBatchCapitalLogBoDao.updateAcctNo(params).getEffect();
					message = params.getModel().getCustName()+"已经成功修改"+params.getModel().getModifyAcctNo();
				}else{
					return RequestSupport.updateReturnJson(false, "修改信息失败："+params.getModel().getCustName()+"修改的资金账号"+params.getModel().getModifyAcctNo()+"不属于该客户", null).toString();

				}
			}else{
				return RequestSupport.updateReturnJson(false, "调用接口失败："+t148ServiceResponse.getRtnCode()+"，失败信息："+t148ServiceResponse.getRtnDesc(), null).toString();
			}
		}
		Map<String,Object> map1 = new HashMap<>();
		return RequestSupport.updateReturnJson(true, message, map1).toString();
	}


	@API(desc = "重发", auth = APIAuth.YES,operation = APIOperation.UPDATE)
	public String dealBatchError(SqlParam<PaymBatchCapitalLogBo> params) throws Exception {
		//获取管理台选中的数据
		String chooseData = (String) params.getParams().get("chooseData");
		JSONArray jsonArray = JSONArray.parseArray(chooseData);
		if (jsonArray.size() <= 0) {
			log.error("所选数组长度为0");
			throw new Exception("所选数组长度为0");
		}

		List<String> count  = new ArrayList<>();
		if(jsonArray.size() > 0){
			List<PaymBatchCapitalLogBo> paymBatchCapitalLogBoList = JSONArray.parseArray(jsonArray.toJSONString(),PaymBatchCapitalLogBo.class);
			 count = paymBatchCapitalLogBoList.stream().map(PaymBatchCapitalLogBo::getBusiType).distinct().collect(Collectors.toList());
			if(count != null && count.size() > 1){
				return RequestSupport.updateReturnJson(false, "请选择相同的业务类型进行处理", null).toString();
			}
		}

		//批量失败记录处理类型
		String failDealType;
		//返回页面用
		String dealName;
		//组装批量失败记录流水号
		List<String> errorSernoList = new ArrayList<>();

		for (int i=0;i<jsonArray.size();i++){
			JSONObject _json = (JSONObject) jsonArray.get(i);
			errorSernoList.add((String) _json.get("capitalSerno"));
		}
		if ("repeat".equals(params.getModel().getFailDealType())){
			failDealType = BatchDealType.RETRY;
			dealName = "重发";
			List<String> capitalSernoList = new ArrayList<>();
			for(int i=0;i<jsonArray.size();i++){
				JSONObject updateData = jsonArray.getJSONObject(i);
				//取交易日期，查询批量执行表日期=交易日期的申请导出执行状态，已执行直接拒绝重发
				String transDate = (String) updateData.get("transDate");
				String busiType = (String) updateData.get("busiType");
				if (!Tools.isNotBlank(busiType)){
					throw new PromptException("交易日期为["+transDate+"]的记录业务类型为空,请检查！");
				}
				if (Tools.isNotBlank(transDate)) {
					if (busiType.equals("4")) {//扣款业务才需要校验
						Map map = new HashMap<String,String>();
						map.put("transDate", transDate);
						List<SqlRow> result = paymBatchCapitalLogBoDao.findBatchExecStatus(map);
						if (result!=null&&result.size()>0) {
							//查询出申请导出已执行
							throw new PromptException("交易日期为["+transDate+"]的交易记录已经执行申请导出,不能重发");
						}
					}
				}else {
					throw new PromptException("所选第["+(i+1)+"]条数据的交易日期为空,请检查！");
				}
				if(Tools.isNotBlank(updateData.get("dealStatus").toString()) && "3".equals(updateData.get("dealStatus").toString())){
					throw new PromptException("处理中的数据不能重复操作");
				}
				if(Tools.isNotBlank(updateData.get("capitalSerno").toString())){
					capitalSernoList.add(updateData.get("capitalSerno").toString());
				}else{
					throw new PromptException("所选择数据流水号为空，请检查数据完整性！");
				}
			}
			T544ServiceRequest t544ServiceRequest = new T544ServiceRequest();
			t544ServiceRequest.setBatchFailSernoList(capitalSernoList);
			t544ServiceRequest.setBatchFailDealType(BatchDealType.RETRY);
			T544ServiceResponse t544ServiceResponse;
			try {
				t544ServiceResponse = t544ServiceDecorator.execute(t544ServiceRequest);
			}catch (BaseBizException e){
				return RequestSupport.updateReturnJson(false, e.getErrorMessage(), null).toString();
			}
			if(t544ServiceResponse != null && Tools.isNotBlank(t544ServiceResponse.getRtnCode()) && !t544ServiceResponse.getRtnCode().equals("000000")){
				throw new PromptException("调用T544重发接口失败：错误代码："+t544ServiceResponse.getRtnCode()+"，错误信息："+t544ServiceResponse.getRtnDesc());
			}
		}else if ("offline".equals(params.getModel().getFailDealType())){
			failDealType = BatchDealType.OFFLINE;
			dealName = "线下处理";
			List<String> capitalSernoList = new ArrayList<>();
			for(int i=0;i<jsonArray.size();i++){
				JSONObject updateDate = jsonArray.getJSONObject(i);
				if(Tools.isNotBlank(updateDate.get("dealStatus").toString()) && "3".equals(updateDate.get("dealStatus").toString())){
					throw new PromptException("处理中的数据不能重复操作");
				}
				if(Tools.isNotBlank(updateDate.get("capitalSerno").toString())){
					capitalSernoList.add(updateDate.get("capitalSerno").toString());
				}else{
					throw new PromptException("所选择数据流水号为空，请检查数据完整系！");
				}
			}
			T544ServiceRequest t544ServiceRequest = new T544ServiceRequest();
			t544ServiceRequest.setBatchFailSernoList(capitalSernoList);
			t544ServiceRequest.setBatchFailDealType(BatchDealType.OFFLINE);
			T544ServiceResponse t544ServiceResponse = t544ServiceDecorator.execute(t544ServiceRequest);
			if(t544ServiceResponse != null && Tools.isNotBlank(t544ServiceResponse.getRtnCode()) && !t544ServiceResponse.getRtnCode().equals("000000")){
				throw new PromptException("调用T544线下处理接口失败：错误代码："+t544ServiceResponse.getRtnCode()+"，错误信息："+t544ServiceResponse.getRtnDesc());
			}
		}else if ("refuse".equals(params.getModel().getFailDealType())){
			/**if(count != null && count.size() > 0 && count.get(0).equals("1")){
				return RequestSupport.updateReturnJson(false, "还款不可以拒绝，只能重发", null).toString();
			}*/
			dealName = "拒绝";
			List<String> capitalSernoList = new ArrayList<>();
			for(int i=0;i<jsonArray.size();i++){
				JSONObject updateDate = jsonArray.getJSONObject(i);
				if(Tools.isNotBlank(updateDate.get("busiType").toString()) && !"4".equals(updateDate.get("busiType").toString())){
					throw new PromptException("解冻并扣款可以拒绝，其他业务类型不能拒绝，请重新选择业务类型");
				}
				if(Tools.isNotBlank(updateDate.get("dealStatus").toString()) && "3".equals(updateDate.get("dealStatus").toString())){
					throw new PromptException("处理中的数据不能重复操作");
				}
				if(Tools.isNotBlank(updateDate.get("capitalSerno").toString())){
					capitalSernoList.add(updateDate.get("capitalSerno").toString());
				}else{
					throw new PromptException("所选择数据流水号为空，请检查数据完整系！");
				}
			}
			T544ServiceRequest t544ServiceRequest = new T544ServiceRequest();
			t544ServiceRequest.setBatchFailSernoList(capitalSernoList);
			t544ServiceRequest.setBatchFailDealType(BatchDealType.REFUSE);
			T544ServiceResponse t544ServiceResponse;
			try {
				t544ServiceResponse = t544ServiceDecorator.execute(t544ServiceRequest);
			}catch (BaseBizException e){
				return RequestSupport.updateReturnJson(false, e.getErrorMessage(), null).toString();
			}
			if(t544ServiceResponse != null && Tools.isNotBlank(t544ServiceResponse.getRtnCode()) && !t544ServiceResponse.getRtnCode().equals("000000")){
				throw new PromptException("调用T544重发接口失败：错误代码："+t544ServiceResponse.getRtnCode()+"，错误信息："+t544ServiceResponse.getRtnDesc());
			}
		}else if ("interrupt".equals(params.getModel().getFailDealType())){
			/**if(count != null && count.size() > 0 && count.get(0).equals("1")){
			 return RequestSupport.updateReturnJson(false, "还款不可以拒绝，只能重发", null).toString();
			 }*/
			dealName = "中断状态处理";
			List<String> capitalSernoList = new ArrayList<>();
			for(int i=0;i<jsonArray.size();i++){
				JSONObject updateDate = jsonArray.getJSONObject(i);
				if(Tools.isNotBlank(updateDate.get("dealStatus").toString()) && !"4".equals(updateDate.get("dealStatus").toString())){
					throw new PromptException("非中断状态不能处理");
				}
				if(Tools.isNotBlank(updateDate.get("capitalSerno").toString())){
					capitalSernoList.add(updateDate.get("capitalSerno").toString());
				}else{
					throw new PromptException("所选择数据流水号为空，请检查数据完整系！");
				}
			}
			T544ServiceRequest t544ServiceRequest = new T544ServiceRequest();
			t544ServiceRequest.setBatchFailSernoList(capitalSernoList);
			t544ServiceRequest.setBatchFailDealType(BatchDealType.INTERRUPT);
			T544ServiceResponse t544ServiceResponse;
			try {
				t544ServiceResponse = t544ServiceDecorator.execute(t544ServiceRequest);
			}catch (BaseBizException e){
				return RequestSupport.updateReturnJson(false, e.getErrorMessage(), null).toString();
			}
			if(t544ServiceResponse != null && Tools.isNotBlank(t544ServiceResponse.getRtnCode()) && !t544ServiceResponse.getRtnCode().equals("000000")){
				throw new PromptException("调用T544重发接口失败：错误代码："+t544ServiceResponse.getRtnCode()+"，错误信息："+t544ServiceResponse.getRtnDesc());
			}
		} else {
			log.error("批量差错处理类型不存在");
			return RequestSupport.updateReturnJson(false, "批量差错处理类型不存在", null).toString();
		}
		//返回页面参数
		Map<String,Object> map1 = new HashMap<>();
		map1.put("length",jsonArray.size());
		return RequestSupport.updateReturnJson(true, "后台"+ dealName +"完成，共选择"+jsonArray.size()+"条批量失败记录", map1).toString();
	}

	private byte[] changeMapToByte(Map<String,Object> map) throws Exception {

		byte[] bytes = null;
		try {
			bytes = JSON.toJSONString(map, true).getBytes();
		} catch (Exception e) {
			log.error("map到byte[]转换异常");
			throw new Exception("map到byte[]转换异常");
		}

		return bytes;
	}
}
