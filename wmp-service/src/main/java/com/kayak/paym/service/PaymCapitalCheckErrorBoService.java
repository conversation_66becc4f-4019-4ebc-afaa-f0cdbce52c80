package com.kayak.paym.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hundsun.jrescloud.rpc.annotation.CloudReference;
import com.kayak.aspect.annotations.API;
import com.kayak.aspect.annotations.APIAuth;
import com.kayak.aspect.annotations.APIDefine;
import com.kayak.aspect.annotations.APIOperation;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.common.constants.ErrorDealType;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.system.RequestSupport;
import com.kayak.core.util.Tools;
import com.kayak.graphql.model.FetcherData;
import com.kayak.paym.dao.PaymCapitalCheckErrorBoDao;
import com.kayak.paym.model.PaymCapitalCheckErrorBo;
import com.kayak.prod.service.M215Service;
import com.kayak.system.dao.M001Dao;
import com.kayak.system.model.M001;
import com.kayakwise.fina.api.T545DubboDecorator;
import com.kayakwise.paym.req.T545ServiceRequest;
import com.kayakwise.paym.resp.T545ServiceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@APIDefine(desc = "资金流水对账差错表实体类服务", model = PaymCapitalCheckErrorBo.class)
public class PaymCapitalCheckErrorBoService {
	protected static final Logger log = LoggerFactory.getLogger(PaymCapitalCheckErrorBoService.class);

	@Autowired
	private PaymCapitalCheckErrorBoDao paymCapitalCheckErrorBoDao;

	@CloudReference
	private T545DubboDecorator t545ServiceDecorator;

	@Autowired
	private M215Service m215Service;
	@Autowired
	private ReportformUtil reportformUtil;
	@Autowired
	private M001Dao m001Dao;

	@API(desc = "查询资金流水对账差错表实体类信息", auth = APIAuth.YES)
	public SqlResult<PaymCapitalCheckErrorBo> findPaymCapitalCheckErrorBos(SqlParam<PaymCapitalCheckErrorBo> params) throws Exception {
		params.setMakeSql(true);
		SqlResult<PaymCapitalCheckErrorBo> sqlResult=paymCapitalCheckErrorBoDao.findPaymCapitalCheckErrorBos(params);
		//获取产品名称、TA名称
		if(null!=sqlResult&&sqlResult.getRows().size()>0){
			List<PaymCapitalCheckErrorBo> sqlList=sqlResult.getRows();
			for(PaymCapitalCheckErrorBo paymCapitalCheckErrorBo:sqlList){
				Map<String, String> prodInfoMap = m215Service.getProdInfo(paymCapitalCheckErrorBo.getLegalCode(), paymCapitalCheckErrorBo.getSystemNo(),paymCapitalCheckErrorBo.getTano(),paymCapitalCheckErrorBo.getProdCode());
				if(prodInfoMap != null && prodInfoMap.size() > 0){
					paymCapitalCheckErrorBo.setProdName(prodInfoMap.get("prod_name"));
				}
				if(null!=paymCapitalCheckErrorBo){
					paymCapitalCheckErrorBo.setTaName(reportformUtil.getFinaTaName(paymCapitalCheckErrorBo.getTano()));
				}
				if(Tools.isNotBlank(paymCapitalCheckErrorBo.getTransOrgno())){
					Map<String,Object> map = new HashMap<>();
					map.put("orgno",paymCapitalCheckErrorBo.getTransOrgno());
					SqlParam<M001> dateParams = new FetcherData<>(map, M001.class);
					SqlResult<M001> m001Info = m001Dao.find(dateParams);
					if(m001Info.getRows() != null && m001Info.getRows().size() > 0){
						paymCapitalCheckErrorBo.setTransOrgName(m001Info.getRows().get(0).getOrgname());
					}
				}
			}
		}
		return sqlResult;
	}

	@API(desc = "差错处理", auth = APIAuth.YES,operation = APIOperation.UPDATE)
	public String dealError(SqlParam<PaymCapitalCheckErrorBo> params) throws Exception {
		//获取管理台选中的数据
		String chooseData = (String) params.getParams().get("chooseData");
		JSONArray jsonArray = JSONArray.parseArray(chooseData);
		if (jsonArray.size() <= 0) {
			log.error("所选数组长度为0");
			throw new Exception("所选数组长度为0");
		}
		//差错处理类型
		String errorDealType = "";
		if (params.getModel().getErrorDealType().equals("dealError")){
			errorDealType = ErrorDealType.DEALERROR.getType();
			try {
				List<String> stringList = new ArrayList<>();
				T545ServiceRequest t545ServiceRequest = new T545ServiceRequest();
				t545ServiceRequest.setErrorDealType(errorDealType);
				jsonArray.stream().forEach(item->{
					JSONObject jsonObject = (JSONObject)item;
					if(jsonObject.get("errorSerno") != null){
						stringList.add( jsonObject.get("errorSerno").toString());
					}
				});
				t545ServiceRequest.setErrorSernoList(stringList);
				T545ServiceResponse t545ServiceResponse = t545ServiceDecorator.execute(t545ServiceRequest);
				String rtnCode = t545ServiceResponse.getRtnCode();
				if (rtnCode != null) {
					throw new PromptException("T545处理重发问题：", t545ServiceResponse.getRtnDesc());
				}
			} catch (Exception e) {
				log.error("重发失败"+e.getMessage());
				throw new RuntimeException("重发失败"+e.getMessage());
			}
		}else{
			errorDealType = ErrorDealType.OFFLINE.getType();
			DaoUtil.doTrans(()->{
				jsonArray.stream().forEach(item->{
					try {
						JSONObject updateDate = (JSONObject)item;
						if(Tools.isNotBlank(updateDate.get("errorSerno").toString())){
							updateDate.put("errorStatus","2");
							paymCapitalCheckErrorBoDao.updateErrorStatus(updateDate).getEffect();
						}else{
							throw new RuntimeException("线下处理失败:资金流水号不能为空");
						}
					} catch (Exception e) {
						log.error("线下处理失败"+e.getMessage());
						throw new RuntimeException("线下处理失败"+e.getMessage());
					}
				});
			}, SubDatabase.DATABASE_FINA_CENTER);
		}

		//组装批量失败记录流水号
		List<String> errorSernoList = new ArrayList<>();

		for (int i=0;i<jsonArray.size();i++){
			JSONObject _json = (JSONObject) jsonArray.get(i);
			errorSernoList.add((String) _json.get("errorSerno"));
		}

		//返回页面参数
		Map<String,Object> map1 = new HashMap<>();
		map1.put("length",jsonArray.size());
		return RequestSupport.updateReturnJson(true, "后台"+(errorDealType.equals(ErrorDealType.DEALERROR.getType())?"调账":"线下处理")+"完成，共选择"+jsonArray.size()+"条差错", map1).toString();
	}

	private byte[] changeMapToByte(Map<String,Object> map) throws Exception {

		byte[] bytes = null;
		try {
			bytes = JSON.toJSONString(map, true).getBytes();
		} catch (Exception e) {
			log.error("map到byte[]转换异常");
			throw new Exception("map到byte[]转换异常");
		}

		return bytes;
	}

}
