package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.util.Tools;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.model.M009;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class M009Dao extends ComnDao {

    private Map<String,String> deptInfos = null;


    public int delete(SqlParam<M009> params) throws Exception {
        String deptId = params.getModel().getDeptid();
        if (Tools.isBlank(deptId)) {
            return 0;
        }
        return super.update("DELETE FROM sys_dept WHERE deptid = '" + deptId + "'").getEffect();
    }

    public void update(SqlParam<M009> params) throws Exception {
        M009 newM009 = params.getModel();
        String oldDeptId = newM009.getDeptid();
        List<M009> m009s = super.findRows(M009.class, "SELECT * FROM sys_dept WHERE deptid like '" + oldDeptId + "%'",
                0, null);
        if (CollectionUtils.isEmpty(m009s)) {
            return;
        }

        // 1、判断parentno是否为自己当前的子部门；拆分父部门和子部门
        List<M009> childM009 = new ArrayList<>();
        M009 oldM009 = null;
        for (M009 m009 : m009s) {
            if (m009.getDeptid().equals(newM009.getDeptid())) {
                oldM009 = m009;
            } else {
                if (m009.getDeptno().equals(newM009.getParentdeptno())) {
                    throw new PromptException("不能部门移动到自己的子部门下");
                }
                childM009.add(m009);
            }
        }
        M009 finalCurrentM009 = oldM009;
        doTrans(() -> {
            // 2、保存新的Dept
            super.update("UPDATE sys_dept SET " +
                    " parentdeptno = $S{parentdeptno}, dept_status = $S{deptStatus}, " +
                    " deptname = $S{deptname}, deptlevel = $S{deptlevel}, " +
                    " depttype = $S{depttype}, " +
                    " address = $S{address}, telno = $S{telno}, deptno = $S{deptno} " +
                    " WHERE deptid = $S{deptid}", newM009);
            if (logoChange(newM009, finalCurrentM009)) {
                // 3、计算新的deptid
                M009 newParentM009 = this.get(newM009.getParentdeptno());
                String newParentDeptDeptid = newParentM009.getDeptid();
                String newDeptId = newParentDeptDeptid + newM009.getDeptno() + "_";

                // 4、子部门id及parentno替换
                for (M009 m009 : childM009) {
                    String deptId = m009.getDeptid();
                    m009.setDeptid(deptId.replaceFirst(oldDeptId, newDeptId));
                    m009.setParentdeptno(newM009.getDeptno());
                    super.update("UPDATE sys_dept " +
                            " SET deptid = $S{deptid},parentdeptno = $S{parentdeptno} " +
                            " WHERE deptno = $S{deptno}", m009);
                }

                newM009.setDeptid(newDeptId);
                super.update("UPDATE sys_dept " +
                        " SET deptid = $S{deptid},parentdeptno = $S{parentdeptno} " +
                        " WHERE deptno = $S{deptno}", newM009);
            }
        });
    }

    private boolean logoChange(M009 newM009, M009 oldM009) {
        return !newM009.getParentdeptno().equals(oldM009.getParentdeptno()) ||
                !newM009.getDeptno().equals(oldM009.getDeptno());
    }

    public SqlResult<M009> find(SqlParam<M009> params) throws Exception {
        return super.findRows("SELECT * FROM sys_dept order by dept_status asc", params);
    }

    public SqlResult<M009> findChildren(SqlParam<M009> params) throws Exception {
        String deptId = params.getModel().getDeptid();
        if (Tools.isBlank(deptId)) {
            return null;
        }
        return super.findRows("SELECT * FROM sys_dept WHERE deptid like'" + deptId + "%'", params);
    }

    public M009 get(String deptno) throws Exception {
        Map<String, Object> mapParams = new HashMap<>(1);
        mapParams.put("deptno", deptno);

        FetcherData<M009> params = new FetcherData<>(mapParams, M009.class);
        params.setMakeSql(true);
        SqlResult<M009> deptSqlResult = this.find(params);
        List<M009> rows = deptSqlResult.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return null;
        } else {
            return rows.get(0);
        }
    }

    public int add(SqlParam<M009> params) throws Exception {
        return super.update(
                "INSERT INTO sys_dept " +
                        "(deptid, deptno, deptname, deptlevel, legal_code," +
                        "depttype, parentdeptno, address, contect, telno, dept_status) " +
                        " VALUES " +
                        "($S{deptid},$S{deptno},$S{deptname},$S{deptlevel},$S{legalCode}," +
                        "$S{depttype},$S{parentdeptno},$S{address},$S{contect},$S{telno},$S{deptStatus})",
                params.getModel()).getEffect();
    }

    public SqlResult<M009> isDeptNo(SqlParam<M009> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("select * from  sys_dept where deptno = $S{deptno}",params);
    }

    public SqlResult<M009> isDeptChildren(SqlParam<M009> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("SELECT t.* FROM sys_dept t where parentdeptno  = $S{deptno}",params);
    }

    public SqlResult<M009> isDeptNoNotAin(SqlParam<M009> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("select * from  sys_dept where deptno = $S{deptno} and deptid != $S{deptid}",params);
    }

    /**
     * @Description 无需考虑并发情况，若是存在，则是多次重新对orgInfos重新赋值而已。
     * @Param
     * @return
     * <AUTHOR>
     * @Date 2020/11/30
     **/
    public Map<String,String> getDeptMap(){
        if (deptInfos == null){
            // 查询sys_org信息存入map中。
            try {
                Map<String, Object> map = new HashMap<>();
                SqlParam<M009> params = new FetcherData<>(map, M009.class) ;
                SqlResult<M009> rows = super.findRows("SELECT deptno,deptname FROM sys_dept ", params);
                deptInfos = rows.getRows().stream().collect(Collectors.toMap(M009::getDeptno, M009::getDeptname));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return deptInfos;
    }

    /**
     * 检查部门下是否有用户
     * @param params
     * @return
     */
    public SqlRow selectisUserByDeptId(SqlParam<M009> params)throws  Exception{
        String deptid = params.getModel().getDeptid();
        if(Tools.isBlank(deptid)){
            return null;
        }
        return super.findRow("select count(u.loginname) num from sys_dept d left join sys_user u on d.deptno = u.deptno where deptid = '"+deptid+"' ",params);
    }
}
