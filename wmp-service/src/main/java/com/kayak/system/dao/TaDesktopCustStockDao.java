package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.constants.SystemParamConstants;
import com.kayak.core.util.Tools;
import com.kayak.system.model.M003;
import com.kayak.system.model.TaDesktopCustStock;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class TaDesktopCustStockDao extends ComnDao {

    //todo 修改sql语句 sysdate --> sysdate()

    public String findCustNum(SqlParam<TaDesktopCustStock> params) throws Exception {
        String custStock = "0";
        SqlResult<TaDesktopCustStock> row = super.findRows("select count(1) as cust_stock from cust_info ", SubDatabase.DATABASE_CUST_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            custStock = row.getRows().get(0).getCustStock();
        }
        return custStock;
    }

    public String findCustNumByMonth(SqlParam<TaDesktopCustStock> params) throws Exception {
        String incrByMonth = "0";
        String sql = "select count(1) as incr_by_month\n" +
                "  from cust_info t\n" +
                " where date_format(t.CREATE_TIME, '%Y-%m') = date_format(sysdate(), '%Y-%m') ";
        SqlResult<TaDesktopCustStock> row = super.findRows(sql, SubDatabase.DATABASE_CUST_CENTER,params);
        if (row != null && row.getRows().size() >= 1) {
            incrByMonth = row.getRows().get(0).getIncrByMonth();
        }
        return incrByMonth;
    }

    public List<SqlRow> findCustNumStat(SqlParam<TaDesktopCustStock> params,int moduleId) throws Exception {
        String tableName = "";
        if(moduleId == SubDatabase.DATABASE_FUND_CENTER){
            tableName = "fund_cust_num_stat";
        }else if(moduleId == SubDatabase.DATABASE_FINA_CENTER){
            tableName = "fina_cust_num_stat";
        }
        String sql = "  select sum(t.public_cust_num) as custNum, t.stat_date as statDate\n" +
                "    from "+tableName+" t\n" +
                "   where t.stat_date between date_format(sysdate() - 30, '%Y-%m-%d') and\n" +
                "         date_format(sysdate(), '%Y-%m-%d')\n" +
                "   group by t.stat_date\n" +
                "   order by t.stat_date\n";
        return super.findRows(sql, moduleId);
    }
}
