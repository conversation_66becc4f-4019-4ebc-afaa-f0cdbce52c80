package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.system.model.M007;
import com.kayak.system.model.AnnounceInfo;
import com.kayak.system.model.AnnounceRole;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M007Dao extends ComnDao {

    public SqlResult<M007> find(SqlParam<M007> params) throws Exception {
        return super.findRows("SELECT a.*,u.username as createuser_name FROM sys_announce a" +
                " LEFT JOIN sys_user u ON u.userid = a.createuserid ", params);
    }

    public M007 get(String annid) throws Exception {
        return super.findRow(M007.class, "SELECT * FROM sys_announce a WHERE annid = $S{annid}",0, annid);
    }

    public SqlResult<M007> show(SqlParam<M007> params) throws Exception {

        StringBuffer sql=new StringBuffer("SELECT a.* FROM sys_announce a " +
                " JOIN sys_announce_role r ON a.annid = r.annid where 1=1 " );
        if(Tools.isNotBlank(params.getModel().getEffectiveDate())){
            sql.append(" AND effective_date <= $S{effectiveDate} ");
        }
        if(Tools.isNotBlank(params.getModel().getInvalidDate())){
            sql.append(" AND invalid_date >= $S{invalidDate} ");
        }
        String [] roldids = params.getModel().getRoleid().split(",");
        String inStr = " AND r.roleid in (";
        for (int i = 0; i < roldids.length; i++){
            if (i == roldids.length - 1){
                inStr += "'"+roldids[i]+"')";
            }else {
                inStr += "'"+roldids[i]+"',";
            }
        }
        sql.append(inStr);
        return super.findRows(sql.toString(), params);
    }

    public void add(SqlParam<M007> param) throws Exception {
        doTrans(() -> {
            M007 model = param.getModel();
            String id = super.update(
                    "INSERT INTO sys_announce (annid, title, content, CRT_DATE, CRT_TIME" +
                            ", createuserid, editdate, edittime, edituserid, annfilepath" +
                            ", annfilename, annfilecode, effective_date, invalid_date,legal_code) " +
                            " VALUES ($AUTOIDS{annid}, $S{title}, $S{content}, $S{crtDate}, $S{crtTime}" +
                            ", $S{createuserid}, $S{editdate}, $S{edittime}, $S{edituserid}" +
                            ", $S{annfilepath}, $S{annfilename}, $S{annfilecode}, $S{effectiveDate},$S{invalidDate},$S{legalCode})", model).getAutoId();
            List<String> roleIds = model.getRoleIds();

            bulkInsertAnnounceRole(id, roleIds);
        });
    }


    public void edit(SqlParam<M007> param) throws Exception {
        doTrans(() -> {
            M007 model = param.getModel();
            super.update(
                    "UPDATE sys_announce SET title = $S{title}, content = $S{content}, editdate = $S{editdate}" +
                            ", edittime = $S{edittime}, edituserid = $S{edituserid}, annfilepath = $S{annfilepath}" +
                            ", annfilename = $S{annfilename}, annfilecode = $S{annfilecode}" +
                            ", effective_date = $S{effectiveDate}, invalid_date = $S{invalidDate} WHERE annid = $S{annid}", model);

            super.update(
                    "DELETE FROM sys_announce_role WHERE annid = $S{annid}", model.getAnnid());

            bulkInsertAnnounceRole(model.getAnnid(), model.getRoleIds());
        });
    }

    public int delete(String annid) throws Exception {
        return super.update("DELETE FROM sys_announce WHERE annid = $S{annid}", annid).getEffect();
    }

    /**
     * 批量插入公告-角色关系表
     * @param annid
     * @param roleIds
     * @throws Exception
     */
    private void bulkInsertAnnounceRole(String annid, List<String> roleIds) throws Exception {
        if (roleIds == null || roleIds.size() == 0) {
            return;
        }

        for (String roleId : roleIds) {
            super.update("INSERT INTO sys_announce_role (annid, roleid) " +
                    "VALUES ($S{annid}, $S{roleid}) ", new AnnounceRole(annid, roleId));
        }
    }
}
