package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.fina.global.utils.Tools;
import com.kayak.system.model.TaDesktopProdStock;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TaDesktopProdNumDao extends ComnDao {

    //todo 修改sql语句 sysdate --> sysdate()

    /**
     * 理财信息查询
     * */
    public String findProdNumForYear() throws Exception {
        String prodNum = "0";
        String sql = " select sum(t.prod_num) as prodNum\n" +
                "    from fina_prod_num_stat t\n" +
                "   where date_format(str_to_date(t.stat_date, '%Y-%m-%d'), '%Y') =\n" +
                "         date_format(sysdate(), '%Y') and system_no = 'fina' ";
        List<SqlRow> row = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER);
        if (row != null && row.size() >= 1 && row.get(0).get("prodnum") != null) {
            prodNum = row.get(0).get("prodnum").toString();
        }
        return prodNum;
    }

    public String findProdNumForMonth() throws Exception {
        String prodNum = "0";
        String sql = "select sum(t.prod_num) as prodNum\n" +
                "     from fina_prod_num_stat t\n" +
                "    where date_format(str_to_date(t.stat_date, '%Y-%m-%d'), '%Y') =\n" +
                "          date_format(sysdate(), '%Y') and system_no = 'fina' ";
        List<SqlRow> row = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER);
        if (row != null && row.size() >= 1 && row.get(0).get("prodnum") != null) {
            prodNum = row.get(0).get("prodnum").toString();
        }
        return prodNum;
    }

    public List<SqlRow> findFindCustVol() throws Exception {
        String sql = "select t.prod_code,sum(t.total_vol) as total_vol from fina_cust_vol t group by t.prod_code ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER);
    }

    /**
     * 基金信息查询
     * */
    public String findFundProdNumForYear() throws Exception {
        String prodNum = "0";
        String sql = " select sum(t.prod_num) as prodNum\n" +
                "    from fund_prod_num_stat t\n" +
                "   where date_format(str_to_date(t.stat_date, '%Y-%m-%d'), '%Y') =\n" +
                "         date_format(sysdate(), '%Y') and system_no = 'fund' ";
        List<SqlRow> row = super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER);
        if (row != null && row.size() >= 1 && row.get(0).get("prodnum") != null) {
            prodNum = row.get(0).get("prodnum").toString();
        }
        return prodNum;
    }

    public String findFundProdNumForMonth() throws Exception {
        String prodNum = "0";
        String sql = "select sum(t.prod_num) as prodNum\n" +
                "     from fund_prod_num_stat t\n" +
                "    where date_format(str_to_date(t.stat_date, '%Y-%m-%d'), '%Y-%m') =\n" +
                "          date_format(sysdate(), '%Y-%m') and system_no = 'fund' ";
        List<SqlRow> row = super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER);
        if (row != null && row.size() >= 1 && row.get(0).get("prodnum") != null) {
            prodNum = row.get(0).get("prodnum").toString();
        }
        return prodNum;
    }

    public List<SqlRow> findFundCustVol() throws Exception {
        String sql = "select t.prod_code,sum(t.total_vol) as total_vol from fund_cust_vol t group by t.prod_code ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER);
    }

}
