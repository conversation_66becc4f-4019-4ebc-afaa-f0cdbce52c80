package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.system.model.ServerModel;
import org.springframework.stereotype.Repository;

@Repository
public class ServerModelDao extends ComnDao {

    public ServerModel get(String modelName) throws Exception {
        return super.findRow(ServerModel.class, "SELECT * FROM sys_server_model WHERE model_name = $S{modelName}", 0, modelName);
    }

}
