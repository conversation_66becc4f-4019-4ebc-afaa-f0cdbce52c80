package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.system.model.M002;
import com.kayak.system.model.M002Item;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class M002Dao extends ComnDao {

    public SqlResult<M002> findDict(SqlParam<M002> params) throws Exception {
        return super.findRows("SELECT * FROM sys_dict", params);
    }

    public int editDict(SqlParam<M002> params) throws Exception {
        return super.update(
                "UPDATE sys_dict SET dictname = $S{dictname} WHERE dict = $S{dict}", params.getModel()).getEffect();
    }

    public int addDict(SqlParam<M002> params) throws Exception {
        return super.update(
                "INSERT INTO sys_dict (dict, dictname,groupdict) " +
                        " VALUES ($S{dict},$S{dictname},$S{groupdict})", params.getModel()).getEffect();
    }

    public void deleteDict(SqlParam<M002> params) throws Exception {
        doTrans(() -> {
            super.update(
                    "DELETE FROM sys_dict_item WHERE dict = $S{dict}", params.getModel()).getEffect();
            super.update(
                    "DELETE FROM sys_dict WHERE dict = $S{dict}", params.getModel()).getEffect();
        });

    }
    public SqlResult<M002> findDictOnly(SqlParam<M002> params) throws Exception {
        return super.findRows("SELECT * FROM sys_dict where dict = $S{dict}", params);
    }

    public SqlResult<M002Item> findDictItem(SqlParam<M002Item> params) throws Exception {
        return super.findRows("SELECT * FROM sys_dict_item WHERE dict = $S{dict} order by itemorder", params);
    }
    public SqlResult<M002Item> findDictItem2(SqlParam<M002Item> params) throws Exception {
        String sql="select i2.* from sys_dict_item i,sys_dict s1,sys_dict_item i2 where i.itemval=s1.dictname and s1.dict=i2.dict and i.dict=$S{dict} and i.itemkey=$S{itemkey}";
        return super.findRows(sql, params);
    }
    public List<SqlRow> findDictItem3(Map<String,Object> params) throws Exception {
        String sql="SELECT si.*,si2.itemkey as itemkey2 ,si2.itemval as itemval2 FROM sys_dict_item si left JOIN sys_dict sd on si.itemval=sd.dictname left JOIN sys_dict_item si2 on sd.dict=si2.dict  where si.dict='quota_dim_type'";
        return super.findRows(sql,params);
    }

    public SqlResult<M002Item> findDictItemOnly(SqlParam<M002Item> params) throws Exception {
        return super.findRows("SELECT * FROM sys_dict_item WHERE dict = $S{dict} and itemkey= $S{itemkey}", params);
    }

    public int addDictItem(SqlParam<M002Item> params) throws Exception {
        return super.update(
                "INSERT INTO sys_dict_item (dict, itemkey, itemval, itemrender, itemorder) " +
                        "VALUES ($S{dict},$S{itemkey},$S{itemval},$S{itemrender},$S{itemorder})",
                params.getModel()).getEffect();
    }

    public int editDictItem(SqlParam<M002Item> params) throws Exception {
        return super.update(
                "UPDATE sys_dict_item SET itemval = $S{itemval}, itemrender = $S{itemrender}, itemorder = $S{itemorder} " +
                        "WHERE dict = $S{dict} AND itemkey = $S{itemkey}", params.getModel()).getEffect();
    }

    public int deleteDictItem(SqlParam<M002Item> params) throws Exception {
        return super.update(
                "DELETE FROM sys_dict_item WHERE dict = $S{dict} AND itemkey = $S{itemkey}",
                params.getModel()).getEffect();
    }
}
