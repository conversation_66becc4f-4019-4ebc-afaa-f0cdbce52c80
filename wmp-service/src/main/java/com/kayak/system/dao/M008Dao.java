package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.model.M008;
import org.springframework.stereotype.Repository;

@Repository
public class M008Dao extends ComnDao {


    public SqlResult<M008> find(SqlParam<M008> params) throws Exception {
        return super.findRows("SELECT t.id, t.server_desc, t.method_desc, t.submit_old_data, t.submit_data, t.result, t.error_msg, t.operation_date, t.operation_time, t2.username FROM sys_operation_log t LEFT JOIN sys_user t2 ON t.userid = t2.userid ", params);
    }
}
