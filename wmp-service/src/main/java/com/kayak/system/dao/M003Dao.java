package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.base.dao.util.DaoUtil;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.DbOfModuleidUtils;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.*;
import com.kayak.core.system.constants.SystemParamConstants;
import com.kayak.core.util.Tools;
import com.kayak.system.model.M003;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class M003Dao extends ComnDao {

    public SqlResult<M003> find(SqlParam<M003> params) throws Exception {
        return super.findRows("SELECT * FROM sys_param ", params);
    }

    public void update(List<M003> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (M003 p : params) {
                super.update("UPDATE sys_param SET paravalue = $S{paravalue} WHERE paraid = $S{paraid}", p);
            }
        });
    }

    //gx
    public SqlResult<M003> findSysParams(SqlParam<M003> params) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder("SELECT " +
                " p.moduleid, " +
                " p.paraid, " +
                " p.paravalue, " +
                " p.paraname, " +
                " p.groupparaid, " +
                " p.dict, " +
                " p.functype, " +
                " p.confoption, " +
                " p.isdisplay, " +
                " p.isedit, " +
                " i.itemval as dict_value " +
                " FROM SYS_PARAM p " +
                " LEFT JOIN sys_dict_item i ON i.dict = p.dict AND i.itemkey = p.paravalue " +
                " WHERE isdisplay = '" + SystemParamConstants.SHOW + "'  ");

        M003 model = params.getModel();


        if (Tools.isNotBlank(model.getParavalue())) {
            sqlBuilder.append(" AND ( p.dict IS NOT NULL AND p.paravalue LIKE '%$U{paravalue}%' or i.itemval LIKE '%$U{paravalue}%') ");
        }

        if (Tools.isNotBlank(model.getParaname())) {
            sqlBuilder.append(" AND p.paraname LIKE '%$U{paraname}%' ");
        }

        sqlBuilder.append(" order by PARAID ");

        return super.findRows(sqlBuilder.toString(), params);
    }

    //添加
    public UpdateResult addSysParam(SqlParam<M003> params) throws Exception {
        return super.update("INSERT INTO SYS_PARAM(moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay) VALUES($S{moduleid},$S{paraid},$S{paravalue},$S{paraname},$S{groupparaid},$S{dict},$S{functype},$S{confoption},$S{isdisplay})", params.getModel());
    }

    //修改
    public UpdateResult updateSysParam(SqlParam<M003> params) throws Exception {
        return super.update("UPDATE SYS_PARAM SET paravalue=$S{paravalue} ,paraname=$S{paraname}   WHERE  moduleid=$S{moduleid} and paraid=$S{paraid} ", DbOfModuleidUtils.selectDbByModuleid(params.getModel().getModuleid()), params.getModel());
    }

    //自动执行设置
    public void updateSysParamAutoExec(SqlParam<M003> params) throws Exception {
        //1.查询出当前系统工作日
        M003 m003 = findOne(params);
        if(m003 == null){
            throw new PromptException("未初始化系统工作日参数");
        }
        //2.设置当前系统工作日下为自动执行
        DaoUtil.doTrans(() -> {
            super.update("UPDATE SYS_PARAM SET paravalue=$S{paravalue} WHERE  moduleid=$S{moduleid} and paraid=$S{paraid} ", DbOfModuleidUtils.selectDbByModuleid(params.getModel().getModuleid()), params.getModel());
            super.update("UPDATE WMP_BATCH_TASK_EXEC SET AUTO_EXEC = $S{paravalue} WHERE moduleid=$S{moduleid} ", DbOfModuleidUtils.selectDbByModuleid(params.getModel().getModuleid()), params.getModel());
        }, DbOfModuleidUtils.selectDbByModuleid(params.getModel().getModuleid()));
    }

    //删除
    public UpdateResult deleteSysParam(SqlParam<M003> params) throws Exception {
        return super.update("DELETE FROM SYS_PARAM WHERE moduleid=$S{moduleid} and paraid=$S{paraid} ",
                params.getModel());
    }

    /**
     * 查询某个系统参数
     *
     * @param params
     * @return
     * @throws Exception
     */
    public M003 findOne(SqlParam<M003> params) throws Exception {
        // oracle 不支持limit 1
        String sql = "SELECT paravalue ,paraname ,paraid ,moduleid ,isdisplay ,groupparaid ,graphql ,functype ,fieldtype ,execaction ,dict ,confoption FROM sys_param";
        SqlResult<M003> row = super.findRows(sql,  DbOfModuleidUtils.selectDbByModuleid(params.getModel().getModuleid()), params);
        if (row != null && row.getRows().size() >= 1) {
            return row.getRows().get(0);
        }
        return null;
    }

    public M003 getCurrentWorkday(SqlParam<M003> params) throws Exception {
        // oracle 不支持limit 1
        String sql =  "select t.paravalue as paravalue from sys_param t where t.paraid='a0000002' and t.paraname='当前系统工作日'";
        SqlResult<M003> row = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER, params);
        if (row != null && row.getRows().size() >0) {
            return row.getRows().get(0);
        }
        return null;
    }
    public List<SqlRow> findParamVal(String val) throws Exception {
        return super.findRows("SELECT * FROM sys_param WHERE paraid ='" +val +"'");
    }

}
