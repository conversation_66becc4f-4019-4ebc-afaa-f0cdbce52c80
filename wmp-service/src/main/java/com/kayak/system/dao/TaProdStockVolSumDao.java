package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.system.model.ServerMethod;
import com.kayak.system.model.TaProdStockVolSum;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TaProdStockVolSumDao extends ComnDao {


    public SqlResult<TaProdStockVolSum> findFina(SqlParam<TaProdStockVolSum> params) throws Exception {
        String sql = "select *\n" +
                "  from (select t.prod_code, sum(t.total_vol) as total_vol\n" +
                "          from fina_cust_vol t\n" +
                "         group by t.prod_code\n" +
                "         order by sum(t.total_vol) desc) t\n" +
                " limit 6 \n";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }

    public SqlResult<TaProdStockVolSum> findFund(SqlParam<TaProdStockVolSum> params) throws Exception {
        String sql = "select *\n" +
                "  from (select t.prod_code, sum(t.total_vol) as total_vol\n" +
                "          from fund_cust_vol t\n" +
                "         group by t.prod_code\n" +
                "         order by sum(t.total_vol) desc) t\n" +
                " limit 6 \n";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);
    }


}
