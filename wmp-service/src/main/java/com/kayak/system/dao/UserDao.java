package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.model.User;
import org.springframework.stereotype.Repository;

@Repository
public class UserDao extends ComnDao {

	public SqlResult<User> findUserOrgIdByOrgNo(SqlParam<User> params) throws Exception {
		String orgno = params.getModel().getOrgno();
		return super.findRows("SELECT orgid FROM sys_org WHERE orgno = '" + orgno + "'", params);
	}

	public SqlResult<User> findUsers(SqlParam<User> params) throws Exception {
		return super.findRows("SELECT u.*,o.orgname FROM sys_user u LEFT JOIN sys_org o ON u.orgno = o.orgno", params);
	}

	public int addUser(SqlParam<User> params) throws Exception {
		return super.update(
				"INSERT INTO sys_user(userid,loginname,passwd,username,orgno,mobileno,pwdsetdate) VALUES($AUTOIDS{userid},$S{loginname},$S{passwd},$S{username},$S{orgno},$S{mobileno},$S{pwdsetdate})",
				params.getModel()).getEffect();
	}

	public int updateUser(SqlParam<User> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET username=$S{username},passwd=$S{passwd},orgno=$S{orgno},mobileno=$S{mobileno} WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public int stopUse(SqlParam<User> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET userstatus='D' WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public int recoverUse(SqlParam<User> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET userstatus='N' WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public int resetPwd(SqlParam<User> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET passwd = $S{passwd},pwdsetdate=$S{pwdsetdate} WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public boolean checkOldPwd(SqlParam<User> params)throws Exception  {
		params.setMakeSql(false);
		SqlResult<User> result = super.findRows("select * from  sys_user where userid = $S{userid} and passwd = $S{oldPwd} ",params);
		return result.getRows().size()>0? false: true;
	}
}
