package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.system.model.M006;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class M006Dao extends ComnDao {

	/*public SqlResult<User> findUserOrgIdByOrgNo(SqlParam<User> params) throws Exception {
		String orgno = params.getModel().getOrgno();
		return super.findRows("SELECT orgid FROM sys_org WHERE orgno = '" + orgno + "'", params);
	}*/

	/**
	 * 用户信息查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M006> findUsers(SqlParam<M006> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("SELECT u.userid, u.loginname,u.passwd,u.username,u.orgno,u.deptno,u.userstatus," +
				"u.idtype,u.idno,u.sex,u.mobileno,u.officeno,u.homeno,u.faxno,u.email,u.postcode,u.address," +
				"u.modifydate,u.pwderrtimes,u.pwderrlockdt,u.pwdsetdate,u.lastlogintime,u.lastloginstation,u.legal_code," +
				"d.deptname,o.orgname FROM sys_user u LEFT JOIN sys_dept d ON u.deptno = d.deptno LEFT JOIN sys_org o ON u.orgno = o.orgno " , params);
	}



	//添加用户
	public int addUser(SqlParam<M006> params) throws Exception {
		return super.update(
				"INSERT INTO sys_user(userid,loginname,passwd,username,orgno,mobileno,deptno,sex,email,userstatus,pwdsetdate,legal_code) VALUES($AUTOIDS{userid},$S{loginname},$S{passwd},$S{username},$S{orgno},$S{mobileno},$S{deptno},$S{sex},$S{email},$S{userstatus},$S{pwdsetdate},$S{legalCode})",
				params.getModel()).getEffect();
	}

	public int updateUser(SqlParam<M006> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET username=$S{username},passwd=$S{passwd},orgno=$S{orgno},deptno=$S{deptno},sex=$S{sex},email=$S{email},mobileno=$S{mobileno},legal_code=$S{legalCode} WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public int stopUse(SqlParam<M006> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET userstatus='D' WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public int recoverUse(SqlParam<M006> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET userstatus='N' WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public int deleteUse(SqlParam<M006> params) throws Exception {
		return super.update(
				"delete from sys_user WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public int resetPwd(SqlParam<M006> params) throws Exception {
		return super.update(
				"UPDATE sys_user SET passwd = $S{passwd},pwdsetdate=$S{pwdsetdate} WHERE userid=$S{userid}",
				params.getModel()).getEffect();
	}

	public SqlResult<M006> isBlankLoginname(SqlParam<M006> params) throws Exception {
		params.setMakeSql(false);
		return super.findRows("select userid, loginname,passwd,username,orgno,deptno,userstatus, idtype,idno,sex,mobileno,officeno,homeno,faxno,email,postcode,address,modifydate,pwderrtimes,pwderrlockdt,pwdsetdate,lastlogintime,lastloginstation,legal_code from  sys_user where loginname = $S{loginname}",params);
	}

	/**
	 * <AUTHOR>
	 * @Description 撤销机构用户的orgno改成合并机构
	 * @Date 2022/3/24
	 * @Param [params]
	 * @return com.kayak.core.sql.UpdateResult
	 **/
	public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
		return super.update("UPDATE sys_user SET ORGNO = $S{mergeOrgno}  WHERE ORGNO = $S{removeOrgno}",params);
	}
}
