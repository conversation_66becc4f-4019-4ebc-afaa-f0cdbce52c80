package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.util.Tools;
import com.kayak.system.model.Menu;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

import static com.kayak.core.system.constants.UserConstants.SUPER_ROLE_ID;

@Repository
public class MenuDao extends ComnDao {

    public List<Menu> findByRoleId(String roleId) throws Exception {
        if (SUPER_ROLE_ID.equals(roleId)) {
            return super.findRows(Menu.class, "SELECT * FROM sys_menu WHERE status = 'N' ORDER BY loadorder", 0, null);
        } else {
            return super.findRows(Menu.class, "SELECT m.* FROM sys_menu m " +
                    " JOIN sys_role_menu rm ON m.menuid = rm.menuid " +
                    " WHERE rm.roleid = '" + roleId + "' AND m.status = 'N' ORDER BY loadorder", 0, null);
        }
    }

    public List<Menu> findByRoleIds(String roleIds) throws Exception {
        if (Tools.isBlank(roleIds)) {
            return Collections.emptyList();
        }
        if (roleIds.contains(SUPER_ROLE_ID)){
            return super.findRows(Menu.class, "SELECT * FROM sys_menu WHERE status = 'N' ORDER BY loadorder", 0, null);
        }
        String[] roleIdArr = roleIds.split(",");
        String inStr = "(";
        for (String roleId : roleIdArr){
            inStr += "'"+roleId+"',";
        }
        inStr = inStr.substring(0,inStr.length()-1) + ")";
        return super.findRows(Menu.class, "SELECT m.* FROM sys_menu m " +
                " JOIN sys_role_menu rm ON m.menuid = rm.menuid " +
                " WHERE rm.roleid in " + inStr + " AND m.status = 'N' ORDER BY loadorder", 0, null);

    }
}
