package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.util.Tools;
import com.kayak.system.model.M004;
import com.kayak.system.model.WorkdayItem;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class M004Dao extends ComnDao {

    public SqlResult<M004> findProgram(SqlParam<M004> params) throws Exception {

//        List<String> list = params.getModel().getSystemNoo();
//        String s = "";
        String sql = "";
//        if (list != null) {
//            for (int i = 0; i < list.size(); i++) {
//                if (list.size() == 1) {
//                    s += "'" + list.get(i) + "'";
//                } else {
//                    if (i == list.size() - 1) {
//                        s += "'" + list.get(i) + "'";
//                    } else {
//                        s += "'" + list.get(i) + "',";
//                    }
//                }
//            }
//            sql = "SELECT * FROM sys_workday_pgm where system_no in ("+s+") ";
//        } else {
//            sql = "SELECT * FROM sys_workday_pgm";
//        }
        sql = "SELECT * FROM sys_workday_pgm";
        return super.findRows(sql, params);
    }

    public int addProgram(SqlParam<M004> params) throws Exception {
        return super.update("INSERT INTO sys_workday_pgm " +
                " (pgmname, pgmno, pgmtype,system_no, legal_code,remark) VALUES " +
                " ($S{pgmname}, $S{pgmno}, $S{pgmtype},  $S{systemNo}, $S{legalCode}, $S{remark})", params.getModel()).getEffect();
    }

    public void delProgram(SqlParam<M004> params) throws Exception {
        doTrans(() -> {
            super.update(
                    "DELETE FROM sys_workday_pgm WHERE pgmno = $S{pgmno}", params.getModel());
            super.update(
                    "DELETE FROM sys_workday_set WHERE pgmno = $S{pgmno}", params.getModel());
        });
    }

    public int updateProgram(SqlParam<M004> params) throws Exception {
        return super.update("UPDATE sys_workday_pgm " +
                " SET pgmname = $S{pgmname}, pgmtype = $S{pgmtype}, remark = $S{remark}" +
                " WHERE pgmno = $S{pgmno} ", params.getModel()).getEffect();
    }

    public M004 getProgram(SqlParam<M004> params) throws Exception {
        return super.findRow(M004.class, "SELECT * FROM sys_workday_pgm WHERE pgmno = $S{pgmno}",
                0, params);
    }

    public List<WorkdayItem> find(WorkdayItem workdayItem) throws Exception {
        return super.findRows(WorkdayItem.class, "SELECT * FROM sys_workday_set WHERE pgmno = $S{pgmno} ",
                0, workdayItem);
    }

    public List<M004> find(M004 workdayItem) throws Exception {
        return super.findRows(M004.class, "SELECT * FROM sys_workday_set WHERE pgmno = $S{pgmno} ",
                0, workdayItem);
    }

    public SqlResult<WorkdayItem> find(SqlParam<WorkdayItem> params) throws Exception {
        String workday = params.getModel().getWorkday();
        String sql;
        if (Tools.isBlank(workday)) {
            sql = "SELECT * FROM sys_workday_set WHERE pgmno = $S{pgmno}";
        } else {
            sql = "SELECT * FROM sys_workday_set WHERE pgmno = $S{pgmno} AND workday LIKE '" + workday + "%'";
        }
        return super.findRows(sql, params);
    }

    public void saveWorkdayItems(String pgmno, String year, List<WorkdayItem> newData)
            throws Exception {
        if (Tools.isBlank(pgmno) || Tools.isBlank(year)) {
            return;
        }
        doTrans(()->{
            super.update("DELETE FROM sys_workday_set " +
                    " WHERE pgmno = '" + pgmno + "' AND workday LIKE '" + year + "%'");
            if (CollectionUtils.isEmpty(newData)) {
                return;
            }

            for (WorkdayItem workdayItem : newData) {
                super.update("INSERT INTO sys_workday_set (pgmno, workday) VALUES " +
                        " ($S{pgmno}, $S{workday})", workdayItem);
            }
        });
    }


    /**
     * 检查同一类型的工作日
     * @param params
     * @return
     */
    public SqlRow checkPgmno(SqlParam<M004> params)throws  Exception{
        //String pgmtype = params.getModel().getPgmtype();
        String pgmtype = params.getModel().getPgmtype();
        String pgmno = params.getModel().getPgmno();
        if(Tools.isBlank(pgmtype) && Tools.isBlank(pgmno)){
            return null;
        }
        return super.findRow("select count(1) nums from sys_workday_pgm where pgmtype = '"+pgmtype+"' and pgmno = '"+pgmno+"'",params);
    }

    public List<SqlRow> findPgmList(SqlParam<M004> params) throws Exception {
        String sql = "SELECT pgmno value, pgmname label FROM sys_workday_pgm";
        return super.findRows(sql, params.getModel());
    }

    /**
     * 检查同一类型的工作日
     * @param params
     * @return
     */
    public SqlRow checkWorkDayType(SqlParam<M004> params)throws  Exception{
        String pgmtype = params.getModel().getPgmtype();
        String systemNo = params.getModel().getSystemNo();

        return super.findRow("select count(1) nums from sys_workday_pgm where SYSTEM_NO = '"+systemNo+"' and pgmtype = '"+pgmtype+"' ",params);
    }

}
