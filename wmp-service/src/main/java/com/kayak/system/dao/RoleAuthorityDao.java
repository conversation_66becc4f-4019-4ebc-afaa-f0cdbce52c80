package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.model.M005;
import com.kayak.system.model.RoleMenu;
import com.kayak.system.model.RoleServer;
import org.springframework.stereotype.Repository;

import java.util.Set;

@Repository
public class RoleAuthorityDao extends ComnDao {

    public void save(String roleId,
                     Set<RoleMenu> roleMenus,
                     Set<RoleServer> roleServers) throws Exception {
        doTrans(() -> {
            super.update("DELETE FROM sys_role_menu WHERE roleid = '" + roleId +  "'");
            for (RoleMenu roleMenu : roleMenus) {
                super.update("INSERT INTO sys_role_menu (menuid, moduleid, roleid) " +
                        " VALUES ($S{menuid},$S{moduleid},$S{roleid}) ", roleMenu);
            }

            super.update("DELETE FROM sys_role_server WHERE roleid = '" + roleId +  "'");
            for (RoleServer roleServer : roleServers) {
                super.update("INSERT INTO sys_role_server (roleid, server) " +
                        " VALUES ($S{roleid},$S{server}) ", roleServer);
            }

        });
    }

    public void saveSon(String roleId,
                     Set<RoleMenu> roleMenus,
                     Set<RoleServer> roleServers) throws Exception {
        doTrans(() -> {
            super.update("DELETE FROM sys_role_menu WHERE roleid = '" + roleId +  "'");
            for (RoleMenu roleMenu : roleMenus) {
                super.update("INSERT INTO sys_role_menu (menuid, moduleid, roleid) " +
                        " VALUES ($S{menuid},$S{moduleid},'"+roleId+"') ", roleMenu);
            }

            super.update("DELETE FROM sys_role_server WHERE roleid = '" + roleId +  "'");
            for (RoleServer roleServer : roleServers) {
                super.update("INSERT INTO sys_role_server (roleid, server) " +
                        " VALUES ('"+roleId+"',$S{server}) ", roleServer);
            }

        });
    }


    /**
     * 根据父角色查询子角色
     * @param roleid
     * @return
     * @throws Exception
     */
    public SqlResult<M005> selectroleidById(String roleid)throws Exception{
        return super.findRows("select roleid FROM sys_role WHERE PARENTROLEID = '" + roleid +  "'",0,null);
    }
}
