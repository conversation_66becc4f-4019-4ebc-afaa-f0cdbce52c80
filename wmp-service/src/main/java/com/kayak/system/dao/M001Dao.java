package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import com.kayak.graphql.model.FetcherData;
import com.kayak.system.model.M001;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class M001Dao extends ComnDao {

    private Map<String, String> orgInfos = null;

    public int delete(SqlParam<M001> params) throws Exception {
        String orgNo = params.getModel().getOrgno();
        String legalCode = params.getModel().getLegalCode();
        if (Tools.isBlank(orgNo) && Tools.isBlank(legalCode)) {
            return 0;
        }
        return super.update("DELETE FROM sys_org WHERE orgno = '" + orgNo + "' and legal_code = '" + legalCode + "'").getEffect();
    }

    public void update(SqlParam<M001> params) throws Exception {
        M001 newM001 = params.getModel();
        if ("00000".equals((String) newM001.getOrgno())) {
            newM001.setParentorgno("ROOT");
        }
        String oldOrgId = newM001.getOrgid();
        List<M001> m001s = super.findRows(M001.class, "SELECT * FROM sys_org WHERE orgid like '" + oldOrgId + "%'",
                0, null);
        if (CollectionUtils.isEmpty(m001s)) {
            return;
        }

        // 1、判断parentno是否为自己当前的子机构；拆分父机构和子机构
        List<M001> childM001 = new ArrayList<>();
        M001 oldM001 = null;
        for (M001 m001 : m001s) {
            if (m001.getOrgid().equals(newM001.getOrgid())) {
                oldM001 = m001;
            } else {
                if (m001.getOrgno().equals(newM001.getParentorgno())) {
                    throw new PromptException("不能将机构移动到自己的子机构下");
                }
                childM001.add(m001);
            }
        }
        M001 finalCurrentM001 = oldM001;
        doTrans(() -> {
            // 2、保存新的Org
            super.update("UPDATE sys_org SET " +
                    " parentorgno = $S{parentorgno}, org_status = $S{orgStatus}, " +
                    " orgname = $S{orgname}, orglevel = $S{orglevel}, " +
                    " orgtype = $S{orgtype}," +
                    " address = $S{address}, telno = $S{telno}, orgno = $S{orgno}, credential_flag = $S{credentialFlag}, credential_num = $S{credentialNum} " +
                    " WHERE orgid = $S{orgid}", newM001);
            if (logoChange(newM001, finalCurrentM001)) {
                // 3、计算新的orgid
                M001 newParentM001 = this.get(newM001.getParentorgno());
                String newParentOrgOrgid = newParentM001.getOrgid();
                String newOrgId = newParentOrgOrgid + newM001.getOrgno() + "_";

                // 4、子机构id及parentno替换
                for (M001 m001 : childM001) {
                    String orgId = m001.getOrgid();
                    m001.setOrgid(orgId.replaceFirst(oldOrgId, newOrgId));
                    m001.setParentorgno(newM001.getOrgno());
                    super.update("UPDATE sys_org " +
                            " SET orgid = $S{orgid},parentorgno = $S{parentorgno} " +
                            " WHERE orgno = $S{orgno}", m001);
                }

                newM001.setOrgid(newOrgId);
                super.update("UPDATE sys_org " +
                        " SET orgid = $S{orgid},parentorgno = $S{parentorgno} " +
                        " WHERE orgno = $S{orgno}", newM001);
            }
        });
    }

    private boolean logoChange(M001 newM001, M001 oldM001) {
        return !newM001.getParentorgno().equals(oldM001.getParentorgno()) ||
                !newM001.getOrgno().equals(oldM001.getOrgno());
    }

    public SqlResult<M001> find(SqlParam<M001> params) throws Exception {
        params.setMakeSql(true);
        StringBuffer sql = new StringBuffer("SELECT * FROM sys_org where 1=1 ");
        if (Tools.isNotBlank(params.getModel().getOrglevel())) {
            sql.append(" and orglevel in (" + params.getModel().getOrglevel() + ") ");
            params.getModel().setOrglevel(null);
        }
        return super.findRows(sql.toString(), params);
    }

    public SqlResult<M001> find2(SqlParam<M001> params) throws Exception {
        return super.findRows("select * from sys_org", params);
    }

    public List<SqlRow> findChildren(M001 params) throws Exception {
        return super.findRows("select * from sys_org s where s.parentorgno=$S{orgno}", params);
    }

    public SqlResult<M001> findChildren(SqlParam<M001> params) throws Exception {
        String orgId = params.getModel().getOrgid();
        if (Tools.isBlank(orgId)) {
            return null;
        }
        return super.findRows("SELECT * FROM sys_org WHERE orgid like'" + orgId + "%'", params);
    }

    public List<M001> queryChildren(String orgno) throws Exception {
        return super.findRows(M001.class, "SELECT * FROM sys_org WHERE PARENTORGNO ='" + orgno + "'", SubDatabase.DATABASE_SYS_CENTER, null);
    }

    public List<M001> queryAllChildren(List<M001> list, String orgno) throws Exception {
        List<M001> m001List = this.queryChildren(orgno);
        if (m001List != null && m001List.size() > 0) {
            for (M001 m001 : m001List) {
                list.add(m001);
                queryAllChildren(list, m001.getOrgno());
            }
        }
        return list;
    }

    public M001 get(String orgno) throws Exception {
        StringBuffer sql = new StringBuffer("SELECT * FROM sys_org where 1=1 ");
        if (Tools.isNotBlank(orgno)) {
            sql.append(" and orgno =" + " " + orgno + " ");
        }
        return super.findRow(M001.class, sql.toString(), SubDatabase.DATABASE_SYS_CENTER, null);
    }

    public int add(SqlParam<M001> params) throws Exception {
        return super.update(
                "INSERT INTO sys_org " +
                        "(orgid,legal_code, orgno, orgname, orglevel, " +
                        "orgtype, parentorgno, address, contect, telno, org_status,credential_flag,credential_num) " +
                        " VALUES " +
                        "($S{orgid},$S{legalCode},$S{orgno},$S{orgname},$S{orglevel}," +
                        "$S{orgtype},$S{parentorgno},$S{address},$S{contect},$S{telno},$S{orgStatus},$S{credentialFlag},$S{credentialNum})",
                params.getModel()).getEffect();
    }

    public SqlResult<M001> isBlankOrglevel(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("select orgid from sys_org where  orgno = $S{orgno}", params);
    }

    /**
     * 通过机构父级编号查询机构信息
     *
     * @param params 父级机构代码
     * @return
     * @throws Exception
     */
    public SqlResult<M001> selectOrgLevelByParno(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("select orglevel,legal_code from sys_org where  orgno = $S{parentorgno}", params);
    }

    /**
     * 通过机构父级编号查询子机构信息
     *
     * @param params 父级机构代码
     * @return
     * @throws Exception
     */
    public SqlResult<M001> selectOrgByParno(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("select orglevel,legal_code from sys_org where  parentorgno = $S{orgno}", params);
    }

    public SqlResult<M001> findChildren2(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("select * from sys_org where  parentorgno = $S{orgno}", params);
    }

    public SqlResult<M001> queryOrgWL(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("select orgid from sys_org where  orglevel = $S{orglevel}", params);
    }

    public SqlResult<M001> findWL(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("SELECT * FROM sys_org where orglevel <= $S{orglevel} and org_status != 'D'", params);
    }

    public SqlResult<M001> findWL1(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("SELECT * FROM sys_org where 0 < orglevel", params);
    }

    /**
     * @return com.kayak.core.sql.SqlResult<com.kayak.system.model.M001>
     * <AUTHOR>
     * @Description 查询额度机构(去掉四级机构)
     * @Date 2022/4/18
     * @Param [params]
     **/
    public SqlResult<M001> findQuotaWL1(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("SELECT * FROM sys_org where 0 < orglevel AND orglevel<4 ", params);
    }

    public SqlResult<M001> findArea(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("SELECT * FROM sys_org where orglevel = 2", params);
    }

    public SqlResult<M001> findOrg(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        return super.findRows("SELECT * FROM sys_org where orglevel > 2", params);
    }

    public SqlResult<M001> checkIsFather(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        String orgId = params.getModel().getOrgid();
        if (Tools.isBlank(orgId)) {
            return null;
        }
        return super.findRows("SELECT * FROM sys_org WHERE PARENTORGNO = $S{orgno}", params);
    }

    /**
     * 检查是否存在相同总行是否存在
     *
     * @param params
     * @return
     */
    public SqlRow selectOrgCount(SqlParam<M001> params) throws Exception {
        String orgno = params.getModel().getOrgno();
        String legalCode = params.getModel().getLegalCode();
        if (Tools.isBlank(orgno) && Tools.isBlank(legalCode)) {
            return null;
        }
        return super.findRow("select count(1) num from sys_org where orgno = '" + orgno + "' and legal_code = '" + legalCode + "' and orglevel = '1'", params);
    }

    /**
     * 检查是否存在下属机构
     *
     * @param params
     * @return
     */
    public SqlRow selectOrgByno(SqlParam<M001> params) throws Exception {
        String orgno = params.getModel().getOrgno();
        String legalCode = params.getModel().getLegalCode();
        if (Tools.isBlank(orgno) && Tools.isBlank(legalCode)) {
            return null;
        }
        return super.findRow("select count(1) nums from sys_org where parentorgno = '" + orgno + "' and legal_code = '" + legalCode + "' and org_status = 'A'", params);
    }

    /**
     * 开启下级机构时，判断上级机构是否开启
     *
     * @param params
     * @return
     */
    public SqlRow selectparentOrgStatus(SqlParam<M001> params) throws Exception {
        String parentNo = params.getModel().getParentorgno();
        String legalCode = params.getModel().getSuperLegalCode();
        if (Tools.isBlank(parentNo) && Tools.isBlank(legalCode)) {
            return null;
        }
        return super.findRow("select count(1) nums from sys_org where orgno = '" + parentNo + "' and legal_code = '" + legalCode + "' and org_status = 'A' ", params);
    }


    /**
     * 检查添加总行是否存在相同的法人代码
     *
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M001> checkIsLegalCode(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        String legalCode = params.getModel().getLegalCode();
        if (Tools.isBlank(legalCode)) {
            return null;
        }
        return super.findRows("SELECT orgid FROM sys_org WHERE legal_code = $S{legalCode} and orglevel = '1'", params);
    }


    /**
     * @return
     * @Description 无需考虑并发情况，若是存在，则是多次重新对orgInfos重新赋值而已。
     * @Param
     * <AUTHOR>
     * @Date 2020/11/30
     **/
    public Map<String, String> getOrgMap() {
        if (orgInfos == null) {
            // 查询sys_org信息存入map中。
            try {
                Map<String, Object> map = new HashMap<>();
                SqlParam<M001> params = new FetcherData<>(map, M001.class);
                SqlResult<M001> rows = super.findRows("SELECT orgno,orgname FROM sys_org", params);
                orgInfos = rows.getRows().stream().collect(Collectors.toMap(M001::getOrgno, M001::getOrgname));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return orgInfos;
    }

    /*public List<Org> findIn(String orgIds) throws Exception {
        if (Tools.strIsEmpty(orgIds)) {
            return new ArrayList<>();
        }
        return super.findRows(Org.class, "SELECT * FROM sys_org WHERE orgid in ('"+ orgIds +"')",
                SubDatabase.DATABASE_SYS_CENTER,null);
    }*/

    public List<M001> findIn(Map<String, Object> params) throws Exception {

        int num = (int) params.get("orgNum");
        if (num == 0) {
            return new ArrayList<>();
        }

        // 页面后台sql框架的in防注入式写法
        String sql = "SELECT * FROM sys_org WHERE orgid in (";

        for (int i = 0; i < num; i++) {
            if (i != 0) {
                sql += ",";
            }
            sql += "$S{orgid" + i + "}";
        }

        sql += ")";

        return super.findRows(M001.class, sql,
                SubDatabase.DATABASE_SYS_CENTER, params);
    }

    /**
     * 把撤销机构的子机构转移到合并机构上
     *
     * @param params
     * @return
     * @throws Exception
     */
    public UpdateResult UpdateParOrgNo(Map<String, Object> params) throws Exception {
        return super.update("UPDATE sys_org SET parentorgno = $S{mergeOrgno}  WHERE parentorgno = $S{removeOrgno}", params);
    }

    /**
     * 修改撤销机构状态为删除
     */
    public UpdateResult updateRemoveOrg(Map<String, Object> params) throws Exception {
        return super.update("UPDATE sys_org SET org_status = 'D' WHERE orgno = $S{removeOrgno}", params);
    }

    public SqlResult<M001> findOrgByOrglevel(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        StringBuffer sb = new StringBuffer("select * from sys_org where 1=1 ");
        if (Tools.isNotBlank(params.getModel().getOrglevel()) && !"undefined".equals(params.getModel().getOrglevel())) {
            sb.append(" and  orglevel = $S{orglevel} ");
        } else {
            M001 m001 = get(params.getModel().getOrgno());
            sb.append(" and orgid like '%" + m001.getOrgid() + "%'");
        }
        SqlResult<M001> sqlResult = super.findRows(sb.toString(), params);
        return sqlResult;
    }

    public int stopOrg(SqlParam<M001> params) throws Exception {
        return super.update(
                "UPDATE sys_org SET ORG_STATUS='I' WHERE ORGNO=$S{orgno}",
                params.getModel()).getEffect();
    }

    public int recoverOrg(SqlParam<M001> params) throws Exception {
        return super.update(
                "UPDATE sys_org SET ORG_STATUS='A' WHERE ORGNO=$S{orgno}",
                params.getModel()).getEffect();
    }

    /**
     * @return com.kayak.core.sql.SqlResult<com.kayak.system.model.M001>
     * <AUTHOR>
     * @Description 获取机构相关信息
     * @Date 2022/1/19
     * @Param [params]
     **/
    public SqlResult<M001> findOrgs(SqlParam<M001> params) throws Exception {
        params.setMakeSql(false);
        StringBuffer sb = new StringBuffer("select AREA_CODE,CREDENTIAL_FLAG,LEGAL_CODE,ORG_STATUS,ORGID,ORGLEVEL,ORGNAME,ORGNO,ORGTYPE,PARENTORGNO from sys_org where 1=1 ");
        if (Tools.isNotBlank(params.getModel().getOrgno())) {
            sb.append(" and  ORGNO= '" + params.getModel().getOrgno() + "'");
        }
        if (Tools.isNotBlank(params.getModel().getLegalCode())) {
            sb.append(" and  legal_code= '" + params.getModel().getLegalCode() + "'");
        }
        return super.findRows(sb.toString(), params);
    }
}
