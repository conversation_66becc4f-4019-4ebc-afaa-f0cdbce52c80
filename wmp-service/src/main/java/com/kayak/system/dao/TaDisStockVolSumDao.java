package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.system.model.TaDisStockVolSum;
import com.kayak.system.model.TaProdStockVolSum;
import org.springframework.stereotype.Repository;

@Repository
public class TaDisStockVolSumDao extends ComnDao {


    public SqlResult<TaDisStockVolSum> findFinaCustTransCfmLogTop5(SqlParam<TaDisStockVolSum> params) throws Exception {
        String sql = " select t.tano,t.amt \n" +
                "  from (select t.tano, sum(t.ACK_AMT) as amt, count(1) as cot\n" +
                "          from fina_cust_trans_cfm_log t\n" +
                "         group by t.tano\n" +
                "         order by cot desc) t\n" +
                " limit 6 \n";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }

    public SqlResult<TaDisStockVolSum> findFundCustTransCfmLogTop5(SqlParam<TaDisStockVolSum> params) throws Exception {
        String sql = " select t.tano,t.amt \n" +
                "  from (select t.tano, sum(t.ACK_AMT) as amt, count(1) as cot\n" +
                "          from fund_cust_trans_cfm_log t\n" +
                "         group by t.tano\n" +
                "         order by cot desc) t\n" +
                " limit 6 \n";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);
    }

}
