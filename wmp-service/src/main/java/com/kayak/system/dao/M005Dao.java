package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.util.Tools;
import com.kayak.system.model.M005;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9 18:08
 * @description
 */

@Repository
public class M005Dao extends ComnDao {

    public M005 findRoleById(String roleid) throws Exception {
        // 左关联本表是为了带出父角色信息
        return super.findRow(M005.class,"SELECT r.*,p.rolename AS parent_role_name FROM sys_role r " +
                " LEFT JOIN sys_role p ON r.parentroleid = p.roleid WHERE r.roleid = '" + roleid + "'",0,null);
    }

    //根据id查询业务代码
    public List<M005> findBusissCodeById(List<String> list) throws Exception {
        // 左关联本表是为了带出父角色信息
        return super.findRows(M005.class,"SELECT busiss_code  FROM sys_role  WHERE roleid='" + list + "'",0,null);
    }


    public List<M005> findAll() throws Exception {
        // 左关联本表是为了带出父角色信息
        return super.findRows(M005.class,"SELECT r.*,p.rolename AS parent_role_name FROM sys_role r " +
                " LEFT JOIN sys_role p ON r.parentroleid = p.roleid ",0,null);
    }

    public List<M005> findRoleByParentId(String parentId) throws Exception {
        // 左关联本表是为了带出父角色信息
        return super.findRows(M005.class,"SELECT r.*,p.rolename AS parent_role_name FROM sys_role r " +
                " LEFT JOIN sys_role p ON r.parentroleid = p.roleid WHERE r.parentroleid = '" + parentId + "'",0,null);
    }

    public M005 findRoleByRoleName(String roleName) throws Exception {
        return super.findRow(M005.class,"SELECT r.*,p.rolename AS parent_role_name FROM sys_role r " +
                " LEFT JOIN sys_role p ON r.parentroleid = p.roleid WHERE r.rolename = '" + roleName + "'",0,null);
    }

    public int deleteRole(String roleId) throws Exception {
        return super.update("DELETE FROM sys_role WHERE roleid = '" + roleId + "'").getEffect();
    }

    public int addRole(SqlParam<M005> params) throws Exception {
        return super.update("INSERT INTO sys_role(roleid,parentroleid,rolename,busiss_code,roletype,descript) VALUES($AUTOIDS{roleid},$S{parentroleid},$S{rolename},$S{busissCode},$S{roletype},$S{descript})",params.getModel()).getEffect();
    }

    public int updateRole(SqlParam<M005> params) throws Exception {
        return super.update("UPDATE sys_role SET parentroleid=$S{parentroleid},rolename=$S{rolename},roletype=$S{roletype},busiss_code=$S{busissCode},descript=$S{descript} WHERE roleid=$S{roleid}",params.getModel()).getEffect();
    }


    /**
     * 检查角色下是否有用户
     * @param params
     * @return
     */
    public SqlRow selectisRoleByRoleid(SqlParam<M005> params)throws  Exception{
        String roleid = params.getModel().getRoleid();
        if(Tools.isBlank(roleid)){
            return null;
        }
        return super.findRow("select count(1) nums from sys_user u where u.userid in " +
                "( select ur.userid userid from sys_role r left join sys_user_role ur on r.roleid = ur.roleid where r.roleid='"+roleid+"')",params);
    }


    /**
     * 检查角色下是否有用户
     * @param params
     * @return
     */
    public SqlRow selectMeunByRoleid(SqlParam<M005> params)throws  Exception{
        String roleid = params.getModel().getRoleid();
        if(Tools.isBlank(roleid)){
            return null;
        }
        return super.findRow("select count(1) nums from sys_user u where u.userid in " +
                "( select ur.userid userid from sys_role r left join sys_user_role ur on r.roleid = ur.roleid where r.roleid='"+roleid+"')",params);
    }

    /**
     * 查询父角色下的所有子角色
     * @param roleid
     * @return
     * @throws Exception
     */
    public List<String> selectroleidById(String roleid)throws  Exception{
        if(Tools.isBlank(roleid)){
            return null;
        }
        return super.findRows(String.class,"select roleid FROM sys_role WHERE PARENTROLEID = '"+roleid+"'",0,null);
    }
}
