package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.SimpleDataUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.system.model.M008;
import com.kayak.system.model.TaDesktopProdStock;
import com.kayak.system.model.TaDesktopCustStock;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Repository
public class TaDesktopProdStockDao extends ComnDao {

    //todo 修改sql语句 sysdate --> sysdate()

    /**
     * 理财查询 start
     * */
    public String findFinaSumProdStock(SqlParam<TaDesktopProdStock> params) throws Exception {
        String prodStock = "0";
        SqlResult<TaDesktopProdStock> row = super.findRows("select round(sum(t.stock_vol*t.nav)) as prod_stock from fina_vol_stock t  ", SubDatabase.DATABASE_FINA_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            prodStock = row.getRows().get(0).getProdStock();
        }
        return prodStock;
    }

    public SqlResult<TaDesktopProdStock>  findFinaCustVol(SqlParam<TaDesktopProdStock> params) throws Exception {
        String sql = "select t.prod_code, sum(t.total_vol) as vol\n" +
                "  from fina_cust_vol t\n" +
                " where date_format(t.CREATE_TIME, '%Y-%m') = date_format(sysdate(), '%Y-%m')\n" +
                " group by t.prod_code ";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER,params);
    }

    public String findFinaNavInfo(String prodCode) throws Exception {
        String nav = "0";
        String sql = "select t.nav as nav,max(t.nav_date) from FINA_NAV_INFO t where t.prod_code = '"+prodCode+"' group by t.nav";
        List<SqlRow> row = super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER);
        if (row != null && row.size() >= 1) {
            nav = row.get(0).get("nav").toString();
        }
        return nav;
    }

    public List<SqlRow> findFinaByMonth() throws Exception {
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DATE, -30);
        Date befor = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String nowStr = sdf.format(now);
        String beforStr = sdf.format(befor);
        String sql = "select sum(t.stock_vol * t.nav) as nav, t.trans_date as transDate\n" +
                "  from fina_vol_stock t\n" +
                " where t.trans_date between '" +beforStr+"'"+
                " and '" +nowStr+
                "' \n" +
                " group by t.trans_date \n" +
                " order by t.trans_date \n";
        return super.findRows(sql, SubDatabase.DATABASE_FINA_CENTER);
    }

    /**
     * 基金查询 start
     * */
    public String findFundSumProdStock(SqlParam<TaDesktopProdStock> params) throws Exception {
        String prodStock = "0";
        SqlResult<TaDesktopProdStock> row = super.findRows("select sum(t.stock_vol*t.nav) as prod_stock from fund_vol_stock t  ", SubDatabase.DATABASE_FUND_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            prodStock = row.getRows().get(0).getProdStock();
        }
        return prodStock;
    }

    public SqlResult<TaDesktopProdStock>  findFundCustVol(SqlParam<TaDesktopProdStock> params) throws Exception {
        String sql = "select t.prod_code, sum(t.total_vol) as vol\n" +
                "  from fund_cust_vol t\n" +
                " where date_format(t.CREATE_TIME, '%Y-%m') = date_format(sysdate(), '%Y-%m')\n" +
                " group by t.prod_code ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);
    }

    public String findFundNavInfo(String prodCode) throws Exception {
        String nav = "0";
        String sql = "select t.nav as nav,max(t.nav_date) from FUND_NAV_INFO t where t.prod_code = '"+prodCode+"' group by t.nav";
        List<SqlRow> row = super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER);
        if (row != null && row.size() >= 1) {
            nav = row.get(0).get("nav").toString();
        }
        return nav;
    }

    public List<SqlRow> findFundByMonth() throws Exception {
        String sql = "select sum(t.stock_vol * t.nav) as nav, t.trans_date as transDate\n" +
                "  from fund_vol_stock t\n" +
                " where t.trans_date between date_format(sysdate() - 30, '%Y-%m-%d') and\n" +
                "       date_format(sysdate(), '%Y-%m-%d')\n" +
                " group by t.trans_date \n" +
                " order by t.trans_date \n";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER);
    }
}
