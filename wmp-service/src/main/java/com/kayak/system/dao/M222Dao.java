package com.kayak.system.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.util.Tools;
import com.kayak.system.model.M222;
import com.kayak.system.model.WorkdayItem;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class M222Dao extends ComnDao {

    public SqlResult<M222> findProgram(SqlParam<M222> params) throws Exception {

//        List<String> list = params.getModel().getSystemNoo();
//        String s = "";
        String sql = "";
//        if (list != null) {
//            for (int i = 0; i < list.size(); i++) {
//                if (list.size() == 1) {
//                    s += "'" + list.get(i) + "'";
//                } else {
//                    if (i == list.size() - 1) {
//                        s += "'" + list.get(i) + "'";
//                    } else {
//                        s += "'" + list.get(i) + "',";
//                    }
//                }
//            }
//            sql = "SELECT * FROM sys_workday_pgm where system_no in ("+s+") ";
//        } else {
//            sql = "SELECT * FROM sys_workday_pgm";
//        }
        sql = "SELECT * FROM sys_workday_pgm";
        return super.findRows(sql, params);
    }

    public SqlResult<SqlRow> queryDict(SqlParam<M222> params) throws Exception {
        List<SqlRow> list = super.findRows(SqlRow.class,"SELECT pgmno as value,pgmname as label FROM sys_workday_pgm", SubDatabase.DATABASE_SYS_CENTER,null);
        SqlResult<SqlRow> sqlResult = new SqlResult();
        sqlResult.setRows(list);
        return sqlResult;
    }

    public int addProgram(SqlParam<M222> params) throws Exception {
        return super.update("INSERT INTO sys_workday_pgm " +
                " (pgmname, pgmno, pgmtype,system_no, legal_code,remark) VALUES " +
                " ($S{pgmname}, $S{pgmno}, $S{pgmtype},  $S{systemNo}, $S{legalCode}, $S{remark})", params.getModel()).getEffect();
    }

    public void delProgram(SqlParam<M222> params) throws Exception {
        doTrans(() -> {
            super.update(
                    "DELETE FROM sys_workday_pgm WHERE pgmno = $S{pgmno}", params.getModel());
            super.update(
                    "DELETE FROM sys_workday_set WHERE pgmno = $S{pgmno}", params.getModel());
        });
    }

    public int updateProgram(SqlParam<M222> params) throws Exception {
        return super.update("UPDATE sys_workday_pgm " +
                " SET pgmname = $S{pgmname}, pgmtype = $S{pgmtype}, remark = $S{remark}" +
                " WHERE pgmno = $S{pgmno} ", params.getModel()).getEffect();
    }

    public M222 getProgram(SqlParam<M222> params) throws Exception {
        return super.findRow(M222.class, "SELECT * FROM sys_workday_pgm WHERE pgmno = $S{pgmno}",
                0, params);
    }

    /**
     * 根据系统编号和法人代码，查询产品工作日方案
     * @param params
     * @return
     * @throws Exception
     */
    public List<M222> getProdPrograms(SqlParam<M222> params) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT pgmno, pgmname FROM sys_workday_pgm WHERE 1=1");
        String systemNo = params.getModel().getSystemNo();
        String legalCode = params.getModel().getLegalCode();
        String pgmtype = params.getModel().getPgmtype();
        if (!Tools.isEmpty(systemNo)) {
            sb.append(" AND system_no = $S{systemNo}");
        }
        if (!Tools.isEmpty(legalCode)) {
            sb.append(" AND legal_code=$S{legalCode}");
        }
        if (Tools.isEmpty(pgmtype)) {
            sb.append(" AND pgmtype='2'");
        } else {
            sb.append(" AND pgmtype=$S{pgmtype}");
        }

        return super.findRows(M222.class, sb.toString(), 0, params.getModel());
    }

    public List<WorkdayItem> find(WorkdayItem workdayItem) throws Exception {
        return super.findRows(WorkdayItem.class, "SELECT * FROM sys_workday_set WHERE pgmno = $S{pgmno} ",
                0, workdayItem);
    }

    public SqlResult<WorkdayItem> find(SqlParam<WorkdayItem> params) throws Exception {
        String workday = params.getModel().getWorkday();
        String sql;
        if (Tools.isBlank(workday)) {
            sql = "SELECT * FROM sys_workday_set WHERE pgmno = $S{pgmno}";
        } else {
            sql = "SELECT * FROM sys_workday_set WHERE pgmno = $S{pgmno} AND workday LIKE '" + workday + "%'";
        }
        return super.findRows(sql, params);
    }

    public void saveWorkdayItems(String pgmno, String year, List<WorkdayItem> newData)
            throws Exception {
        if (Tools.isBlank(pgmno) || Tools.isBlank(year)) {
            return;
        }
        doTrans(()->{
            super.update("DELETE FROM sys_workday_set " +
                    " WHERE pgmno = '" + pgmno + "' AND workday LIKE '" + year + "%'");
            if (CollectionUtils.isEmpty(newData)) {
                return;
            }

            for (WorkdayItem workdayItem : newData) {
                super.update("INSERT INTO sys_workday_set (pgmno, workday) VALUES " +
                        " ($S{pgmno}, $S{workday})", workdayItem);
            }
        });
    }


    /**
     * 检查同一类型的工作日
     * @param params
     * @return
     */
    public SqlRow checkPgmno(SqlParam<M222> params)throws  Exception{
        String pgmtype = params.getModel().getPgmtype();
        String pgmno = params.getModel().getPgmno();
        if(Tools.isBlank(pgmtype) && Tools.isBlank(pgmno)){
            return null;
        }
        return super.findRow("select count(1) nums from sys_workday_pgm where pgmtype = '"+pgmtype+"' and pgmno = '"+pgmno+"'",params);
    }

}
