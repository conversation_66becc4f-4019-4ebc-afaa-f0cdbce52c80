package com.kayak.system.model;

import com.kayak.common.base.BaseModel;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.util.List;

@GraphQLModel(fetcher = "m222Service", table = "sys_workday_pgm")
@Data
public class M222 extends BaseModel {

    @GraphQLField(key = true, kkhtml = "KFieldText", label = "方案编号", sql = "pgmno = $S{pgmno}", field = "pgmno",  kkhtmlDefault = true)
    private String pgmno;

    @GraphQLField(kkhtml = "KFieldText", label = "方案名称", sql = " pgmname like '%$U{pgmname}%' ", field = "pgmname")
    private String pgmname;

    @GraphQLField(label = "方案类型", sql = "pgmtype = $S{pgmtype}",
            field = "pgmtype")
    private String pgmtype;

    @GraphQLField(kkhtml = "KFieldText", label = "备注", sql = "remark = $S{remark}", field = "remark",  kkhtmlDefault = true)
    private String remark;

    @GraphQLField(kkhtml = "KFieldText",sql = "(legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode})", field = "legal_code",  kkhtmlDefault = true)
    private String legalCode;


    @GraphQLField(kkhtml = "KFieldSelect", sql = "system_no=$S{systemNo}", label = "系统编号", field = "system_no",kkhtmlExt="{\"data-dict\": \"system_no\"}",  kkhtmlDefault = true)
    private String systemNo;

    private List<String> systemNoo;

}
