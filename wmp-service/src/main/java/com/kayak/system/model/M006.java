package com.kayak.system.model;

import com.kayak.common.base.BaseModel;
import com.kayak.core.desensitized.BaseDesensitized;
//import com.kayak.core.desensitized.CustNameDesensitized;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

@Data
@GraphQLModel(fetcher = "m006Service", table = "sys_user")
public class M006 extends BaseModel {

	private String orgname;

	@GraphQLField
	private String parentOrgno;

	@GraphQLField
	private String parentdeptno;

	@GraphQLField(key = true, sql = "userid = $S{userid}", field = "userid")
	private String userid;
	@GraphQLField(kkhtml = "KFieldText", label = "登录名称", kkhtmlDefault = true, sql = "u.loginname like '%$U{loginname}%'", field = "loginname")
	private String loginname;
	@GraphQLField(sql = "u.passwd = $S{passwd}", field = "passwd", desensitized = BaseDesensitized.class)
	private String passwd;
	@GraphQLField(kkhtml = "KFieldText", label = "用户名称",  kkhtmlDefault = true,sql = "u.username like '%$U{username}%'", field = "username")
	private String username;

	@GraphQLField(kkhtml = "KFieldCascader", kkhtmlExt="{\"data-diffcondition\":\"orgno,parentorgno\",\"data-graphql\":\"{queryM001(action:\\\"find\\\") {rows{orgno, orgname, parentorgno, orgid},results}}\",\"data-display-child\":\"children\",\"data-check-strictly\":true,\"data-show-num\":true,\"data-props\":\"{ expandTrigger: 'hover'}\",\"data-size\":\"medium\",\"data-clearable\":true,\"data-fileterable\":true,\"data-display-field\":\"orgname\",\"data-value-field\":\"orgno\"}", label = "所属机构", sql = "o.orgno = $S{orgno}", field = "orgno",  kkhtmlDefault = true)
	private String orgno;

	@GraphQLField(kkhtml = "KFieldCascader",  kkhtmlExt="{\"data-diffcondition\":\"deptno,parentdeptno\",\"data-graphql\":\"{queryM009(action:\\\"find\\\") {rows{deptno, deptname, parentdeptno, deptid},results}}\",\"data-display-child\":\"children\",\"data-check-strictly\":true,\"data-show-num\":true,\"data-props\":\"{ expandTrigger: 'hover'}\",\"data-size\":\"medium\",\"data-clearable\":true,\"data-fileterable\":true,\"data-display-field\":\"deptname\",\"data-value-field\":\"deptno\"}", label = "所属部门", sql = "d.deptno = $S{deptno}", field = "deptno",  kkhtmlDefault = true)
	private String deptno;

	@GraphQLField(kkhtml = "KFieldSelect", label = "用户状态", kkhtmlDefault = true, kkhtmlExt="{\"data-dict\": \"userstatus\"}", sql = "u.userstatus = $S{userstatus}", field = "userstatus")
	private String userstatus;
	@GraphQLField(sql = "u.idtype = $S{idtype}", field = "idtype")
	private String idtype;
	@GraphQLField(sql = "u.idno = $S{idno}", field = "idno")
	private String idno;
	@GraphQLField( label = "性别", sql = "u.sex = $S{sex}", field = "sex", kkhtmlExt="{\"data-dict\": \"sex\"}",  kkhtmlDefault = true )
	private String sex;
	@GraphQLField( label = "移动电话", sql = "u.mobileno = $S{mobileno}", field = "mobileno")
	private String mobileno;
	@GraphQLField(sql = "u.officeno = $S{officeno}", field = "officeno")
	private String officeno;
	@GraphQLField(sql = "u.homeno = $S{homeno}", field = "homeno")
	private String homeno;
	@GraphQLField(sql = "u.faxno = $S{faxno}", field = "faxno")
	private String faxno;
	@GraphQLField( label = "电子邮箱", sql = "u.email = $S{email}", field = "email")
	private String email;
	@GraphQLField(sql = "u.postcode = $S{postcode}", field = "postcode")
	private String postcode;
	@GraphQLField(sql = "u.address = $S{address}", field = "address")
	private String address;
	@GraphQLField(sql = "u.createdate = $S{createdate}", field = "createdate")
	private String createdate;
	@GraphQLField(sql = "u.modifydate = $S{modifydate}", field = "modifydate")
	private String modifydate;
	@GraphQLField(sql = "u.pwderrtimes = $S{pwderrtimes}", field = "pwderrtimes")
	private String pwderrtimes;
	@GraphQLField(sql = "u.pwderrlockdt = $S{pwderrlockdt}", field = "pwderrlockdt")
	private String pwderrlockdt;
	@GraphQLField(sql = "u.pwdsetdate = $S{pwdsetdate}", field = "pwdsetdate")
	private String pwdsetdate;
	@GraphQLField(sql = "lu.astlogintime = $S{lastlogintime}", field = "lastlogintime")
	private String lastlogintime;
	@GraphQLField(sql = "u.lastloginstation = $S{lastloginstation}", field = "lastloginstation")
	private String lastloginstation;
	@GraphQLField(sql = "o.orgid like '$U{orgid}%'", field = "orgid")
	private String orgid;

	@GraphQLField
	private String roleids;

	@GraphQLField
	private String deptname;

	@GraphQLField
	private String oldPwd;

	@GraphQLField(sql = "(u.legal_code = $S{legalCode} or $S{legalCode} = $S{superLegalCode})",label = "法人代码", field = "legal_code")
	private String legalCode;

	public String getOrgname() {
		return orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}

	public String getParentOrgno() {
		return parentOrgno;
	}

	public void setParentOrgno(String parentOrgno) {
		this.parentOrgno = parentOrgno;
	}

	public String getParentdeptno() {
		return parentdeptno;
	}

	public void setParentdeptno(String parentdeptno) {
		this.parentdeptno = parentdeptno;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getLoginname() {
		return loginname;
	}

	public void setLoginname(String loginname) {
		this.loginname = loginname;
	}

	public String getPasswd() {
		return passwd;
	}

	public void setPasswd(String passwd) {
		this.passwd = passwd;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getOrgno() {
		return orgno;
	}

	public void setOrgno(String orgno) {
		this.orgno = orgno;
	}

	public String getDeptno() {
		return deptno;
	}

	public void setDeptno(String deptno) {
		this.deptno = deptno;
	}

	public String getUserstatus() {
		return userstatus;
	}

	public void setUserstatus(String userstatus) {
		this.userstatus = userstatus;
	}

	public String getIdtype() {
		return idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}

	public String getIdno() {
		return idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getMobileno() {
		return mobileno;
	}

	public void setMobileno(String mobileno) {
		this.mobileno = mobileno;
	}

	public String getOfficeno() {
		return officeno;
	}

	public void setOfficeno(String officeno) {
		this.officeno = officeno;
	}

	public String getHomeno() {
		return homeno;
	}

	public void setHomeno(String homeno) {
		this.homeno = homeno;
	}

	public String getFaxno() {
		return faxno;
	}

	public void setFaxno(String faxno) {
		this.faxno = faxno;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCreatedate() {
		return createdate;
	}

	public void setCreatedate(String createdate) {
		this.createdate = createdate;
	}

	public String getModifydate() {
		return modifydate;
	}

	public void setModifydate(String modifydate) {
		this.modifydate = modifydate;
	}

	public String getPwderrtimes() {
		return pwderrtimes;
	}

	public void setPwderrtimes(String pwderrtimes) {
		this.pwderrtimes = pwderrtimes;
	}

	public String getPwderrlockdt() {
		return pwderrlockdt;
	}

	public void setPwderrlockdt(String pwderrlockdt) {
		this.pwderrlockdt = pwderrlockdt;
	}

	public String getPwdsetdate() {
		return pwdsetdate;
	}

	public void setPwdsetdate(String pwdsetdate) {
		this.pwdsetdate = pwdsetdate;
	}

	public String getLastlogintime() {
		return lastlogintime;
	}

	public void setLastlogintime(String lastlogintime) {
		this.lastlogintime = lastlogintime;
	}

	public String getLastloginstation() {
		return lastloginstation;
	}

	public void setLastloginstation(String lastloginstation) {
		this.lastloginstation = lastloginstation;
	}

	public String getOrgid() {
		return orgid;
	}

	public void setOrgid(String orgid) {
		this.orgid = orgid;
	}

	public String getRoleids() {
		return roleids;
	}

	public void setRoleids(String roleids) {
		this.roleids = roleids;
	}

	public String getDeptname() {
		return deptname;
	}

	public void setDeptname(String deptname) {
		this.deptname = deptname;
	}

	public String getOldPwd() {
		return oldPwd;
	}

	public void setOldPwd(String oldPwd) {
		this.oldPwd = oldPwd;
	}

	public String getLegalCode() {
		return legalCode;
	}

	public void setLegalCode(String legalCode) {
		this.legalCode = legalCode;
	}
}