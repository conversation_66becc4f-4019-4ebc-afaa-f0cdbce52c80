package com.kayak.system.model;

import com.kayak.common.base.BaseModel;
import com.kayak.graphql.annotation.GraphQLField;
import com.kayak.graphql.annotation.GraphQLModel;
import lombok.Data;

import java.util.Map;

@GraphQLModel(fetcher = "taProdStockVolSumService")
@Data
public class TaProdStockVolSum extends BaseModel {


    @GraphQLField(label = "产品代码",field = "prod_code")
    private String prodCode;

    @GraphQLField(label = "产品名称",field = "prod_name")
    private String prodName;

    @GraphQLField(label = "份额",field = "total_vol")
    private String totalVol;

    @GraphQLField(label = "金额",field = "amt")
    private String amt;

    @GraphQLField(label = "产品类型",field = "prod_type")
    private String prodType;

}
