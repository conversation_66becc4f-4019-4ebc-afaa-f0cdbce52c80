package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M659;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M659Dao extends ComnDao {


	@Autowired
	private ReportformUtil reportformUtil;
	/**
	 * 保有量统计报表【按产品类型】
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M659> findFundVolStockForOrg(SqlParam<M659> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("select t.fund_type,\n" +
				"       sum(t.stock_vol) as stock_vol,\n" +
				"       sum(t.stock_vol * t.nav) as stock_amt,\n" +
				"       sum(t.effe_num) as effe_num,\n" +
				"       t.trans_date\n" +
				"  from fund_vol_stock t\n" +
				" where 1 = 1 \n" +reportformUtil.getOrgIdForOrgLevel("t.",params.getParams().get("userid").toString())+
				" group by t.fund_type, t.trans_date\n" +
				" order by t.trans_date desc\n ", SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
