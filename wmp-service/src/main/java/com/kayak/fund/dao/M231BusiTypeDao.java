package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.DataStatus;
import com.kayak.common.constants.OpenFlag;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import com.kayak.fund.model.M231BusiType;
import com.kayak.graphql.model.FetcherData;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M231BusiTypeDao extends ComnDao{

    private String property(){
        return "select  b.tano,b.BUSI_CODE,b.data_status ";
    }

    public SqlResult<M231BusiType> findByDisCode(SqlParam<M231BusiType> param)throws Exception{
        param.setMakeSql(true);
        return super.findRows(property() + " FROM fund_ta_busi_limit b where b.tano is not null", SubDatabase.DATABASE_FUND_CENTER,param);
    }
    public SqlResult<M231BusiType> selectByDisCode(SqlParam<M231BusiType> param)throws Exception{
        param.setMakeSql(false);
        return super.findRows(property() + " FROM fund_ta_busi_limit b where b.tano =$S{tano} ",SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public SqlResult<M231BusiType> checkIsExist(SqlParam<M231BusiType> param)throws Exception{
        param.setMakeSql(false);
        String sql = null;
        if(DataStatus.ADD.equals(param.getModel().getDataStatus())){
             sql = property() + " FROM fund_ta_busi_limit b  where b.tano=$S{tano} ";
        }else{
            sql = property() + " FROM fund_ta_busi_limit b  where b.tano=$S{tano} and data_status=$S{dataStatus} ";
        }
        return super.findRows(sql,SubDatabase.DATABASE_FUND_CENTER, param);
    }

    public void addBusiType(SqlParam<M231BusiType> param)throws Exception{
        super.update("insert into  fund_ta_busi_limit(tano,busi_code,data_status,create_time,update_time,legal_code,allow_flag,err_msg)" +
                "values($S{tano},$S{busiCode},$S{dataStatus},CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,$S{legalCode}, $S{allowFlag}, $S{errMsg})", SubDatabase.DATABASE_FUND_CENTER, param.getModel());
    }

    public void updateBusiType(SqlParam<M231BusiType> param)throws Exception{
        super.update("update fund_ta_busi_limit where tano=$S{tano} and busi_code=$S{busiCode} and data_status=$S{dataStatus} ", SubDatabase.DATABASE_FUND_CENTER, param.getModel());
    }

    public void delBusiType(SqlParam<M231BusiType> param)throws Exception{
        super.update("delete from fund_ta_busi_limit where tano=$S{tano} and busi_code=$S{busiCode} and data_status=$S{dataStatus} ", SubDatabase.DATABASE_FUND_CENTER, param.getModel());
    }

    public int addHis(SqlParam<M231BusiType> params , String openFlag , String sql) throws Exception{
        String sqlAll = "insert into TA_DISTRIBUTOR_BUSI_HIS(distributor_code,business_type,data_status,crt_user,crt_time,update_time,upd_user,oper_user,oper_date,oper_flag,process_instance_id) " +
                "select distributor_code,business_type,data_status,crt_user,create_time,update_time,upd_user,$S{crtUser},current_timestamp,'"+openFlag+"',$S{processInstanceId} from TA_DISTRIBUTOR_BUSI  where "
                + sql ;
        String sqlDb2 = "insert into TA_DISTRIBUTOR_BUSI_HIS(distributor_code,business_type,data_status,crt_user,crt_time,update_time,upd_user,oper_user,oper_date,oper_flag,process_instance_id) " +
                "select distributor_code,business_type,data_status,crt_user,create_time,update_time,upd_user,$S{crtUser},current timestamp,'"+openFlag+"',$S{processInstanceId} from TA_DISTRIBUTOR_BUSI  where "
                + sql ;
        Sql sqlExec = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
        return super.update(sqlExec,params.getModel()).getEffect();
    }
    public int clearData(SqlParam<M231BusiType> params, String openFlag, String clearSql) throws Exception {
        this.addHis(params,openFlag,"  distributor_code=$S{distributorCode}");
        return super.update("DELETE FROM TA_DISTRIBUTOR_BUSI WHERE DATA_STATUS in ("+clearSql+") and  DISTRIBUTOR_CODE=$S{distributorCode}"
                , params.getModel()).getEffect();
    }
    /**
     * 更新审批状态用 外层会有doTrans
     * */
    public void approveData(SqlParam<M231BusiType> params , String clearSql)throws Exception{
        SqlResult<M231BusiType> allList = this.selectByDisCode(params);
        SqlParam<M231BusiType> pojoParam =  new FetcherData<>(params.getParams() , M231BusiType.class);
        this.clearData(params, OpenFlag.APPROVE,clearSql);
        for(int i = 0;i < allList.getRows().size();i++){
            M231BusiType pojo = allList.getRows().get(i);
            if(DataStatus.EFFECTED .equals(pojo.getDataStatus())){
                continue;
            }
            BeanUtils.copyProperties(pojo,pojoParam.getModel());
            if(DataStatus.DELETED .equals(pojo.getDataStatus())){
                pojoParam.getModel().setDataStatus(DataStatus.EFFECTED);
                this.delBusiType(pojoParam);
            }
            if(DataStatus.APPROVE .equals(pojo.getDataStatus())||DataStatus.ADD.equals(pojo.getDataStatus())){
                pojoParam.getModel().setDataStatus(DataStatus.EFFECTED);
                this.addBusiType(pojoParam);
            }
            if(DataStatus.UPDATE.equals(pojo.getDataStatus())){
                pojoParam.getModel().setDataStatus(DataStatus.EFFECTED);
                this.updateBusiType(pojoParam);
            }
        }
    }
    public SqlResult<M231BusiType> findHistory(SqlParam<M231BusiType> params)throws Exception{
        return findRows(property() +"  FROM TA_DISTRIBUTOR_BUSI where process_instance_id=$S{processInstanceId}   ", params);
    }
}
