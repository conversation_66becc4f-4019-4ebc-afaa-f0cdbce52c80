package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M604;
import org.springframework.stereotype.Repository;

@Repository
public class M604Dao extends ComnDao {

    public SqlResult<M604> findInfos(SqlParam<M604> params) throws Exception {
        return super.findRows("select tano,busi_code,legal_code,allow_flag,err_msg,data_status,create_time,update_time from fund_ta_busi_limit",SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public UpdateResult addInfo(SqlParam<M604> params) throws Exception {
        String mysql = "INSERT INTO fund_ta_busi_limit( tano,busi_code,legal_code,allow_flag,err_msg,data_status,create_time,update_time) "+
                "VALUES($S{tano}, $S{busiCode}, $S{legalCode}, $S{allowFlag}, $S{errMsg}, $S{dataStatus}, sysdate(), sysdate())";
        String oracle = "INSERT INTO fund_ta_busi_limit( tano,busi_code,legal_code,allow_flag,err_msg,data_status,create_time,update_time) " +
                "VALUES($S{tano}, $S{busiCode}, $S{legalCode}, $S{allowFlag}, $S{errMsg}, $S{dataStatus}, sysdate(), sysdate())";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public UpdateResult deleteInfo(SqlParam<M604> params) throws Exception {
        return super.update("DELETE FROM fund_ta_busi_limit WHERE tano = $S{tano} and busi_code = $S{busiCode}",SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public UpdateResult updateInfo(SqlParam<M604> params) throws Exception {
        String mysql = "UPDATE fund_ta_busi_limit set tano = $S{tano}, busi_code = $S{busiCode}, allow_flag = $S{allowFlag}, err_msg = $S{errMsg}, data_status = $S{dataStatus},update_time = sysdate() WHERE tano = $S{tano} and busi_code = $S{busiCode} ";
        String oracle = "UPDATE fund_ta_busi_limit set tano = $S{tano}, busi_code = $S{busiCode}, allow_flag = $S{allowFlag}, err_msg = $S{errMsg}, data_status = $S{dataStatus},update_time = sysdate WHERE tano = $S{tano} and busi_code = $S{busiCode} ";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }
}
