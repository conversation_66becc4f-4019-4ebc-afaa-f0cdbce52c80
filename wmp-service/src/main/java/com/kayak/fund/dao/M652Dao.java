package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M652;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M652Dao extends ComnDao {

	/**
	 * 基金销售收入统计报表（按机构）
	 * @param params
	 * @return M652
	 * @throws Exception
	 */
	public SqlResult<M652> findFundBonusSum(SqlParam<M652> params) throws Exception {
		//params.setMakeSql(true);
		StringBuilder sql = new StringBuilder("select * from ( ");
		sql.append("select  t.trans_orgno, " +
						"sum(case when t.busi_code = '120' then t.TRANS_FEE else 0 end ) as rgFee,  " +
						"sum(case when t.busi_code = '122' then t.TRANS_FEE else 0 end ) as sgFee,  " +
						"sum(case when t.busi_code = '136' then t.TRANS_FEE else 0 end ) as zhFee,  " +
						"sum(case when t.busi_code = '124' then t.TRANS_FEE else 0 end ) as shFee,  " +
						"sum(case when t.busi_code = '126' then t.TRANS_FEE else 0 end ) as ztgFee,  " +
						"sum(case when t.busi_code = '139' then t.TRANS_FEE else 0 end ) as dtfee,  " +/*--定投*/
						"sum(RECUPERATE_FEE) as recuperate_fee, " +/*--定投*/
						"sum(CHANGE_FEE) as change_fee, " +/*--定投*/
						"sum(TRANSFER_FEE) as transfer_fee, " +/*--定投*/
						"sum(TRANS_FEE) as trans_fee, " +/*--定投*/
						"sum(agency_fee) as agency_fee " +
						"from " +
						"(" +
						"select " +
						"cfm.*," +
						"req.trans_orgno " +
						"from fund_cust_trans_cfm_log cfm " +
						"inner join fund_cust_trans_req_log req " +
						"on cfm.app_serno=req.app_serno and cfm.system_no = 'FUND' " +
						"and cfm.trans_status='3'  ");
		if(StringUtils.isNotBlank(params.getModel().getTransOrgno())){
			sql.append(" and req.trans_orgno='"+params.getModel().getTransOrgno()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMinTransDate())){
			sql.append(" and cfm.ack_date <'"+params.getModel().getMinTransDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMaxTransDate())){
			sql.append(" and cfm.ack_date >='"+params.getModel().getMaxTransDate()+"' ");
		}
		sql.append(")t group by t.trans_orgno");
		sql.append("  UNION ALL ");
		sql.append("select  t.trans_orgno, " +
				"sum(case when t.busi_code = '120' then t.TRANS_FEE else 0 end ) as rgFee,  " +
				"sum(case when t.busi_code = '122' then t.TRANS_FEE else 0 end ) as sgFee,  " +
				"sum(case when t.busi_code = '136' then t.TRANS_FEE else 0 end ) as zhFee,  " +
				"sum(case when t.busi_code = '124' then t.TRANS_FEE else 0 end ) as shFee,  " +
				"sum(case when t.busi_code = '126' then t.TRANS_FEE else 0 end ) as ztgFee,  " +
				"sum(case when t.busi_code = '139' then t.TRANS_FEE else 0 end ) as dtfee, " +/*--定投*/
				"sum(RECUPERATE_FEE) as recuperate_fee, " +/*--定投*/
				"sum(CHANGE_FEE) as change_fee, " +/*--定投*/
				"sum(TRANSFER_FEE) as transfer_fee, " +/*--定投*/
				"sum(TRANS_FEE) as trans_fee, " +/*--定投*/
				"sum(agency_fee) as agency_fee " +
				"from " +
				"(" +
				"select " +
				"cfm.*," +
				"req.trans_orgno " +
				"from fund_cust_trans_cfm_log_h cfm " +
				"inner join fund_cust_trans_req_log_h req " +
				"on cfm.app_serno=req.app_serno and cfm.system_no = 'FUND' " +
				"and cfm.trans_status='3'  ");
		if(StringUtils.isNotBlank(params.getModel().getTransOrgno())){
			sql.append(" and req.trans_orgno='"+params.getModel().getTransOrgno()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMinTransDate())){
			sql.append(" and cfm.ack_date <'"+params.getModel().getMinTransDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMaxTransDate())){
			sql.append(" and cfm.ack_date >='"+params.getModel().getMaxTransDate()+"' ");
		}
		sql.append(")t group by t.trans_orgno");
		sql.append(")");
		return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
