package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M647;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 */
@Repository
public class M647Dao extends ComnDao {


	/**
	 * 频繁开销户明细
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M647> findOpenCloseFundCustAcctForOrg(SqlParam<M647> params) throws Exception {
		StringBuffer sb = new StringBuffer(" select ");
				if(params.getModel().getOrgLevel().equals("1")){
					sb.append(" t2.bank_code as org_code,");
				}else if(params.getModel().getOrgLevel().equals("2")){
					sb.append(" t2.branch_code as org_code,");
				}else if(params.getModel().getOrgLevel().equals("3")){
					sb.append(" t2.sub_branch_code as org_code,");
				}
				sb.append("       t.tano,\n" +
							"       t.ta_name,\n" +
							"       sum(case\n" +
							"             when t.busi_code = '101' then\n" +
							"              1\n" +
							"             else\n" +
							"              0\n" +
							"           end) as  open_num,\n" +
							"       sum(case\n" +
							"             when t.busi_code = '102' then\n" +
							"              1\n" +
							"             else\n" +
							"              0\n" +
							"           end) as remover_num,\n" +
							"       sum(case\n" +
							"             when t.busi_code = '108' then\n" +
							"              1\n" +
							"             else\n" +
							"              0\n" +
							"           end) as register_num,\n" +
							"       sum(case\n" +
							"             when t.busi_code = '109' then\n" +
							"              1\n" +
							"             else\n" +
							"              0\n" +
							"           end) as cancel_register_num,\n" +
							"       t.cust_type\n" +
							"  from fund_cust_trans_cfm_log t\n" +
							"  left join fund_cust_trans_req_log t2 on t.app_serno = t2.app_serno\n" +
							" where t.busi_code in ('108', '109', '102', '101')");

		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append("and t.busi_date >= '"+params.getModel().getBusiDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
			sb.append("and t.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBelongOrgno())){
			if(params.getModel().getOrgLevel().equals("1")){
				sb.append("and t2.bank_code = '"+params.getModel().getBelongOrgno()+"'");
			}else if(params.getModel().getOrgLevel().equals("2")){
				sb.append("and t2.branch_code = '"+params.getModel().getBelongOrgno()+"'");
			}else if(params.getModel().getOrgLevel().equals("3")){
				sb.append("and t2.sub_branch_code = '"+params.getModel().getBelongOrgno()+"'");
			}
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
			sb.append("and t.tano like '%"+params.getModel().getTano()+"%'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getCustType())){
			sb.append("and t.cust_type = '"+params.getModel().getCustType()+"'");
		}
			sb.append(" group by ");
		if(params.getModel().getOrgLevel().equals("1")){
			sb.append(" t2.bank_code ,");
		}else if(params.getModel().getOrgLevel().equals("2")){
			sb.append(" t2.branch_code,");
		}else if(params.getModel().getOrgLevel().equals("3")){
			sb.append(" t2.sub_branch_code,");
		}
			sb.append("t.tano,t.ta_name,t.cust_type");

		List<M647> volList = super.findRows(M647.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M647> sqlRowSqlResult = new SqlResult<>();
		sqlRowSqlResult.setResults(volList.size());
		sqlRowSqlResult.setRows(
				volList.subList(
						params.getStart(), Math.min(params.getStart() + params.getLimit(), volList.size())
				)
		);
		sqlRowSqlResult.setDesensitized(false);
		return sqlRowSqlResult;
	}

}