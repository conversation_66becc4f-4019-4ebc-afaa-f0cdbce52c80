package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.FundDiscountDetail;
import com.kayak.fund.model.M235;
import org.springframework.stereotype.Repository;

@Repository
public class FundDiscountDetailDao extends ComnDao {

	public SqlResult<FundDiscountDetail> findFundDiscountDetails(SqlParam<FundDiscountDetail> params) throws Exception {
		return super.findRows("SELECT discount_code,dim1_min,dim1_max,dim2_min,dim2_max,dim3_min,dim3_max,cast(discount_rate*100 as decimal(5, 2)) as discount_rate FROM fund_discount_detail where discount_code=$S{discountCode} order by dim1_min ,dim2_min ,dim3_min ", SubDatabase.DATABASE_FUND_CENTER, params);
	}

	public UpdateResult addFundDiscountDetail(SqlParam<FundDiscountDetail> params) throws Exception {
		return super.update("INSERT INTO fund_discount_detail(discount_code,dim1_min,dim1_max,dim2_min,dim2_max,dim3_min,dim3_max,discount_rate) VALUES($S{discountCode},$S{dim1Min},$S{dim1Max},$S{dim2Min},$S{dim2Max},$S{dim3Min},$S{dim3Max},$D{discountRate}*0.01)",
				SubDatabase.DATABASE_FUND_CENTER, params.getModel());
	}
	
	public UpdateResult deleteFundDiscountDetail(SqlParam<M235> params) throws Exception {
		return super.update("DELETE FROM fund_discount_detail WHERE  discount_code=$S{discountCode} ",
				SubDatabase.DATABASE_FUND_CENTER, params.getModel());
	}

}
