package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M603;
import org.springframework.stereotype.Repository;

/**
 * 基金分红方案查询：当前表结构先挂在基金模块下，后期会移到产品中心。
 * 原因：因后期计划，在产品创建的时候就将基金代理关系新增进去，而管理台功能不支持跨数据库事务一致性问题，从而需要将当前表挪到平台中心
 * -- 基金代理关系 和 基金转换关系查询 这两个页面同样
 */
@Repository
public class M603Dao extends ComnDao {

	/**
	 * 基金分红方案查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M603> findFundProdBasicParam(SqlParam<M603> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("SELECT prod_code,\n" +
				"       CHARGE_TYPE,\n" +
				"       REGIST_DATE,\n" +
				"       DIV_DATE,\n" +
				"       XR_DATE,\n" +
				"       XR_TYPE,\n" +
				"       DIV_PER_UNIT,\n" +
				"       DRAW_BONUS_UNIT,\n" +
				"       DOWNLOAD_DATE,\n" +
				"       TOTAL_DIV_AMT,\n" +
				"       TANO\n" +
				"  from fund_prod_div_info\n", SubDatabase.DATABASE_FUND_CENTER, params);
	}
}