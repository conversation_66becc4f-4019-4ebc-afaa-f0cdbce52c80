package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M230;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Repository
public class M230Dao extends ComnDao {

    public List<M230> findCusts(SqlParam<M230> param)  throws Exception {
        String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
                " where 1=1  ";
        if(Tools.isNotBlank(param.getModel().getCustName())){
            sql = sql + " and cust_name = '"+param.getModel().getCustName()+"'";
        }
        if(Tools.isNotBlank(param.getModel().getIdType())){
            sql = sql + " and id_type = '"+param.getModel().getIdType()+"'";
        }
        if(Tools.isNotBlank(param.getModel().getIdCode())){
            sql = sql + " and id_code = '"+param.getModel().getIdCode()+"'";
        }
        if(Tools.isNotBlank(param.getModel().getCustNo())){
            sql = sql + " and cust_no = '"+param.getModel().getCustNo()+"'";
        }
        return super.findRows(M230.class,sql, SubDatabase.DATABASE_CUST_CENTER,param.getModel());
    }

    public M230 getCusts(String no)  throws Exception {
        String sql = "select cust_no, cust_type, cust_name, id_type, id_code from cust_info\n" +
                " where cust_no = '"+no+"'";
        return super.findRow(M230.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }

    public String getCustNoByAcctNo(String no)  throws Exception {
        String sql = "select cust_no from cust_trans_acct\n" +
                " where acct_no = '"+no+"'";
        return super.findRow(String.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }

    public List<M230> getCustNoByTransAcctNo(SqlParam<M230> param)  throws Exception {
        String sql = "select TRANS_ACCT_NO from cust_trans_acct\n" +
                " where acct_no = '"+param.getModel().getAcctNo()+"'";
        return super.findRows(M230.class,sql, SubDatabase.DATABASE_CUST_CENTER,param.getModel());
    }

    public String getAcctNo(String no)  throws Exception {
        String sql = "select acct_no from cust_trans_acct\n" +
                " where cust_no = '"+no+"'";
        return super.findRow(String.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }

    public SqlResult<M230> findM230s(SqlParam<M230> param)  throws Exception {

        String sql = " select t1.prod_code,\n" +
                "       t1.system_no,\n" +
                "       t1.ta_acct_no,\n" +
                "       t1.total_vol,\n" +
                "       t1.avail_vol,\n" +
                "       t1.cust_no,\n" +
                "       t1.tano,\n" +
                "       t1.def_div_method,\n" +
                "       t1.redeem_frozen_vol,\n" +
                "       t1.convert_income,\n" +
                "       t1.THIS_CASH_BONUS,\n" +
                "       t1.THIS_REDEEM_AMT,\n" +
                "       t1.THIS_BUY_AMT,\n" +
                "       t1.YESTERDAY_PROFIT,\n" +
                "       decode(t1.total_buy_vol,\n" +
                "              0,\n" +
                "              0,\n" +
                "              round(total_buy_amt / total_buy_vol, 2)) as total_cost,\n" +
                "       (t1.abn_frozen_vol + t1.elisor_frozen_vol + t1.transfer_frozen_vol +\n" +
                "       t1.org_frozen_vol + t1.notrans_frozen_vol) as not_redeem_frozen_vol\n" +

                "  from fund_cust_vol t1 \n" +
                " where 1 = 1  \n";

        if(Tools.isNotBlank(param.getModel().getCustNo())){
            sql = sql +" and t1.cust_no in ('"+param.getModel().getCustNo()+"')";
            param.getModel().setCustNo(null);
        }
        if(Tools.isNotBlank(param.getModel().getTransAcctNo())){
            sql = sql +" and t1.trans_acct_no in ('"+param.getModel().getTransAcctNo()+"')";
            param.getModel().setTransAcctNo(null);
        }
        if(Tools.isNotBlank(param.getModel().getSystemNo())){
            sql = sql +" and t1.system_no = '"+param.getModel().getSystemNo()+"' ";
            param.getModel().setSystemNo(null);
        }
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, param);
    }

    public M230 finaNav(M230 m230) throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String now = sdf.format(new Date());
        String sql2 = "select nav from fund_nav_info where nav_date = "+now+"\n"+
                "and tano = $S{tano} and prod_code = $S{prodCode} and legal_code = $S{legalCode}";
        M230 nav = super.findRow(M230.class,sql2,SubDatabase.DATABASE_FUND_CENTER,m230);
        return nav;
    }

    public M230 finaMaxNav(M230 m230) throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String now = sdf.format(new Date());
        String sql2 = "select t.NAV from fund_nav_info t where t.TANO=$S{tano} AND t.PROD_CODE = $S{prodCode} " +
                "AND t.nav_date = (select max(b.nav_date) from fund_nav_info b where b.TANO=t.TANO AND b.PROD_CODE=t.PROD_CODE AND b.LEGAL_CODE=t.LEGAL_CODE)";
        M230 nav = super.findRow(M230.class,sql2,SubDatabase.DATABASE_FUND_CENTER,m230);
        return nav;
    }


}
