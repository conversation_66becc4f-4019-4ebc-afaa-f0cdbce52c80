package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M667;
import org.springframework.stereotype.Repository;

@Repository
public class M667Dao extends ComnDao {

	/**
	 *
	 * 基金销售异常交易明细报表
	 */
	public SqlResult<M667> findFundVolCheck(SqlParam<M667> params) throws Exception {

		StringBuffer sb = new StringBuffer();
		sb.append(" SELECT ABNORMAL_SERNO, ABNORMAL_TYPE, TRANS_ACCT_NO, TRANS_DATE, TRANS_TIME, BUSI_CODE, TRANS_AMT, BEGIN_DATE, END_DATE, CUST_NO, TA_ACCT_NO, TANO, PROD_CODE, ABNORMAL_DESC, SYS_AMT, SYS_VOL, OTHER_AMT, OTHER_VOL, TA_NAME, PROD_NAME, ACCT_NO, CUST_NAME\n" +
				"FROM FUND_CUST_ABNORMAL_LOG t  order by t.trans_date desc ");
		return super.findRows(sb.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
