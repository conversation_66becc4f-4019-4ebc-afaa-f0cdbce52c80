package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M604;
import com.kayak.fund.model.M607;
import org.springframework.stereotype.Repository;

@Repository
public class M607Dao extends ComnDao {


    public SqlResult<M607> findFundStatusBusiCfgInfos(SqlParam<M607> params) throws Exception {
        return super.findRows("select tano,busi_code,legal_code,allow_flag,err_msg,data_status,create_time,update_time,prod_code from fund_prod_busi_limit",SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public UpdateResult addFundStatusBusiCfgInfo(SqlParam<M607> params) throws Exception {
        String mysql = "INSERT INTO fund_prod_busi_limit( tano,busi_code,legal_code,allow_flag,err_msg,data_status,create_time,update_time,prod_code) "+
                "VALUES($S{tano}, $S{busiCode}, $S{legalCode}, $S{allowFlag}, $S{errMsg}, $S{dataStatus}, sysdate(), sysdate(), $S{prodCode})";
        String oracle = "INSERT INTO fund_prod_busi_limit( tano,busi_code,legal_code,allow_flag,err_msg,data_status,create_time,update_time,prod_code) " +
                "VALUES($S{tano}, $S{busiCode}, $S{legalCode}, $S{allowFlag}, $S{errMsg}, $S{dataStatus}, sysdate, sysdate, $S{prodCode})";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public UpdateResult deleteFundStatusBusiCfgInfo(SqlParam<M607> params) throws Exception {
        return super.update("DELETE FROM fund_prod_busi_limit WHERE tano = $S{tano} and busi_code = $S{busiCode}",SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public UpdateResult updateCustManagerInfo(SqlParam<M607> params) throws Exception {
        String mysql = "UPDATE fund_prod_busi_limit set tano = $S{tano}, busi_code = $S{busiCode}, allow_flag = $S{allowFlag}, err_msg = $S{errMsg}, data_status = $S{dataStatus},update_time = sysdate() WHERE tano = $S{tano} and busi_code = $S{busiCode} ";
        String oracle = "UPDATE fund_prod_busi_limit set tano = $S{tano}, busi_code = $S{busiCode}, allow_flag = $S{allowFlag}, err_msg = $S{errMsg}, data_status = $S{dataStatus},update_time = sysdate WHERE tano = $S{tano} and busi_code = $S{busiCode} ";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }
}
