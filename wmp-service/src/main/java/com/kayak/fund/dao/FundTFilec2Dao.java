package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.FundTFilec2;
import com.kayak.fund.model.FundTFilec2;
import org.springframework.stereotype.Repository;

@Repository
public class FundTFilec2Dao extends ComnDao {

    public SqlResult<FundTFilec2> findFundTFilec2(SqlParam<FundTFilec2> params) throws Exception {
        return super.findRows("select chargepaymethod from fund_t_filec2 where 1=1 \n ",SubDatabase.DATABASE_FUND_CENTER, params);
    }

}
