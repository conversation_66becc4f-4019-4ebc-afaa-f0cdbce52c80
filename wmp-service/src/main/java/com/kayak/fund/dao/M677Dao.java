package com.kayak.fund.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.system.constants.SystemParamConstants;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M677;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class M677Dao extends ComnDao {

    public SqlResult<M677> find(SqlParam<M677> params) throws Exception {
        return super.findRows("SELECT * FROM sys_param ",SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public void update(List<M677> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (M677 p : params) {
                super.update("UPDATE sys_param SET paravalue = $S{paravalue} WHERE paraid = $S{paraid}",SubDatabase.DATABASE_FUND_CENTER, p);
            }
        });
    }

    //gx
    public SqlResult<M677> findSysParams(SqlParam<M677> params) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder("SELECT " +
                " p.moduleid, " +
                " p.paraid, " +
                " p.paravalue, " +
                " p.paraname, " +
                " p.groupparaid, " +
                " p.dict, " +
                " p.functype, " +
                " p.confoption, " +
                " p.isedit, " +
                " p.isdisplay " +
                " FROM SYS_PARAM p " +
                " WHERE isdisplay = '" + SystemParamConstants.SHOW + "'  ");

        M677 model = params.getModel();


        /** if (Tools.isNotBlank(model.getParavalue())) {
            sqlBuilder.append(" AND ( p.dict IS NOT NULL AND p.paravalue LIKE '%$U{paravalue}%') ");
        }*/

        if (Tools.isNotBlank(model.getParaname())) {
            sqlBuilder.append(" AND p.paraname LIKE '%$U{paraname}%' ");
        }

        sqlBuilder.append(" order by PARAID ");

        return super.findRows(sqlBuilder.toString(),SubDatabase.DATABASE_FUND_CENTER, params);
    }

    //添加
    public UpdateResult addSysParam(SqlParam<M677> params) throws Exception {
        return super.update("INSERT INTO SYS_PARAM(moduleid,paraid,paravalue,paraname,groupparaid,dict,functype,confoption,isdisplay) VALUES($S{moduleid},$S{paraid},$S{paravalue},$S{paraname},$S{groupparaid},$S{dict},$S{functype},$S{confoption},$S{isdisplay})",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    //修改
    public UpdateResult updateSysParam(SqlParam<M677> params) throws Exception {
        return super.update("UPDATE SYS_PARAM SET paravalue=$S{paravalue} ,paraname=$S{paraname}   WHERE  moduleid=$S{moduleid} and paraid=$S{paraid} ",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    //删除
    public UpdateResult deleteSysParam(SqlParam<M677> params) throws Exception {
        return super.update("DELETE FROM SYS_PARAM WHERE moduleid=$S{moduleid} and paraid=$S{paraid} ",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    /**
     * 查询某个系统参数
     *
     * @param params
     * @return
     * @throws Exception
     */
    public M677 findOne(SqlParam<M677> params) throws Exception {
        // oracle 不支持limit 1
        String sql = "SELECT paravalue ,paraname ,paraid ,moduleid ,isdisplay ,groupparaid ,graphql ,functype ,fieldtype ,execaction ,dict ,confoption FROM sys_param";
        SqlResult<M677> row = super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
        if (row != null && row.getRows().size() >= 1) {
            return row.getRows().get(0);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Description 查询基金业务参数系统休市时间
     * @Date 2021/12/21
     * @Param [params]
     * @return com.kayak.core.sql.SqlResult<com.kayak.fund.model.M677>
     **/
    public SqlResult<M677> findParaValue(SqlParam<M677> params) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder("SELECT " +
                " p.moduleid, " +
                " p.paraid, " +
                " p.paravalue, " +
                " p.paraname, " +
                " p.groupparaid, " +
                " p.dict, " +
                " p.functype, " +
                " p.confoption, " +
                " p.isedit, " +
                " p.isdisplay " +
                " FROM SYS_PARAM p " +
                " WHERE p.paraid='50000005'  ");

        return super.findRows(sqlBuilder.toString(),SubDatabase.DATABASE_FUND_CENTER, params);
    }
}
