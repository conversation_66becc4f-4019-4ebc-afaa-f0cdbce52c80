package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.fund.model.M639;
import com.kayak.system.model.AnnounceInfo;
import com.kayak.system.model.AnnounceRole;

import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M639Dao extends ComnDao {

    public SqlResult<M639> find(SqlParam<M639> params) throws Exception {
        return super.findRows("select t.rule_no,\n" +
                "       t.rule_desc,\n" +
                "       t.sign_date,\n" +
                "       t.sign_year,\n" +
                "       t.final_date,\n" +
                "       t.final_amount,\n" +
                "       t.ave_begin,\n" +
                "       t.ave_end,\n" +
                "       t.ave_amount,\n" +
                "       t.prod_ever,\n" +
                "       t.rule_input1,\n" +
                "       t.rule_input2,\n" +
                "       t.create_time,\n" +
                "       t.update_time\n" +
                "  from FINA_OLDCUST_RULE t \n" +
                "  where 1=1 ORDER BY t.SIGN_DATE desc ", SubDatabase.DATABASE_FINA_CENTER,params);
    }
    public SqlResult<SqlRow> findDict(SqlParam<M639> params) throws Exception {
        List<SqlRow> list = super.findRows(SqlRow.class,"select t.rule_no as label,t.rule_no as value \n" +
                "  from FINA_OLDCUST_RULE t \n" +
                "  where 1=1 ", SubDatabase.DATABASE_FINA_CENTER,params);
        SqlResult<SqlRow> sqlResult = new SqlResult<>();
        sqlResult.setRows(list);
        return sqlResult;
    }
    public M639 get(String ruleNo) throws Exception {
        return super.findRow(M639.class, "SELECT * FROM FINA_OLDCUST_RULE a WHERE rule_no = $S{ruleNo}", SubDatabase.DATABASE_FINA_CENTER, ruleNo);
    }

    public SqlResult<AnnounceInfo> show(SqlParam<AnnounceInfo> params) throws Exception {
        return super.findRows("SELECT a.* FROM sys_announce a " +
                " JOIN sys_announce_role r ON a.annid = r.annid " +
                " WHERE r.roleid = $S{roleid} " +
                " AND effective_date <= $S{effectiveDate} " +
                " AND invalid_date >= $S{invalidDate}", params);
    }

    public void add(SqlParam<M639> param) throws Exception {
            M639 model = param.getModel();
           super.update(
                    "insert into FINA_OLDCUST_RULE\n" +
                            "  (RULE_NO,\n" +
                            "   RULE_DESC,\n" +
                            "   SIGN_DATE,\n" +
                            "   SIGN_YEAR,\n" +
                            "   FINAL_DATE,\n" +
                            "   FINAL_AMOUNT,\n" +
                            "   AVE_BEGIN,\n" +
                            "   AVE_END,\n" +
                            "   AVE_AMOUNT,\n" +
                            "   PROD_EVER,\n" +
                            "   create_time,\n" +
                            "   update_TIME)\n" +
                            "values\n" +
                            "  ($S{ruleNo},\n" +
                            "   $S{ruleDesc},\n" +
                            "   $S{signDate},\n" +
                            "   $S{signYear},\n" +
                            "   $S{finalDate},\n" +
                            "   $S{finalAmount},\n" +
                            "   $S{aveBegin},\n" +
                            "  $S{aveEnd},\n" +
                            "   $S{aveAmount},\n" +
                            "   $S{prodEver},\n" +
                            "   sysdate(),current_timestamp) ", SubDatabase.DATABASE_FINA_CENTER,model);
    }


    public void edit(SqlParam<M639> param) throws Exception {
            M639 model = param.getModel();
            super.update(
                    "UPDATE FINA_OLDCUST_RULE\n" +
                            "   SET RULE_DESC    = $S{ruleDesc},\n" +
                            "       SIGN_DATE    = $S{signDate},\n" +
                            "       SIGN_YEAR    = $S{signYear},\n" +
                            "       FINAL_DATE   = $S{finalDate},\n" +
                            "       FINAL_AMOUNT = $S{finalAmount},\n" +
                            "       AVE_BEGIN    = $S{aveBegin},\n" +
                            "       AVE_END      = $S{aveEnd},\n" +
                            "       AVE_AMOUNT   = $S{aveAmount},\n" +
                            "       PROD_EVER    = $S{prodEver},\n" +
                            "       update_time     = sysdate()\n" +
                            " WHERE RULE_NO = $S{ruleNo} ", SubDatabase.DATABASE_FINA_CENTER, model);

    }

    public int delete(String ruleNo) throws Exception {
        return super.update("DELETE FROM FINA_OLDCUST_RULE WHERE rule_no = $S{ruleNo}", SubDatabase.DATABASE_FINA_CENTER, ruleNo).getEffect();
    }

    /**
     * 批量插入公告-角色关系表
     * @param annid
     * @param roleIds
     * @throws Exception
     */
    private void bulkInsertAnnounceRole(String annid, List<String> roleIds) throws Exception {
        if (roleIds == null || roleIds.size() == 0) {
            return;
        }

       /** for (String roleId : roleIds) {
            super.update("INSERT INTO sys_announce_role (annid, roleid) " +
                    "VALUES ($S{annid}, $S{roleid});", new AnnounceRole(annid, roleId));
        }*/
    }
}
