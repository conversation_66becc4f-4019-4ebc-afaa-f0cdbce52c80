package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.SequenceIncreaseUtil;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M608;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M608Dao extends ComnDao {

    @Autowired
    private SequenceIncreaseUtil sequenceIncreaseUtil;

    public SqlResult<M608> findFundStatusBusiCfgs(SqlParam<M608> params) throws Exception {
        SqlResult<M608> rows = super.findRows("SELECT train_no ,title,content,personnel ,address,take_times ,train_date,train_time_desc ,input_userid,last_upd_time " +
                "FROM fund_sales_train_record", SubDatabase.DATABASE_FUND_CENTER, params);
        return rows;
    }

    public UpdateResult addFundStatusBusiCfg(SqlParam<M608> params) throws Exception {
        String trainNo = sequenceIncreaseUtil.increaseNum("fund_sales_train_record:train_no",SubDatabase.DATABASE_FUND_CENTER);
        params.getModel().setTrainNo(trainNo);
        String mysql = "INSERT INTO fund_sales_train_record(train_no ,title,content,personnel ,address,take_times ,train_date,train_time_desc ,input_userid ) VALUES($S{trainNo} ,$S{title},$S{content},$S{personnel} ,$S{address},$S{takeTimes},$S{trainDate},$S{trainTimeDesc},$S{inputUserid})";
        String oracle = "INSERT INTO fund_sales_train_record(train_no ,title,content,personnel ,address,take_times ,train_date,train_time_desc ,input_userid,last_upd_time ) VALUES($S{trainNo} ,$S{title},$S{content},$S{personnel} ,$S{address},$S{takeTimes},$S{trainDate},$S{trainTimeDesc},$S{inputUserid},current_timestamp)";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult updateFundStatusBusiCfg(SqlParam<M608> params) throws Exception {
        String mysql = "UPDATE fund_sales_train_record SET title = $S{title},content = $S{content},personnel = $S{personnel} ,address=$S{address},take_times = $S{takeTimes} ,train_date=$S{trainDate},train_time_desc=$S{trainTimeDesc} ,input_userid=$S{inputUserid} WHERE train_no=$S{trainNo}";
        String oracle = "UPDATE fund_sales_train_record SET title = $S{title},content = $S{content},personnel = $S{personnel} ,address=$S{address},take_times = $S{takeTimes} ,train_date=$S{trainDate},train_time_desc=$S{trainTimeDesc} ,input_userid=$S{inputUserid},last_upd_time = current_timestamp WHERE train_no=$S{trainNo}";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult deleteFundStatusBusiCfg(SqlParam<M608> params) throws Exception {
        return super.update("DELETE FROM fund_sales_train_record WHERE train_no=$S{trainNo} ", SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

}
