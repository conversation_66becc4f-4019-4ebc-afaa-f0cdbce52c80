package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;

import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M684;
import org.springframework.stereotype.Repository;

@Repository
public class M684Dao extends ComnDao {

    public SqlResult<M684> findM684s(SqlParam<M684> params) throws Exception {
        return super.findRows("select t.tano,t.APPLY_SERNO,\n" +
                "       t.capitaltype,\n" +
                "       t.PAYMENTDAY,\n" +
                "       t.paymentamount,\n" +
                "       t.out_acct_no,\n" +
                "       t.in_acct_no,\n" +
                "       t.SUMMARY,\n" +
                "       t.EXEC_STATUS,\n" +
                "       t.returncode,\n" +
                "       t.returnmsg,t.TRANSFER_STATUS\n" +
                "  from fund_m_tafee t\n" +
                " where 1=1 \n", SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public SqlResult<M684> findTano(SqlParam<M684> params) throws Exception {
        return super.findRows("SELECT " +
                " t1.tano,t1.ta_name " +
                " FROM fund_ta_info t1 " , SubDatabase.DATABASE_FUND_CENTER, params);
    }

}
