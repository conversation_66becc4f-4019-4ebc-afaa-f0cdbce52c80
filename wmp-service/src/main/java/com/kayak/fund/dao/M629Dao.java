package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M629;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 */
@Repository
public class M629Dao extends ComnDao {


	/**
	 * 频繁开销户明细
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M629> findFundCustTransReqLog(SqlParam<M629> params) throws Exception {
		StringBuffer sb = new StringBuffer(" select * from (");
		sb.append(" select * from (select t1.TRANS_ORGNO,\n" +
				"		t1.busi_date,\n" +
				"       t1.prod_code,\n" +
				"       t1.prod_name,\n" +
				"       sum(t1.app_amt) as app_amt,\n" +
				"       sum(t1.app_vol) as app_vol,\n" +
				"       t1.tano,\n" +
				"       t1.ta_name,\n" +
				"       t1.busi_code,\n" +
				"       sum(case\n" +
				"             when t1.channel_flag != '2' then\n" +
				"              t1.app_amt\n" +
				"             else\n" +
				"              0\n" +
				"           end) as online_app_amt,\n" +
				"       sum(case\n" +
				"             when t1.channel_flag != '2' then\n" +
				"              t1.app_vol\n" +
				"             else\n" +
				"              0\n" +
				"           end) as online_app_vol,\n" +
				"           sum(t2.trans_fee) as trans_fee\n" +
				"  from fund_cust_trans_cfm_log t2\n" +
				"  left join fund_cust_trans_req_log t1 on t1.app_serno = t2.app_serno where t1.system_no='FUND' ");
		//查询条件
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append(" and t1.busi_date >='"+params.getModel().getBusiDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append(" and t1.busi_date <='"+params.getModel().getBusiEndDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
			sb.append(" and t1.tano like'%"+params.getModel().getTano()+"%'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTransOrgno())){
			sb.append(" and t1.TRANS_ORGNO ='"+params.getModel().getTransOrgno()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getProdCode())){
			sb.append(" and t1.prod_code like'%"+params.getModel().getProdCode()+"%'");
		}
			sb.append("  group by t1.TRANS_ORGNO,\n" +
					"			t1.busi_date,\n" +
					"          t1.prod_code,\n" +
					"          t1.prod_name,\n" +
					"          t1.tano,\n" +
					"          t1.ta_name,\n" +
					"          t1.busi_code,\n" +
					"          t2.trans_fee ");
		sb.append(" order by t1.busi_date desc ) union all select * from (");
		sb.append("select t1.TRANS_ORGNO,\n" +
				"		t1.busi_date,\n" +
				"       t1.prod_code,\n" +
				"       t1.prod_name,\n" +
				"       sum(t1.app_amt) as app_amt,\n" +
				"       sum(t1.app_vol) as app_vol,\n" +
				"       t1.tano,\n" +
				"       t1.ta_name,\n" +
				"       t1.busi_code,\n" +
				"       sum(case\n" +
				"             when t1.channel_flag != '2' then\n" +
				"              t1.app_amt\n" +
				"             else\n" +
				"              0\n" +
				"           end) as online_app_amt,\n" +
				"       sum(case\n" +
				"             when t1.channel_flag != '2' then\n" +
				"              t1.app_vol\n" +
				"             else\n" +
				"              0\n" +
				"           end) as online_app_vol,\n" +
				"           sum(t2.trans_fee) as trans_fee\n" +
				"  from fund_cust_trans_cfm_log_h t2 \n" +
				"  left join fund_cust_trans_req_log t1 on t1.app_serno = t2.app_serno where t1.system_no='FUND' ");
		//查询条件
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append(" and t1.busi_date >='"+params.getModel().getBusiDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append(" and t1.busi_date <='"+params.getModel().getBusiEndDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
			sb.append(" and t1.tano like'%"+params.getModel().getTano()+"%'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTransOrgno())){
			sb.append(" and t1.TRANS_ORGNO ='"+params.getModel().getTransOrgno()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getProdCode())){
			sb.append(" and t1.prod_code like'%"+params.getModel().getProdCode()+"%'");
		}
		sb.append("  group by t1.TRANS_ORGNO,\n" +
				"			t1.busi_date,\n" +
				"          t1.prod_code,\n" +
				"          t1.prod_name,\n" +
				"          t1.tano,\n" +
				"          t1.ta_name,\n" +
				"          t1.busi_code,\n" +
				"          t2.trans_fee");
		sb.append("  order by t1.busi_date desc ) ) ");
		List<M629> volList = super.findRows(M629.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M629> sqlRowSqlResult = new SqlResult<>();
		sqlRowSqlResult.setResults(volList.size());
		sqlRowSqlResult.setRows(
				volList.subList(
						params.getStart(), Math.min(params.getStart() + params.getLimit(), volList.size())
				)
		);
		sqlRowSqlResult.setDesensitized(false);
		return sqlRowSqlResult;
	}

}