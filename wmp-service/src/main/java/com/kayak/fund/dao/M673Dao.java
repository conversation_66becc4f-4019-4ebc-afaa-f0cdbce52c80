package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M673;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class M673Dao extends ComnDao {

    public SqlResult<M673> findM673s(SqlParam<M673> param) throws  Exception{
        param.setMakeSql(true);
        String sql = "select * from fund_sale_fee_divide";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public List<M673> ListM673(M673 param) throws  Exception{
        String sql = "select * from fund_sale_fee_divide\n"
                +" where prod_code = $S{prodCode} and tano = $S{tano} and begin_date = $S{beginDate} and end_date = $S{endDate}";
        return super.findRows(M673.class,sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public List<SqlRow> findVolStock(M673 param) throws  Exception{
        String sql = "select tano,orgno,prod_code,\n"
                +"sum(case when cust_type = '1' then stock_vol else 0 end) as per_vol,\n"
                +"sum(case when cust_type = '0' then stock_vol else 0 end) as org_vol,\n"
                +"sum(case when cust_type = '0' or cust_type = '1' then stock_vol else 0 end) as vol\n"
                +" from fund_vol_stock \n"
                +" where prod_code = $S{prodCode} and tano = $S{tano} and trans_date >= $S{beginDate} and trans_date <= $S{endDate}\n"
                +"group by tano,orgno,prod_code";
        return super.findRows(SqlRow.class,sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public List<SqlRow> findSaleStock(M673 param) throws  Exception{
        String sql = "select tano,orgno,prod_code,sum(stock_amt) stock_amt\n" +
                "from fund_sale_stock\n"+
                " where prod_code = $S{prodCode} and tano = $S{tano} and trans_date >= $S{beginDate} and trans_date <= $S{endDate}\n"+
                "group by tano,orgno,prod_code";;
        return super.findRows(SqlRow.class,sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    //批量修改
    public void updateList(List<M673> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (M673 p : params) {
                String sql = "UPDATE fund_sale_fee_divide SET \n" +
                        "adjust_amt=$S{adjustAmt},actual_divide_amt=$S{actualDivideAmt},out_acct_no=$S{outAcctNo},in_acct_no=$S{inAcctNo}\n"+
                        " where orgno = $S{orgno} and prod_code = $S{prodCode} and tano = $S{tano} and begin_date = $S{beginDate} and end_date = $S{endDate}";

                super.update(sql,SubDatabase.DATABASE_FUND_CENTER, p);
            }
        });
    }

    //批量添加
    public void addList(List<M673> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (M673 p : params) {
                String sql = "INSERT INTO fund_sale_fee_divide\n" +
                        "(tano,prod_code,orgno,begin_date,end_date,\n" +
                        "total_amount,bank_ratio,refer_divide_type,total_stock,\n" +
                        "org_stock,refer_divide_amt,adjust_amt,actual_divide_amt,\n" +
                        "transfer_status,out_acct_no,in_acct_no,trans_date,fee_type)\n" +
                        "VALUES ($S{tano},$S{prodCode},$S{orgno},$S{beginDate},$S{endDate},\n" +
                        "$S{totalAmount},$S{bankRatio},$S{referDivideType},$S{totalStock},\n" +
                        "$S{orgStock},$S{referDivideAmt},$S{adjustAmt},$S{actualDivideAmt},\n" +
                        "$S{transferStatus},$S{outAcctNo},$S{inAcctNo},$S{transDate},$S{feeType})\n";
                super.update(sql,SubDatabase.DATABASE_FUND_CENTER, p);
            }
        });
    }

    //添加
    public UpdateResult add673(SqlParam<M673> params) throws Exception {
        String sql = "INSERT INTO fund_sale_fee_divide\n" +
                "(tano,prod_code,orgno,begin_date,end_date,\n" +
                "total_amount,bank_ratio,refer_divide_type,total_stock,\n" +
                "org_stock,refer_divide_amt,adjust_amt,actual_divide_amt,\n" +
                "transfer_status,out_acct_no,in_acct_no)\n" +
                "VALUES ($S{tano},$S{prodCode},$S{orgno},$S{beginDate},$S{endDate},\n" +
                "$S{totalAmount},$S{bankRatio},$S{referDivideType},$S{totalStock},\n" +
                "$S{orgStock},$S{referDivideAmt},$S{adjustAmt},$S{actualDivideAmt},\n" +
                "$S{transferStatus},$S{outAcctNo},$S{inAcctNo})\n";
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    //修改
    public UpdateResult update673(SqlParam<M673> params) throws Exception {
        String sql = "UPDATE fund_sale_fee_divide SET \n" +
                "adjust_amt=$S{adjustAmt},actual_divide_amt=$S{actualDivideAmt},\n" +
                "transfer_status=$S{transferStatus},out_acct_no=$S{outAcctNo},in_acct_no=$S{inAcctNo}\n"+
                " where orgno = $S{orgno} and prod_code = $S{prodCode} and tano = $S{tano} and begin_date = $S{beginDate} and end_date = $S{endDate}";
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER, params);
    }
}
