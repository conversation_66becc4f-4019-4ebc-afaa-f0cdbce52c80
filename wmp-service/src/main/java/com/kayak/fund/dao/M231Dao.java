package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;

import com.kayak.fina.param.model.M514;
import com.kayak.fund.model.M231;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 原名 fundTaInfoDao TA信息服务
 */
@Repository
public class M231Dao extends ComnDao {

    //校验销售商与产品是否可以关联
    public void disprodformyActionbefor(Map<String, Object> params) throws Exception {
        String sqlAll = "select decode (instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fund_ta_info" +
                "              where tano = $S{tano})),null,0,instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fund_ta_info" +
                "              where tano = $S{tano})) )result " +
                "  from DUAL";
        String sqlDb2 = "select decode (instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fund_ta_info" +
                "              where tano = $S{tano})),null,0,instr((select case" +
                "                       when (REDEEM_CFM_M = '0' OR APPLY_CFM_M = '0' OR" +
                "                            SUBS_CFM_N = '0') then" +
                "                        '0'" +
                "                       else" +
                "                        '01'" +
                "                     end" +
                "                from ta_prod_open" +
                "               where prod_code = $S{prodCode}),(select batch_no" +
                "               from fund_ta_info" +
                "              where tano = $S{tano})) )result " +
                "  from sysibm.sysdummy1";
        Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);
        List<SqlRow> datas = super.findRows(SqlRow.class, sql, SubDatabase.DATABASE_FUND_CENTER, params);

        if (datas.get(0).getInteger("result") < 1) {
            throw new PromptException("M23103", " 产品 [" + params.get("prodCode") + "] 不可与该销售商关联,请关闭当前页再试");
        }
    }


    public void disprodformyAction(List<Map<String, Object>> params) throws Exception {

        doTrans(() -> {
            for (Map<String, Object> param : params) {
                String sqlAll = "INSERT INTO ta_prod_distributor (" +
                        " tano,  prod_code," +
                        " status," +
                        " create_time," +
                        " update_time" +
                        " )" +
                        " VALUES(" +
                        " $S{tano}, $S{prodCode}," +
                        " '1'," +
                        " current_timestamp," +
                        " current_timestamp" +
                        ")";
                String sqlDb2 = "INSERT INTO ta_prod_distributor (" +
                        " tano,  prod_code," +
                        " status," +
                        " create_time," +
                        " update_time" +
                        " )" +
                        " VALUES(" +
                        " $S{tano}, $S{prodCode}," +
                        " '1'," +
                        " current timestamp," +
                        " current timestamp" +
                        ")";
                Sql sql = Sql.build().oracleSql(sqlAll).db2Sql(sqlDb2);

                super.update(sql, SubDatabase.DATABASE_FUND_CENTER, param);
            }
        });
    }

    //统计该tano记录数
    public int findTaInfoCounts(Map<String, Object> params) throws Exception {
        String sql = "SELECT COUNT(1) count FROM fund_ta_info WHERE tano = $S{tano} ";
        List<SqlRow> datas = super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
        return datas.get(0).getInteger("count");
    }

    public SqlResult<M231> findTaDistributorInfosByTaskGroup(SqlParam<M231> params, String sql) throws Exception {
        return findRows("SELECT " +
                "tano,ta_name,ta_simplify_name,ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code," +
                "tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type," +
                "is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list," +
                "check_type,is_predistribution_acct,present_confirm_num,create_time,create_user,update_time,update_user,task_group,file_imp_flag,is_holidays_send," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_import_c6_26_file,remark,pgmno,legal_code,is_own_ta," +
                "imp_task_group,exp_task_group" +
                " FROM fund_ta_info " + sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }

    //查询TA信息
    public SqlResult<M231> findfundTaInfos(SqlParam<M231> params) throws Exception {

        String sql = "SELECT ta.tano,\n" +
                "       ta.ta_name,\n" +
                "       ta.ta_simplify_name,\n" +
                "       ta.ta_status,\n" +
                "       ta.norm_legal_code,\n" +
                "       ta.norm_legal_type,\n" +
                "       ta.norm_legal_id_code,\n" +
                "       ta.tech_connector,\n" +
                "       ta.tech_connector_mobile,\n" +
                "       ta.busi_connector,\n" +
                "       ta.busi_connector_mobile,\n" +
                "       ta.address,\n" +
                "       ta.email,\n" +
                "       ta.postcode,\n" +
                "       ta.fax,\n" +
                "       ta.interface_type,\n" +
                "       ta.interface_id,\n" +
                "       ta.is_import_c1c5_file,\n" +
                "       ta.is_import_c6_26_file,\n" +
                "       ta.is_import_sale_fee_file,\n" +
                "       ta.allow_break_redeem,\n" +
                "       ta.is_trans_much_acct,\n" +
                "       ta.is_single_trust,\n" +
                "       ta.convert_ack_method,\n" +
                "       ta.is_vol_list,\n" +
                "       ta.check_type,\n" +
                "       ta.is_predistribution_acct,\n" +
                "       ta.present_confirm_num,\n" +
                "       ta.create_time,\n" +
                "       ta.create_user,\n" +
                "       ta.update_time,\n" +
                "       ta.update_user,\n" +
                "       ta.remark,\n" +
                "       ta.file_imp_flag,\n" +
                "       ta.is_holidays_send,\n" +
                "       ta.fundday_file_path,\n" +
                "       ta.cfm_file_path,\n" +
                "       ta.req_file_path,\n" +
                "       ta.freez_file_type,\n" +
                "       ta.pgmno,\n" +
                "       ta.is_own_ta,\n" +
                "       ta.send_type,\n" +
                "       ta.ta_type,\n" +
                "       ta.is_elec_contract,\n" +
                "       ta.elec_contract_TYPE,\n" +
                "       ta.support_trust_type,\n" +
                "       tii.INTERFACE_FILE_NAME,ta.SUBS_TRANSFER_TIME," +
                "       ta.APPLY_TRANSFER_TIME," +
                "       ta.SUBS_REFUND_ACCT," +
                "       ta.APPLY_REFUND_ACCT" +
                "  FROM fund_ta_info ta\n" +
                "  LEFT JOIN wmp_interface_version_info tii ON ta.interface_id =\n" +
                "  tii.interface_version\n" +
                " ORDER BY ta.create_time desc \n";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }
    
    //增加TA信息
    public int addTaInfo(SqlParam<M231> params) throws Exception {
        String sql = "INSERT INTO fund_ta_info (" +
                "     tano, ta_name," +
                "     ta_status," +
                "     norm_legal_code,      norm_legal_type," +
                "     norm_legal_id_code,   tech_connector," +
                "     tech_connector_mobile,  busi_connector,  busi_connector_mobile," +
                "     address,           email," +
                "     fax,               postcode," +
                "     interface_type,    interface_id," +
                "     is_import_c1c5_file, is_import_c6_26_file, is_import_sale_fee_file," +
                "     allow_break_redeem,   is_trans_much_acct," +
                "     is_single_trust,    convert_ack_method," +
                "     is_vol_list," +
                "     check_type," +
                "     remark,req_file_path,cfm_file_path," +
                "     fundday_file_path,is_holidays_send, create_time,create_user," +
                "     update_time,update_user,pgmno,imp_task_group,exp_task_group," +
                "     legal_code,is_own_ta,is_predistribution_acct,SEND_TYPE,is_ELEC_CONTRACT,ELEC_CONTRACT_TYPE,TA_TYPE,SUPPORT_TRUST_TYPE,SUBS_TRANSFER_TIME," +
                "     APPLY_TRANSFER_TIME," +
                "       SUBS_REFUND_ACCT," +
                "      APPLY_REFUND_ACCT)" +
                "     VALUES(" +
                "     $S{tano}, $S{taName}," +
                "     $S{taStatus}," +
                "     $S{normLegalCode},       $S{normLegalType}," +
                "     $S{normLegalIdCode}," +
                "     $S{techConnector},   $S{techConnectorMobile}," +
                "     $S{busiConnector},   $S{busiConnectorMobile}," +
                "     $S{address},          $S{email}," +
                "     $S{fax},              $S{postcode}," +
                "     $S{interfaceType},   $S{interfaceId}," +
                "     $S{isImportC1c5File},$S{isImportC626File},$S{isImportSaleFeeFile}," +
                "     $S{allowBreakRedeem},  $S{isTransMuchAcct}," +
                "     $S{isSingleTrust},     $S{convertAckMethod}," +
                "     $S{isVolList}," +
                "     $S{checkType}," +
                "     $S{remark}," +
                "     $S{reqFilePath},$S{cfmFilePath},$S{funddayFilePath}," +
                "     $S{isHolidaysSend}," +
                "     current_timestamp,$S{createser}," +
                "     current_timestamp,$S{updateUser}," +
                "     $S{pgmno},$S{impTaskGroup},$S{expTaskGroup}, " +
                "     $S{superLegalCode}, $S{isOwnTa},$S{isPredistributionAcct},$S{sendType},$S{isElecContract},$S{elecContractType},$S{taType},$S{supportTrustType},$S{subsTransferTime}," +
                "$S{subsRefundAcct},$S{applyTransferTime},$S{applyRefundAcct})";


        return super.update(sql, SubDatabase.DATABASE_FUND_CENTER, params.getModel()).getEffect();
    }

    //修改TA信息
    public void updateTaInfo(SqlParam<M231> params) throws Exception {

        doTrans(() -> {
            this.updateTa(params);
//            this.addHistory(params, OpenFlag.UPDATE);
        });
    }

    //删除销售商信息和清算组成员配置表信息
    public int deleteTaDistributorInfo(SqlParam<M231> params) throws Exception {
        doTrans(() -> {
                    super.update("DELETE FROM TA_CLEAR_GROUP_MEMBER WHERE group_member = $S{tano}", SubDatabase.DATABASE_FUND_CENTER, params.getModel().getTano());
                    super.update("DELETE FROM fund_ta_info WHERE tano = $S{tano}", SubDatabase.DATABASE_FUND_CENTER,
                            params.getModel()).getEffect();
                }
        );
        return 1;
    }


    public void startTaDistributorInfo(SqlParam<M231> params) throws Exception {

        doTrans(() -> {
            super.update(
                    " UPDATE fund_ta_info" +
                        " SET ta_status = '1'" +
                        " WHERE tano = $S{tano} ", SubDatabase.DATABASE_FUND_CENTER, params.getModel());
//            this.addHis(params, OpenFlag.UPDATE);
        });
    }


    public int stopTaDistributorInfoBefor(Map<String, Object> params) throws Exception {

        List<SqlRow> datas = super.findRows("SELECT" +
                " count(*) as count" +
                " FROM prod_quota_info" +
                " WHERE tano = $S{tano}" +
                " AND total_quota > 0", SubDatabase.DATABASE_SYS_CENTER, params);
        return datas.get(0).getInteger("count");
    }


    public void stopTaDistributorInfo(SqlParam<M231> params) throws Exception {

        doTrans(() -> {
//            this.addHis(params, OpenFlag.DELETE);
            super.update(
                    " UPDATE fund_ta_info" +
                        " SET ta_status = '0'" +
                        " WHERE tano = $S{tano} ", SubDatabase.DATABASE_FUND_CENTER, params.getModel()).getEffect();
        });
    }

    //修改TA信息
    public void updateTa(SqlParam<M231> params) throws Exception {
        String sql = " UPDATE fund_ta_info " +
                "               SET ta_name = $S{taName}," +
                "                   ta_status           = $S{taStatus}," +
                "                   norm_legal_code     = $S{normLegalCode}," +
                "                   norm_legal_type     = $S{normLegalType}," +
                "                   norm_legal_id_code  = $S{normLegalIdCode}," +
                "                   tech_connector   = $S{techConnector}," +
                "                   tech_connector_mobile = $S{techConnectorMobile}," +
                "                   busi_connector        = $S{busiConnector}," +
                "                   busi_connector_mobile = $S{busiConnectorMobile}," +
                "                   address            = $S{address}," +
                "                   email              = $S{email}," +
                "                   fax                = $S{fax}," +
                "                   postcode           = $S{postcode}," +
                "                   interface_type     = $S{interfaceType}," +
                "                   interface_id  = $S{interfaceId}," +
                "                   is_import_c1c5_file   = $S{isImportC1c5File}," +
                "                   is_import_c6_26_file   = $S{isImportC626File}," +
                "                   is_import_sale_fee_file   = $S{isImportSaleFeeFile}," +
                "                   allow_break_redeem = $S{allowBreakRedeem}," +
                "                   is_trans_much_acct = $S{isTransMuchAcct}," +
                "                   is_single_trust    = $S{isSingleTrust}," +
                "                   convert_ack_method = $S{convertAckMethod}," +
                "                   is_vol_list        = $S{isVolList}," +
                "                   check_type         = $S{checkType}," +
                "                   remark             = $S{remark}," +
                "                   req_file_path      = $S{reqFilePath}," +
                "                   cfm_file_path      = $S{cfmFilePath}," +
                "                   fundday_file_path  = $S{funddayFilePath}," +
                "                   is_holidays_send   = $S{isHolidaysSend}," +
                "                   pgmno              =$S{pgmno}," +
                "                   is_own_ta          =$S{isOwnTa}," +
                "                   update_user           = $S{createser}," +
                "                   imp_task_group           = $S{impTaskGroup}," +
                "                   exp_task_group           = $S{expTaskGroup}," +
                "                   is_predistribution_acct           = $S{isPredistributionAcct}," +
                "                   IS_ELEC_CONTRACT           = $S{isElecContract}," +
                "                   SEND_TYPE           = $S{sendType}," +
                "                   ELEC_CONTRACT_TYPE           = $S{elecContractType}," +
                "                   TA_TYPE           = $S{taType}," +
                "                   SUPPORT_TRUST_TYPE           = $S{supportTrustType}," +
                "                   SUBS_TRANSFER_TIME           = $S{subsTransferTime}," +
                "                   SUBS_REFUND_ACCT           = $S{subsRefundAcct}," +
                "                   APPLY_TRANSFER_TIME           = $S{applyTransferTime}," +
                "                   APPLY_REFUND_ACCT           = $S{applyRefundAcct}," +
                "                   update_time           = current_timestamp" +
                "  WHERE tano = $S{tano}  ";
        super.update(sql, SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public void addHistory(SqlParam<M231> params, String openFlag) throws Exception {
        super.update(historySql(openFlag) + "  tano = $S{tano} ", SubDatabase.DATABASE_FUND_CENTER, params.getModel()).getEffect();

    }

    public SqlResult<M231> findHistory(SqlParam<M231> params) throws Exception {
        params.setMakeSql(false);
        String sql = "SELECT  "
                + "	dis.tano,dis.ta_name,dis.ta_simplify_name,dis.ta_status,dis.norm_legal_code,"
                + "	dis.norm_legal_type,dis.norm_legal_id_code,dis.tech_connector,dis.tech_connector_mobile,dis.busi_connector,dis.busi_connector_mobile,dis.address,"
                + "	dis.email,dis.postcode,dis.fax,dis.interface_type,dis.interface_id,dis.is_import_c1c5_file,dis.is_import_c6_26_file,dis.is_import_sale_fee_file,"
                + "	dis.allow_break_redeem,dis.is_trans_much_acct,dis.is_single_trust,dis.convert_ack_method,dis.is_vol_list,dis.check_type,"
                + "	dis.is_predistribution_acct,dis.present_confirm_num,dis.create_time,dis.create_user,dis.update_time,dis.update_user,"
                + "	dis.remark,dis.file_imp_flag,dis.is_holidays_send,dis.fundday_file_path,dis.cfm_file_path,dis.req_file_path,"
                + "	dis.freez_file_type, dis.pgmno FROM fund_ta_info_His dis where dis.process_instance_id = $S{processInstanceId} ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public String historySql(String openFlag) {
        return "INSERT INTO fund_ta_info_his( tano,ta_name,ta_simplify_name," +
                "ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code,tech_connector,tech_connector_mobile,busi_connector," +
                "busi_connector_mobile,address,email,postcode,fax,interface_type,interface_id,is_import_c1c5_file,is_import_sale_fee_file," +
                "allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct," +
                "present_confirm_num,create_time,create_user,update_time,update_user,remark,task_group,file_imp_flag,batch_no,oper_user,oper_date,oper_flag," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_holidays_send,is_import_c6_26_file," +
                "process_ta_status,process_instance_id,pgmno,is_own_ta,exp_task_group,imp_task_group)" +
                "  SELECT " +
                "  tano,ta_name,ta_simplify_name," +
                "ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code,tech_connector,tech_connector_mobile,busi_connector," +
                "busi_connector_mobile,address,email,postcode,fax,interface_type,interface_id,is_import_c1c5_file,is_import_sale_fee_file," +
                "allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list,check_type,is_predistribution_acct," +
                "present_confirm_num,create_time,create_user,update_time,update_user,remark,task_group,file_imp_flag,null,$S{createser},current_timestamp,'" + openFlag + "' ," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_holidays_send,is_import_c6_26_file," +
                "process_status,$S{processInstanceId},pgmno,is_own_ta,exp_task_group,imp_task_group " +
                "  FROM fund_ta_info WHERE ";
    }

    public int addHis(SqlParam<M231> param, String openFlag) throws Exception {
        param.setMakeSql(false);
        return super.update(historySql(openFlag) + "  tano= $S{tano}", SubDatabase.DATABASE_FUND_CENTER, param.getModel()).getEffect();
    }

    public SqlResult<M231> select(SqlParam<M231> params) throws Exception {
        return super.findRows("SELECT " +
                "tano,ta_name,ta_simplify_name,ta_status,norm_legal_code,norm_legal_type,norm_legal_id_code," +
                "tech_connector,tech_connector_mobile,busi_connector,busi_connector_mobile,address,email,postcode,fax,interface_type," +
                "is_import_c1c5_file,is_import_sale_fee_file,allow_break_redeem,is_trans_much_acct,is_single_trust,convert_ack_method,is_vol_list," +
                "check_type,is_predistribution_acct,present_confirm_num,create_time,create_user,update_time,update_user,task_group,file_imp_flag,is_holidays_send," +
                "fundday_file_path,cfm_file_path,req_file_path,freez_file_type,is_import_c6_26_file,remark,pgmno,is_own_ta," +
                "imp_task_group,exp_task_group" +
                " FROM fund_ta_info WHERE tano= $S{tano}", SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public SqlRow findTaNum(String tano) throws Exception {
        return super.findRow("SELECT count(1) num  from fund_ta_info where tano = '"+tano+"' and ta_status = '0' ",SubDatabase.DATABASE_FUND_CENTER, tano);
    }

    //删除销售商信息和清算组成员配置表信息
    public int deleteTaInfo(SqlParam<M231> params) throws Exception {
        doTrans(() -> {
                    super.update("DELETE FROM fund_ta_info WHERE tano = $S{tano}", SubDatabase.DATABASE_FUND_CENTER,
                            params.getModel()).getEffect();
                }
        );
        return 1;
    }

    public String findTaName(String tano) throws  Exception{
        String sql = "select ta_name from fund_ta_info where tano = '" + tano + "'" ;
        SqlRow row = super.findRow(sql, SubDatabase.DATABASE_FUND_CENTER,null);
        String taName="";//TA名称
        if (row != null && row.size() >= 1 && StringUtils.isNotBlank(row.getString("ta_name"))){
            taName=row.getString("ta_name");
        }
        return taName;
    }
    /**
     *
     * @param params 传入参数
     * @return  查询版本
     * @throws Exception
     */
    public SqlResult<M231> findTaInterfaceInfoVersion(SqlParam<M231> params) throws Exception {
        return super.findRows("SELECT distinct interface_version, interface_file_name from wmp_interface_version_info", SubDatabase.DATABASE_FUND_CENTER,params);
    }

    /**
     *
     * @param params 接口类型
     * @return  接口版本
     * @throws Exception
     */
    public SqlResult<M231> findInterfaceIdByType(SqlParam<M231> params) throws Exception{
        return super.findRows("SELECT distinct interface_id,interface_name, " +
                "interface_id from wmp_interface_info where interface_type = $S{interfaceType}", SubDatabase.DATABASE_FUND_CENTER,params);
    }

}
