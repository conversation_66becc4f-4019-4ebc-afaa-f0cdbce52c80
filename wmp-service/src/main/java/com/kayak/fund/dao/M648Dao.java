package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M648;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 */
@Repository
public class M648Dao extends ComnDao {


	/**
	 * 频繁开销户明细
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M648> findFummaryFundCustTransReqLog(SqlParam<M648> params) throws Exception {
		StringBuffer sb = new StringBuffer(" select ");
				if(params.getModel().getOrgLevel().equals("1")){
					sb.append(" t.bank_code as org_code,");
				}else if(params.getModel().getOrgLevel().equals("2")){
					sb.append(" t.branch_code as org_code,");
				}else if(params.getModel().getOrgLevel().equals("3")){
					sb.append(" t.sub_branch_code as org_code,");
				}
				sb.append("t.tano,\n" +
						"       t.ta_name,\n" +
						"       t.busi_date,\n" +
						"       t.cust_type， t.prod_code， t.prod_name，\n" +
						//120认购确认+122申购确认
						" sum(decode(t.busi_code, '020', t.app_amt, 0)+decode(t.busi_code, '022',  t.app_amt, 0)) as buy_amt, "+
						" sum(decode(t.busi_code, '020', 1, 0)+decode(t.busi_code, '022', 1, 0)) as buy_num, " +
						//121预约认购确认
						"       sum(decode(t.busi_code, '021', t.app_amt, 0)) as subscribe_buy_amt,\n" +
						"       sum(decode(t.busi_code, '021', 1, 0)) as subscribe_buy_num,\n" +
						//124赎回确认
						"       sum(decode(t.busi_code, '024', t.app_vol, 0)) as redeem_vol,\n" +
						"       sum(decode(t.busi_code, '024', 1, 0)) as redeem_num,\n" +
						//125预约赎回确认
						"       sum(decode(t.busi_code, '025', t.app_vol, 0)) as subscribe_redeem_vol,\n" +
						"       sum(decode(t.busi_code, '025', 1, 0)) as subscribe_redeem_num,\n" +
						//142强制赎回确认
						"       sum(decode(t.busi_code, '042', t.app_vol, 0)) as force_redeem_vol,\n" +
						"       sum(decode(t.busi_code, '042', 1, 0)) as force_redeem_num,\n" +
						//129设置分红方式确认
						"       sum(decode(t.busi_code, '029', 1, 0)) as def_div_method_num,\n" +
						//127转销售人/机构转入确认
						"       sum(decode(t.busi_code, '027', t.app_vol, 0)) as transfer_in_vol,\n" +
						"       sum(decode(t.busi_code, '027', 1, 0)) as transfer_in_num,\n" +
						//128转销售人/机构转出确认
						"       sum(decode(t.busi_code, '028', t.app_vol, 0)) as transfer_out_vol,\n" +
						"       sum(decode(t.busi_code, '028', 1, 0)) as transfer_out_num,\n" +
						//131份额冻结确认
						"       sum(decode(t.busi_code, '031', t.app_vol, 0)) as frozen_vol,\n" +
						"       sum(decode(t.busi_code, '031', 1, 0)) as frozen_num,\n" +
						//132份额解冻确认
						"       sum(decode(t.busi_code, '032', t.app_vol, 0)) as thaw_vol,\n" +
						"       sum(decode(t.busi_code, '032', 1, 0)) as thaw_num,\n" +
						//133非交易过户确认
						"       sum(decode(t.busi_code, '033', t.app_vol, 0)) as trade_vol,\n" +
						"       sum(decode(t.busi_code, '033', 1, 0)) as trade_num,\n" +
						//139定时定额申购确认
						"       sum(decode(t.busi_code, '039', t.app_amt, 0)) as regular_amt,\n" +
						"       sum(decode(t.busi_code, '039', 1, 0)) as regular_num,\n" +
						//自动赎回
						"       sum(decode(t.busi_code, '0', t.app_vol, 0)) as automatic_redeem_vol,\n" +
						"       sum(decode(t.busi_code, '0', 1, 0)) as automatic_redeem_num,\n" +
						//136产品转换确认
						"       sum(decode(t.busi_code, '036', t.app_vol, 0)) as prod_vol,\n" +
						"       sum(decode(t.busi_code, '036', 1, 0)) as prod_num\n" +
						"  FROM fund_cust_trans_req_log t \n" +
						" WHERE 1 = 1 and t.system_no='FUND' ");

		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append("and t.busi_date >= '"+params.getModel().getBusiDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
			sb.append("and t.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getAckDate())){
			sb.append("and t.ack_date >= '"+params.getModel().getBusiDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getAckEndDate())){
			sb.append("and t.ack_date <= '"+params.getModel().getBusiEndDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBelongOrgno())){
			if(params.getModel().getOrgLevel().equals("1")){
				sb.append("and t.bank_code = '"+params.getModel().getBelongOrgno()+"'");
			}else if(params.getModel().getOrgLevel().equals("2")){
				sb.append("and t.branch_code = '"+params.getModel().getBelongOrgno()+"'");
			}else if(params.getModel().getOrgLevel().equals("3")){
				sb.append("and t.sub_branch_code = '"+params.getModel().getBelongOrgno()+"'");
			}
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
			sb.append("and t.tano like '%"+params.getModel().getTano()+"%'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getProdCode())){
			sb.append("and t.prod_code like '%"+params.getModel().getProdCode()+"%'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getCustType())){
			sb.append("and t.cust_type = '"+params.getModel().getCustType()+"'");
		}
			sb.append(" group by ");
		if(params.getModel().getOrgLevel().equals("1")){
			sb.append(" t.bank_code ,");
		}else if(params.getModel().getOrgLevel().equals("2")){
			sb.append(" t.branch_code,");
		}else if(params.getModel().getOrgLevel().equals("3")){
			sb.append(" t.sub_branch_code,");
		}
			sb.append("t.tano,t.ta_name,t.cust_type,t.prod_name,t.prod_code,t.busi_date order by t.busi_date desc ");

		List<M648> volList = super.findRows(M648.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M648> sqlRowSqlResult = new SqlResult<>();
		sqlRowSqlResult.setResults(volList.size());
		sqlRowSqlResult.setRows(volList);
		// 手动分页
		int count = volList.size();
		int start = params.getStart();
		int limit = params.getLimit();
		if (limit == 0){
			sqlRowSqlResult.setRows(volList);
		}else {
			sqlRowSqlResult.setRows(volList.subList(start, Math.min(start + limit, count)));
		}
		sqlRowSqlResult.setResults(count);
		sqlRowSqlResult.setDesensitized(false);
		return sqlRowSqlResult;
	}

}