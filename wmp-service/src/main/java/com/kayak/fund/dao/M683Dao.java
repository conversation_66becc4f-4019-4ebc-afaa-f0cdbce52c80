package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M683;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M683Dao extends ComnDao {

    public SqlResult<M683> findM683(SqlParam<M683> params) throws Exception {
        String custNo = params.getModel().getCustNo();
        params.getModel().setCustNo(null);
        String sql1 = "select * from (select t1.cust_name,t1.cust_no,t2.cust_type,t1.tano,t1.legal_code, " +
                "    t1.prod_code,t2.prod_name,t1.acct_no," +
                "    t1.trans_amt,t2.busi_date,t2.ack_date," +
                "    t1.trans_date,t2.busi_code,t1.capital_status," +
                "    t2.ta_rtn_code,t2.ta_rtn_desc,t1.host_rtn_code," +
                "    t1.host_rtn_desc,t1.branch_code,t1.sub_branch_code," +
                "    t1.cur,to_char(t1.update_time,'yyyymmdd') as update_time,t1.ta_ack_serno," +
                "    t1.trans_acct_no,t2.app_serno,t1.remark,t2.ta_name" +
                "    from PAYM_BATCH_CAPITAL_LOG t1 left join FUND_CUST_TRANS_CFM_LOG t2" +
                "    on t1.ta_ack_serno = t2.ta_ack_serno left join fund_ta_info t3 on t1.tano=t3.tano where 1 = 1 ";
        if(Tools.isNotBlank(custNo)){
            sql1 = sql1 +" and t1.cust_no in ('"+custNo+"')";

        }
        sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(),M683.class);
        sql1 = sql1+" order by t1.trans_date desc nulls last) ";
        String sql2 = "select * from ( select t1.cust_name,t1.cust_no,t2.cust_type,t1.tano,t1.legal_code, " +
                "    t1.prod_code,t2.prod_name,t1.acct_no," +
                "    t1.trans_amt,t2.busi_date,t2.ack_date," +
                "    t1.trans_date,t2.busi_code,t1.capital_status," +
                "    t2.ta_rtn_code,t2.ta_rtn_desc,t1.host_rtn_code," +
                "    t1.host_rtn_desc,t1.branch_code,t1.sub_branch_code," +
                "    t1.cur,to_char(t1.update_time,'yyyymmdd') as update_time,t1.ta_ack_serno," +
                "    t1.trans_acct_no,t2.app_serno,t1.remark,t2.ta_name" +
                "    from PAYM_BATCH_CAPITAL_LOG_H t1 left join FUND_CUST_TRANS_CFM_LOG_H t2" +
                "    on t1.ta_ack_serno = t2.ta_ack_serno left join fund_ta_info t3 on t1.tano=t3.tano where 1 = 1 ";
        if(Tools.isNotBlank(custNo)){
            sql2 = sql2 +" and t1.cust_no in ('"+custNo+"')";

        }
        sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M683.class);
        sql2 = sql2 +" order by t1.trans_date desc nulls last) ";
        String sql =
                "select * from (" + sql1 +
                        " union all \n" +
                        sql2 + " ) ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }


}
