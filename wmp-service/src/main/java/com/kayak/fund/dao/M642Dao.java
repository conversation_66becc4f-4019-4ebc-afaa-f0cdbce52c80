package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M629;
import com.kayak.fund.model.M642;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M642Dao extends ComnDao {

	/**
	 * 销售量统计报表
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M642> findFundCustTransCfmLogInfo(SqlParam<M642> params) throws Exception {
		//params.setMakeSql(true);
		StringBuffer sb = new StringBuffer("");
		sb.append("select distinct t.tano, t.ta_name,t.busi_date,t.prod_code, t.prod_name, sum(t.app_amt) as app_amt\n" +
				"  from ( select * from (select t.tano,\n" +
				"               t.ta_name,\n" +
				"               t.busi_date,\n" +
				"               t.prod_code,\n" +
				"               t.prod_name,\n" +
				"               sum(case\n" +
				"                     when t.busi_code in ('120', '122','159') then\n" +
				"                      t.app_amt\n" +
				"                     else\n" +
				"                      0\n" +
				"                   end) as app_amt\n" +
				"          from fund_cust_trans_cfm_log t\n" +
				"         where 1 = 1 and t.system_no='FUND' ");
				if(params.getModel() != null&& Tools.isNotBlank(params.getModel().getBusiDate())){
					sb.append(" and t.busi_date >='"+params.getModel().getBusiDate()+"'");
				}
				if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
					sb.append(" and t.busi_date <='"+params.getModel().getBusiEndDate()+"'");
				}
				if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
					sb.append(" and t.tano like '%"+params.getModel().getTano()+"%'");
				}
				if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getProdCode())){
					sb.append(" and t.prod_code like '%"+params.getModel().getProdCode()+"%'");
				}
				sb.append("  group by t.tano, t.ta_name, t.prod_code, t.prod_name, t.busi_date order by t.busi_date desc )\n" +
							"        union all \n" +
							"     select * from (  select t.tano,\n" +
							"               t.ta_name,\n" +
							"               t.busi_date,\n" +
							"               t.prod_code,\n" +
							"               t.prod_name,\n" +
							"               sum(case\n" +
							"                     when t.busi_code in ('120', '122','159') then\n" +
							"                      t.app_amt\n" +
							"                     else\n" +
							"                      0\n" +
							"                   end) as app_amt\n" +
							"          from fund_cust_trans_cfm_log_h t\n" +
							"         where 1 = 1 and t.system_no='FUND' ");
			if(params.getModel() != null&& Tools.isNotBlank(params.getModel().getBusiDate())){
				sb.append(" and t.busi_date >='"+params.getModel().getBusiDate()+"'");
			}
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
				sb.append(" and t.busi_date <='"+params.getModel().getBusiEndDate()+"'");
			}
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
				sb.append(" and t.tano like '%"+params.getModel().getTano()+"%'");
			}
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getProdCode())){
				sb.append(" and t.prod_code like '%"+params.getModel().getProdCode()+"%'");
			}
			sb.append(" group by t.tano, t.ta_name, t.prod_code, t.prod_name, t.busi_date order by t.busi_date desc )) t\n" +
				" group by t.tano, t.ta_name, t.prod_code, t.prod_name, t.busi_date \n");

			List<M642> volList = super.findRows(M642.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
			SqlResult<M642> sqlRowSqlResult = new SqlResult<>();
			sqlRowSqlResult.setResults(volList.size());
			if (params.getLimit() == 0){
				sqlRowSqlResult.setRows(volList);
			}else {
				sqlRowSqlResult.setRows(
						volList.subList(
								params.getStart(), Math.min(params.getStart() + params.getLimit(), volList.size())
						)
				);
			}

			sqlRowSqlResult.setDesensitized(false);
			return sqlRowSqlResult;
	}
}
