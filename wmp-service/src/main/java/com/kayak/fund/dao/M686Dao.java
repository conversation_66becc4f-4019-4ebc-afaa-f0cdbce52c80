package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.trans.model.M531;
import com.kayak.fund.model.M683;
import com.kayak.fund.model.M686;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M686Dao extends ComnDao {

    public SqlResult<M686> findM686(SqlParam<M686> params) throws Exception {
        //params.setMakeSql(true);
        String sql = " select t.seqence_no,\n" +
                "       t.trans_date,\n" +
                "       t.tano,\n" +
                "       t.check_flag,\n" +
                "       t.check_oprater,\n" +
                "       t.check_date,\n" +
                "       t.check_time,\n" +
                "       t.total_amt,\n" +
                "       t.out_acct_no,\n" +
                "       t.remark,\n" +
                "       b.out_acct_name\n" +
                "  from fund_batch_capital_check t\n" +
                "  left join (select acct_no, max(acct_name) as out_acct_name\n" +
                "               from fund_acct_info\n" +
                "              group by acct_no) b on t.out_acct_no = b.acct_no  where 1=1 ";
        if(Tools.isNotBlank(params.getModel().getTano())){
            sql = sql+" and t.tano = '"+params.getModel().getTano()+"' ";
        }
        if(Tools.isNotBlank(params.getModel().getTransDateEnd())){
            sql = sql+" and t.trans_date <= '"+params.getModel().getTransDateEnd()+"' ";
        }
        if(Tools.isNotBlank(params.getModel().getTransDate())){
            sql = sql+" and t.trans_date >= '"+params.getModel().getTransDate()+"' ";
        }
        if(Tools.isNotBlank(params.getModel().getCheckFlag())){
            sql = sql+" and t.check_flag = '"+params.getModel().getCheckFlag()+"' ";
        }
        if(Tools.isNotBlank(params.getModel().getOutAcctNo())){
            sql = sql+" and t.out_acct_no ='"+params.getModel().getOutAcctNo()+"' ";
        }
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public int updateCheckFlag(SqlParam<M686> params) throws Exception {
        String sql ="update fund_batch_capital_check t set t.check_flag='1',t.CHECK_OPRATER =$S{checkOprater},t.CHECK_DATE=to_char(sysdate(),'yyyyMMdd'),t.CHECK_TIME=to_char(sysdate,'hh24miss') where t.SEQENCE_NO=$S{seqenceNo} ";
        return super.update(
                sql,SubDatabase.DATABASE_FUND_CENTER,
                params.getModel()).getEffect();
    }

}
