package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.cust.dao.M111Dao;
import com.kayak.cust.model.M111;
import com.kayak.fund.model.M670;
import com.kayak.graphql.model.FetcherData;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class M670Dao extends ComnDao {
    @Autowired
    private M111Dao m111Dao;

    public SqlResult<M670> find670s(SqlParam<M670> params) throws Exception {
        params.setMakeSql(true);
        if (StringUtils.isBlank(params.getModel().getCustManagerName())){
            StringBuffer sql = new StringBuffer(" select * from ( ");
            sql.append(" select * from ( select busi_date, cust_manager,trans_orgno,tano,ta_name, " +
                    "prod_code,prod_name,busi_code,cust_no,cust_name,APP_SERNO, " +
                    " channel_flag,app_amt,app_vol,id_type,id_code,acct_no " +
                    "  from fund_cust_trans_req_log t  where 1=1 and t.system_no='FUND' and t.cust_manager is not null ) ");
            sql.append(" UNION ALL select * from (");
            sql.append(" select busi_date, cust_manager,trans_orgno,tano,ta_name," +
                    " prod_code,prod_name,busi_code,cust_no,cust_name,APP_SERNO," +
                    "channel_flag,app_amt,app_vol ,id_type,id_code,acct_no " +
                    "from fund_cust_trans_req_log_h t  where 1=1 and t.system_no='FUND' and  t.cust_manager is not null ");
            sql.append(" )) order by busi_date desc");
            return super.findRows(sql.toString() , SubDatabase.DATABASE_FUND_CENTER, params);
        }
        Map<String,Object> map = new HashMap<>();
        map.put("custManager",params.getModel().getCustManager());
        map.put("custManagerName",params.getModel().getCustManagerName());
        SqlParam<M111> m111SqlParam = new FetcherData(map,M111.class);
        SqlResult<M111> m111SqlResult = m111Dao.findCustManagerInfos(m111SqlParam);
        List<M111> m111List = m111SqlResult.getRows();
        if (m111List != null && m111List.size() > 0){
            params.getModel().setCustManagerName(null);
            String inStr = "where cust_manager in (";
            for (int i = 0; i < m111List.size(); i ++){
                inStr += m111List.get(i).getCustManager();
                if (i == m111List.size() -1){
                    inStr += ")";
                }else {
                    inStr += ",";
                }
            }
            StringBuffer sql = new StringBuffer(" select * from ( ");
            sql.append("select busi_date, cust_manager,trans_orgno,ta_name, " +
                    "prod_code,prod_name,busi_code,cust_no,cust_name, APP_SERNO," +
                    " channel_flag,app_amt,app_vol,id_type,id_code,acct_no  " +
                    "  from fund_cust_trans_req_log t "+inStr);
            sql.append(" UNION ALL ");
            sql.append(" select busi_date, cust_manager,trans_orgno,ta_name," +
                    " prod_code,prod_name,busi_code,cust_no,cust_name,APP_SERNO," +
                    "channel_flag,app_amt,app_vol,id_type,id_code,acct_no  " +
                    "from fund_cust_trans_req_log_h t "+inStr);
            sql.append(" ) where cust_manager is not null ");
            return super.findRows(sql.toString() , SubDatabase.DATABASE_FUND_CENTER, params);
        }
        SqlResult<M670> sqlResult = new SqlResult<>();
        sqlResult.setRows(new ArrayList<>());
        sqlResult.setDesensitized(false);
        sqlResult.setResults(0l);
        return sqlResult;
    }
}
