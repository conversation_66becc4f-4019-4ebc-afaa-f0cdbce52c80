package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M664;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M664Dao extends ComnDao {

	/**
	 * 月交易确认汇总数据
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M664> findFundMonthAckSum(SqlParam<M664> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("select t.stat_year,\n" +
				"       t.begin_date,\n" +
				"       t.prod_code,\n" +
				"       t.end_date,\n" +
				"       t.cust_type,\n" +
				"       t.busi_code,\n" +
				"       sum(t.sum_num) as sum_num,\n" +
				"       sum(t.sum_acct) as sum_acct,\n" +
				"       sum(t.total_ack_amt) as total_ack_amt,\n" +
				"       sum(t.total_ack_vol) as total_ack_vol\n" +
				"  from fund_month_ack_sum t\n" +
				" group by t.stat_year, t.begin_date, t.end_date, t.cust_type, t.busi_code,t.prod_code \n", SubDatabase.DATABASE_FUND_CENTER, params);

	}
}
