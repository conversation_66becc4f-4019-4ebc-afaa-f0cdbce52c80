package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M518;
import com.kayak.fund.model.M645;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M645Dao extends ComnDao {

	/**
	 * 资金入账异常汇总报表
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M645> findPaymErrorBatchCapital(SqlParam<M645> params) throws Exception {
		String sql1 = " select * from ( select t.trans_date,\n" +
				"               t.ori_trans_date,\n" +
				"               t.tano,\n" +
				"               t.prod_code,\n" +
				"               t.busi_type,\n" +
				"               count(t.tano) as trans_num,\n" +
				"               sum(t.trans_amt) as abnormal_amt,\n" +
				"               t2.ta_name\n" +
				"          from paym_error_batch_capital t left join fund_ta_info t2 on t.tano=t2.tano\n" +
				"         where t.system_no = 'FUND' ";
		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(),M645.class);
		sql1 +="  group by t.trans_date,\n" +
				"                  t.ori_trans_date,\n" +
				"                  t.tano,\n" +
				"                  t.prod_code,\n" +
				"                  t.busi_type,\n" +
				"                  t2.ta_name order by t.trans_date, t.tano,t2.ta_name desc   )";
		String sql2 = "  select * from (  select t.trans_date,\n" +
				"               t.ori_trans_date,\n" +
				"               t.tano,\n" +
				"               t.prod_code,\n" +
				"               t.busi_type,\n" +
				"               count(t.tano) as trans_num,\n" +
				"               sum(t.trans_amt) as abnormal_amt,\n" +
				"               t2.ta_name\n" +
				"          from paym_error_batch_capital_h t left join fund_ta_info t2 on t.tano=t2.tano\n" +
				"         where t.system_no = 'FUND' ";
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M645.class);
		sql2 +="        group by t.trans_date,\n" +
				"                  t.ori_trans_date,\n" +
				"                  t.tano,\n" +
				"                  t.prod_code,\n" +
				"                  t.busi_type,\n" +
				"                  t2.ta_name  order by t.trans_date, t.tano,t2.ta_name desc )";
		String sql =
				"select * from ( " + sql1 +
						"  union all \n" +
						sql2 + ") t ";
		return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);
	}
}
