package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M638;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M638Dao extends ComnDao {

	public SqlResult<M638> findfundManagerInfos(SqlParam<M638> params) throws Exception {
		return super.findRows("SELECT  manager_code,legal_code,manager_name,address,connector,telno,remark,update_time FROM fund_manager_info", SubDatabase.DATABASE_FUND_CENTER,params);
	}

	public SqlResult<SqlRow> queryDict(SqlParam<M638> params) throws Exception {
		List<SqlRow> list = super.findRows(SqlRow.class,"SELECT manager_code as value,manager_name as label FROM fund_manager_info", SubDatabase.DATABASE_FUND_CENTER,null);
		SqlResult<SqlRow> sqlResult = new SqlResult();
		sqlResult.setRows(list);
		return sqlResult;
	}

	public UpdateResult addfundManagerInfo(SqlParam<M638> params) throws Exception {
		return super.update("INSERT INTO fund_manager_info(manager_code,legal_code,manager_name,address,connector,telno,remark)" +
						" VALUES($S{managerCode},$S{legalCode},$S{managerName},$S{address},$S{connector},$S{telno},$S{remark})",
				SubDatabase.DATABASE_FUND_CENTER,params.getModel());
	}
	
	public UpdateResult updatefundManagerInfo(SqlParam<M638> params) throws Exception {
		return super.update("UPDATE fund_manager_info SET manager_name=$S{managerName} ,address=$S{address} ,connector=$S{connector} ,telno=$S{telno} ,remark=$S{remark}  WHERE  manager_code=$S{managerCode} AND legal_code=$S{legalCode} ",
				SubDatabase.DATABASE_FUND_CENTER,params.getModel());
	}
	
	public UpdateResult deletefundManagerInfo(SqlParam<M638> params) throws Exception {
		return super.update("DELETE FROM fund_manager_info WHERE  manager_code=$S{managerCode} AND legal_code=$S{legalCode} ",
				SubDatabase.DATABASE_FUND_CENTER,params.getModel());
	}

}
