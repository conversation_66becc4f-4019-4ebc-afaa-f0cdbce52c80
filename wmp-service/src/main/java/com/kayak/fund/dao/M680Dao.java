package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.trans.model.M521;
import com.kayak.fund.model.M680;
import com.kayak.graphql.model.FetcherData;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public class M680Dao extends ComnDao {

    public List<M680> findCusts(SqlParam<M680> param)  throws Exception {
        String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
                " where cust_name = $S{custName} and id_type = $S{idType} and id_code = '" + param.getModel().getIdCode() + "'";
        return super.findRows(M680.class,sql, SubDatabase.DATABASE_CUST_CENTER,param.getModel());
    }

    public M680 getCusts(String no)  throws Exception {
        String sql = "select cust_no, cust_name, id_type, id_code from cust_info\n" +
                " where cust_no = '"+no+"'";
        return super.findRow(M680.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }

    public String getCustNoByAcctNo(String no)  throws Exception {
        String sql = "select cust_no from cust_trans_acct\n" +
                " where acct_no = '"+no+"'";
        return super.findRow(String.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }

    public String getAcctNoByCustNo(String no)  throws Exception {
        String sql = "select acct_no from cust_trans_acct\n" +
                " where cust_no = '"+no+"'";
        return super.findRow(String.class,sql, SubDatabase.DATABASE_CUST_CENTER,null);
    }

    public String getTaNameByTano(String no)  throws Exception {
        String sql = "select ta_name from FUND_TA_INFO\n" +
                " where tano = '"+no+"'";
        return super.findRow(String.class,sql, SubDatabase.DATABASE_FUND_CENTER,null);
    }

    /** 查客户信息和TA信息 **/
    public SqlResult<M680> findCustTAInfo(FetcherData<M680> params) throws Exception {
        params.setMakeSql(true);
        String taCustSql = " SELECT t1.trans_acct_no,\n" +
                "       t1.tano,\n" +
                "       t1.legal_code,\n" +
                "       t1.ta_acct_no,\n" +
                "       t1.acct_status,\n" +
                "       t1.cust_no,\n" +
                "       t1.system_no,\n" +
                "       t1.OPEN_DATE,\n" +
                "       t2.ta_name,\n" +
                "       t3.cust_name,\n" +
                "       t3.acct_no,\n" +
                "       t3.id_type,\n" +
                "       t3.id_code\n" +
                "  FROM fund_cust_ta_acct t1\n" +
                "  left join fund_ta_info t2 on t1.tano = t2.tano\n" +
                "  left join fund_cust_ta_acct t3 on t1.TRANS_ACCT_NO = t3.TRANS_ACCT_NO and t1.tano=t3.tano\n" +
                " where 1 = 1 ";
        if(Tools.isNotBlank(params.getModel().getCustNo())){
            taCustSql = taCustSql +" and t1.cust_no in ('"+params.getModel().getCustNo()+"')";
        }
        taCustSql = taCustSql + " order by t1.OPEN_DATE desc nulls last ";
        params.getModel().setCustNo(null);
        SqlResult<M680> sqlResult = super.findRows(taCustSql,SubDatabase.DATABASE_FUND_CENTER,params);
       /** List<M680> volList = sqlResult.getRows();
        if (volList != null && volList.size() > 0) {
            for (M680 m680 : volList) {
                M680 cust = this.getCusts(m680.getCustNo());
                if (cust != null){
                    m680.setCustName(cust.getCustName());
                    m680.setIdCode(cust.getIdCode());
                    m680.setIdType(cust.getIdType());
                }
                String acctNo = this.getAcctNoByCustNo(m680.getCustNo());
                m680.setAcctNo(acctNo);
                String taName = this.getTaNameByTano(m680.getTano());
                m680.setTaName(taName);
            }
        }*/
        sqlResult.setDesensitized(false);
        return sqlResult;
    }


    /**
     * 查TA账号信息表-获取TA信息
     */
    public SqlResult<M680> queryTA(SqlParam<M680> params) throws Exception {
        return super.findRows("select tano from fund_cust_ta_acct", SubDatabase.DATABASE_FUND_CENTER, params);
    }

    /**
     * <AUTHOR>
     * @Description 客户TA账号表，交易机构改为合并机构
     * @Date 2022/3/24
     * @Param [params]
     * @return com.kayak.core.sql.UpdateResult
     **/
    public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
        return super.update("UPDATE fund_cust_ta_acct SET TRANS_ORGNO = $S{mergeOrgno} WHERE TRANS_ORGNO = $S{removeOrgno} ",
                SubDatabase.DATABASE_FUND_CENTER,params);
    }
    
}

