package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M626;
import org.springframework.stereotype.Repository;

/**
 * 客户份额差错查询
 *
 * <AUTHOR>
 * @date 2021-06-19 11:05
 */
@Repository
public class M626Dao extends ComnDao {

    public SqlResult<M626> findAll(SqlParam<M626> params) throws Exception{
        //TODO : 待查询份额差错表
        String sql = "select * from fund_vol_check ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }
}
