package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;


import com.kayak.core.util.Tools;
import com.kayak.fund.model.M681;
import org.springframework.stereotype.Repository;

@Repository
public class M681Dao extends ComnDao {

    public SqlResult<M681> findM681s(SqlParam<M681> params) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT t1.prod_code,\n" +
                "       t1.legal_code,\n" +
                "       t1.tano,\n" +
                "       t1.busi_code,\n" +
                "       t1.should_transfer_date,\n" +
                "       t1.transfer_amt,\n" +
                "       t1.transfer_status,\n" +
                "       t1.out_acct_no,\n" +
                "       t1.in_acct_no,\n" +
                "       b.out_acct_name,\n" +
                "       c.in_acct_name\n" +
                "  FROM fund_capital_transfer_info t1\n" +
                "  left join (select acct_no, max(acct_name) as out_acct_name\n" +
                "               from fund_acct_info\n" +
                "              group by acct_no) b on t1.out_acct_no = b.acct_no\n" +
                "  left join (select acct_no, max(acct_name) as in_acct_name\n" +
                "               from fund_acct_info\n" +
                "              group by acct_no) c on t1.in_acct_no = c.acct_no where 1=1 \n");
        if(Tools.isNotBlank(params.getModel().getBusiCode())){
            sql.append(" and t1.busi_code = '"+params.getModel().getBusiCode()+"'" );
        }
        if(Tools.isNotBlank(params.getModel().getShouldTransferDate())){
            sql.append(" and t1.should_transfer_date = '"+params.getModel().getBusiCode()+"'" );
        }
        if(Tools.isNotBlank(params.getModel().getTransferStatus())){
            sql.append(" and t1.transfer_status = '"+params.getModel().getTransferStatus()+"'" );
        }
        if(Tools.isNotBlank(params.getModel().getTano())){
            sql.append(" and t1.tano = '"+params.getModel().getTano()+"'" );
        }
        if(Tools.isNotBlank(params.getModel().getProdCode())){
            sql.append(" and t1.prod_code = '"+params.getModel().getProdCode()+"'" );
        }
        return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public SqlResult<M681> findTano(SqlParam<M681> params) throws Exception {
        return super.findRows("SELECT " +
                " t1.tano,t1.ta_name " +
                " FROM fund_ta_info t1 " , SubDatabase.DATABASE_FUND_CENTER, params);
    }

}
