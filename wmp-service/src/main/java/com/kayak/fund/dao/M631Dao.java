package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M631;
import org.springframework.stereotype.Repository;

@Repository
public class M631Dao extends ComnDao {

    public SqlResult<M631> findInfos(SqlParam<M631> params) throws Exception {
        return super.findRows("select t.busi_date,t.cust_name,t.id_type,t.id_code,t.ori_acct_no,t.acct_no,t.channel_flag from cust_acct_log t where t.busi_code='158' order by t.busi_date desc \n ",SubDatabase.DATABASE_CUST_CENTER, params);
    }

}
