package com.kayak.fund.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;

import com.kayak.fund.model.M674;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;

/**
 * 托管行管理, 原名 FundTruteeInfoService
 */
@Repository
public class M674Dao extends ComnDao {

    public SqlResult<M674> findFundTruteeInfos(SqlParam<M674> params) throws Exception {
        return super.findRows("SELECT 'FUND' as system_no, trutee_code,legal_code,trutee_name,account_version,name_format,fax FROM fund_trutee_info", SubDatabase.DATABASE_FUND_CENTER,params);
    }

    public SqlResult<SqlRow> findFundTrutee() throws Exception {
        List<SqlRow> list = super.findRows(SqlRow.class,"SELECT trutee_code as value,trutee_name as label FROM fund_trutee_info", SubDatabase.DATABASE_FUND_CENTER,null);
        SqlResult<SqlRow> sqlResult = new SqlResult();
        sqlResult.setRows(list);
        return sqlResult;
    }

    public UpdateResult addFundTruteeInfo(SqlParam<M674> params) throws Exception {
        return super.update("INSERT INTO fund_trutee_info(trutee_code,legal_code,trutee_name,account_version,name_format,fax)" +
                        " VALUES($S{truteeCode},$S{legalCode},$S{truteeName},$S{accountVersion},$S{nameFormat},$S{fax})",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult updateFundTruteeInfo(SqlParam<M674> params) throws Exception {
        return super.update("UPDATE fund_trutee_info SET trutee_name=$S{truteeName} ,account_version=$S{accountVersion} ,name_format=$S{nameFormat} ,fax=$S{fax}  WHERE  trutee_code=$S{truteeCode} AND legal_code=$S{legalCode} ",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult deleteFundTruteeInfo(SqlParam<M674> params) throws Exception {
        return super.update("DELETE FROM fund_trutee_info WHERE  trutee_code=$S{truteeCode} AND legal_code=$S{legalCode} ",
                SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

}
