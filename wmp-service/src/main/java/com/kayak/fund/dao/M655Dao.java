package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M655;
import org.springframework.stereotype.Repository;

@Repository
public class M655Dao extends ComnDao {

	/**
	 * 分红汇总报表
	 * @param params
	 * @return M655
	 * @throws Exception
	 */
	public SqlResult<M655> findFundBonusSum(SqlParam<M655> params) throws Exception {
		params.setMakeSql(true);
		StringBuffer sql = new StringBuffer();
		sql.append("select * from ( select * from ( select tano, ta_name, prod_code, prod_name, cust_type, legal_code,DIV_DATE,BASE_VOL,DIV_AMT,DIV_VOL,TRANS_ORGNO \n" +
						"          from FUND_CUST_DIV_DETAIL_H where 1=1  and system_no='FUND' )\n" +
						"        UNION ALL\n" +
						"        select * from ( select tano, ta_name, prod_code, prod_name, cust_type, legal_code,DIV_DATE,BASE_VOL,DIV_AMT,DIV_VOL,TRANS_ORGNO \n" +
						"          from FUND_CUST_DIV_DETAIL where 1=1  and system_no='FUND' )) where 1=1 ");
		if(Tools.isNotBlank(params.getModel().getOrgno())){
			sql.append(" and trans_orgno = '"+params.getModel().getOrgno()+"' ");
			params.getModel().setOrgno(null);
		}
		return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
