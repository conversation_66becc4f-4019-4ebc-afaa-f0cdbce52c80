package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M658;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M658Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;
	/**
	 * 保有量统计报表【按机构】
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M658> findFundVolStockForOrg(SqlParam<M658> params,String inStr) throws Exception {
		params.setMakeSql(true);
		return super.findRows("select t.orgno,sum(t.stock_vol) as stock_vol,sum(t.stock_vol*t.nav) as stock_amt,sum(t.effe_num) as effe_num,t.trans_date from fund_vol_stock t where 1=1 " +reportformUtil.getOrgIdForOrgLevel("t.",params.getParams().get("userid").toString())+
						""+ inStr +" group by t.orgno,t.trans_date  order by t.trans_date desc ",
				SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
