package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M229;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M229Dao extends ComnDao {

    public SqlResult<M229> listM229(SqlParam<M229> param)  throws Exception {
        param.setMakeSql(true);
        String sql = "select t1.tano, t2.ta_name, t1.prod_code, t1.nav,t1.legal_code,\n"+
                "t1.nav_date, t1.total_nav, t1.ten_thousand_income_amt, t1.seven_days_income\n"+
                "from fund_nav_info t1 left join fund_ta_info t2 on t1.tano = t2.tano  order by t1.nav_date desc \n";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

}
