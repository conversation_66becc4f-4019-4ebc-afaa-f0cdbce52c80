package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M610;
import org.springframework.stereotype.Repository;

@Repository
public class M610Dao extends ComnDao {

    public SqlResult<M610> findPaymCapitalCheckErrorBos(SqlParam<M610> params) throws Exception {
        return super.findRows("SELECT pcce.error_serno,pcce.error_status,pcce.check_result,pcce.ori_capital_serno,pcce.ori_trans_date,pcce.ori_busi_type,pcce.error_deal_type,pcce.error_date,pcce.error_busi_type,pcce.tano,pcce.prod_code,pcce.trans_amt,pcce.channel_flag,pcce.channel_date,pcce.channel_time,pcce.channel_serno,pcce.cust_no,pcce.trans_acct_no,pcce.acct_no,pcce.cust_name,pcce.target_acct_name,pcce.target_acct_no,pcce.frozen_serno,pcce.capital_status,pcce.legal_code,pcce.bank_code,pcce.branch_code,pcce.sub_branch_code,pcce.trans_orgno,pcce.inputuser,pcce.busi_code,pcce.cur,pcce.cash_flag,pcce.unfit_reason,pcce.remark,pcce.create_time,pcce.update_time,pcl.host_rtn_code,pcl.host_rtn_desc FROM paym_capital_check_error pcce LEFT JOIN paym_capital_log pcl ON pcce.ori_capital_serno = pcl.capital_serno and pcce.ori_trans_date = pcl.trans_date WHERE pcce.error_status in ('0','A','6','C','9')", SubDatabase.DATABASE_FUND_CENTER, params);
    }
}
