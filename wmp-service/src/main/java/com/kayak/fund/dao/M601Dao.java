package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M601;
import org.springframework.stereotype.Repository;

/**
 * 基金代理关系：当前表结构先挂在基金模块下，后期会移到产品中心。
 * 原因：因后期计划，在产品创建的时候就将基金代理关系新增进去，而管理台功能不支持跨数据库事务一致性问题，从而需要将当前表挪到平台中心
 * -- 基金转换关系查询 和 基金分红方案查询 这两个页面同样
 */
@Repository
public class M601Dao extends ComnDao {

	/**
	 * 基金代理关系查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M601> findFundProdConvertInfo(SqlParam<M601> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("select tano,prod_name,PROD_CODE,orgno,share_class,target_tano,VOLLOWER_LIMIT,VOLUPPER_LIMIT,TARGET_PROD_CODE,TARGET_SHARE_CLASS,TARGET_SHARE_TYPE,DAYS_LOWER_LIMIT,INDIVIDUALOR_INSTITUTION,REGISTRAR_CODE,OPERATE_DATE,TOTAL_FLAG,UPDATE_TYPE,downloaddate from FUND_PROD_CONVERT_INFO", SubDatabase.DATABASE_FUND_CENTER, params);
	}
}