package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M643;
import org.springframework.stereotype.Repository;

@Repository
public class M643Dao extends ComnDao {

	/**
	 * 产品费率信息查询
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M643> findFundCustTransCfmLog(SqlParam<M643> params) throws Exception {
		//params.setMakeSql(true);
		StringBuffer sql = new StringBuffer(" select * from (");
		sql.append("select t2.TRANS_ORGNO as trans_orgno,\n" +
				"       t1.busi_code,\n" +
				"       t1.prod_code,\n" +
				"       t1.prod_name,\n" +
				"       t2.fund_type,\n" +
				"       case when t1.busi_code = '120' then to_char(sum(t1.ack_amt)) else to_char('0') end as subscribe_amt,\n" +
				"       case when t1.busi_code = '120' then to_char(sum(t1.ack_vol)) else to_char('0') end as subscribe_vol,\n" +
				"       case when t1.busi_code = '120' then to_char(count(t1.app_serno)) else to_char('0') end as subscribe_cnt,\n" +
				"       case when t1.busi_code = '122' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as app_pur_amt,\n" +
				"       case when t1.busi_code = '122' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as app_pur_vol,\n" +
				"       case when t1.busi_code = '122' then  to_char(count(t1.app_serno)) else to_char('0')  end as app_pur_cnt,\n" +
				"       case when t1.busi_code = '139' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as fixed_investment_amt,\n" +
				"       case when t1.busi_code = '139' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as fixed_investment_vol,\n" +
				"       case when t1.busi_code = '139' then  to_char(count(t1.app_serno)) else to_char('0')  end as fixed_investment_cnt,\n" +
				"       case when t1.busi_code = '124' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as redeem_amt,\n" +
				"       case when t1.busi_code = '124' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as redeem_vol,\n" +
				"       case when t1.busi_code = '124' then  to_char(count(t1.app_serno)) else to_char('0')  end as redeem_cnt,\n" +
				"       case when t1.busi_code = '142' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as rorce_reddem_amt,\n" +
				"       case when t1.busi_code = '142' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as rorce_reddem_vol,\n" +
				"       case when t1.busi_code = '142' then  to_char(count(t1.app_serno)) else to_char('0')  end as rorce_reddem_cnt,\n" +
				"       case when t1.busi_code = '136' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as fund_convert_amt,\n" +
				"       case when t1.busi_code = '136' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as fund_convert_vol,\n" +
				"       case when t1.busi_code = '136' then  to_char(count(t1.app_serno)) else to_char('0')  end as fund_convert_cnt,\n" +
				"       case when t1.busi_code = '127' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as transfer_in_amt,\n" +
				"       case when t1.busi_code = '127' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as transfer_in_vol,\n" +
				"       case when t1.busi_code = '127' then  to_char(count(t1.app_serno)) else to_char('0')  end as transfer_in_cnt,\n" +
				"       case when t1.busi_code = '128' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as transfer_out_amt,\n" +
				"       case when t1.busi_code = '128' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as transfer_out_vol,\n" +
				"       case when t1.busi_code = '128' then  to_char(count(t1.app_serno)) else to_char('0')  end as transfer_out_cnt,\n" +
				"       case when t1.busi_code = '131' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as frozen_amt,\n" +
				"       case when t1.busi_code = '131' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as frozen_vol,\n" +
				"       case when t1.busi_code = '131' then  to_char(count(t1.app_serno)) else to_char('0')  end as frozen_cnt,\n" +
				"       case when t1.busi_code = '132' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as thaw_amt,\n" +
				"       case when t1.busi_code = '132' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as thaw_vol,\n" +
				"       case when t1.busi_code = '132' then  to_char(count(t1.app_serno)) else to_char('0')  end as thaw_cnt,\n" +
				"       case when t1.busi_code = '133' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as non_trading_amt,\n" +
				"       case when t1.busi_code = '133' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as non_trading_vol,\n" +
				"       case when t1.busi_code = '133' then  to_char(count(t1.app_serno)) else to_char('0')  end as non_trading_cnt,\n" +
				"       case when t1.busi_code = '143' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as dividend_amt,\n" +
				"       case when t1.busi_code = '143' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as dividend_vol,\n" +
				"       case when t1.busi_code = '143' then  to_char(count(t1.app_serno)) else to_char('0')  end as dividend_cnt\n" +
				"  from fund_cust_trans_req_log t2\n" +
				"  left join fund_cust_trans_cfm_log t1 on t1.app_serno = t2.app_serno\n" +
				"  where 1=1 and t1.busi_code in ('120','122','139','124','142','136','127','128','131','132','133','143')\n");
		if(Tools.isNotBlank(params.getModel().getProdCode())){
			sql.append(" and t1.prod_code like '%"+params.getModel().getProdCode()+"%'");
		}
		if(Tools.isNotBlank(params.getModel().getTransOrgno())){
			sql.append(" and t2.trans_orgno = '"+params.getModel().getTransOrgno()+"' ");
		}
		if(Tools.isNotBlank(params.getModel().getFundType())){
			sql.append(" and t2.fund_type = '"+params.getModel().getFundType()+"'");
		}
		if(Tools.isNotBlank(params.getModel().getChannelFlag())){
			sql.append(" and t2.channel_flag = '"+params.getModel().getChannelFlag()+"' ");
		}
		if(Tools.isNotBlank(params.getModel().getBusiCode())){
			sql.append(" and  t1.busi_code = '"+params.getModel().getBusiCode()+"'");
		}
		if(Tools.isNotBlank(params.getModel().getTano())){
			sql.append(" and t1.tano like '%"+params.getModel().getTano()+"%'");
		}
		if(Tools.isNotBlank(params.getModel().getBusiDate())){
			sql.append(" and t1.busi_date >= '"+params.getModel().getBusiDate()+"' ");
		}
		if(Tools.isNotBlank(params.getModel().getBusiEndDate())){
			sql.append(" and t1.busi_date <= '"+params.getModel().getBusiEndDate()+"' ");
		}
		sql.append("  group by t2.TRANS_ORGNO,t1.busi_code,t2.fund_type,t1.prod_code,t1.prod_name  ");
		sql.append(" UNION ALL ");
		sql.append("select t2.TRANS_ORGNO as trans_orgno,\n" +
				"       t1.busi_code,\n" +
				"       t1.prod_code,\n" +
				"       t1.prod_name,\n" +
				"       t2.fund_type,\n" +
				"       case when t1.busi_code = '120' then to_char(sum(t1.ack_amt)) else to_char('0') end as subscribe_amt,\n" +
				"       case when t1.busi_code = '120' then to_char(sum(t1.ack_vol)) else to_char('0') end as subscribe_vol,\n" +
				"       case when t1.busi_code = '120' then to_char(count(t1.app_serno)) else to_char('0') end as subscribe_cnt,\n" +
				"       case when t1.busi_code = '122' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as app_pur_amt,\n" +
				"       case when t1.busi_code = '122' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as app_pur_vol,\n" +
				"       case when t1.busi_code = '122' then  to_char(count(t1.app_serno)) else to_char('0')  end as app_pur_cnt,\n" +
				"       case when t1.busi_code = '139' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as fixed_investment_amt,\n" +
				"       case when t1.busi_code = '139' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as fixed_investment_vol,\n" +
				"       case when t1.busi_code = '139' then  to_char(count(t1.app_serno)) else to_char('0')  end as fixed_investment_cnt,\n" +
				"       case when t1.busi_code = '124' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as redeem_amt,\n" +
				"       case when t1.busi_code = '124' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as redeem_vol,\n" +
				"       case when t1.busi_code = '124' then  to_char(count(t1.app_serno)) else to_char('0')  end as redeem_cnt,\n" +
				"       case when t1.busi_code = '142' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as rorce_reddem_amt,\n" +
				"       case when t1.busi_code = '142' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as rorce_reddem_vol,\n" +
				"       case when t1.busi_code = '142' then  to_char(count(t1.app_serno)) else to_char('0')  end as rorce_reddem_cnt,\n" +
				"       case when t1.busi_code = '136' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as fund_convert_amt,\n" +
				"       case when t1.busi_code = '136' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as fund_convert_vol,\n" +
				"       case when t1.busi_code = '136' then  to_char(count(t1.app_serno)) else to_char('0')  end as fund_convert_cnt,\n" +
				"       case when t1.busi_code = '127' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as transfer_in_amt,\n" +
				"       case when t1.busi_code = '127' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as transfer_in_vol,\n" +
				"       case when t1.busi_code = '127' then  to_char(count(t1.app_serno)) else to_char('0')  end as transfer_in_cnt,\n" +
				"       case when t1.busi_code = '128' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as transfer_out_amt,\n" +
				"       case when t1.busi_code = '128' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as transfer_out_vol,\n" +
				"       case when t1.busi_code = '128' then  to_char(count(t1.app_serno)) else to_char('0')  end as transfer_out_cnt,\n" +
				"       case when t1.busi_code = '131' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as frozen_amt,\n" +
				"       case when t1.busi_code = '131' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as frozen_vol,\n" +
				"       case when t1.busi_code = '131' then  to_char(count(t1.app_serno)) else to_char('0')  end as frozen_cnt,\n" +
				"       case when t1.busi_code = '132' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as thaw_amt,\n" +
				"       case when t1.busi_code = '132' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as thaw_vol,\n" +
				"       case when t1.busi_code = '132' then  to_char(count(t1.app_serno)) else to_char('0')  end as thaw_cnt,\n" +
				"       case when t1.busi_code = '133' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as non_trading_amt,\n" +
				"       case when t1.busi_code = '133' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as non_trading_vol,\n" +
				"       case when t1.busi_code = '133' then  to_char(count(t1.app_serno)) else to_char('0')  end as non_trading_cnt,\n" +
				"       case when t1.busi_code = '143' then  to_char(sum(t1.ack_amt)) else to_char('0')  end as dividend_amt,\n" +
				"       case when t1.busi_code = '143' then  to_char(sum(t1.ack_vol)) else to_char('0')  end as dividend_vol,\n" +
				"       case when t1.busi_code = '143' then  to_char(count(t1.app_serno)) else to_char('0')  end as dividend_cnt\n" +
				"  from fund_cust_trans_req_log_h t2\n" +
				"  left join fund_cust_trans_cfm_log_h t1  on t1.app_serno = t2.app_serno\n" +
				"  where 1=1 and t1.busi_code in ('120','122','139','124','142','136','127','128','131','132','133','143')\n");
		if(Tools.isNotBlank(params.getModel().getProdCode())){
			sql.append(" and t1.prod_code like '%"+params.getModel().getProdCode()+"%'");
		}
		if(Tools.isNotBlank(params.getModel().getTransOrgno())){
			sql.append(" and t2.trans_orgno = '"+params.getModel().getTransOrgno()+"' ");
		}
		if(Tools.isNotBlank(params.getModel().getFundType())){
			sql.append(" and t2.fund_type = '"+params.getModel().getFundType()+"'");
		}
		if(Tools.isNotBlank(params.getModel().getChannelFlag())){
			sql.append(" and t2.channel_flag = '"+params.getModel().getChannelFlag()+"' ");
		}
		if(Tools.isNotBlank(params.getModel().getBusiCode())){
			sql.append(" and  t1.busi_code = '"+params.getModel().getBusiCode()+"'");
		}
		if(Tools.isNotBlank(params.getModel().getTano())){
			sql.append(" and t1.tano like '%"+params.getModel().getTano()+"%'");
		}
		if(Tools.isNotBlank(params.getModel().getBusiDate())){
			sql.append(" and t1.busi_date >= '"+params.getModel().getBusiDate()+"' ");
		}
		if(Tools.isNotBlank(params.getModel().getBusiEndDate())){
			sql.append(" and t1.busi_date <= '"+params.getModel().getBusiEndDate()+"' ");
		}
		sql.append("  group by t2.TRANS_ORGNO,t1.busi_code,t2.fund_type,t1.prod_code,t1.prod_name  ");
		sql.append(" ) ");
		return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}

}
