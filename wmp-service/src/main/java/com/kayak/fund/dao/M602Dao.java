package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M602;
import org.springframework.stereotype.Repository;

/**
 * 基金转换关系查询：当前表结构先挂在基金模块下，后期会移到产品中心。
 * 原因：因后期计划，在产品创建的时候就将基金代理关系新增进去，而管理台功能不支持跨数据库事务一致性问题，从而需要将当前表挪到平台中心
 * -- 基金代理关系 和 基金分红方案查询 这两个页面同样
 */
@Repository
public class M602Dao extends ComnDao {

	/**
	 * 基金转换关系查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M602> findFundProdBasicParam(SqlParam<M602> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("SELECT prod_code," +
				"legal_code," +
				"tano," +
				"orgno," +
				"share_class," +
				"target_tano," +
				"vollower_limit," +
				"volupper_limit," +
				"target_prod_code," +
				"target_share_class," +
				"target_share_type," +
				"days_lower_limit," +
				"individualor_institution," +
				"registrar_code," +
				"operate_date  from fund_prod_convert_info", SubDatabase.DATABASE_FUND_CENTER, params);
	}

	
	
}