package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M657;
import org.springframework.stereotype.Repository;

@Repository
public class M657Dao extends ComnDao {

	/**
	 * 资金入账异常汇总报表
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M657> findFundCustAbnormalLog(SqlParam<M657> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("select t.cust_no,\n" +
				"       t.trans_acct_no,\n" +
				"       t.busi_code,\n" +
				"       t.abnormal_type,\n" +
				"       t.trans_amt,\n" +
				"       t.begin_date,\n" +
				"       t.end_date,\n" +
				"       t2.id_type,\n" +
				"       t2.id_code,\n" +
				"       t2.cust_name\n" +
				"  from FUND_CUST_ABNORMAL_LOG t\n" +
				"  left join fund_cust_ta_acct t2 on t.tano = t2.tano\n" +
				"                                and t.trans_acct_no = t2.trans_acct_no\n" +
				" where 1 = 1\n", SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
