package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M625;
import org.springframework.stereotype.Repository;

/**
 * 资金差错处理处理类 - 基金代销
 *
 * <AUTHOR>
 * @date 2021-06-17 19:38
 */
@Repository
public class M625Dao extends ComnDao {

    public SqlResult<M625> findAll(SqlParam<M625> params) throws Exception {
        String sql = "SELECT batch_deal_fail_cause,capital_serno,exec_id,file_no,deal_status,tano,prod_code,trans_date,busi_type,trans_amt,cust_no,cust_name,trans_acct_no,acct_no,target_acct_name,target_acct_no,frozen_serno,capital_status,legal_code,bank_code,branch_code,sub_branch_code,trans_orgno,inputuser,busi_code,cur,cash_flag,host_rtn_code,host_rtn_desc,fail_deal_type,fail_date,fail_busi_type,modify_acct_no,remark,create_time,update_time " +
                " FROM paym_batch_capital_log" +
                " WHERE deal_status in ('0','2','4') ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public UpdateResult updateAcctNo(SqlParam<M625> params) throws Exception {
        String sql = "UPDATE paym_batch_capital_log SET modify_acct_no=$S{modifyAcctNo}  WHERE  capital_serno=$S{capitalSerno} ";
		return super.update(sql, SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }
}
