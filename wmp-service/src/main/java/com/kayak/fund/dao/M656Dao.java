package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M656;
import org.springframework.stereotype.Repository;

@Repository
public class M656Dao extends ComnDao {

	/**
	 * 客户分红明细报表
	 * @param params
	 * @return M656
	 * @throws Exception
	 */
	public SqlResult<M656> findFundBonusSum(SqlParam<M656> params) throws Exception {
		params.setMakeSql(true);
		StringBuffer sql = new StringBuffer();
		sql.append("select *\n" +
				"  from (select * from (select tano,\n" +
						"               ta_name,\n" +
						"               prod_code,\n" +
						"               prod_name,\n" +
						"               cust_name,\n" +
						"               acct_no,\n" +
						"               acct_no as query_acct_no,\n" +
						"               cust_type,\n" +
						"               legal_code,div_date,cust_no,id_code,id_type,BASE_VOL,DIV_AMT,DIV_VOL \n" +
						"          from FUND_CUST_DIV_DETAIL where 1=1 and system_no='FUND' ");
		if(Tools.isNotBlank(params.getModel().getQueryAcctNo())){
			sql.append(" and acct_no = '"+params.getModel().getQueryAcctNo()+"' ");
		}
		sql.append(" ) UNION ALL select * from (");
		sql.append("        select tano,\n" +
				"               ta_name,\n" +
				"               prod_code,\n" +
				"               prod_name,\n" +
				"               cust_name,\n" +
				"               acct_no,\n" +
				"               acct_no as query_acct_no,\n" +
				"               cust_type,\n" +
				"               legal_code,div_date,cust_no,id_code,id_type,BASE_VOL,DIV_AMT,DIV_VOL \n" +
				"          from FUND_CUST_DIV_DETAIL_H where 1=1  and system_no='FUND' " );
		if(Tools.isNotBlank(params.getModel().getQueryAcctNo())){
			sql.append(" and acct_no = '"+params.getModel().getQueryAcctNo()+"' ");
		}
		params.getModel().setAcctNo(null);
		sql.append(" )) t where 1=1 ");
		//缺少分红基数，分红金额，分红份额三个字段
		return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
