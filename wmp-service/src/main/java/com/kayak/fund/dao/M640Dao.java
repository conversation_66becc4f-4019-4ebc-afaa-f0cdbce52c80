package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import com.kayak.fund.model.M640;
import org.springframework.stereotype.Repository;

@Repository
public class M640Dao extends ComnDao {

	/**
	 * 产品费率信息查询
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M640> findFundProdRateFee(SqlParam<M640> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("SELECT rate_code,tano,prod_code,legal_code,charge_type,target_prod_code,target_charge_type,dist_code," +
				"branch_code,busi_code,capital_type,cust_type,get_rate_method,fixed_fee,vol_lower_limit,vol_upper_limit,amt_lower_limit," +
				"amt_upper_limit,days_lower_limit,days_upper_limit,max_fee,min_fee,rate_fee,rate_fee_flag,comp_prop,comp_capital_type," +
				"whole_flag,modify_way,effective_date,download_date FROM fund_prod_rate_fee ", SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
