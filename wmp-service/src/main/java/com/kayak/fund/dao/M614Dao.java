package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;

import com.kayak.fund.model.M614;
import org.springframework.stereotype.Repository;

/**
 * 接口版本与索引关系管理，原名 TaInterfaceInfoDao
 */
@Repository
public class M614Dao extends ComnDao {

    /**
     *
     * @param params 传入参数
     * @return  查询结果
     * @throws Exception
     */
    public SqlResult<M614> findTaInterfaceInfoDao(SqlParam<M614> params) throws Exception {
        return super.findRows("SELECT system_no,interface_id, interface_name, interface_type, interface_version, " +
                "data_status from wmp_interface_info where data_status = 'E'",SubDatabase.DATABASE_FUND_CENTER, params);
    }

    /**
     * 根据接口名称查询接口信息
     * @param params 传入参数
     * @return 查询结果
     * @throws Exception
     */
    public SqlResult<M614> findTaInterfaceInfoByName(SqlParam<M614> params) throws Exception {
        return super.findRows("SELECT interface_id, interface_name, interface_type, interface_version, data_status from wmp_interface_info where interface_name = $S{interfaceName}", SubDatabase.DATABASE_FUND_CENTER, params);
    }


    /**
     *
     * @param params 传入参数
     * @return 查询结果
     * @throws Exception
     */
    public SqlResult<M614> findTaInterfaceInfoByNo(SqlParam<M614> params) throws Exception {
        return super.findRows("SELECT interface_id, interface_name, interface_type, interface_version, " +
                "data_status from wmp_interface_info where interface_id = $S{interfaceId}",SubDatabase.DATABASE_FUND_CENTER, params);
    }

    /**
     *
     * @param params 传入参数
     * @return  查询版本
     * @throws Exception
     */
    public SqlResult<M614> findTaInterfaceInfoVersion(SqlParam<M614> params) throws Exception {
        return super.findRows("SELECT distinct interface_version, interface_file_name from wmp_interface_version_info", SubDatabase.DATABASE_FUND_CENTER,params);
    }

    /**
     *
     * @param params 传入参数
     * @return   添加是否成功
     * @throws Exception
     */
    public UpdateResult addTaInterfaceInfoDao(SqlParam<M614> params) throws Exception {
        String sql = "INSERT INTO wmp_interface_info (interface_id, interface_name,interface_type, interface_version, data_status, create_time, " +
                "create_user, update_time, update_user,legal_code,system_no)  " +
                "VALUES($S{interfaceId},$S{interfaceName},$S{interfaceType},$S{interfaceVersion},$S{dataStatus},CURRENT_TIMESTAMP,$S{createUser},CURRENT_TIMESTAMP,$S{updateUser},$S{legalCode},$S{systemNo})";
        //设置接口id为系统毫秒值，16位长度
        params.getModel().setInterfaceId(String.format("%016d", System.currentTimeMillis()));
        return super.update(sql, SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    /**
     *
     * @param params 传入参数
     * @return  更新是否成功
     * @throws Exception
     */
    public UpdateResult updateTaInterfaceInfoDao(SqlParam<M614> params) throws Exception {
        return super.update("UPDATE wmp_interface_info set interface_name = $S{interfaceName},interface_type = $S{interfaceType},interface_version = $S{interfaceVersion},update_time = CURRENT_TIMESTAMP ,update_user = $S{updateUser} WHERE interface_id = $S{interfaceId}"
                , SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    /*
    停用----> 销售商接口关联关系维护信息
    */
    public UpdateResult stopTaInterfaceInfo(SqlParam<M614> params) throws Exception {
        return super.update("UPDATE wmp_interface_info " +
                "SET DATA_STATUS = 'D' " +
                "WHERE interface_id = $S{interfaceId} " +
                "AND interface_version = $S{interfaceVersion} ", SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    /*
    启用----> 销售商接口关联关系维护信息
    */
    public UpdateResult startTaInterfaceInfo(SqlParam<M614> params) throws Exception {
        return super.update("UPDATE wmp_interface_info " +
                "SET DATA_STATUS = 'E' " +
                "WHERE interface_id = $S{interfaceId} " +
                "AND interface_version = $S{interfaceVersion} ", SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    /**
     *
     * @param params 传入参数
     * @return  删除是否成功
     * @throws Exception
     */
    public UpdateResult deleteTaInterfaceInfoDao(SqlParam<M614> params) throws Exception {
        return super.update("DELETE FROM wmp_interface_info where interface_id = $S{interfaceId}",SubDatabase.DATABASE_FUND_CENTER,
                params.getModel());
    }

    /**
     *
     * @param params 接口类型
     * @return  接口版本
     * @throws Exception
     */
    public SqlResult<M614> findVersionByType(SqlParam<M614> params) throws Exception{
        return super.findRows("SELECT distinct interface_version,interface_name, " +
                "interface_id from wmp_interface_info where interface_type = $S{interfaceType}", SubDatabase.DATABASE_FUND_CENTER,params);
    }




}
