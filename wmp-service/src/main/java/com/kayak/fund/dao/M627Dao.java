package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M627;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 */
@Repository
public class M627Dao extends ComnDao {

	/**
	 * 查询源泉宝签约变更解约流水
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M627> findFundRiskMatchSum(SqlParam<M627> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("\n" +
				"select t.cust_risk_level, \n" +
				"       t.prod_risk_level, \n" +
				"       sum(t.sum_amt) as sum_amt, \n" +
				"       sum(t.sum_num) as SUM_NUM, \n" +
				"       t.match_flag,\n" +
				"       t.trans_date\n" +
				"  from fund_risk_match_sum t\n" +
				"  where 1=1 \n" +
				" group by t.cust_risk_level,t.prod_risk_level,t.match_flag,t.trans_date  order by t.trans_date desc\n", SubDatabase.DATABASE_FUND_CENTER, params);
	}

	/**
	 * 查询源泉宝签约变更解约流水
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M627> findFundRiskMatchByTransData(SqlParam<M627> params) throws Exception {

		StringBuffer sb = new StringBuffer("select  sum(t.sum_amt)  as total_sum_amt, sum(t.sum_num)  as total_sum_num,t.trans_date, t.match_flag from fund_risk_match_sum t where 1=1 ");
		if(params.getModel() != null && Tools.isNotBlank(params.getModel().getTransDate())){
			sb.append(" and trans_date ='"+params.getModel().getTransDate()+"'");
		}
		/**if(params.getModel() != null && Tools.isNotBlank(params.getModel().getEndTransDate())){
			sb.append(" and trans_date ='"+params.getModel().getTransDate()+"'");
		}*/
		sb.append(" group by t.trans_date,t.match_flag ");

		List<M627> volList = super.findRows(M627.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M627> sqlRowSqlResult = new SqlResult<>();
		sqlRowSqlResult.setResults(volList.size());
		sqlRowSqlResult.setRows(volList);
		sqlRowSqlResult.setDesensitized(false);
		return sqlRowSqlResult;
	}

	/**
	 * 查询源泉宝签约变更解约流水
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M627> findFundTotalRiskMatchByTransData(SqlParam<M627> params) throws Exception {
	//	params.setMakeSql(true);
		StringBuffer sb = new StringBuffer("select sum(t.sum_amt) as total_amt,sum(t.sum_num) as total_num from fund_risk_match_sum t where 1=1 ");
		if(params.getModel() != null && Tools.isNotBlank(params.getModel().getTransDate())){
			sb.append(" and trans_date >='"+params.getModel().getTransDate()+"'");
		}
		if(params.getModel() != null && Tools.isNotBlank(params.getModel().getEndTransDate())){
			sb.append(" and trans_date <='"+params.getModel().getTransDate()+"'");
		}
		List<M627> volList = super.findRows(M627.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M627> sqlRowSqlResult = new SqlResult<>();
		sqlRowSqlResult.setResults(volList.size());
		sqlRowSqlResult.setRows(volList);
		sqlRowSqlResult.setDesensitized(false);
		return sqlRowSqlResult;
	}
}