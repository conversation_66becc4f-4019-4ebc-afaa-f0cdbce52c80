package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M669;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


@Repository
public class M669Dao extends ComnDao {

    public SqlResult<M669> findM669s(SqlParam<M669> params, List<String> codeList) throws Exception {
        params.setMakeSql(true);
        if (StringUtils.isBlank(params.getModel().getFundType())){
            String sql = "SELECT t1.tano,\n" +
                    "       t1.prod_code,\n" +
                    "       t1.nav_date,\n" +
                    "       t1.nav,\n" +
                    "       t1.legal_code,\n" +
                    "       t1.total_nav,\n" +
                    "       t1.ten_thousand_income_amt,\n" +
                    "       t1.seven_days_income,\n" +
                    "       t2.ta_name\n" +
                    "  FROM fund_nav_info t1\n" +
                    "  left join fund_ta_info t2 on t1.tano = t2.tano\n ";
            return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
        }
        params.getModel().setFundType(null);
        String inStr = " where prod_code in (";
        if (codeList != null && codeList.size() > 0){
            for (int i = 0; i < codeList.size(); i ++){
                inStr += "'" + codeList.get(i) + "'";
                if (i != codeList.size() - 1){
                    inStr += ",";
                }
            }
            inStr += ")";
            String sql = "SELECT t1.tano,\n" +
                    "       t1.prod_code,\n" +
                    "       t1.nav_date,\n" +
                    "       t1.nav,\n" +
                    "       t1.legal_code,\n" +
                    "       t1.total_nav,\n" +
                    "       t1.ten_thousand_income_amt,\n" +
                    "       t1.seven_days_income,\n" +
                    "       t2.ta_name\n" +
                    "  FROM fund_nav_info t1\n" +
                    "  left join fund_ta_info t2 on t1.tano = t2.tano\n " + inStr;
            return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
        }else {
            SqlResult<M669> sqlResult = new SqlResult<>();
            sqlResult.setRows(new ArrayList<>());
            sqlResult.setDesensitized(false);
            sqlResult.setResults(0l);
            return sqlResult;
        }
    }

}
