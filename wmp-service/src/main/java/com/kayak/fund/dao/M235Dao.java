package com.kayak.fund.dao;

import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M235;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import com.kayak.base.dao.ComnDao;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import java.util.List;

import static com.kayak.prod.utils.UnderlineToCamelUtils.sqlRowToCamel;

@Repository
public class M235Dao extends ComnDao {

    public SqlResult<M235> findFundDiscountInfos(SqlParam<M235> params) throws Exception {
        return super.findRows(
                "SELECT discount_code, " +
                        " discount_name, " +
                        " busi_code, " +
                        " legal_code, " +
                        " remark, " +
                        " discount_role, " +
                        " discount_type, " +
                        " constant_rate  " +
                        " FROM " +
                        " fund_discount_info ", SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public SqlResult<M235> findAllDiscountCode(SqlParam<M235> params) throws Exception {

        return super.findRows("SELECT discount_code,discount_name FROM fund_discount_info", SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public UpdateResult addFundDiscountInfo(SqlParam<M235> params) throws Exception {
        return super.update("INSERT INTO fund_discount_info( " +
                        "    discount_code, " +
                        "    busi_code, " +
                        "    remark, " +
                        "    discount_role, " +
                        "    legal_code, " +
                        "    discount_type, " +
                        "    constant_rate, " +
                        "    discount_name " +
                        ") values ( " +
                        "    $S{discountCode}, " +
                        "    $S{busiCode}, " +
                        "    $S{remark}, " +
                        "    $S{discountRole}, " +
                        "    $S{legalCode}, " +
                        "    $S{discountType}, " +
                        "    $D{constantRate}, " +
                        "    $S{discountName} " +
                        ")",
                SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public UpdateResult updateFundDiscountInfo(SqlParam<M235> params) throws Exception {
        return super.update("update fund_discount_info set busi_code = $S{busiCode},legal_code = $S{legalCode}," +
                        "remark = $S{remark},discount_role = $S{discountRole},discount_type = $S{discountType}," +
                        "constant_rate = $S{constantRate}, discount_name = $S{discountName}" +
                        " WHERE  discount_code=$S{discountCode} ",
                SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public UpdateResult deleteFundDiscountInfo(SqlParam<M235> params) throws Exception {
        return super.update("DELETE FROM fund_discount_info WHERE  discount_code=$S{discountCode} ",
                SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public SqlResult findDiscountInfosByProd(SqlParam<M235> params) throws Exception {
        String sql = "SELECT" +
                " f1.tano," +
                " f1.prod_code prod_code," +
                " f1.begin_date begin_date," +
                " f2.legal_code legal_code," +
                " f2.discount_code discount_code," +
                " f2.discount_name discount_name," +
                " f2.busi_code busi_code," +
                " f2.discount_role discount_role," +
                " f2.discount_type discount_type " +
                "FROM fund_prod_discount f1 LEFT JOIN " +
                "fund_discount_info f2 ON f1.discount_code=f2.discount_code " +
                "WHERE f2.legal_code = $S{legalCode}" +
                " AND f1.tano=$S{tano} " +
                " AND f1.prod_code=$S{prodCode} ";
        List<SqlRow> sqlRows = super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params.getModel());
        List<SqlRow> sqlRowList = sqlRowToCamel(sqlRows);
        return SqlResult.build(sqlRowList);
    }

    public SqlResult<M235> findDiscountInfos(SqlParam<M235> params) throws Exception {
        String sql = "SELECT discount_code, discount_name, busi_code, remark, discount_role,legal_code," +
                " discount_type, constant_rate FROM fund_discount_info where legal_code = $S{legalCode}";
        if (StringUtils.isNotBlank(params.getModel().getBusiCode())){
            sql += " and busi_code = $S{busiCode}";
        }
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public SqlResult findProdDiscountInfo(SqlParam<M235> params) throws Exception {
        //params.setMakeSql(true);
        String sql = "SELECT discount_code FROM fund_prod_discount WHERE discount_code=$S{discountCode} ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);

    }

    public String queryMaxDiscountCode(SqlParam<M235> params) throws Exception {
        params.setMakeSql(false);
        SqlResult<M235> sqlResult =  super.findRows("select max(discount_code) as discount_code from  fund_discount_info "
                , SubDatabase.DATABASE_FUND_CENTER, params);
        return sqlResult.getRows().size() > 0 ? sqlResult.getRows().get(0).getDiscountCode():null;
    }

    public void useDiscount(M235 m235)throws Exception{
        // 删除产品关联的折扣率方案
        String sql = "DELETE FROM fund_prod_discount " +
                "WHERE " +
                " tano = $S{tano} " +
                " AND discount_code = $S{discountCode} "+
                " AND prod_code = $S{prodCode} ";
        super.update(sql, SubDatabase.DATABASE_FUND_CENTER, m235);
        sql = "INSERT INTO fund_prod_discount(prod_code, tano, discount_code,busi_code, begin_date)" +
                " VALUES ($S{prodCode}, $S{tano}, $S{discountCode},$S{busiCode}, $S{beginDate})";
        super.update(sql, SubDatabase.DATABASE_FUND_CENTER, m235);
    }
}
