package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M671;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M671Dao extends ComnDao {

    @Autowired
    private ReportformUtil reportformUtil;

    public SqlResult<M671> findM671s(SqlParam<M671> params) throws  Exception{
        params.setMakeSql(true);
        String sql = "select t.tano,\n" +
                "       t.trans_date,\n" +
                "       t.cust_manager,\n" +
                "       t.prod_code,\n" +
                "       sum(t.stock_vol) as total_vol,\n" +
                "       sum(t.stock_vol * nav) as total_amt,\n" +
                "       sum(t.effe_num) as total_num,\n" +
                "       t2.ta_name\n" +
                "  from fund_vol_stock_cm t\n" +
                "  left join fund_ta_info t2 on t.tano = t2.tano\n" +
                " where 1 = 1\n"  +reportformUtil.getOrgIdForOrgLevel("t.",params.getAuthInfo().get("userid").toString())+
                " group by t.tano, t.trans_date, t.cust_manager, t.prod_code, t2.ta_name order by t.trans_date desc \n";

        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);
    }
}
