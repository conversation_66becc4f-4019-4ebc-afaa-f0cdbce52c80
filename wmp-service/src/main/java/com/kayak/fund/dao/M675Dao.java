package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M671;
import com.kayak.fund.model.M675;
import com.kayak.graphql.model.FetcherData;
import com.kayak.until.MakeSqlUntil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M675Dao extends ComnDao {
    public SqlResult<M675> findM675(SqlParam<M675> param) throws  Exception{
        param.setMakeSql(false);
        String sql3 = "";
        if(Tools.isNotBlank(param.getModel().getOrglevel())){
            if(param.getModel().getOrglevel().equals("1")){
                sql3 += " and bank_code = '"+param.getModel().getTransOrgno()+"' " ;
            }else if(param.getModel().getOrglevel().equals("2")){
                sql3 += " and BRANCH_CODE = '"+param.getModel().getTransOrgno()+"'  ";
            }else{
                sql3 += " and TRANS_ORGNO = '"+param.getModel().getTransOrgno()+"'  ";
            }
            param.getModel().setOrglevel(null);
            param.getModel().setTransOrgno(null);
        }
        String sql1 = "select * from ( select t1.tano,\n" +
                "               t1.busi_date,\n" +
                "               t1.trans_orgno,\n" +
                "               t1.cust_type,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '001' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as khs,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '002' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as xhs,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '008' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as djs,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '009' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as qxdjs,\n" +
                "               0 as yxhs,\n" +
                "               t2.ta_name\n" +
                "          from fund_cust_acct_req_log t1 left join fund_ta_info t2 on t1.tano = t2.tano\n" +
                "         where 1 = 1 and t1.system_no='FUND' \n" ;

        if(Tools.isNotBlank(sql3)){
           sql1 += sql3;
        }
        sql1 +=  "   group by t1.tano, t1.busi_date, t1.trans_orgno, t1.cust_type,t2.ta_name order by t1.busi_date desc )";
        sql1 = MakeSqlUntil.makeSql(sql1,param.getParams(),M675.class);

        String sql2 = " select * from ( select t1.tano,\n" +
                "               t1.busi_date,\n" +
                "               t1.trans_orgno,\n" +
                "               t1.cust_type,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '001' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as khs,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '002' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as xhs,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '008' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as djs,\n" +
                "               sum(case\n" +
                "                     when t1.busi_code = '009' then\n" +
                "                      1\n" +
                "                     else\n" +
                "                      0\n" +
                "                   end) as qxdjs,\n" +
                "               0 as yxhs,\n" +
                "               t2.ta_name\n" +
                "          from fund_cust_acct_req_log_h t1 left join fund_ta_info t2 on t1.tano = t2.tano\n" +
                "         where 1 = 1  and t1.system_no='FUND' ";
        if(Tools.isNotBlank(sql3)){
            sql2 += sql3;
        }
        sql2 += " group by t1.tano, t1.busi_date, t1.trans_orgno, t1.cust_type,t2.ta_name  order by t1.busi_date desc )";
        sql2 = MakeSqlUntil.makeSql(sql2,param.getParams(),M675.class);
        String sql =
                "select * from ( " + sql1 +
                        "union all \n" +
                        sql2 + ") ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public String findTaName(String tano) throws  Exception{
        String sql = "select ta_name from fund_ta_info where tano = '" + tano + "'" ;
        SqlRow row = super.findRow(sql, SubDatabase.DATABASE_FUND_CENTER,null);
        String taName="";//TA名称
        if (row != null && row.size() >= 1 && StringUtils.isNotBlank(row.getString("ta_name"))){
            taName=row.getString("ta_name");
        }
        return taName;
    }
}
