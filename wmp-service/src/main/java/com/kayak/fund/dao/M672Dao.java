package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M671;
import com.kayak.fund.model.M672;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class M672Dao extends ComnDao {
    //查询认购、申购、定投、赎回、转换、转托
    public SqlResult<M672> findM672(SqlParam<M672> param) throws  Exception{
         StringBuffer sql = new StringBuffer("select * from (");
         sql.append("select \n" +
                 "       sum(case when t.busi_code = '020' then t.app_amt else 0 end) as rg_amt,\n" +
                 "       sum(case when t.busi_code = '022' then t.app_amt else 0 end) as sg_amt,\n" +
                 "       sum(case when t.busi_code = '039' then t.app_amt else 0 end) as dt_amt,\n" +
                 "       sum(case when t.busi_code = '024' then t.app_vol else 0 end) as sh_vol,\n" +
                 "       sum(case when t.busi_code = '024' then t.app_amt else 0 end) as sh_amt,\n" +
                 "       sum(case when t.busi_code = '038' then t.app_amt else 0 end) as zhc_vol,\n" +
                 "       sum(case when t.busi_code = '037' then t.app_amt else 0 end) as zhr_vol,\n" +
                 "       sum(case when t.busi_code = '027' then t.app_amt else 0 end) as ztr,\n" +
                 "       sum(case when t.busi_code = '028' then t.app_amt else 0 end) as ztc,\n" +
                 "       t.channel_flag\n" );

         sql.append("  from fund_cust_trans_req_log t where 1=1 and t.system_no='FUND' \n");
        if(StringUtils.isNotBlank(param.getModel().getBusiDate())){
            sql.append(" and t.BUSI_DATE >=  '"+param.getModel().getBusiDate()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getBusiEndDate())){
            sql.append(" and t.BUSI_DATE <=  '"+param.getModel().getBusiEndDate()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and t.cust_manager = '"+param.getModel().getCustManager()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getOrgno())){
            sql.append(" and t.TRANS_ORGNO = '"+param.getModel().getOrgno()+"'");
        }
         sql.append(" group by  t.channel_flag\n");
        sql.append(" UNION ALL ");
        sql.append("select \n" +
                "       sum(case when t.busi_code = '020' then t.app_amt else 0 end) as rg_amt,\n" +
                "       sum(case when t.busi_code = '022' then t.app_amt else 0 end) as sg_amt,\n" +
                "       sum(case when t.busi_code = '039' then t.app_amt else 0 end) as dt_amt,\n" +
                "       sum(case when t.busi_code = '024' then t.app_vol else 0 end) as sh_vol,\n" +
                "       sum(case when t.busi_code = '024' then t.app_amt else 0 end) as sh_amt,\n" +
                "       sum(case when t.busi_code = '038' then t.app_amt else 0 end) as zhc_vol,\n" +
                "       sum(case when t.busi_code = '037' then t.app_amt else 0 end) as zhr_vol,\n" +
                "       sum(case when t.busi_code = '027' then t.app_amt else 0 end) as ztr,\n" +
                "       sum(case when t.busi_code = '028' then t.app_amt else 0 end) as ztc,\n" +
                "       t.channel_flag\n" );

        sql.append("  from fund_cust_trans_req_log_h t where 1=1  and t.system_no='FUND' \n");
        if(StringUtils.isNotBlank(param.getModel().getBusiDate())){
            sql.append(" and t.BUSI_DATE >=  '"+param.getModel().getBusiDate()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getBusiEndDate())){
            sql.append(" and t.BUSI_DATE <=  '"+param.getModel().getBusiEndDate()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and t.cust_manager = '"+param.getModel().getCustManager()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getOrgno())){
            sql.append(" and t.TRANS_ORGNO = '"+param.getModel().getOrgno()+"'");
        }
        sql.append(" group by  t.channel_flag\n");
        sql.append(" ) ");
        return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER,param);
    }

    //查询签约数、有效客户数
    public SqlResult<M672> queryCustProtocolInfo(SqlParam<M672> param) throws  Exception{
        StringBuffer sql = new StringBuffer("select count(t.cust_no) as effective_num\n" +
                "  from cust_protocol_info t\n" +
                "  left join cust_info t2 on t.cust_no = t2.cust_no\n" +
                " where t.sign_status = '1' ");
        if(StringUtils.isNotBlank(param.getModel().getBusiDate())){
            sql.append(" and t.CREATE_TIME >=  to_date('"+param.getModel().getBusiDate()+"','yyyyMMdd')");
        }
        if(StringUtils.isNotBlank(param.getModel().getBusiEndDate())){
            sql.append(" and t.CREATE_TIME <=  to_date('"+param.getModel().getBusiEndDate()+"','yyyyMMdd')");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and t2.cust_manager = '"+param.getModel().getCustManager()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getOrgno())){
            sql.append(" and t.TRANS_ORGNO = '"+param.getModel().getOrgno()+"'");
        }
        return super.findRows(sql.toString(), SubDatabase.DATABASE_CUST_CENTER,param);
    }

    //查询定投协议表的开通数
    public SqlResult<M672> queryPlanCustProtocol(SqlParam<M672> param) throws  Exception{
        StringBuffer sql = new StringBuffer("select count(*) plan_open_num \n" +
                "  from plan_cust_protocol t\n" +
                " where t.plan_protocol_status = '1'\n");
        if(StringUtils.isNotBlank(param.getModel().getBusiDate())){
            sql.append(" and t.CREATE_TIME >=  to_date('"+param.getModel().getBusiDate()+"','yyyyMMdd')");
        }
        if(StringUtils.isNotBlank(param.getModel().getBusiEndDate())){
            sql.append(" and t.CREATE_TIME <=  to_date('"+param.getModel().getBusiEndDate()+"','yyyyMMdd')");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and t.cust_manager = '"+param.getModel().getCustManager()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and (t.TRANS_BANK_CODE = '"+param.getModel().getOrgno()+"' or t.TRANS_BRANCH_CODE = '"+param.getModel().getOrgno()+"' or t.TRANS_SUB_BRANCH = '"+param.getModel().getOrgno()+"')");
        }
        return super.findRows(sql.toString(), SubDatabase.DATABASE_PLAN_CENTER,param);
    }

    //查询定投扣款次数
    public SqlResult<M672> queryPlanAmt(SqlParam<M672> param) throws  Exception{
        StringBuffer sql = new StringBuffer("select count(*) deduction_one,t.cust_no ,\n" +
                "  from plan_cust_exec_log t\n" +
                " where t.exec_status = '1'");
        if(StringUtils.isNotBlank(param.getModel().getBusiDate())){
            sql.append(" and t.CREATE_TIME >=  to_date('"+param.getModel().getBusiDate()+"','yyyyMMdd')");
        }
        if(StringUtils.isNotBlank(param.getModel().getBusiEndDate())){
            sql.append(" and t.CREATE_TIME <=  to_date('"+param.getModel().getBusiEndDate()+"','yyyyMMdd')");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and t.cust_manager = '"+param.getModel().getCustManager()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and (t.TRANS_BANK_CODE = '"+param.getModel().getOrgno()+"' or t.TRANS_BRANCH_CODE = '"+param.getModel().getOrgno()+"' or t.TRANS_SUB_BRANCH = '"+param.getModel().getOrgno()+"')");
        }
        sql.append(" group by t.cust_no ");
        return super.findRows(sql.toString(), SubDatabase.DATABASE_PLAN_CENTER,param);
    }

    //查询所有客户经理
    public SqlResult<M672> querCustManagerInfo(SqlParam<M672> param) throws  Exception{
        StringBuffer sql = new StringBuffer("select t.cust_manager,t.cust_manager_name, t.TRANS_ORGNO as orgno from cust_manager_info t where 1=1");
        if(StringUtils.isNotBlank(param.getModel().getCustManagerName())){
            sql.append(" and t.cust_manager_name like '%"+param.getModel().getCustManagerName()+"%'");
        }
        if(StringUtils.isNotBlank(param.getModel().getCustManager())){
            sql.append(" and t.cust_manager = '"+param.getModel().getCustManager()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getOrgno())){
            sql.append(" and t.TRANS_ORGNO = '"+param.getModel().getOrgno()+"'");
        }
        return super.findRows(sql.toString(), SubDatabase.DATABASE_CUST_CENTER,param);
    }
  /** public SqlResult<M672> queryPlanCustProtocol(SqlParam<M672> param) throws  Exception{
        StringBuffer sql = new StringBuffer("select count(*), t.cust_no\n" +
                "  from plan_cust_protocol t\n" +
                " where t.plan_protocol_status = '1'\n");
        if(StringUtils.isNotBlank(param.getModel().getBusiDate())){
            sql.append(" and t.CREATE_TIME >= "+param.getModel().getBusiDate()+"'");
        }
        if(StringUtils.isNotBlank(param.getModel().getBusiEndDate())){
            sql.append(" and t.CREATE_TIME <= "+param.getModel().getBusiEndDate()+"'");
        }
        sql.append( " group by t.cust_no ");
        return super.findRows(sql.toString(), SubDatabase.DATABASE_CUST_CENTER,param);
    }*/

  /**
   * <AUTHOR>
   * @Description 客户协议表，交易机构改为合并机构
   * @Date 2022/3/24
   * @Param [params]
   * @return com.kayak.core.sql.UpdateResult
   **/
  public UpdateResult UpdateOrgNo(Map<String,Object> params) throws Exception {
      return super.update("UPDATE cust_protocol_info SET TRANS_ORGNO = $S{mergeOrgno} WHERE TRANS_ORGNO = $S{removeOrgno} ",
              SubDatabase.DATABASE_CUST_CENTER,params);
  }

}
