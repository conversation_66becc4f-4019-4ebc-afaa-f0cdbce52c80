package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M518;
import com.kayak.fund.model.M646;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M646Dao extends ComnDao {

	/**
	 * 资金入账异常汇总报表
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M646> findPaymErrorBatchCapital(SqlParam<M646> params) throws Exception {
		String sql1 = "select * from ( select t.trans_date,\n" +
				"               t.ori_trans_date,\n" +
				"               t.ori_capital_serno,\n" +
				"               t.tano,\n" +
				"               t.prod_code,\n" +
				"               t.cust_no,\n" +
				"               t.busi_code,\n" +
				"               t.trans_amt,\n" +
				"               t3.ta_name,\n" +
				"               t2.cust_name,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code\n" +
				"          from paym_error_batch_capital t\n" +
				"          left join fund_cust_ta_acct t2 on t.tano = t2.tano\n" +
				"                                        and t.trans_acct_no =\n" +
				"                                            t2.trans_acct_no\n" +
				"          left join fund_ta_info t3 on t.tano = t3.tano\n" +
				"         where t.system_no = 'FUND' )";
		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M646.class);
		String sql2 = " select * from (  select t.trans_date,\n" +
				"               t.ori_trans_date,\n" +
				"               t.ori_capital_serno,\n" +
				"               t.tano,\n" +
				"               t.prod_code,\n" +
				"               t.cust_no,\n" +
				"               t.busi_code,\n" +
				"               t.trans_amt,\n" +
				"               t3.ta_name,\n" +
				"               t2.cust_name,\n" +
				"               t2.id_type,\n" +
				"               t2.id_code\n" +
				"          from paym_error_batch_capital_h t\n" +
				"          left join fund_cust_ta_acct t2 on t.tano = t2.tano\n" +
				"                                        and t.trans_acct_no =\n" +
				"                                            t2.trans_acct_no\n" +
				"          left join fund_ta_info t3 on t.tano = t3.tano\n" +
				"         where t.system_no = 'FUND' )";
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M646.class);
		String sql =
				"select * from ( " + sql1 +
						" union all \n" +
						sql2 + ") t order by t.trans_date desc";
		return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);

	}
}
