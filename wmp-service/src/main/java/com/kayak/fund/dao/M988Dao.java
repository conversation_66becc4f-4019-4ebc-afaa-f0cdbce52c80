package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M988;
import com.kayak.fund.model.M988;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

/**
 *
 */
@Repository
public class M988Dao extends ComnDao {

	/**
	 * 基金资金类交易确认历史流水查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M988> findFundCustCfmParam(SqlParam<M988> params) throws Exception {
		//params.setMakeSql(true);
		String sql2 = "select c.cust_name,\n" +
				"               c.ACCT_NO,\n" +
				"               c.trans_acct_no,\n" +
				"               c.busi_date,\n" +
				"               to_char(c.mactime, 'YYYY-MM-DD HH24:MI:SS') as mactime,\n" +
				"               c.busi_code,\n" +
				"               c.tano,\n" +
				"               c.prod_code,\n" +
				"               c.app_amt,\n" +
				"               c.ack_amt,\n" +
				"               c.app_vol,\n" +
				"               c.ack_vol,\n" +
				"               c.ack_date,\n" +
				"               c.charge_type,\n" +
				"               c.cust_type,\n" +
				"               c.ta_acct_no,\n" +
				"               c.cur,\n" +
				"               c.cust_manager,\n" +
				"               c.def_div_method,\n" +
				"               c.target_prod_code,\n" +
				"               c.app_serno,\n" +
				"               c.trans_status,\n" +
				"               c.capital_status,\n" +
				"               c.remark,\n" +
				"               c.legal_code,\n" +
				"               c.trans_fee, \n" +
				"        t.ta_name\n" +
				"          from fund_cust_trans_cfm_log_h c\n" +
				"          LEFT JOIN fund_cust_trans_req_log_h r ON r.app_serno = c.app_serno " +
				"          left join fund_ta_info t on r.tano = t.tano " +
				"          where c.system_no='FUND'  ";
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M988.class);
		String sql =
				"select * from ( " + sql2 + ") where 1=1 order by mactime desc nulls last ";
		return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
	}
}