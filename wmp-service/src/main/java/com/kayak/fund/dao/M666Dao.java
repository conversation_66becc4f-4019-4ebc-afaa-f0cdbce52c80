package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M666;
import org.springframework.stereotype.Repository;

@Repository
public class M666Dao extends ComnDao {
    public SqlResult<M666> queryFundFreqTrans(SqlParam<M666> param) throws  Exception{
        param.setMakeSql(true);
        String sql = "select cust_no,cust_name,acct_no,tano,ta_name,prod_code,prod_name,ABNORMAL_TYPE,trans_amt,BEGIN_DATE,END_DATE from fund_cust_abnormal_log t order by t.BEGIN_DATE desc ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

}
