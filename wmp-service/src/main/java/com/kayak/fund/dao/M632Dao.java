package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.param.model.M518;
import com.kayak.fund.model.M632;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

/**
 *
 */
@Repository
public class M632Dao extends ComnDao {

	/**
	 * 基金资金类交易流水查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M632> findFundProdBasicParam(SqlParam<M632> params) throws Exception {
		//params.setMakeSql(true);
		String sql2 = "  select r.cust_name,\n" +
				"        r.ACCT_NO,\n" +
				"        r.trans_acct_no,\n" +
				"        r.busi_date,\n" +
				"        to_char(r.mactime, 'YYYY-MM-DD HH24:MI:SS') as mactime,\n" +
				"        r.busi_code,\n" +
				"        r.trans_code,\n" +
				"        r.tano,\n" +
				"        r.prod_code,\n" +
				"        r.app_amt,\n" +
				"        r.ack_amt,\n" +
				"        r.app_vol,\n" +
				"        r.ack_vol,\n" +
				"        r.ack_date,\n" +
				"        r.charge_type,\n" +
				"        r.cust_type,\n" +
				"        r.ta_acct_no,\n" +
				"        r.cur,\n" +
				"        r.branch_code,\n" +
				"        r.sub_branch_code,\n" +
				"        r.cust_manager,\n" +
				"        r.def_div_method,\n" +
				"        r.channel_flag,\n" +
				"        r.channel_serno,\n" +
				"        r.target_prod_code,\n" +
				"        r.rtn_code,\n" +
				"        r.app_serno,\n" +
				"        r.trans_status,\n" +
				"        r.rtn_desc,\n" +
				"        r.capital_status,\n" +
				"        r.remark,\n" +
				"        r.legal_code,\n" +
				"        c.trans_fee,\n" +
				"        r.ta_name\n" +
				"   from fund_cust_trans_req_log r\n" +
				"   FULL JOIN fund_cust_trans_cfm_log c ON r.app_serno = c.app_serno where r.system_no='FUND' \n";

		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M632.class);
		String sql =
				"select * from ( " + sql2 + ") where 1=1 and trans_status!='1' order by mactime desc nulls last ";
		return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
	}
}