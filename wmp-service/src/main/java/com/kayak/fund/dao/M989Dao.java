package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M989;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

/**
 * @ClassName M989Dao
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/10 20:07
 * @Version 1.0
 **/
@Repository
public class M989Dao extends ComnDao {

    /**
     * 基金账户类交易申请历史流水
     * @param params
     * @return
     * @throws Exception
     */
    public SqlResult<M989> findFundProdBasicParam(SqlParam<M989> params) throws Exception {
        //params.setMakeSql(true);
        String sql1 = " select APP_SERNO,trans_Code,\n" +
                "               busi_Code,\n" +
                "               tano,\n" +
                "               ta_Acct_No,\n" +
                "               acct_No,\n" +
                "               cust_Name,\n" +
                "               instrepr_Id_Type,\n" +
                "               instrepr_Id_Code,\n" +
                "               branch_Code,\n" +
                "               sub_Branch_Code,\n" +
                "               trans_Acct_No,\n" +
                "               busi_Date,\n" +
                "               TO_CHAR(mactime,'hh24miss') as mactime,\n" +
                "               cust_Manager,\n" +
                "               ack_Date,\n" +
                "               trans_Status,\n" +
                "               channel_Flag from fund_cust_acct_req_log_h where 1=1  and system_no='FUND'";
        sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M989.class);
        sql1 = sql1+ " order by mactime desc ";
        String sql =
                "select * from ( " + sql1 + ")tt1 where 1=1 ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }
    
}
