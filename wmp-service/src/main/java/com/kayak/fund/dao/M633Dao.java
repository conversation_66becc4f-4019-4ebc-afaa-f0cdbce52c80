package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M632;
import com.kayak.fund.model.M633;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

/**
 *
 */
@Repository
public class M633Dao extends ComnDao {

	/**
	 * 基金账户类交易申请流水
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M633> findFundProdBasicParam(SqlParam<M633> params) throws Exception {
		//params.setMakeSql(true);
		params.getModel().setLegalCode(null);
		String sql2 = " select t1.APP_SERNO,\n" +
				"       t1.trans_Code,\n" +
				"       t1.busi_Code,\n" +
				"       t1.tano,\n" +
				"       t1.ta_Acct_No,\n" +
				"       t1.acct_No,\n" +
				"       t1.cust_Name,\n" +
				"       t1.instrepr_Id_Type,\n" +
				"       t1.instrepr_Id_Code,\n" +
				"       t1.branch_Code,\n" +
				"       t1.sub_Branch_Code,\n" +
				"       t1.trans_Acct_No,\n" +
				"       t1.busi_Date,\n" +
				"       DATE_FORMAT(mactime,'%Y-%m-%d %H:%i:%s') as mactime,\n" +
				"       t1.cust_Manager,\n" +
				"       t1.ack_Date,\n" +
				"       t1.trans_Status,\n" +
				"       t1.channel_Flag,\n" +
				"       t2.ta_name\n" +
				"  from fund_cust_acct_req_log t1\n" +
				"  left join fund_ta_info t2 on t1.tano = t2.tano where 1=1  and t1.system_no='FUND' \n ";
		if(Tools.isNotBlank(params.getModel().getAcctNo())){
			sql2 += " and t1.acct_no = '"+params.getModel().getAcctNo()+"' ";
			params.getModel().setAcctNo(null);
		}
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M633.class);
		sql2 = sql2+ " order by t1.mactime desc ";
		String sql =
				"select * from ( " + sql2 + ") where 1=1 ";
		return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
	}
}