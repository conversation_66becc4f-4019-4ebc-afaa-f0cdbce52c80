package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M650;
import org.springframework.stereotype.Repository;

/**
 *日产品基本信息数据
 */
@Repository
public class M650Dao extends ComnDao {


	/**
	 * 日产品基本信息数据
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M650> queryFundDayProdBaseInfo(SqlParam<M650> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("select * from FUND_DAY_PROD_BASE_INFO", SubDatabase.DATABASE_FUND_CENTER, params);
	}

}