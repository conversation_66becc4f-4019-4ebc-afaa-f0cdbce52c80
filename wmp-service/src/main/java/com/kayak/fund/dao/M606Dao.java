package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M606;
import org.springframework.stereotype.Repository;

@Repository
public class M606Dao extends ComnDao {

    public SqlResult<M606> findInfos(SqlParam<M606> params) throws Exception {
        return super.findRows("select * from fund_invest_report_doc",SubDatabase.DATABASE_FUND_CENTER, params);
    }

    public UpdateResult deleteInfo(SqlParam<M606> params) throws Exception {
        return super.update("DELETE FROM fund_invest_report_doc where doc_no = $S{docNo}",SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

    public UpdateResult addInfo(SqlParam<M606> params) throws Exception {
        String mysql = "INSERT INTO fund_invest_report_doc(doc_no, doc_type, doc_name, doc_path, attachment_full_name, doc_date, doc_remark)"+
                "VALUES($S{docNo}, $S{docType}, $S{docName}, $S{docPath}, $S{attachmentFullName}, $S{docDate}, $S{docRemark})";
        String oracle = "INSERT INTO fund_invest_report_doc(doc_no, doc_type, doc_name, doc_path, attachment_full_name, doc_date, doc_remark, last_upd_time)"+
                "VALUES($S{docNo}, $S{docType}, $S{docName}, $S{docPath}, $S{attachmentFullName}, $S{docDate}, $S{docRemark}, current_timestamp)";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }
}
