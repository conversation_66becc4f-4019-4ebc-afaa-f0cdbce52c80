package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fina.global.utils.Tools;
import com.kayak.fina.param.model.M505;
import com.kayak.fund.model.M660;
import com.kayak.fund.model.M665;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M665Dao extends ComnDao {

	/**
	 * 月交易确认汇总数据
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M665> findFundMonthRevenueSum(SqlParam<M665> params) throws Exception {
		params.setMakeSql(true);
		StringBuffer sb = new StringBuffer();
		sb.append(" select t.tano,t.prod_code,\n" +
				"        t.stat_year,\n" +
				"        t.report_cycle,\n" +
				"        t.begin_date,\n" +
				"        t.end_date,\n" +
				"        t.total_charge,\n" +
				"        t.total_subs_charge,\n" +
				"        t.total_apply_charge,\n" +
				"        t.total_redeem_charge,\n" +
				"        t.total_transfer_charge,\n" +
				"        t.total_service_charge,\n" +
				"        t.total_cust_charge,\n" +
				"        t.total_other_revenue,\n" +
				"        t.total_revenue,\n" +
				"        t2.year_total_charge,\n" +
				"        t2.year_total_subs_charge,\n" +
				"        t2.year_total_apply_charge,\n" +
				"        t2.year_total_redeem_charge,\n" +
				"        t2.year_total_transfer_charge,\n" +
				"        t2.year_total_service_charge,\n" +
				"        t2.year_total_cust_charge,\n" +
				"        t2.year_total_other_revenue,\n" +
				"        t2.year_total_revenue\n" +
				"   from FUND_MONTH_REVENUE_SUM t\n" +
				"   left join (select t.tano,\n" +
				"                     sum(t.total_charge) as year_total_charge,\n" +
				"                     sum(t.total_subs_charge) as year_total_subs_charge,\n" +
				"                     sum(t.total_apply_charge) as year_total_apply_charge,\n" +
				"                     sum(t.total_redeem_charge) as year_total_redeem_charge,\n" +
				"                     sum(t.total_transfer_charge) as year_total_transfer_charge,\n" +
				"                     sum(t.total_service_charge) as year_total_service_charge,\n" +
				"                     sum(t.total_cust_charge) as year_total_cust_charge,\n" +
				"                     sum(t.total_other_revenue) as year_total_other_revenue,\n" +
				"                     sum(t.total_revenue) as year_total_revenue\n" +
				"                from FUND_MONTH_REVENUE_SUM t\n" +
				"               where 1=1 " );
				/**if(StringUtils.isNotBlank(params.getModel().getReportDate())){
					sb.append("and t.stat_year = '"+params.getModel().getStatYear()+"' and t.stat_month <= '"+params.getModel().getStatMonth()+"' ");
				}
				sb.append(" group by t.tano) t2 on t.tano = t2.tano ");
				sb.append(" where 1=1 ");
				if(StringUtils.isNotBlank(params.getModel().getReportDate())){
					sb.append("and t.stat_year = '"+params.getModel().getStatYear()+"' and t.stat_month = '"+params.getModel().getStatMonth()+"' ");
				}*/
				if(StringUtils.isNotBlank(params.getModel().getProdCode())){
					sb.append(" and t.prod_code = '"+params.getModel().getProdCode()+"'");
				}
				if(StringUtils.isNotBlank(params.getModel().getStatYear())){
				 	sb.append("and t.stat_year = '"+params.getModel().getStatYear()+"'");
				}
				if(StringUtils.isNotBlank(params.getModel().getBeginDate())){
					sb.append("and t.begin_date > '"+params.getModel().getBeginDate()+"'");
				}
				if(StringUtils.isNotBlank(params.getModel().getEndDate())){
					sb.append("and t.end_date < '"+params.getModel().getEndDate()+"'");
				}
				 sb.append(" group by t.tano) t2 on t.tano = t2.tano ");
				 sb.append(" where 1=1 ");
				if(StringUtils.isNotBlank(params.getModel().getProdCode())){
					sb.append(" and t.prod_code = '"+params.getModel().getProdCode()+"'");
				}
				 if(StringUtils.isNotBlank(params.getModel().getStatYear())){
				 	sb.append("and t.stat_year = '"+params.getModel().getStatYear()+"'");
				 }
				if(StringUtils.isNotBlank(params.getModel().getBeginDate())){
					sb.append("and t.begin_date > '"+params.getModel().getBeginDate()+"'");
				}
				if(StringUtils.isNotBlank(params.getModel().getEndDate())){
					sb.append("and t.end_date < '"+params.getModel().getEndDate()+"'");
				}
// 手动分页
		List<M665> volList = super.findRows(M665.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M665> sqlResult = new SqlResult<>();
		sqlResult.setResults(volList.size());
		sqlResult.setRows(
				volList.subList(
						params.getStart(), Math.min(params.getStart() + params.getLimit(), volList.size())
				)
		);
		sqlResult.setDesensitized(false);
		return sqlResult;
	}
}
