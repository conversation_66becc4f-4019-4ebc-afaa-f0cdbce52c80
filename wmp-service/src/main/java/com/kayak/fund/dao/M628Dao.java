package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M628;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 */
@Repository
public class M628Dao extends ComnDao {


	/**
	 * 频繁开销户明细
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M628> findOpenCloseFundCustAcct(SqlParam<M628> params) throws Exception {
		StringBuffer sb = new StringBuffer("select id_code,trans_orgno,cust_manager,cust_type,cust_name,id_type,id_code,tano,protocol_serno,busi_date_list,id_code as query_id_code from ( ");
		sb.append(
				"select t.id_code,\n" +
				"       t.trans_orgno,\n" +
				"       t.cust_manager,\n" +
				"       t.cust_type,\n" +
				"       t.cust_name,\n" +
				"       t.id_type,\n" +
				"       t.tano,\n" +

				"       t.protocol_serno,\n" +
				"       XMLAGG(XMLPARSE(content to_char(t.BUSI_DATE)|| '-') ORDER BY t.BUSI_DATE).getclobval() AS busi_date_list \n" +
				"  from fund_cust_acct_req_log t\n" +
				"  where 1=1 and t.system_no='FUND' ");
		//开销户间隔天数
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getOcDate())){
			sb.append(" and t.protocol_serno in (select t3.protocol_serno from (select t2.protocol_serno,\n" +
					"       (to_date(max(t2.busi_date), 'yyyy.mm.dd') -\n" +
					"       to_date(min(t2.busi_date), 'yyyy.mm.dd')) as ts\n" +
					"  from fund_cust_acct_req_log t2 where 1=1  ");

			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
				sb.append("and t2.busi_date >= '"+params.getModel().getBusiDate()+"'");
			}
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
				sb.append("and t2.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
			}
			sb.append(" group by t2.protocol_serno) t3 where t3.ts >'"+params.getModel().getOcDate()+"')");
		}
		//开销户次数
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getOcNum())){
			sb.append(" and t.id_code in (select t4.id_code from (select count(*)as ocNum,t.id_code from fund_cust_acct_req_log t  where 1=1 ");
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
				sb.append("and t.busi_date >= '"+params.getModel().getBusiDate()+"'");
			}
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
				sb.append("and t.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
			}
			sb.append("group by t.id_code ) t4 where t4.ocNum>'"+params.getModel().getOcNum()+"')");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append("and t.busi_date >= '"+params.getModel().getBusiDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
			sb.append("and t.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getQueryIdCode())){
			sb.append("and t.id_code = '"+params.getModel().getQueryIdCode()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
			sb.append("and t.tano = '"+params.getModel().getTano()+"'");
		}
			sb.append(" group by t.trans_orgno,\n" +
				"          t.cust_manager,\n" +
				"          t.cust_type,\n" +
				"          t.cust_name,\n" +
				"          t.id_type,\n" +
				"          t.id_code,\n" +
				"          t.tano,\n" +
				"           t.protocol_serno having count(t.id_code) >= 2 ");
		sb.append(" UNION ALL ");
		sb.append(
				"select t.id_code,\n" +
						"       t.trans_orgno,\n" +
						"       t.cust_manager,\n" +
						"       t.cust_type,\n" +
						"       t.cust_name,\n" +
						"       t.id_type,\n" +
						"       t.tano,\n" +
						"       t.protocol_serno,\n" +
						"       XMLAGG(XMLPARSE(content to_char(t.BUSI_DATE)|| '-') ORDER BY t.BUSI_DATE).getclobval() AS busi_date_list \n" +
						"  from fund_cust_acct_req_log_h t\n" +
						"  where 1=1  and t.system_no='FUND' ");
		//开销户间隔天数
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getOcDate())){
			sb.append(" and t.protocol_serno in (select t3.protocol_serno from (select t2.protocol_serno,\n" +
					"       (to_date(max(t2.busi_date), 'yyyy.mm.dd') -\n" +
					"       to_date(min(t2.busi_date), 'yyyy.mm.dd')) as ts\n" +
					"  from fund_cust_acct_req_log_h t2 where 1=1  ");

			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
				sb.append("and t2.busi_date >= '"+params.getModel().getBusiDate()+"'");
			}
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
				sb.append("and t2.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
			}
			sb.append(" group by t2.protocol_serno) t3 where t3.ts >'"+params.getModel().getOcDate()+"')");
		}
		//开销户次数
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getOcNum())){
			sb.append(" and t.id_code in (select t4.id_code from (select count(*)as ocNum,t.id_code from fund_cust_acct_req_log_h t  where 1=1 ");
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
				sb.append("and t.busi_date >= '"+params.getModel().getBusiDate()+"'");
			}
			if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
				sb.append("and t.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
			}
			sb.append("group by t.id_code ) t4 where t4.ocNum>'"+params.getModel().getOcNum()+"')");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiDate())){
			sb.append("and t.busi_date >= '"+params.getModel().getBusiDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getBusiEndDate())){
			sb.append("and t.busi_date <= '"+params.getModel().getBusiEndDate()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getQueryIdCode())){
			sb.append("and t.id_code = '"+params.getModel().getQueryIdCode()+"'");
		}
		if(params.getModel() != null&&Tools.isNotBlank(params.getModel().getTano())){
			sb.append("and t.tano = '"+params.getModel().getTano()+"'");
		}
		sb.append(" group by t.trans_orgno,\n" +
				"          t.cust_manager,\n" +
				"          t.cust_type,\n" +
				"          t.cust_name,\n" +
				"          t.id_type,\n" +
				"          t.id_code,\n" +
				"          t.tano,\n" +
				"           t.protocol_serno having count(t.id_code) >= 2 ");
		sb.append(" ) ");
		List<M628> volList = super.findRows(M628.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M628> sqlRowSqlResult = new SqlResult<>();
		sqlRowSqlResult.setResults(volList.size());
		sqlRowSqlResult.setRows(volList.subList(
				params.getStart(), Math.min(params.getStart() + params.getLimit(), volList.size())
		));
		sqlRowSqlResult.setDesensitized(false);
		return sqlRowSqlResult;
	}

}