package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M678;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M678Dao extends ComnDao {
    public SqlResult<M678> findM678(SqlParam<M678> params) throws Exception {
        params.setMakeSql(true);
        String sql = " SELECT TRANS_SERNO,APP_SERNO,TRANS_CODE,TRANS_NAME,STATUS FROM AGGREGATION_TRANS_LOG WHERE STATUS IN('3','5','6','7') ORDER BY CRT_DATE,CRT_TIME ";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
    }


}
