package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M654;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M654Dao extends ComnDao {

	/**
	 * 基金销售收入统计报表(按产品)
	 * @param params
	 * @return M654
	 * @throws Exception
	 */
	public SqlResult<M654> findFundBonusSum(SqlParam<M654> params) throws Exception {
		//params.setMakeSql(true);
		StringBuilder sql = new StringBuilder("select * from ( ");
		sql.append(" select t.prod_code,\n" +
				"       t.tano,\n" +
				"       t.ta_name,\n" +
				"       t.prod_name,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '120' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as rgFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '122' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as sgFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '136' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as zhFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '124' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as shFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '126' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as ztgFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '139' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as dtfee,\n" +
				"sum(RECUPERATE_FEE) as recuperate_fee, " +/*--定投*/
				"sum(CHANGE_FEE) as change_fee, " +/*--定投*/
				"sum(TRANSFER_FEE) as transfer_fee, " +/*--定投*/
				"sum(TRANS_FEE) as trans_fee, " +/*--定投*/
				"sum(agency_fee) as agency_fee " +
				"  from (select cfm.*\n" +
				"          from fund_cust_trans_cfm_log cfm\n" +
				"          where 1=1 and cfm.trans_status = '3' and cfm.system_no = 'FUND' ");
		if(StringUtils.isNotBlank(params.getModel().getTano())){
			sql.append(" and cfm.tano='"+params.getModel().getTano()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getProdCode())){
			sql.append(" and cfm.prod_code='"+params.getModel().getProdCode()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMinTransDate())){
			sql.append(" and cfm.ack_date <'"+params.getModel().getMinTransDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMaxTransDate())){
			sql.append(" and cfm.ack_date >='"+params.getModel().getMaxTransDate()+"' ");
		}
		sql.append(" ) t  group by t.prod_code, t.tano, t.prod_name,t.ta_name");
		sql.append(" UNION ALL ");
		sql.append(" select t.prod_code,\n" +
				"       t.tano,\n" +
				"       t.ta_name,\n" +
				"       t.prod_name,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '120' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as rgFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '122' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as sgFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '136' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as zhFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '124' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as shFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '126' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as ztgFee,\n" +
				"       sum(case\n" +
				"             when t.busi_code = '139' then\n" +
				"              t.TRANS_FEE\n" +
				"             else\n" +
				"              0\n" +
				"           end) as dtfee,\n" +
				"sum(RECUPERATE_FEE) as recuperate_fee, " +/*--定投*/
				"sum(CHANGE_FEE) as change_fee, " +/*--定投*/
				"sum(TRANSFER_FEE) as transfer_fee, " +/*--定投*/
				"sum(TRANS_FEE) as trans_fee, " +/*--定投*/
				"sum(agency_fee) as agency_fee " +
				"  from (select cfm.*\n" +
				"          from fund_cust_trans_cfm_log_h cfm\n" +
				"          where 1=1   and cfm.trans_status = '3' and cfm.system_no = 'FUND' ");
		if(StringUtils.isNotBlank(params.getModel().getTano())){
			sql.append(" and cfm.tano='"+params.getModel().getTano()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getProdCode())){
			sql.append(" and cfm.prod_code='"+params.getModel().getProdCode()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMinTransDate())){
			sql.append(" and cfm.ack_date <'"+params.getModel().getMinTransDate()+"' ");
		}
		if(StringUtils.isNotBlank(params.getModel().getMaxTransDate())){
			sql.append(" and cfm.ack_date >='"+params.getModel().getMaxTransDate()+"' ");
		}
		sql.append(" ) t  group by t.prod_code, t.tano, t.prod_name,t.ta_name");
		sql.append(" ) ");
		return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
