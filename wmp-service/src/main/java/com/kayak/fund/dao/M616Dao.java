package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;

import com.kayak.fund.model.M616;
import com.kayak.fund.model.M616JSONTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 原名 TaDisInterfaceInfoDao
 */
@Repository
public class M616Dao extends ComnDao {
    public SqlResult<M616> query(SqlParam<M616> param) throws  Exception{
        param.setMakeSql(true);
        return super.findRows("SELECT interface_version,interface_file_name,interface_file_route,interface_upload_date" +
                ",data_status,creator,updator,create_time,update_time FROM wmp_interface_version_info WHERE 1=1 ", SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public int add(SqlParam<M616> param) throws Exception{
        param.getModel().setCreator("admin"); // ???
        param.getModel().setInterfaceUploadDate(DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) );
        return super.update("INSERT INTO wmp_interface_version_info(INTERFACE_VERSION,INTERFACE_FILE_NAME,INTERFACE_FILE_ROUTE," +
                "INTERFACE_UPLOAD_DATE," +
                "DATA_STATUS,CREATOR,CREATE_TIME,UPDATE_TIME)VALUES($S{interfaceVersion},$S{interfaceFileName},$S{interfaceFileRoute}," +
                "$S{interfaceUploadDate},'E',$S{creator},sysdate(),sysdate())",SubDatabase.DATABASE_FUND_CENTER,param.getModel()).getEffect();
    }

    public SqlResult<M616> checkOne(SqlParam<M616> param) throws Exception{
        param.setMakeSql(false);
        return super.findRows("SELECT interface_version,interface_file_name,interface_file_route,interface_upload_date" +
                ",data_status,creator,updator,create_time,update_time FROM wmp_interface_version_info WHERE 1=1 " +
                " AND  interface_version = $S{interfaceVersion}  ",SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public int addJSON(M616JSONTemplate param) throws Exception{
        return super.update("INSERT INTO wmp_interface_detail_info(INTERFACE_VERSION,INTERFACE_FILE_TYPE,INTERFACE_JSON_NAME," +
                "INTERFACE_JSON_ROUTE,INTERFACE_CREATE_DATE,DATA_STATUS,CREATOR,create_time,update_time)VALUES($S{interfaceVersion}," +
                "$S{interfaceFileType},$S{interfaceJsonName},$S{interfaceJsonRoute},$S{interfaceCreateDate}," +
                "'E',$S{creator},sysdate(),sysdate())",SubDatabase.DATABASE_FUND_CENTER,param).getEffect();
    }

    /**
     * 查询接口明细信息
     * @param param
     * @return
     * @throws Exception
     */
    public SqlResult<M616JSONTemplate> queryJSON(SqlParam<M616JSONTemplate> param) throws  Exception{
        param.setMakeSql(true);
        return super.findRows("SELECT interface_version,interface_file_type,interface_json_name,interface_json_route,interface_create_date" +
                ",data_status,creator,updator,create_time,update_time FROM wmp_interface_detail_info WHERE 1=1 ",
                SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public int stop(SqlParam<M616> param) throws Exception{
        param.setMakeSql(false);
        return super.update("update wmp_interface_version_info set DATA_STATUS='D'  " +
                "where interface_version = $S{interfaceVersion}",SubDatabase.DATABASE_FUND_CENTER,param.getModel()).getEffect();
    }
    public int stopJSON(SqlParam<M616> param) throws Exception{
        param.setMakeSql(false);
        return super.update("update wmp_interface_detail_info set DATA_STATUS='D' " +
                " where interface_version = $S{interfaceVersion}",SubDatabase.DATABASE_FUND_CENTER,param.getModel()).getEffect();
    }
    public int start(SqlParam<M616> param) throws Exception{
        param.setMakeSql(false);
        return super.update("update wmp_interface_version_info set DATA_STATUS='E' " +
                " where interface_version = $S{interfaceVersion}",SubDatabase.DATABASE_FUND_CENTER,param.getModel()).getEffect();
    }
    public int startJSON(SqlParam<M616> param) throws Exception{
        param.setMakeSql(false);
        return super.update("update wmp_interface_detail_info set DATA_STATUS='E'" +
                "  where interface_version = $S{interfaceVersion}",SubDatabase.DATABASE_FUND_CENTER,param.getModel()).getEffect();
    }
    public SqlResult checkStatus(SqlParam<M616> params) throws Exception{
        params.setMakeSql(false);
        return super.findRows("SELECT wivi.interface_version FROM wmp_interface_version_info wivi " +
                " INNER JOIN WMP_INTERFACE_INFO wii ON wivi.INTERFACE_VERSION  = wii.INTERFACE_VERSION " +
                " INNER JOIN FUND_TA_INFO fti ON wii.INTERFACE_ID = fti.INTERFACE_ID " +
                " where wivi.DATA_STATUS = 'E' and wivi.interface_version = $S{interfaceVersion}  ",SubDatabase.DATABASE_FUND_CENTER,params);
    }
}
