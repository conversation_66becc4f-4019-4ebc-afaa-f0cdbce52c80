package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fina.param.model.M518;
import com.kayak.fund.model.M644;
import com.kayak.until.MakeSqlUntil;
import org.springframework.stereotype.Repository;

@Repository
public class M644Dao extends ComnDao {

	/**
	 * 产品费率信息查询
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M644> findFundCustTransReq(SqlParam<M644> params) throws Exception {
		//params.setMakeSql(true);
		String custNo = params.getModel().getCustNo();
		params.getModel().setCustNo(null);
		String sql1 = "select t.tano,\n" +
				"            t.ta_name,\n" +
				"               t.trans_orgno,\n" +
				"               t.busi_code,\n" +
				"              t.cust_name,\n" +
				"                       t.cust_no,\n" +
				"                       t.acct_no,\n" +
				"                       t.app_amt,\n" +
				"                       t.app_vol,\n" +
				"                       t.prod_risk_level,\n" +
				"                       t.cust_risk_level,\n" +
				"               t.id_type,\n" +
				"               t.id_code\n" +
				"                  from fund_cust_trans_req_log t where 1=1 and t.system_no='FUND' ";
		if(Tools.isNotBlank(custNo)){
			sql1 = sql1 +" and cust_no in ('"+custNo+"')";
		}
		sql1 = MakeSqlUntil.makeSql(sql1,params.getParams(), M644.class);
		String sql2 = " select t.tano,\n" +
				"                       t.ta_name,\n" +
				"                       t.trans_orgno,\n" +
				"                       t.busi_code,\n" +
				"                       t.cust_name,\n" +
				"                       t.cust_no,\n" +
				"                       t.acct_no,\n" +
				"                       t.app_amt,\n" +
				"                       t.app_vol,\n" +
				"                       t.prod_risk_level,\n" +
				"                       t.cust_risk_level,\n" +
				"               t.id_type,\n" +
				"               t.id_code\n" +
				"                  from fund_cust_trans_req_log_h t where 1=1  and t.system_no='FUND' ";
		if(Tools.isNotBlank(custNo)){
			sql2 = sql2 +" and cust_no in ('"+custNo+"')";
		}
		sql2 = MakeSqlUntil.makeSql(sql2,params.getParams(),M644.class);
		String sql =
				"select * from ( " + sql1 +
						"union all \n" +
						sql2 + ") ";
		return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);
	}
}
