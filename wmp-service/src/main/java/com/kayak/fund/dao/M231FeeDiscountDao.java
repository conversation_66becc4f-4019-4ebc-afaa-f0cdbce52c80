package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.AddFrom;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.exception.PromptException;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.SqlRow;
import com.kayak.core.system.RequestSupport;

import com.kayak.fund.model.M231FeeDiscount;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 最大折扣率服务，原名 fundTaPrjFeeDiscountDao
 */
@Repository
public class M231FeeDiscountDao extends ComnDao {
	/**
	 * 修改标志
	 */
	private final  String OPER_FLA_UPT = "1";
	/**
	 * 删除标志
	 */
	private final  String OPER_FLA_DEL = "2";

	private String property(){
		return "SELECT t.prod_code," +
				"       t.tano," +
				"       t.fee_type," +
				"       t.create_time," +
				"       t.crt_user," +
				"       t.update_time," +
				"       t.upd_user," +
				"       t.remark," +
				"       t.resolve_way ";
	}
	public SqlResult<M231FeeDiscount> findM231FeeDiscounts(SqlParam<M231FeeDiscount> params) throws Exception {
		String sql = property()+"       ,t.min_discount * 100 AS min_discount," +

				"       dis.ta_name" +
				"  FROM fina_ta_prj_fee_discount t" +
				"  LEFT JOIN fund_ta_info dis "+
				"    ON t.tano = dis.tano ";

		SqlResult<M231FeeDiscount> rows =super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
		return rows;
	}
	public SqlResult<M231FeeDiscount> findM231FeeDiscountsByDisCode(SqlParam<M231FeeDiscount> params) throws Exception {
		String sql = property()+"       ,t.min_discount * 100 AS min_discount" +
				"       ,t.add_from,info.prod_short_name," +
				"       dis.ta_name" +
				"  FROM fina_ta_prj_fee_discount t" +
				"  LEFT JOIN fund_prod_info info" +
				"    ON t.prod_code = info.prod_code "+
				"  LEFT JOIN fund_ta_info dis "+
				"    ON t.tano = dis.tano where 1=1";

		if(!StringUtils.isEmpty(params.getModel().getProdCode())){
			sql += "  and  t.prod_code like '%$U{prodCode}%'";
		}
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql += "  and t.tano = $S{tano}";
		}
		SqlResult<M231FeeDiscount> rows =super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER, params);
		return rows;
	}

	public SqlResult<M231FeeDiscount> findM231FeeDiscountsByProdCode(SqlParam<M231FeeDiscount> params) throws Exception {
		String sql = property()+",t.min_discount * 100 AS min_discount" +
				"      ,t.add_from ,info.prod_short_name," +
				"       dis.ta_name" +
				"  FROM fina_ta_prj_fee_discount t" +
				"  LEFT JOIN fund_prod_info info" +
				"    ON t.prod_code = info.prod_code "+
				"  LEFT JOIN fund_ta_info dis "+
				"    ON t.tano = dis.tano where 1=1";

		if(!StringUtils.isEmpty(params.getModel().getProdCode())){
			sql += "  and t.prod_code = $S{prodCode}";
		}
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql += " and  t.tano like '%$U{tano}%'";
		}
		SqlResult<M231FeeDiscount> rows =super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
		return rows;
	}

	public SqlResult<M231FeeDiscount> findM231FeeDiscount(SqlParam<M231FeeDiscount> params) throws Exception {
		String sql = "SELECT t.prod_code  FROM fina_ta_prj_fee_discount t where  t.prod_code = $S{prodCode} " +
			"  AND t.tano = $S{tano} " +
			"  AND t.fee_type = $S{feeType} ";
		SqlResult<M231FeeDiscount> rows =super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, params);
		return rows;
	}

	public void addM231FeeDiscount(List<M231FeeDiscount> params) throws Exception {
		doTrans(() -> {
			for (M231FeeDiscount m231FeeDiscount : params) {
				String sql = "SELECT COUNT(1) AS count " +
						"                    FROM fina_ta_prj_fee_discount t " +
						"                    WHERE " +
						"                    t.prod_code = $S{prodCode} " +
						"                    AND t.tano = $S{tano} " +
						"                    AND t.fee_type = $S{feeType}  ";
				List<SqlRow> rows = super.findRows(sql, SubDatabase.DATABASE_SYS_CENTER, m231FeeDiscount);
				if(rows.get(0).getInteger("count") > 0){
					//已经存在，不插入
					continue;
				}
				String sqlAll = "INSERT INTO fina_ta_prj_fee_discount(prod_code,tano,fee_type,min_discount,crt_time,crt_user,upd_time,upd_user,remark,resolve_way,add_from,legal_code) " +
								"VALUES($S{prodCode},$S{tano},$S{feeType},$D{minDiscount},current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark},$S{resolveWay},$S{addFrom},$S{superLegalCode})";
				super.update(sqlAll,SubDatabase.DATABASE_SYS_CENTER, m231FeeDiscount);				}
		});

	}

	/**
	 * M50307U002HIS,M50307U002
	 * OPER_FLA = "1" 修改
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public String updateM231FeeDiscount(SqlParam<M231FeeDiscount> params) throws Exception {

		doTrans(() -> {
//			this.addHistory(params,"1");
			int i = this.updatePojo(params);
			if(i < 1){
				throw new PromptException("M23104", "修改失败");
			}
		});
		return RequestSupport.updateReturnJson(true, "修改成功", null).toString();
	}

	/**
	 * M50307U003HIS,M50307U003
	 * g OPER_FLA = "2";删除
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public String deleteM231FeeDiscount(SqlParam<M231FeeDiscount> params) throws Exception {
		doTrans(() -> {
//			this.addHistory(params,"2");
			int i = this.delete(params);
			if(i < 1){
				throw new PromptException("M23105","修改失败");
			}
		});
		return RequestSupport.updateReturnJson(true, "删除成功", null).toString();
	}

	public int add(SqlParam<M231FeeDiscount> params)throws Exception{
		String sqlAll = "INSERT INTO fina_ta_prj_fee_discount(prod_code,tano,fee_type,min_discount,crt_time,crt_user,upd_time,upd_user,remark,resolve_way,add_from) VALUES($S{prodCode},$S{tano},$S{feeType},$D{minDiscount},current_timestamp,$S{crtUser},current_timestamp,$S{updUser},$S{remark},$S{resolveWay},$S{addFrom})";
		return super.update(sqlAll,3,params.getModel()).getEffect();
	}
	public int delete(SqlParam<M231FeeDiscount> params)throws Exception{
		return super.update("DELETE FROM fina_ta_prj_fee_discount   WHERE" +
						" prod_code=$S{prodCode} " +
						"   AND tano=$S{tano} " +
						"   AND fee_type=$S{feeType}", SubDatabase.DATABASE_SYS_CENTER,
				params.getModel()).getEffect();
	}
	public int updatePojo(SqlParam<M231FeeDiscount> params)throws Exception{
		String sqlAll = "UPDATE fina_ta_prj_fee_discount SET min_discount=$D{minDiscount}  ,upd_time=current_timestamp ,upd_user=$S{updUser} ,remark=$S{remark} ,resolve_way=$S{resolveWay}  WHERE prod_code=$S{prodCode} AND tano=$S{tano} AND fee_type=$S{feeType} ";
		return super.update(sqlAll,SubDatabase.DATABASE_SYS_CENTER,params.getModel()).getEffect();
	}

	/**
	 * 这个方法要查询单边锁，如果销售商代码有从产品页面添加的数据责不允许修改表 如果产品代码有从销售商页面添加的数据不允许修改表
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M231FeeDiscount> getLock(SqlParam<M231FeeDiscount> params)throws Exception{
		String sql = property() + " FROM fina_ta_prj_fee_discount t WHERE ";
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql += " t.tano=$S{tano}  AND t.ADD_FROM =  '"+ AddFrom.PROD+"'  ";
		}else if(!StringUtils.isEmpty(params.getModel().getProdCode())){
			sql += " t.prod_code=$S{prodCode}  AND t.ADD_FROM = '"+AddFrom.DIS+"'  ";
		}
		return super.findRows( sql,SubDatabase.DATABASE_SYS_CENTER,params);
	}

	public int clearData(SqlParam<M231FeeDiscount> params, String openFlag, String clearSql) throws Exception {
		this.addHisApprove(params,openFlag);
		String sql = "DELETE FROM fina_ta_prj_fee_discount WHERE ";
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql += " and tano=$S{tano}  AND ADD_FROM !=  '"+AddFrom.PROD+"'  ";
		}else { //!StringUtils.isEmpty(params.getModel().getProdCode())
			sql += " and prod_code=$S{prodCode}  AND ADD_FROM != '"+AddFrom.DIS+"'  ";
		}
		return super.update(sql, SubDatabase.DATABASE_SYS_CENTER, params.getModel()).getEffect();
	}

	public int addHistory(SqlParam<M231FeeDiscount> params, String oper_fla) throws Exception{
		return  addHis(params,oper_fla,"   prod_code=$S{prodCode} AND tano=$S{tano}  AND fee_type=$S{feeType} ");
	}
	public int addHis(SqlParam<M231FeeDiscount> params, String openFlag, String sql)throws Exception{
		String sqlAll = "INSERT INTO fina_ta_prj_fee_discount_HIS " +
				"  (PROD_CODE,tano,FEE_TYPE,MIN_DISCOUNT,CRT_TIME,CRT_USER,UPD_TIME,UPD_USER,REMARK,OPER_USER,OPER_DATE,OPER_FLAG,RESOLVE_WAY,add_from) select  " +
				"  PROD_CODE,tano,FEE_TYPE,MIN_DISCOUNT,CRT_TIME,CRT_USER,UPD_TIME,UPD_USER,REMARK,$S{crtUser},current_timestamp,'"+openFlag+"',RESOLVE_WAY,add_from from fina_ta_prj_fee_discount WHERE " + sql;
		return super.update(sqlAll,SubDatabase.DATABASE_SYS_CENTER,params.getModel()).getEffect();
	}
	public void addHisApprove(SqlParam<M231FeeDiscount> params, String openFlag)throws Exception{
		String sql = null;
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql = " tano=$S{tano}  AND ADD_FROM !=  '"+AddFrom.PROD+"'  ";
		}else if(!StringUtils.isEmpty(params.getModel().getProdCode())){
			sql = " prod_code=$S{prodCode}  AND ADD_FROM != '"+AddFrom.DIS+"'  ";
		}
		this.addHis(params,openFlag,sql);
	}
	/*public SqlResult<Ta3006> findHistory(SqlParam<Ta3006> params)throws Exception{
    	return findRows(property() + ",t.min_discount * 100 AS min_discount  FROM fund_ta_prj_fee_discount_HIS  t where ",3,params);
	}*/
	public SqlResult<M231FeeDiscount> select(SqlParam<M231FeeDiscount> params)throws Exception{
		String sql = property()+"  ,t.min_discount "  + " FROM fina_ta_prj_fee_discount t WHERE ";
		if(!StringUtils.isEmpty(params.getModel().getTano())){
			sql += " tano=$S{tano}  AND ADD_FROM !=  '"+AddFrom.PROD+"'  ";
		}else if(!StringUtils.isEmpty(params.getModel().getProdCode())){
			sql += " prod_code=$S{prodCode}  AND ADD_FROM != '"+AddFrom.DIS+"'  ";
		}
		return super.findRows( sql,SubDatabase.DATABASE_SYS_CENTER,params);
	}

}
