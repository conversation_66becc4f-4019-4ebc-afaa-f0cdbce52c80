package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.SequenceIncreaseUtil;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M609;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class M609Dao extends ComnDao {

    @Autowired
    private SequenceIncreaseUtil sequenceIncreaseUtil;

    public SqlResult<M609> findFundSalesViolations(SqlParam<M609> params) throws Exception {
        SqlResult<M609> rows = super.findRows("SELECT violation_no ,title,content,solve ,violation_date,input_userid ,last_upd_time,user_name " +
                "FROM fund_sales_violation", SubDatabase.DATABASE_FUND_CENTER, params);
        return rows;
    }

    public UpdateResult addInfo(SqlParam<M609> params) throws Exception {
        String mysql = "INSERT INTO fund_sales_violation(violation_no ,title,content,solve ,violation_date,input_userid,user_name  ) VALUES($S{violationNo} ,$S{title},$S{content},$S{solve} ,$S{violationDate},$S{inputUserid},$S{userName})";
        String oracle = "INSERT INTO fund_sales_violation(violation_no ,title,content,solve ,violation_date,input_userid ,last_upd_time,user_name ) VALUES($S{violationNo} ,$S{title},$S{content},$S{solve} ,$S{violationDate},$S{inputUserid},current_timestamp,$S{userName})";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult addFundSalesViolation(SqlParam<M609> params) throws Exception {
        String violationNo = sequenceIncreaseUtil.increaseNum("fund_sales_violation:violation_no",SubDatabase.DATABASE_FUND_CENTER);
        params.getModel().setViolationNo(violationNo);
       return addInfo(params);
    }

    public UpdateResult updateFundSalesViolation(SqlParam<M609> params) throws Exception {
        String mysql = "UPDATE fund_sales_violation SET title=$S{title},content=$S{content},solve=$S{solve} ,violation_date=$S{violationDate},input_userid=$S{inputUserid}  WHERE violation_no=$S{violationNo}";
        String oracle = "UPDATE fund_sales_violation SET title=$S{title},content=$S{content},solve=$S{solve} ,violation_date=$S{violationDate},input_userid=$S{inputUserid} ,last_upd_time = current_timestamp WHERE violation_no=$S{violationNo}";
        Sql sql = Sql.build().oracleSql(oracle).mysqlSql(mysql);
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    public UpdateResult deleteFundSalesViolation(SqlParam<M609> params) throws Exception {
        return super.update("DELETE FROM fund_sales_violation WHERE violation_no=$S{violationNo} ", SubDatabase.DATABASE_FUND_CENTER, params.getModel());
    }

}
