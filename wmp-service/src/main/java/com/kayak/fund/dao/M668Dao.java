package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M667;
import com.kayak.fund.model.M668;
import org.springframework.stereotype.Repository;

@Repository
public class M668Dao extends ComnDao {

	/**
	 * 基金销售异常交易汇总报表
	 */
	public SqlResult<M668> findFundVolCheck(SqlParam<M668> params) throws Exception {

		StringBuffer sb = new StringBuffer();
		sb.append("SELECT tano,\n" +
				"       ta_name,\n" +
				"       prod_code,\n" +
				"       COUNT(tano) error_count,\n" +
				"       SUM(t.sys_amt) sys_amt,\n" +
				"       SUM(t.sys_vol) sys_vol,\n" +
				"       SUM(t.other_amt) other_amt,\n" +
				"       SUM(t.other_vol) other_vol,\n" +
				"       ABNORMAL_TYPE\n" +
				"  FROM fund_cust_abnormal_log t\n" +
				" where 1 = 1\n" );

		 if (Tools.isNotBlank(params.getModel().getTano())){
			 sb.append(" and tano  = '"+params.getModel().getTano()+"'");
		 }
		if (Tools.isNotBlank(params.getModel().getProdCode())){
			sb.append(" and prod_code like '%"+params.getModel().getProdCode()+"%'");
		}
		if (Tools.isNotBlank(params.getModel().getBeginDate()) && Tools.isNotBlank(params.getModel().getEndDate())){
			sb.append(" and trans_date>='"+params.getModel().getBeginDate()+"' and trans_date<='"+params.getModel().getEndDate()+"' ");
		}
		sb.append(" GROUP BY tano, prod_code, ABNORMAL_TYPE, ta_name");

		return super.findRows(sb.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
