package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M651;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class M651Dao extends ComnDao {

	/**
	 * 基金销售收入统计报表
	 * @param params
	 * @return M651
	 * @throws Exception
	 */
	public SqlResult<M651> findFundBonusSum(SqlParam<M651> params) throws Exception {
		//params.setMakeSql(true);

		StringBuilder sql = new StringBuilder("select * from (select   \n" +
				"        t.tano,  \n" +
				"        t.ta_name,  \n" +
				"        t.prod_name,  \n" +
				"        t.prod_code,  \n" +
				"        sum(case when t.busi_code = '120' then t.TRANS_FEE else 0 end ) as rgFee,   \n" +
				"        sum(case when t.busi_code = '122' then t.TRANS_FEE else 0 end ) as sgFee,  \n" +
				"        sum(case when t.busi_code = '136' then t.TRANS_FEE else 0 end ) as zhFee,  \n" +
				"        sum(case when t.busi_code = '124' then t.TRANS_FEE else 0 end ) as shFee,  \n" +
				"        sum(case when t.busi_code = '126' then t.TRANS_FEE else 0 end ) as ztgFee, \n" +
				"        sum(case when t.busi_code = '139' then t.TRANS_FEE else 0 end ) as dtfee \n" +
				"        from (select cfm.*  \n" +
				"        from  \n" +
				"        fund_cust_trans_cfm_log cfm  \n" +
				"        inner join  \n" +
				"        fund_cust_trans_req_log req  \n" +
				"        on cfm.app_serno=req.app_serno  and cfm.system_no = 'FUND' \n");
				if(StringUtils.isNotBlank(params.getModel().getTransOrgno())){
					sql.append(" and req.trans_orgno='"+params.getModel().getTransOrgno()+"' ");
				}
				if(StringUtils.isNotBlank(params.getModel().getTano())){
					sql.append(" and cfm.tano='"+params.getModel().getTano()+"' ");
				}
				if(StringUtils.isNotBlank(params.getModel().getProdCode())){
					sql.append(" and cfm.prod_code='"+params.getModel().getProdCode()+"' ");
				}

				if(StringUtils.isNotBlank(params.getModel().getMinTransDate())){
					sql.append(" and cfm.ack_date >='"+params.getModel().getMinTransDate()+"' ");
				}
				if(StringUtils.isNotBlank(params.getModel().getMaxTransDate())){
					sql.append(" and cfm.ack_date <='"+params.getModel().getMaxTransDate()+"' ");
				}
				sql.append("   and cfm.trans_status='3')t group by t.tano,t.prod_code,t.ta_name,t.prod_name\n" +
				"        union all\n" +
				"        select   \n" +
				"        t.tano,  \n" +
				"        t.ta_name,  \n" +
				"        t.prod_name,  \n" +
				"        t.prod_code,  \n" +
				"        sum(case when t.busi_code = '120' then t.app_amt else 0 end ) as rgFee,   \n" +
				"        sum(case when t.busi_code = '122' then t.app_amt else 0 end ) as sgFee,  \n" +
				"        sum(case when t.busi_code = '136' then t.app_amt else 0 end ) as zhFee,  \n" +
				"        sum(case when t.busi_code = '124' then t.app_amt else 0 end ) as shFee,  \n" +
				"        sum(case when t.busi_code = '126' then t.app_amt else 0 end ) as ztgFee, \n" +
				"        sum(case when t.busi_code = '139' then t.app_amt else 0 end ) as dtfee \n" +
				"        from (select cfm.*  \n" +
				"        from  \n" +
				"        fund_cust_trans_cfm_log_h cfm  \n" +
				"        inner join  \n" +
				"        fund_cust_trans_req_log_h req  \n" +
				"        on cfm.app_serno=req.app_serno  and cfm.system_no = 'FUND' \n");
				if(StringUtils.isNotBlank(params.getModel().getTransOrgno())){
					sql.append(" and req.trans_orgno='"+params.getModel().getTransOrgno()+"' ");
				}
				if(StringUtils.isNotBlank(params.getModel().getTano())){
					sql.append(" and cfm.tano='"+params.getModel().getTano()+"' ");
				}
				if(StringUtils.isNotBlank(params.getModel().getProdCode())){
					sql.append(" and cfm.prod_code='"+params.getModel().getProdCode()+"' ");
				}

				if(StringUtils.isNotBlank(params.getModel().getMinTransDate())){
					sql.append(" and cfm.ack_date >='"+params.getModel().getMinTransDate()+"' ");
				}
				if(StringUtils.isNotBlank(params.getModel().getMaxTransDate())){
					sql.append(" and cfm.ack_date <='"+params.getModel().getMaxTransDate()+"' ");
				}
				sql.append("  and cfm.trans_status='3')t group by t.tano,t.prod_code,t.ta_name,t.prod_name) t");

		//sql.append(")t group by t.tano,t.prod_code,t.ta_name,t.prod_name");
		return super.findRows(sql.toString(), SubDatabase.DATABASE_FUND_CENTER, params);
	}
}
