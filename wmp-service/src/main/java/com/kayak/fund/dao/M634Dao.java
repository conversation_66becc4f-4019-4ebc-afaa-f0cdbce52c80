package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M634;
import org.springframework.stereotype.Repository;

/**
 *
 */
@Repository
public class M634Dao extends ComnDao {

	/**
	 * 客户异常交易查询
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M634> findFundProdBasicParam(SqlParam<M634> params) throws Exception {
		params.setMakeSql(true);
		return super.findRows("SELECT abnormal_serno,abnormal_type,trans_acct_no,trans_time,busi_code,trans_amt,begin_date," +
				"end_date,cust_no,ta_acct_no,tano,prod_code,ABNORMAL_DESC,sys_amt,sys_vol,other_amt,other_vol,ta_name,TRANS_DATE from fund_cust_abnormal_log where 1=1 order by trans_date desc ", SubDatabase.DATABASE_FUND_CENTER, params);
	}
}