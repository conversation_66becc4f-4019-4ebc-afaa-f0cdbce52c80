package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fina.param.model.M547;
import com.kayak.fina.param.model.M550;

import com.kayak.fund.model.M676;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class M676Dao extends ComnDao {

    public SqlResult<M676> findM676s(SqlParam<M676> param) throws  Exception{
        param.setMakeSql(true);
        String sql = "select TANO, PROD_CODE, ORGNO, BEGIN_DATE, END_DATE, " +
                "FEE_TYPE, TOTAL_AMOUNT, BANK_RATIO, REFER_DIVIDE_TYPE, " +
                "TOTAL_STOCK, ORG_STOCK, REFER_DIVIDE_AMT, ADJUST_AMT, " +
                "ACTUAL_DIVIDE_AMT, TRANSFER_STATUS, TRANS_DATE, ACTUAL_TRANS_DATE," +
                " OUT_ACCT_NO, IN_ACCT_NO, CUR " +
                "from fund_sale_fee_divide";
        return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public List<M676> ListM676(M676 param) throws  Exception{
        String sql = "select TANO, PROD_CODE, ORGNO, BEGIN_DATE, END_DATE, " +
                "FEE_TYPE, TOTAL_AMOUNT, BANK_RATIO, REFER_DIVIDE_TYPE, " +
                "TOTAL_STOCK, ORG_STOCK, REFER_DIVIDE_AMT, ADJUST_AMT, " +
                "ACTUAL_DIVIDE_AMT, TRANSFER_STATUS, TRANS_DATE, ACTUAL_TRANS_DATE," +
                " OUT_ACCT_NO, IN_ACCT_NO, CUR " +
                " from fund_sale_fee_divide\n"
                +" where prod_code = $S{prodCode} and tano = $S{tano} and begin_date = $S{beginDate} and end_date = $S{endDate}";
        return super.findRows(M676.class,sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public List<M547> findM547s(M676 param) throws  Exception{
        String sql = "select tano,orgno,prod_code,cust_type,\n"
                +"sum(case when cust_type = '1' then stock_vol else 0 end) as per_vol,\n"
                +"sum(case when cust_type = '0' then stock_vol else 0 end) as org_vol,\n"
                +"sum(case when cust_type = '0' or cust_type = '1' then stock_vol else 0 end) as vol\n"
                +" from fund_vol_stock \n"
                +" where prod_code = $S{prodCode} and tano = $S{tano} and trans_date >= $S{beginDate} and trans_date <= $S{endDate}\n"
                +"group by tano,orgno,prod_code,cust_type ";
        return super.findRows(M547.class,sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    public List<M550> findM550s(M676 param) throws  Exception{
        String sql = "select tano,orgno,prod_code,cust_type,sum(stock_amt) stock_amt\n" +
                "from fund_sale_stock\n"+
                " where prod_code = $S{prodCode} and tano = $S{tano} and trans_date >= $S{beginDate} and trans_date <= $S{endDate}\n"+
                "group by tano,orgno,prod_code,cust_type ";;
        return super.findRows(M550.class,sql, SubDatabase.DATABASE_FUND_CENTER,param);
    }

    //批量修改
    public void updateList(List<M676> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (M676 p : params) {
                String sql = "UPDATE fund_sale_fee_divide SET \n" +
                        "adjust_amt=$S{adjustAmt},actual_divide_amt=$S{actualDivideAmt},out_acct_no=$S{outAcctNo},in_acct_no=$S{inAcctNo},cur =$S{cur} \n"+
                        " where orgno = $S{orgno} and prod_code = $S{prodCode} and tano = $S{tano} and begin_date = $S{beginDate} and end_date = $S{endDate}";

                super.update(sql,SubDatabase.DATABASE_FUND_CENTER, p);
            }
        });
    }

    //批量添加
    public void addList(List<M676> params) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        doTrans(() -> {
            for (M676 p : params) {
                String sql = "INSERT INTO fund_sale_fee_divide\n" +
                        "(tano,prod_code,orgno,begin_date,end_date,\n" +
                        "total_amount,bank_ratio,refer_divide_type,total_stock,\n" +
                        "org_stock,refer_divide_amt,adjust_amt,actual_divide_amt,\n" +
                        "transfer_status,out_acct_no,in_acct_no,trans_date,fee_type,cur,cust_type)\n" +
                        "VALUES ($S{tano},$S{prodCode},$S{orgno},$S{beginDate},$S{endDate},\n" +
                        "$S{totalAmount},$S{bankRatio},$S{referDivideType},$S{totalStock},\n" +
                        "$S{orgStock},$S{referDivideAmt},$S{adjustAmt},$S{actualDivideAmt},\n" +
                        "$S{transferStatus},$S{outAcctNo},$S{inAcctNo},$S{transDate},$S{feeType},$S{cur},$S{custType})\n";
                super.update(sql,SubDatabase.DATABASE_FUND_CENTER, p);
            }
        });
    }

    //添加
    public UpdateResult add676(SqlParam<M676> params) throws Exception {
        String sql = "INSERT INTO fund_sale_fee_divide\n" +
                "(tano,prod_code,orgno,begin_date,end_date,\n" +
                "total_amount,bank_ratio,refer_divide_type,total_stock,\n" +
                "org_stock,refer_divide_amt,adjust_amt,actual_divide_amt,\n" +
                "transfer_status,out_acct_no,in_acct_no)\n" +
                "VALUES ($S{tano},$S{prodCode},$S{orgno},$S{beginDate},$S{endDate},\n" +
                "$S{totalAmount},$S{bankRatio},$S{referDivideType},$S{totalStock},\n" +
                "$S{orgStock},$S{referDivideAmt},$S{adjustAmt},$S{actualDivideAmt},\n" +
                "$S{transferStatus},$S{outAcctNo},$S{inAcctNo})\n";
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER,params.getModel());
    }

    //修改
    public UpdateResult update676(SqlParam<M676> params) throws Exception {
        String sql = "UPDATE fund_sale_fee_divide SET \n" +
                "adjust_amt=$S{adjustAmt},actual_divide_amt=$S{actualDivideAmt},\n" +
                "transfer_status=$S{transferStatus},out_acct_no=$S{outAcctNo},in_acct_no=$S{inAcctNo},cur=$S{cur}\n"+
                " where orgno = $S{orgno} and prod_code = $S{prodCode} and tano = $S{tano} and begin_date = $S{beginDate} and end_date = $S{endDate}";
        return super.update(sql,SubDatabase.DATABASE_FUND_CENTER, params);
    }
}
