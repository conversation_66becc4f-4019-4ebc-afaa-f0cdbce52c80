package com.kayak.fund.dao;

import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.Sql;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.sql.UpdateResult;
import com.kayak.fund.model.M630;
import org.springframework.stereotype.Repository;

@Repository
public class M630Dao extends ComnDao {

    public SqlResult<M630> findInfos(SqlParam<M630> params) throws Exception {
        return super.findRows("select t.cust_risk_level, count(t.cust_risk_level) as assess_num,sum(count(*)) over() as assess_count \n" +
                "  from cust_risk_assess_info t\n" +
                " group by t.cust_risk_level\n ",SubDatabase.DATABASE_CUST_CENTER, params);
    }

}
