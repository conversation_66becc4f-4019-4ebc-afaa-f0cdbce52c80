package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.common.util.ReportformUtil;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.fund.model.M660;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class M660Dao extends ComnDao {

	@Autowired
	private ReportformUtil reportformUtil;
	/**
	 * 保有量统计报表【按产品类型】
	 * @param params
	 * @returnM007
	 * @throws Exception
	 */
	public SqlResult<M660> findFundVolStockForOrg(SqlParam<M660> params) throws Exception {
		params.setMakeSql(true);
		StringBuffer sb = new StringBuffer();
		sb.append(" select t.tano,\n" +
				"       t.prod_code,\n" +
				"       sum(t.stock_vol) as stock_vol,\n" +
				"       sum(t.stock_vol * t.nav) as stock_amt,\n" +
				"       sum(t.effe_num) as effe_num,\n" +
				"       t.trans_date,\n" +
				"       t2.ta_name\n" +
				"  from fund_vol_stock t\n" +
				"  left join fund_ta_info t2 on t.tano = t2.tano where 1=1 " );
		sb.append(reportformUtil.getOrgIdForOrgLevel("t.",params.getParams().get("userid").toString()));
		if(StringUtils.isNotBlank(params.getModel().getProdCode())){
			sb.append(" and t.prod_code = '"+params.getModel().getProdCode()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTano())){
			sb.append(" and  t.tano = '"+params.getModel().getTano()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getCustType())){
			sb.append(" and  t.cust_type = '"+params.getModel().getCustType()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getFundType())){
			sb.append(" and  t.fund_type = '"+params.getModel().getFundType()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getOrgno())){
			sb.append(" and  t.orgno = '"+params.getModel().getOrgno()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransDate())){
			sb.append(" and  t.trans_date >= '"+params.getModel().getTransDate()+"'");
		}
		if(StringUtils.isNotBlank(params.getModel().getTransEndDate())){
			sb.append("  and t.trans_date <= '"+params.getModel().getTransEndDate()+"'");
		}
		sb.append("         group by  t.tano, t.prod_code, t.trans_date, t2.ta_name\n" +
				"         order by t.trans_date , t.tano  desc ");

		List<M660> volList = super.findRows(M660.class,sb.toString(), SubDatabase.DATABASE_FUND_CENTER,params);
		SqlResult<M660> sqlResult = new SqlResult<>();
		sqlResult.setResults(volList.size());
		sqlResult.setRows(volList);
		sqlResult.setDesensitized(false);
		return sqlResult;
	}
}
