package com.kayak.fund.dao;


import com.kayak.base.dao.ComnDao;
import com.kayak.common.constants.SubDatabase;
import com.kayak.core.sql.SqlParam;
import com.kayak.core.sql.SqlResult;
import com.kayak.core.util.Tools;
import com.kayak.fund.model.M648;
import com.kayak.fund.model.M649;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *日份额保有量汇总数据
 */
@Repository
public class M649Dao extends ComnDao {

	/**
	 * 日份额保有量汇总数据
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public SqlResult<M649> queryDayRetentionSum(SqlParam<M649> params) throws Exception {
		params.setMakeSql(true);
		String sql = " select * from FUND_DAY_VOL_SUM ";
		return super.findRows(sql, SubDatabase.DATABASE_FUND_CENTER,params);
	}
}