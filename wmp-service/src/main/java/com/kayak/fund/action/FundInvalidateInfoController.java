package com.kayak.fund.action;

import com.kayak.core.action.BaseController;
import com.kayak.core.system.RequestSupport;
import com.kayak.fund.service.M609Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

/**
 * @Description 文件上传与下载
 */
@Scope
@RestController
public class FundInvalidateInfoController extends BaseController {
    @Autowired
    private M609Service uploadExcelService;

    @PostMapping(value = "/excel/uploadFundViolation.json")
    @ResponseBody
    public String uploadExcel(@RequestParam(value = "file", required = false) MultipartFile file, HttpServletResponse response) {
        response.setContentType("text/html;chartset=UTF-8");
        // 1、文件校验
        String fileName = file.getOriginalFilename();
        if(file==null||file.isEmpty()){
            return RequestSupport.updateReturnJson(false, "文件不存在" + fileName, null).toString();
        }
        //文件后缀
        String extension = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : "";
        if (!"xlsx".equals(extension) && !"xls".equals(extension)) {
            return RequestSupport.updateReturnJson(false, "请上传Excel文件" + fileName, null).toString();
        }


        try(InputStream in = file.getInputStream()){
            uploadExcelService.uploadSalerViolateExcel(in,file);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RequestSupport.updateReturnJson(false, "文件上传失败！", null).toString();
        }
        return RequestSupport.updateReturnJson(true, "上传成功" + fileName, null).toString();


    }

}
