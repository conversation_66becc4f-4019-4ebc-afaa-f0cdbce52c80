##################################
#   JRES
##################################
# \u670D\u52A1\u522B\u79F0
app.alias=wmp-service
# \u670D\u52A1\u540D\u79F0
app.name=wmp-service
# \u670D\u52A1\u5206\u7EC4
app.group=FDS
# \u7248\u672C
app.version=2.0
# \u90E8\u7F72\u5305\u7248\u672C
app.deploy.version=2.0

# \u6CE8\u518C\u4E2D\u5FC3\u5730\u5740
app.registry.address=127.0.0.1:2181
# \u670D\u52A1WEB\u7AEF\u53E3
app.server.port=38089

app.host=127.0.0.1
app.owner=localhost

config.location=classpath:middleware.properties
logging.config=classpath:log4j2.xml
##################################
#   \u914D\u7F6E\u4E2D\u5FC3
##################################
# \u914D\u7F6E\u4E2D\u5FC3\u5730\u5740
configCenterUrls=************:9094
# \u5206\u652F
configBranchName=fdsbala
# \u662F\u5426\u542F\u7528\u81EA\u52A8\u66F4\u65B0
configcenter.spring.properties.auto-update-enabled=true

file.filePath=C:/wmp
file.transferAgreementFileName=\u8F6C\u8BA9\u534F\u8BAE.docx

##################################
#   ECM??????
##################################
# ?????????
ecm.ip=127.0.0.1
# ??????
ecm.port=8080
# ???????
ecm.userName=admin
# ??????
ecm.password=admin123
# ??????
ecm.groupName=default
# ??????????
ecm.indexModelCode=INDEX_MODEL
# ??????????
ecm.documentModelCode=DOCUMENT_MODEL
# ??????ID
ecm.contentId=CONTENT_001
# ??????????
ecm.filePartName=FILE_PART

##################################
#   ????????
##################################
# ??????
file.tem.path=C:/wmp/temp
# ????
upload.path=C:/wmp/upload
# Excel????
excel.maxlen=100
# Excel????
excel.tem.path=C:/wmp/excel/temp
# Excel????(Windows)
excel.conf.pathForWindows=C:/wmp/excel/conf
# Excel????
excel.conf.path=C:/wmp/excel/conf
# JSON????
json.conf.path=C:/wmp/json/conf

##################################
#   ?????
##################################
# ???????
spring.datasource.url=**********************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# ?????
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=WmpHikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

##################################
#   ?????
##################################
# ???????????????
workflow.enabled=false