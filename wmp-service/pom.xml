<?xml version="1.0"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kayak.wmp</groupId>
        <artifactId>wmp</artifactId>
        <version>1.0.0</version>
    </parent>
    <groupId>com.kayak.wmp</groupId>
    <artifactId>wmp-service</artifactId>
    <version>1.0.0</version>
    <name>wmp-service</name>
    <url>http://maven.apache.org</url>

    <dependencies>
       <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>fina-center-springcloud-dubbo</artifactId>
            <version>0.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>wmp-base-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>fina-center-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>prod-center-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>plan-center-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>paym-api</artifactId>
            <version>0.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Temporarily commented out - jar file not available
        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>bala-center-springcloud-dubbo</artifactId>
            <version>0.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        -->

        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>prod-center-springcloud-dubbo</artifactId>
            <version>0.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>cust-center-springcloud-dubbo</artifactId>
            <version>0.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.kayakwise.wmp</groupId>
            <artifactId>plan-center-springcloud-dubbo</artifactId>
            <version>0.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- POI dependency moved to newer version section -->

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.10</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.3</version>
        </dependency>
        <!--文件传输平台jar包引入 begin-->
        <dependency>
            <groupId>com.dcfs.fts</groupId>
            <artifactId>ftsp-sdk1.8</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.10</version>
        </dependency>
        <dependency>
            <groupId>org.owasp.esapi</groupId>
            <artifactId>esapi</artifactId>
            <version>2.1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>
        <!--文件传输平台jar包引入 end-->

        <!--<dependency>
            <groupId>com.sunyard</groupId>
            <artifactId>SunECMClient</artifactId>
            <version>3.3.0</version>
        </dependency>-->
        <dependency>
            <groupId>sun-client</groupId>
            <artifactId>sun-client</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.sunyard.ws</groupId>
            <artifactId>sun-ws-all</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.hundsun.jrescloud.middleware</groupId>
            <artifactId>middleware-configcenter-client</artifactId>
            <version>1.2.14</version>
        </dependency>

        <dependency>
            <groupId>com.hundsun.jrescloud.middleware</groupId>
            <artifactId>middleware-configcenter-common</artifactId>
            <version>1.2.14</version>
        </dependency>

        <dependency>
            <groupId>com.hundsun.jrescloud.middleware</groupId>
            <artifactId>middleware-base-common</artifactId>
            <version>1.0.7</version>
        </dependency>

        <dependency>
            <groupId>com.hundsun.jrescloud.middleware</groupId>
            <artifactId>middleware-tenant-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Apache Curator Dependencies -->
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>4.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.14</version>
        </dependency>

        <!-- K-Cloud Core Dependencies -->
        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-action</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-action-dubbo</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-core</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-dao</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-cache</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-config</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-xsql</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Additional Spring Dependencies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <!-- Spring Util is part of spring-core, no separate dependency needed -->

        <!-- Apache Commons -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>

        <!-- FastJSON -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.68</version>
        </dependency>

        <!-- JresCloud RPC -->
        <dependency>
            <groupId>com.hundsun.jrescloud</groupId>
            <artifactId>jrescloud-rpc-api</artifactId>
            <version>3.0.8.4</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.10</version>
            <scope>provided</scope>
        </dependency>

        <!-- Spring Boot Core Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
            <version>2.2.2.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>2.2.2.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>2.2.2.RELEASE</version>
        </dependency>

        <!-- Spring Data Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.2.2.RELEASE</version>
        </dependency>

        <!-- Servlet API -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>4.0.1</version>
            <scope>provided</scope>
        </dependency>

        <!-- Apache POI for Excel -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <!-- Google Guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>28.2-jre</version>
        </dependency>

        <!-- DOM4J -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>

        <!-- JSON -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20200518</version>
        </dependency>

        <!-- Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.10.1</version>
        </dependency>

        <!-- EasyExcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
        </dependency>

        <!-- Joda Time -->
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10.5</version>
        </dependency>

        <!-- Spring Quartz -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>5.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>

        <!-- MySQL Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.25</version>
        </dependency>

        <!-- XML Beans -->
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>

        <!-- JAXB -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.1</version>
        </dependency>

        <!-- Tomcat Embed -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.29</version>
        </dependency>

        <!-- CGLib -->
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>3.3.0</version>
        </dependency>

        <!-- JresCloud Common -->
        <dependency>
            <groupId>com.hundsun.jrescloud</groupId>
            <artifactId>jrescloud-common-boot</artifactId>
            <version>3.0.8.4</version>
        </dependency>

        <dependency>
            <groupId>com.hundsun.jrescloud</groupId>
            <artifactId>jrescloud-common-exception</artifactId>
            <version>3.0.8.4</version>
        </dependency>

        <dependency>
            <groupId>com.hundsun.jrescloud</groupId>
            <artifactId>jrescloud-common-gm</artifactId>
            <version>3.0.8.4</version>
        </dependency>

        <!-- Business Center APIs -->
        <dependency>
            <groupId>com.kayakwise.cust</groupId>
            <artifactId>cust-center-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.kayakwise.bala</groupId>-->
<!--            <artifactId>bala-center-api</artifactId>-->
<!--            <version>0.0.1-RELEASE</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.kayakwise.fina</groupId>
            <artifactId>fina-center-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.kayakwise.plan</groupId>
            <artifactId>plan-center-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.kayakwise.prod</groupId>
            <artifactId>prod-center-api</artifactId>
            <version>0.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.kayakwise</groupId>
            <artifactId>kayakwise-transaction-api</artifactId>
            <version>0.0.4</version>
        </dependency>

        <!--单元测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>2.2.2.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>wmp-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.8.RELEASE</version>
                <configuration>
                    <mainClass>com.kayak.WmpService</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
