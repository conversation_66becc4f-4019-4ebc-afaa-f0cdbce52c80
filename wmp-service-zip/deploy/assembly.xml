<assembly
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
    <!--	<id>release</id>-->
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.parent.basedir}/${main.artifactId}/target</directory>
            <includes>
                <!--				<include>${main.artifactId}-${project.version}.jar</include>-->
                <include>${main.artifactId}.jar</include>
            </includes>
            <outputDirectory>/${main.artifactId}/${main.artifactId}</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.parent.basedir}/${main.artifactId}/src/main/resources</directory>
            <includes>
                <include>**</include>
            </includes>
            <excludes>
                <exclude>log4j2.xml</exclude>
                <exclude>application.properties</exclude>
                <exclude>middleware.properties</exclude>
            </excludes>
            <outputDirectory>/${main.artifactId}/${main.artifactId}/config</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>deploy</directory>
            <includes>
                <include>**</include>
            </includes>
            <excludes>
                <exclude>assembly.xml</exclude>
            </excludes>
            <outputDirectory>/${main.artifactId}</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>  