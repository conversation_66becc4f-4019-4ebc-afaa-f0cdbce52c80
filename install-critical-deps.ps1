# Install critical missing dependencies
Write-Host "Installing critical missing dependencies..." -ForegroundColor Green

$criticalDeps = @(
    @{jar="k-cloud-action-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-action"; version="1.0.0"},
    @{jar="k-cloud-core-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-core"; version="1.0.0"},
    @{jar="k-cloud-dao-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-dao"; version="1.0.0"},
    @{jar="k-cloud-cache-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-cache"; version="1.0.0"},
    @{jar="fastjson-1.2.68.jar"; groupId="com.alibaba"; artifactId="fastjson"; version="1.2.68"},
    @{jar="jrescloud-rpc-api-*******.jar"; groupId="com.hundsun.jrescloud"; artifactId="jrescloud-rpc-api"; version="*******"},
    @{jar="commons-lang3-3.12.0.jar"; groupId="org.apache.commons"; artifactId="commons-lang3"; version="3.12.0"},
    @{jar="spring-beans-5.2.9.RELEASE.jar"; groupId="org.springframework"; artifactId="spring-beans"; version="5.2.9.RELEASE"},
    @{jar="spring-context-5.2.9.RELEASE.jar"; groupId="org.springframework"; artifactId="spring-context"; version="5.2.9.RELEASE"},
    @{jar="spring-core-5.2.9.RELEASE.jar"; groupId="org.springframework"; artifactId="spring-core"; version="5.2.9.RELEASE"}
)

$successCount = 0
$failCount = 0

foreach ($dep in $criticalDeps) {
    $jarPath = "lib-extracted\$($dep.jar)"
    
    if (Test-Path $jarPath) {
        Write-Host "Installing: $($dep.groupId):$($dep.artifactId):$($dep.version)" -ForegroundColor Cyan
        
        $result = & mvn install:install-file -Dfile="$jarPath" -DgroupId="$($dep.groupId)" -DartifactId="$($dep.artifactId)" -Dversion="$($dep.version)" -Dpackaging=jar -DgeneratePom=true 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: $($dep.jar)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "FAILED: $($dep.jar)" -ForegroundColor Red
            Write-Host "Error: $result" -ForegroundColor Red
            $failCount++
        }
    } else {
        Write-Host "FILE NOT FOUND: $jarPath" -ForegroundColor Yellow
        $failCount++
    }
}

Write-Host "`nInstallation Summary:" -ForegroundColor Yellow
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red

if ($successCount -gt 0) {
    Write-Host "`nNow checking local Maven repository for additional dependencies..." -ForegroundColor Green
    
    # Check if dependencies are available in local repository
    $localRepo = "$env:USERPROFILE\.m2\repository"
    Write-Host "Local Maven repository: $localRepo" -ForegroundColor Yellow
    
    $checkDeps = @(
        "org\springframework\spring-beans\5.2.9.RELEASE",
        "org\springframework\spring-context\5.2.9.RELEASE", 
        "org\springframework\spring-core\5.2.9.RELEASE",
        "org\springframework\spring-web\5.2.9.RELEASE",
        "org\springframework\spring-webmvc\5.2.9.RELEASE",
        "org\springframework\spring-tx\5.2.9.RELEASE",
        "org\springframework\boot\spring-boot\2.2.2.RELEASE",
        "org\springframework\boot\spring-boot-autoconfigure\2.2.2.RELEASE",
        "org\projectlombok\lombok\1.18.10",
        "com\alibaba\fastjson\1.2.68"
    )
    
    foreach ($depPath in $checkDeps) {
        $fullPath = Join-Path $localRepo $depPath
        if (Test-Path $fullPath) {
            Write-Host "FOUND: $depPath" -ForegroundColor Green
        } else {
            Write-Host "MISSING: $depPath" -ForegroundColor Red
        }
    }
}

Write-Host "`nCritical dependencies installation completed!" -ForegroundColor Green
