<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>



    <groupId>com.kayak.wmp</groupId>
    <artifactId>wmp</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <distributionManagement>
        <site>
            <id>website</id>
            <url>scp://webhost.company.com/www/website</url>
        </site>
    </distributionManagement>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <modules>
        <module>wmp-service</module>
		<module>wmp-service-zip</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>k-cloud</groupId>
            <artifactId>k-cloud-service-starter-micro-server</artifactId>
            <version>1.0.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>k-cloud</groupId>-->
<!--            <artifactId>k-cloud-discovery-nacos</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
    </dependencies>

    <!--<repositories>
        <repository>
            <id>Kayakwise</id>
            <name>Kayakwise Maven</name>
            <url>https://maven.kkws.cn/content/repositories/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        &lt;!&ndash; 阿里云仓库 &ndash;&gt;
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/repositories/central/</url>
        </repository>
    </repositories>-->
</project>