# Create Maven install script for extracted dependencies
param(
    [string]$LibDir = ".\lib-extracted",
    [string]$OutputScript = ".\install-to-maven.bat"
)

Write-Host "Creating Maven install script..." -ForegroundColor Green

# Get all jar files
$jarFiles = Get-ChildItem -Path $LibDir -Filter "*.jar"
Write-Host "Found $($jarFiles.Count) jar files" -ForegroundColor Yellow

# Create install commands
$installCommands = @()
$installCommands += "@echo off"
$installCommands += "echo Starting Maven installation of $($jarFiles.Count) dependencies..."
$installCommands += "echo."
$installCommands += "set SUCCESS_COUNT=0"
$installCommands += "set FAIL_COUNT=0"
$installCommands += ""

$processedCount = 0
foreach ($jar in $jarFiles) {
    $processedCount++
    $jarName = $jar.BaseName
    Write-Host "[$processedCount/$($jarFiles.Count)] Processing: $jarName" -ForegroundColor Cyan
    
    $groupId = ""
    $artifactId = ""
    $version = ""
    
    # Try to get Maven coordinates from META-INF/maven
    try {
        $tempDir = Join-Path $env:TEMP "maven_extract_$([System.Guid]::NewGuid().ToString('N')[0..7] -join '')"
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        
        # Extract META-INF directory
        $null = & jar -xf $jar.FullName -C $tempDir "META-INF" 2>$null
        
        # Find pom.properties files
        $pomPropsFiles = Get-ChildItem -Path $tempDir -Recurse -Filter "pom.properties" -ErrorAction SilentlyContinue
        
        if ($pomPropsFiles.Count -gt 0) {
            $pomProps = Get-Content $pomPropsFiles[0].FullName -ErrorAction SilentlyContinue
            foreach ($line in $pomProps) {
                if ($line -match "^groupId=(.+)$") {
                    $groupId = $matches[1].Trim()
                }
                elseif ($line -match "^artifactId=(.+)$") {
                    $artifactId = $matches[1].Trim()
                }
                elseif ($line -match "^version=(.+)$") {
                    $version = $matches[1].Trim()
                }
            }
        }
        
        # Clean up temp directory
        Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    catch {
        # Ignore errors and continue with filename parsing
    }
    
    # If unable to get coordinates from META-INF, parse from filename
    if (-not $groupId -or -not $artifactId -or -not $version) {
        # Parse filename pattern: artifactId-version.jar
        if ($jarName -match "^(.+?)-(\d+(?:\.\d+)*(?:-[A-Za-z0-9\.]+)?)$") {
            $artifactId = $matches[1]
            $version = $matches[2]
            
            # Determine groupId based on common patterns
            switch -Regex ($jarName) {
                "^spring-boot" { $groupId = "org.springframework.boot" }
                "^spring-" { $groupId = "org.springframework" }
                "^jackson-" { 
                    if ($jarName -match "jackson-databind|jackson-core|jackson-annotations") {
                        $groupId = "com.fasterxml.jackson.core"
                    } else {
                        $groupId = "com.fasterxml.jackson.dataformat"
                    }
                }
                "^commons-" { $groupId = "commons-" + $artifactId.Replace("commons-", "") }
                "^jrescloud-" { $groupId = "com.hundsun.jrescloud" }
                "^nacos-" { $groupId = "com.alibaba.nacos" }
                "^netty-" { $groupId = "io.netty" }
                "^tomcat-embed" { $groupId = "org.apache.tomcat.embed" }
                "^log4j-" { $groupId = "org.apache.logging.log4j" }
                "^slf4j-" { $groupId = "org.slf4j" }
                "^httpclient|^httpcore|^httpmime" { $groupId = "org.apache.httpcomponents" }
                "^poi" { $groupId = "org.apache.poi" }
                "^mysql-connector" { $groupId = "mysql" }
                "^jedis" { $groupId = "redis.clients" }
                "^gson" { $groupId = "com.google.code.gson" }
                "^guava" { $groupId = "com.google.guava" }
                "^fastjson" { $groupId = "com.alibaba" }
                "^hibernate-validator" { $groupId = "org.hibernate.validator" }
                "^validation-api" { $groupId = "javax.validation" }
                "^k-cloud-" { $groupId = "k-cloud" }
                "^middleware-" { $groupId = "com.hundsun.jrescloud.middleware" }
                "^(fina|cust|prod|plan|paym|wmp|bala)-" { $groupId = "com.kayakwise.wmp" }
                default { $groupId = "unknown" }
            }
        }
        else {
            # Cannot parse version, use defaults
            $artifactId = $jarName
            $version = "1.0.0"
            $groupId = "unknown"
        }
    }
    
    # Generate Maven install command
    $jarPath = $jar.FullName
    $installCmd = "mvn install:install-file -Dfile=`"$jarPath`" -DgroupId=$groupId -DartifactId=$artifactId -Dversion=$version -Dpackaging=jar -DgeneratePom=true"
    
    $installCommands += "echo [$processedCount/$($jarFiles.Count)] Installing: $groupId`:$artifactId`:$version"
    $installCommands += $installCmd
    $installCommands += "if errorlevel 1 ("
    $installCommands += "    echo [ERROR] Installation failed: $jarName"
    $installCommands += "    set /a FAIL_COUNT+=1"
    $installCommands += ") else ("
    $installCommands += "    echo [SUCCESS] Installation successful: $jarName"
    $installCommands += "    set /a SUCCESS_COUNT+=1"
    $installCommands += ")"
    $installCommands += "echo."
}

$installCommands += "echo =================================="
$installCommands += "echo Installation Summary:"
$installCommands += "echo Total dependencies: $($jarFiles.Count)"
$installCommands += "echo Successful installations: %SUCCESS_COUNT%"
$installCommands += "echo Failed installations: %FAIL_COUNT%"
$installCommands += "echo =================================="
$installCommands += "echo."
$installCommands += "echo All dependencies processing completed!"
$installCommands += "echo You can now use these dependencies in your Maven projects."
$installCommands += "pause"

# Save install script
$installCommands | Out-File -FilePath $OutputScript -Encoding UTF8
Write-Host "Maven install script created: $OutputScript" -ForegroundColor Green
Write-Host "Run the script to install all dependencies to your local Maven repository." -ForegroundColor Yellow
