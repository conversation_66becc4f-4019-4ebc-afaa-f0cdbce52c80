# 从Spring Boot Fat JAR中提取依赖并安装到Maven仓库的脚本
# 作者: Augment Agent
# 用途: 从wmp-service.jar中提取所有依赖jar包并安装到本地Maven仓库

param(
    [string]$FatJarPath = ".\extracted-dependencies\wmp-service\wmp-service\wmp-service.jar",
    [string]$ExtractDir = ".\lib-extracted",
    [string]$MavenInstallScript = ".\install-to-maven.bat"
)

Write-Host "开始从Fat JAR中提取依赖..." -ForegroundColor Green

# 创建提取目录
if (Test-Path $ExtractDir) {
    Remove-Item $ExtractDir -Recurse -Force
}
New-Item -ItemType Directory -Path $ExtractDir -Force | Out-Null

# 提取BOOT-INF/lib目录下的所有jar文件
Write-Host "正在提取jar文件..." -ForegroundColor Yellow
$tempDir = Join-Path $ExtractDir "temp"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# 使用jar命令提取BOOT-INF/lib目录
& jar -xf $FatJarPath -C $tempDir "BOOT-INF/lib"

# 移动jar文件到根目录
$libDir = Join-Path $tempDir "BOOT-INF\lib"
if (Test-Path $libDir) {
    Get-ChildItem -Path $libDir -Filter "*.jar" | ForEach-Object {
        Move-Item $_.FullName (Join-Path $ExtractDir $_.Name)
    }
}

# 清理临时目录
Remove-Item $tempDir -Recurse -Force

# 获取所有提取的jar文件
$jarFiles = Get-ChildItem -Path $ExtractDir -Filter "*.jar"
Write-Host "成功提取 $($jarFiles.Count) 个jar文件" -ForegroundColor Green

# 创建Maven安装脚本
$installCommands = @()
$installCommands += "@echo off"
$installCommands += "echo 开始安装依赖到Maven仓库..."
$installCommands += "echo."

foreach ($jar in $jarFiles) {
    $jarName = $jar.BaseName
    Write-Host "分析jar文件: $jarName" -ForegroundColor Cyan
    
    # 尝试从jar文件名解析Maven坐标
    $groupId = ""
    $artifactId = ""
    $version = ""
    
    # 检查jar内部的META-INF/maven目录来获取准确的Maven坐标
    try {
        $tempExtractDir = Join-Path $ExtractDir "temp_$jarName"
        New-Item -ItemType Directory -Path $tempExtractDir -Force | Out-Null
        
        # 提取META-INF目录
        & jar -xf $jar.FullName -C $tempExtractDir "META-INF" 2>$null
        
        # 查找pom.properties文件
        $pomPropsFiles = Get-ChildItem -Path $tempExtractDir -Recurse -Filter "pom.properties" -ErrorAction SilentlyContinue
        
        if ($pomPropsFiles.Count -gt 0) {
            $pomProps = Get-Content $pomPropsFiles[0].FullName
            foreach ($line in $pomProps) {
                if ($line -match "^groupId=(.+)$") {
                    $groupId = $matches[1]
                }
                elseif ($line -match "^artifactId=(.+)$") {
                    $artifactId = $matches[1]
                }
                elseif ($line -match "^version=(.+)$") {
                    $version = $matches[1]
                }
            }
        }
        
        # 清理临时目录
        Remove-Item $tempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    catch {
        Write-Host "无法从META-INF中获取Maven坐标，尝试从文件名解析" -ForegroundColor Yellow
    }
    
    # 如果无法从META-INF获取坐标，尝试从文件名解析
    if (-not $groupId -or -not $artifactId -or -not $version) {
        # 常见的jar文件命名模式: artifactId-version.jar
        if ($jarName -match "^(.+)-(\d+(?:\.\d+)*(?:-[A-Za-z0-9]+)?)$") {
            $artifactId = $matches[1]
            $version = $matches[2]
            
            # 根据已知的包名模式设置groupId
            if ($jarName -match "^spring-") {
                $groupId = "org.springframework"
            }
            elseif ($jarName -match "^jackson-") {
                $groupId = "com.fasterxml.jackson.core"
            }
            elseif ($jarName -match "^commons-") {
                $groupId = "commons-" + $artifactId.Replace("commons-", "")
            }
            elseif ($jarName -match "^jrescloud-") {
                $groupId = "com.hundsun.jrescloud"
            }
            elseif ($jarName -match "^nacos-") {
                $groupId = "com.alibaba.nacos"
            }
            else {
                $groupId = "unknown"
            }
        }
        else {
            # 无法解析版本，使用默认值
            $artifactId = $jarName
            $version = "1.0.0"
            $groupId = "unknown"
        }
    }
    
    # 生成Maven安装命令
    $jarPath = $jar.FullName.Replace('\', '/')
    $installCmd = "mvn install:install-file -Dfile=`"$jarPath`" -DgroupId=$groupId -DartifactId=$artifactId -Dversion=$version -Dpackaging=jar"
    $installCommands += "echo 安装: ${groupId}:${artifactId}:${version}"
    $installCommands += $installCmd
    $installCommands += "if errorlevel 1 ("
    $installCommands += "    echo 安装失败: $jarName"
    $installCommands += ") else ("
    $installCommands += "    echo 安装成功: $jarName"
    $installCommands += ")"
    $installCommands += "echo."
}

$installCommands += "echo 所有依赖安装完成！"
$installCommands += "pause"

# 保存安装脚本
$installCommands | Out-File -FilePath $MavenInstallScript -Encoding UTF8
Write-Host "Maven安装脚本已生成: $MavenInstallScript" -ForegroundColor Green

Write-Host "提取完成！" -ForegroundColor Green
Write-Host "提取的jar文件位于: $ExtractDir" -ForegroundColor Yellow
Write-Host "运行以下命令安装到Maven仓库: ${MavenInstallScript}" -ForegroundColor Yellow
