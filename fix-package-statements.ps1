# Fix broken package statements in bala service files
Write-Host "Fixing broken package statements..." -ForegroundColor Green

$problematicFiles = @(
    "wmp-service\src\main\java\com\kayak\bala\service\M906Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M910Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M936Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M942Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M945Service.java"
)

foreach ($file in $problematicFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing: $file" -ForegroundColor Cyan
        
        $content = Get-Content $file -Raw
        
        # Fix broken package statement
        $content = $content -replace "^ackage com\.kayak\.bala\.service;", "package com.kayak.bala.service;"
        
        # Write back the fixed content
        [System.IO.File]::WriteAllText($file, $content, [System.Text.UTF8Encoding]::new($false))
        
        Write-Host "  Fixed: $file" -ForegroundColor Green
    } else {
        Write-Host "  Not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "Package statement fix completed!" -ForegroundColor Green
