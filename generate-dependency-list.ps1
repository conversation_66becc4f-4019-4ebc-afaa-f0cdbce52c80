# Generate dependency list from install script
param(
    [string]$InstallScript = ".\install-to-maven.bat",
    [string]$OutputFile = ".\dependencies-list.txt"
)

Write-Host "Generating dependency list..." -ForegroundColor Green

$dependencies = @()
$dependencies += "# WMP Project Dependencies List"
$dependencies += "# Generated from extracted jar files"
$dependencies += "# Total: 200 dependencies"
$dependencies += ""
$dependencies += "## Maven Dependencies (for pom.xml)"
$dependencies += ""

# Read the install script and extract dependency information
$scriptContent = Get-Content $InstallScript -Encoding UTF8
$dependencyCount = 0

foreach ($line in $scriptContent) {
    if ($line -match "echo \[(\d+)/200\] Installing: (.+):(.+):(.+)") {
        $index = $matches[1]
        $groupId = $matches[2]
        $artifactId = $matches[3]
        $version = $matches[4]
        $dependencyCount++
        
        $dependencies += "<dependency>"
        $dependencies += "    <groupId>$groupId</groupId>"
        $dependencies += "    <artifactId>$artifactId</artifactId>"
        $dependencies += "    <version>$version</version>"
        $dependencies += "</dependency>"
        $dependencies += ""
    }
}

$dependencies += "## Summary"
$dependencies += "Total dependencies extracted: $dependencyCount"
$dependencies += ""
$dependencies += "## Key Dependencies Include:"
$dependencies += "- Spring Boot 2.2.2.RELEASE"
$dependencies += "- Spring Framework 5.2.9.RELEASE"
$dependencies += "- Jackson 2.10.x"
$dependencies += "- Apache Commons libraries"
$dependencies += "- Netty 4.1.x"
$dependencies += "- JresCloud *******"
$dependencies += "- Nacos 1.1.4"
$dependencies += "- MySQL Connector 8.0.25"
$dependencies += "- Redis Jedis 3.1.0"
$dependencies += "- Apache POI 3.17"
$dependencies += "- Log4j2 2.13.3"
$dependencies += "- Custom WMP modules (com.kayakwise.wmp)"
$dependencies += "- K-Cloud framework modules"
$dependencies += ""
$dependencies += "## Installation Instructions:"
$dependencies += "1. Run install-to-maven.bat to install all dependencies to local Maven repository"
$dependencies += "2. Add required dependencies to your pom.xml from the list above"
$dependencies += "3. Run 'mvn clean compile' to verify dependencies are resolved"

# Save dependency list
$dependencies | Out-File -FilePath $OutputFile -Encoding UTF8
Write-Host "Dependency list generated: $OutputFile" -ForegroundColor Green
Write-Host "Found $dependencyCount dependencies" -ForegroundColor Yellow
