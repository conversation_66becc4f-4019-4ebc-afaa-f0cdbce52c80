# Extract K-Cloud classes from jar files
Write-Host "Extracting K-Cloud classes from jar files..." -ForegroundColor Green

$kcloudJars = @(
    "k-cloud-action-1.0.0.jar",
    "k-cloud-core-1.0.0.jar", 
    "k-cloud-dao-1.0.0.jar",
    "k-cloud-cache-1.0.0.jar"
)

$extractDir = "wmp-service\src\main\java"

foreach ($jarName in $kcloudJars) {
    $jarPath = "lib-extracted\$jarName"
    
    if (Test-Path $jarPath) {
        Write-Host "Extracting from: $jarName" -ForegroundColor Cyan
        
        # Extract Java source files from jar
        try {
            & jar -xf $jarPath -C $extractDir 2>$null
            Write-Host "SUCCESS: Extracted from $jarName" -ForegroundColor Green
        }
        catch {
            Write-Host "FAILED: Could not extract from $jarName" -ForegroundColor Red
        }
    } else {
        Write-Host "NOT FOUND: $jarPath" -ForegroundColor Yellow
    }
}

Write-Host "K-Cloud class extraction completed!" -ForegroundColor Green
