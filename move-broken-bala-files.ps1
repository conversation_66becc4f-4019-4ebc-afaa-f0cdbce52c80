# Move broken bala service files to disabled folder
Write-Host "Moving broken bala service files..." -ForegroundColor Green

$brokenFiles = @(
    "M902Service.java",
    "M906Service.java", 
    "M910Service.java",
    "M936Service.java",
    "M942Service.java",
    "M945Service.java"
)

$sourceDir = "wmp-service\src\main\java\com\kayak\bala\service"
$disabledDir = "$sourceDir\disabled"

foreach ($file in $brokenFiles) {
    $sourcePath = "$sourceDir\$file"
    $destPath = "$disabledDir\$file"
    
    if (Test-Path $sourcePath) {
        Move-Item $sourcePath $destPath -Force
        Write-Host "Moved: $file" -ForegroundColor Green
    } else {
        Write-Host "Not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "Broken files moved successfully!" -ForegroundColor Green
