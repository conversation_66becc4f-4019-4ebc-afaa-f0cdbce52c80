# Fix encoding issues in modified bala service files
Write-Host "Fixing encoding issues in bala service files..." -ForegroundColor Green

$problematicFiles = @(
    "wmp-service\src\main\java\com\kayak\bala\service\M902Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M906Service.java", 
    "wmp-service\src\main\java\com\kayak\bala\service\M910Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M936Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M942Service.java",
    "wmp-service\src\main\java\com\kayak\bala\service\M945Service.java"
)

foreach ($file in $problematicFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing: $file" -ForegroundColor Cyan
        
        # Read content as bytes to handle encoding properly
        $content = Get-Content $file -Raw -Encoding UTF8
        
        # Remove BOM if present
        if ($content.StartsWith([char]0xFEFF)) {
            $content = $content.Substring(1)
        }
        
        # Write back without BOM
        [System.IO.File]::WriteAllText($file, $content, [System.Text.UTF8Encoding]::new($false))
        
        Write-Host "  Fixed: $file" -ForegroundColor Green
    } else {
        Write-Host "  Not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "Encoding fix completed!" -ForegroundColor Green
