# Install key dependencies for WMP project
Write-Host "Installing key dependencies..." -ForegroundColor Green

$keyDeps = @(
    @{file="k-cloud-action-dubbo-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-action-dubbo"; version="1.0.0"},
    @{file="k-cloud-cache-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-cache"; version="1.0.0"},
    @{file="k-cloud-config-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-config"; version="1.0.0"},
    @{file="k-cloud-core-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-core"; version="1.0.0"},
    @{file="k-cloud-dao-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-dao"; version="1.0.0"},
    @{file="k-cloud-service-starter-micro-server-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-service-starter-micro-server"; version="1.0.0"},
    @{file="k-cloud-xsql-1.0.0.jar"; groupId="k-cloud"; artifactId="k-cloud-xsql"; version="1.0.0"},
    @{file="wmp-base-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="wmp-base-api"; version="0.0.1-RELEASE"},
    @{file="fina-center-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="fina-center-api"; version="0.0.1-RELEASE"},
    @{file="fina-center-springcloud-dubbo-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="fina-center-springcloud-dubbo"; version="0.0.1-RELEASE"},
    @{file="prod-center-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="prod-center-api"; version="0.0.1-RELEASE"},
    @{file="prod-center-springcloud-dubbo-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="prod-center-springcloud-dubbo"; version="0.0.1-RELEASE"},
    @{file="plan-center-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="plan-center-api"; version="0.0.1-RELEASE"},
    @{file="plan-center-springcloud-dubbo-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="plan-center-springcloud-dubbo"; version="0.0.1-RELEASE"},
    @{file="cust-center-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="cust-center-api"; version="0.0.1-RELEASE"},
    @{file="cust-center-springcloud-dubbo-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="cust-center-springcloud-dubbo"; version="0.0.1-RELEASE"},
    @{file="paym-api-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="paym-api"; version="0.0.1-RELEASE"},
    @{file="bala-center-springcloud-dubbo-0.0.1-RELEASE.jar"; groupId="com.kayakwise.wmp"; artifactId="bala-center-springcloud-dubbo"; version="0.0.1-RELEASE"},
    @{file="ftsp-sdk1.8-2.2.jar"; groupId="com.dcfs.fts"; artifactId="ftsp-sdk1.8"; version="2.2"},
    @{file="sun-client-1.0.jar"; groupId="sun-client"; artifactId="sun-client"; version="1.0"},
    @{file="sun-ws-all-3.2.0.jar"; groupId="com.sunyard.ws"; artifactId="sun-ws-all"; version="3.2.0"},
    @{file="middleware-configcenter-client-1.2.14.jar"; groupId="com.hundsun.jrescloud.middleware"; artifactId="middleware-configcenter-client"; version="1.2.14"}
)

$successCount = 0
$failCount = 0

foreach ($dep in $keyDeps) {
    $jarPath = "lib-extracted\$($dep.file)"
    if (Test-Path $jarPath) {
        Write-Host "Installing: $($dep.groupId):$($dep.artifactId):$($dep.version)" -ForegroundColor Cyan
        
        $result = & mvn install:install-file -Dfile="$jarPath" -DgroupId="$($dep.groupId)" -DartifactId="$($dep.artifactId)" -Dversion="$($dep.version)" -Dpackaging=jar -DgeneratePom=true 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: $($dep.file)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "FAILED: $($dep.file)" -ForegroundColor Red
            $failCount++
        }
    } else {
        Write-Host "FILE NOT FOUND: $jarPath" -ForegroundColor Yellow
        $failCount++
    }
}

# Install parent POM
Write-Host "Installing parent POM..." -ForegroundColor Cyan
$result = & mvn install:install-file -Dfile="k-cloud-service-parent.pom" -DgroupId=k-cloud -DartifactId=k-cloud-service -Dversion=1.0.0 -Dpackaging=pom 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "SUCCESS: k-cloud-service parent POM" -ForegroundColor Green
    $successCount++
} else {
    Write-Host "FAILED: k-cloud-service parent POM" -ForegroundColor Red
    $failCount++
}

Write-Host "`nInstallation Summary:" -ForegroundColor Yellow
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
