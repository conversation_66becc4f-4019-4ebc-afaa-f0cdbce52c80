server:
  port: 8080
  servlet:
    context-path: /wmp

spring:
  application:
    name: wmp-service
  profiles:
    active: dev

logging:
  level:
    root: INFO
    com.kayak: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
