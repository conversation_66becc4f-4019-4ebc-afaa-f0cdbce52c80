# Extract dependencies from Spring Boot Fat JAR and install to Maven repository
param(
    [string]$FatJarPath = ".\extracted-dependencies\wmp-service\wmp-service\wmp-service.jar",
    [string]$ExtractDir = ".\lib-extracted"
)

Write-Host "Starting dependency extraction..." -ForegroundColor Green

# Create extraction directory
if (Test-Path $ExtractDir) {
    Remove-Item $ExtractDir -Recurse -Force
}
New-Item -ItemType Directory -Path $ExtractDir -Force | Out-Null

# Extract BOOT-INF/lib directory
Write-Host "Extracting jar files..." -ForegroundColor Yellow
$tempDir = Join-Path $ExtractDir "temp"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Use jar command to extract BOOT-INF/lib directory
& jar -xf $FatJarPath -C $tempDir "BOOT-INF/lib"

# Move jar files to root directory
$libDir = Join-Path $tempDir "BOOT-INF\lib"
if (Test-Path $libDir) {
    Get-ChildItem -Path $libDir -Filter "*.jar" | ForEach-Object {
        Move-Item $_.FullName (Join-Path $ExtractDir $_.Name)
    }
}

# Clean up temp directory
Remove-Item $tempDir -Recurse -Force

# Get all extracted jar files
$jarFiles = Get-ChildItem -Path $ExtractDir -Filter "*.jar"
Write-Host "Successfully extracted $($jarFiles.Count) jar files" -ForegroundColor Green

# Create Maven install script
$installCommands = @()
$installCommands += "@echo off"
$installCommands += "echo Starting Maven installation..."
$installCommands += "echo."

foreach ($jar in $jarFiles) {
    $jarName = $jar.BaseName
    Write-Host "Analyzing jar: $jarName" -ForegroundColor Cyan
    
    # Try to parse Maven coordinates from jar file name
    $groupId = ""
    $artifactId = ""
    $version = ""
    
    # Check META-INF/maven directory for accurate Maven coordinates
    try {
        $tempExtractDir = Join-Path $ExtractDir "temp_$jarName"
        New-Item -ItemType Directory -Path $tempExtractDir -Force | Out-Null
        
        # Extract META-INF directory
        & jar -xf $jar.FullName -C $tempExtractDir "META-INF" 2>$null
        
        # Find pom.properties files
        $pomPropsFiles = Get-ChildItem -Path $tempExtractDir -Recurse -Filter "pom.properties" -ErrorAction SilentlyContinue
        
        if ($pomPropsFiles.Count -gt 0) {
            $pomProps = Get-Content $pomPropsFiles[0].FullName
            foreach ($line in $pomProps) {
                if ($line -match "^groupId=(.+)$") {
                    $groupId = $matches[1]
                }
                elseif ($line -match "^artifactId=(.+)$") {
                    $artifactId = $matches[1]
                }
                elseif ($line -match "^version=(.+)$") {
                    $version = $matches[1]
                }
            }
        }
        
        # Clean up temp directory
        Remove-Item $tempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    catch {
        Write-Host "Cannot get Maven coordinates from META-INF, trying filename parsing" -ForegroundColor Yellow
    }
    
    # If unable to get coordinates from META-INF, try parsing from filename
    if (-not $groupId -or -not $artifactId -or -not $version) {
        # Common jar naming pattern: artifactId-version.jar
        if ($jarName -match "^(.+)-(\d+(?:\.\d+)*(?:-[A-Za-z0-9]+)?)$") {
            $artifactId = $matches[1]
            $version = $matches[2]
            
            # Set groupId based on known package patterns
            if ($jarName -match "^spring-") {
                $groupId = "org.springframework"
            }
            elseif ($jarName -match "^jackson-") {
                $groupId = "com.fasterxml.jackson.core"
            }
            elseif ($jarName -match "^commons-") {
                $groupId = "commons-" + $artifactId.Replace("commons-", "")
            }
            elseif ($jarName -match "^jrescloud-") {
                $groupId = "com.hundsun.jrescloud"
            }
            elseif ($jarName -match "^nacos-") {
                $groupId = "com.alibaba.nacos"
            }
            else {
                $groupId = "unknown"
            }
        }
        else {
            # Cannot parse version, use defaults
            $artifactId = $jarName
            $version = "1.0.0"
            $groupId = "unknown"
        }
    }
    
    # Generate Maven install command
    $jarPath = $jar.FullName.Replace('\', '/')
    $installCmd = "mvn install:install-file -Dfile=`"$jarPath`" -DgroupId=$groupId -DartifactId=$artifactId -Dversion=$version -Dpackaging=jar"
    $installCommands += "echo Installing: $groupId`:$artifactId`:$version"
    $installCommands += $installCmd
    $installCommands += "if errorlevel 1 ("
    $installCommands += "    echo Installation failed: $jarName"
    $installCommands += ") else ("
    $installCommands += "    echo Installation successful: $jarName"
    $installCommands += ")"
    $installCommands += "echo."
}

$installCommands += "echo All dependencies installed!"
$installCommands += "pause"

# Save install script
$installScript = ".\install-to-maven.bat"
$installCommands | Out-File -FilePath $installScript -Encoding UTF8
Write-Host "Maven install script generated: $installScript" -ForegroundColor Green

Write-Host "Extraction completed!" -ForegroundColor Green
Write-Host "Extracted jar files location: $ExtractDir" -ForegroundColor Yellow
Write-Host "Run the following command to install to Maven repository: $installScript" -ForegroundColor Yellow
