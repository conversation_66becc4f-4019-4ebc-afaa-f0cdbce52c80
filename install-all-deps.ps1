# Install all remaining dependencies
Write-Host "Installing all remaining dependencies..." -ForegroundColor Green

$jarFiles = Get-ChildItem "lib-extracted" -Filter "*.jar"
$successCount = 0
$failCount = 0

foreach ($jar in $jarFiles) {
    $jarName = $jar.BaseName
    Write-Host "Installing: $jarName" -ForegroundColor Cyan
    
    # Parse Maven coordinates from filename
    $groupId = ""
    $artifactId = ""
    $version = ""
    
    # Try to parse filename pattern: artifactId-version.jar
    if ($jarName -match "^(.+?)-(\d+(?:\.\d+)*(?:-[A-Za-z0-9\.]+)?)$") {
        $artifactId = $matches[1]
        $version = $matches[2]
        
        # Determine groupId based on common patterns
        switch -Regex ($jarName) {
            "^spring-boot" { $groupId = "org.springframework.boot" }
            "^spring-" { $groupId = "org.springframework" }
            "^jackson-" { 
                if ($jarName -match "jackson-databind|jackson-core|jackson-annotations") {
                    $groupId = "com.fasterxml.jackson.core"
                } else {
                    $groupId = "com.fasterxml.jackson.dataformat"
                }
            }
            "^commons-" { $groupId = "commons-" + $artifactId.Replace("commons-", "") }
            "^jrescloud-" { $groupId = "com.hundsun.jrescloud" }
            "^nacos-" { $groupId = "com.alibaba.nacos" }
            "^netty-" { $groupId = "io.netty" }
            "^tomcat-embed" { $groupId = "org.apache.tomcat.embed" }
            "^log4j-" { $groupId = "org.apache.logging.log4j" }
            "^slf4j-" { $groupId = "org.slf4j" }
            "^httpclient|^httpcore|^httpmime" { $groupId = "org.apache.httpcomponents" }
            "^poi" { $groupId = "org.apache.poi" }
            "^mysql-connector" { $groupId = "mysql" }
            "^jedis" { $groupId = "redis.clients" }
            "^gson" { $groupId = "com.google.code.gson" }
            "^guava" { $groupId = "com.google.guava" }
            "^fastjson" { $groupId = "com.alibaba" }
            "^hibernate-validator" { $groupId = "org.hibernate.validator" }
            "^validation-api" { $groupId = "javax.validation" }
            "^k-cloud-" { $groupId = "k-cloud" }
            "^middleware-" { $groupId = "com.hundsun.jrescloud.middleware" }
            "^(fina|cust|prod|plan|paym|wmp|bala)-" { $groupId = "com.kayakwise.wmp" }
            "^lombok" { $groupId = "org.projectlombok" }
            "^aspectjweaver" { $groupId = "org.aspectj" }
            "^micrometer" { $groupId = "io.micrometer" }
            "^HdrHistogram" { $groupId = "org.hdrhistogram" }
            "^LatencyUtils" { $groupId = "org.latencyutils" }
            "^disruptor" { $groupId = "com.lmax" }
            "^ehcache" { $groupId = "net.sf.ehcache" }
            "^dom4j" { $groupId = "org.dom4j" }
            "^zookeeper" { $groupId = "org.apache.zookeeper" }
            "^curator" { $groupId = "org.apache.curator" }
            "^rxjava" { $groupId = "io.reactivex" }
            "^rxnetty" { $groupId = "io.reactivex" }
            "^simpleclient" { $groupId = "io.prometheus" }
            "^snakeyaml" { $groupId = "org.yaml" }
            "^woodstox" { $groupId = "com.fasterxml.woodstox" }
            "^stax" { $groupId = "javax.xml.stream" }
            "^txw2" { $groupId = "org.glassfish.jaxb" }
            "^jaxb" { $groupId = "org.glassfish.jaxb" }
            "^istack" { $groupId = "com.sun.istack" }
            "^jakarta" { $groupId = "jakarta." + $artifactId.Replace("jakarta.", "") }
            "^javax" { $groupId = "javax." + $artifactId.Replace("javax.", "") }
            "^jboss" { $groupId = "org.jboss.logging" }
            "^classmate" { $groupId = "com.fasterxml" }
            "^javassist" { $groupId = "org.javassist" }
            "^asm" { $groupId = "org.ow2.asm" }
            "^cglib" { $groupId = "cglib" }
            "^animal-sniffer" { $groupId = "org.codehaus.mojo" }
            "^error_prone" { $groupId = "com.google.errorprone" }
            "^j2objc" { $groupId = "com.google.j2objc" }
            "^jsr305" { $groupId = "com.google.code.findbugs" }
            "^checker-compat" { $groupId = "org.checkerframework" }
            "^spotbugs" { $groupId = "com.github.spotbugs" }
            "^audience" { $groupId = "org.apache.yetus" }
            "^bcprov" { $groupId = "org.bouncycastle" }
            "^nekohtml" { $groupId = "net.sourceforge.nekohtml" }
            "^xercesImpl" { $groupId = "xerces" }
            "^xml-apis" { $groupId = "xml-apis" }
            "^xmlbeans" { $groupId = "org.apache.xmlbeans" }
            "^xmlpull" { $groupId = "xmlpull" }
            "^xpp3" { $groupId = "xpp3" }
            "^xom" { $groupId = "xom" }
            "^xalan" { $groupId = "xalan" }
            "^xstream" { $groupId = "com.thoughtworks.xstream" }
            "^batik" { $groupId = "org.apache.xmlgraphics" }
            "^bsh" { $groupId = "org.beanshell" }
            "^antisamy" { $groupId = "org.owasp.antisamy" }
            "^esapi" { $groupId = "org.owasp.esapi" }
            "^curvesapi" { $groupId = "com.github.virtuald" }
            "^easyexcel" { $groupId = "com.alibaba" }
            "^jline" { $groupId = "jline" }
            "^joda-time" { $groupId = "joda-time" }
            "^json-" { $groupId = "org.json" }
            "^jul-to-slf4j" { $groupId = "org.slf4j" }
            "^jcl-over-slf4j" { $groupId = "org.slf4j" }
            "^kayakwise-transaction" { $groupId = "com.kayakwise" }
            "^opentracing" { $groupId = "io.opentracing" }
            "^apm-toolkit" { $groupId = "org.apache.skywalking" }
            default { $groupId = "unknown" }
        }
    }
    else {
        # Cannot parse version, use defaults
        $artifactId = $jarName
        $version = "1.0.0"
        $groupId = "unknown"
    }
    
    # Skip if already installed
    $localRepoPath = "$env:USERPROFILE\.m2\repository\" + $groupId.Replace(".", "\") + "\" + $artifactId + "\" + $version
    if (Test-Path $localRepoPath) {
        Write-Host "SKIPPED: Already installed - $jarName" -ForegroundColor Yellow
        continue
    }
    
    # Install the jar
    $jarPath = $jar.FullName
    $result = & mvn install:install-file -Dfile="$jarPath" -DgroupId="$groupId" -DartifactId="$artifactId" -Dversion="$version" -Dpackaging=jar -DgeneratePom=true 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: $jarName" -ForegroundColor Green
        $successCount++
    } else {
        Write-Host "FAILED: $jarName" -ForegroundColor Red
        $failCount++
    }
}

Write-Host "`nInstallation Summary:" -ForegroundColor Yellow
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
